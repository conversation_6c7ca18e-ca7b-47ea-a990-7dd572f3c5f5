<?php

function add_admin_log($data) {
    if(!is_array($data))
        return FALSE;

    $adminLogModel = new \App\Models\AdminLogModel();

    $result = $adminLogModel->insert($data);

    return $result;
}

function check_logged_admin() {
    $session = session();

    if(isset($_SESSION['admin_logged_in']['admin_id'])) {
        return TRUE;
    } else
        return FALSE;
}


function has_permission($permission, $can, $admin_id=FALSE) {

    $session = session();
    if (!$session->get('admin_logged_in'))
        return FALSE;

    $admin_session = $session->get('admin_logged_in');

    // this case check for current logged user
    if($admin_id === FALSE) {
        if(!isset($admin_session['admin_id']))
            return FALSE;
        $admin_id = $admin_session['admin_id'];

    }


    // check SuperAdmin or Admin
    $adminModel = new \App\Models\AdminModel();


    if($admin_session['role'] == 'SuperAdmin' || $admin_session['role'] == 'Admin')
        return TRUE;

    // normal user
    $adminPermissionFeatureModel = new \App\Models\AdminPermissionFeatureModel();

    $result = $adminPermissionFeatureModel->where(['admin_id' => $admin_id, 'feature_slug' => $permission, $can => 1])->countAllResults();

    if($result == 1)
        return TRUE;
    else
        return FALSE;

}

function show_404() {
    throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
}

function get_gravatar( $email, $s = 150, $d = 'identicon', $r = 'g', $img = false, $atts = array() ) {
    $url = 'https://www.gravatar.com/avatar/';
    $url .= md5( strtolower( trim( $email ) ) );
    $url .= "?s=$s&d=$d&r=$r";
    if ( $img ) {
        $url = '<img src="' . $url . '"';
        foreach ( $atts as $key => $val )
            $url .= ' ' . $key . '="' . $val . '"';
        $url .= ' />';
    }
    return $url;
}


function remove_accents($str, $uppercase=false) {
    $str = trim(mb_strtolower($str));
    $str = preg_replace('/(à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ)/', 'a', $str);
    $str = preg_replace('/(è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ)/', 'e', $str);
    $str = preg_replace('/(ì|í|ị|ỉ|ĩ)/', 'i', $str);
    $str = preg_replace('/(ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ)/', 'o', $str);
    $str = preg_replace('/(ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ)/', 'u', $str);
    $str = preg_replace('/(ỳ|ý|ỵ|ỷ|ỹ)/', 'y', $str);
    $str = preg_replace('/(đ)/', 'd', $str);
    $str = preg_replace('/[^a-z0-9-\s]/', '', $str);
    if($uppercase)
        return strtoupper($str);
    else
        return $str;
}

function timespan($datetime, $full = false) {
    $now = new DateTime;
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);

    $diff->w = floor($diff->d / 7);
    $diff->d -= $diff->w * 7;

    $string = array(
        'y' => 'năm',
        'm' => 'tháng',
        'w' => 'tuần',
        'd' => 'ngày',
        'h' => 'giờ',
        'i' => 'phút',
        's' => 'giây',
    );
    foreach ($string as $k => &$v) {
        if ($diff->$k) {
            $v = $diff->$k . ' ' . $v . ($diff->$k > 1 ? '' : '');
        } else {
            unset($string[$k]);
        }
    }

    if ($full) $string = array_slice($string, 0, $full);
    return $string ? implode(', ', $string) . ' trước' : 'vừa mới';
}

function is_mobile() {
    $request = \Config\Services::request();
    $agent = $request->getUserAgent();
    if ($agent->isMobile())
        return TRUE;
    else
        return FALSE;
}

function create_paycode($invoice_id) {
    $prefix = "SEP";
    if(strlen($invoice_id) < 8) {
        $zero_add = 8 - strlen($invoice_id);
        $pay_code = $prefix;
        for($i = 0; $i < $zero_add;$i++) {
            $pay_code = $pay_code . '0';
        }

        $pay_code = $pay_code . $invoice_id;


    } else {
        $pay_code = $prefix . $invoice_id;

    }
    return $pay_code;
}

function billing_cycle_to_month($b) {
    $map = [
        'free' => 1,
        'monthly' => 1,
        'quarterly' => 3,
        'semi-annually' => 6,
        'annually' => 12,
        'biennially' => 24,
        'triennially' => 36,
    ];

    if(isset($map[$b]))
        return $map[$b];
    else
        return FALSE;
}

function get_send_to($notification_id) {
    $notificationToModel = new \App\Models\NotificationToModel();
    $userModel = new \App\Models\UserModel();
    $bankAccountModel = new \App\Models\BankAccountModel();

    $results = $notificationToModel->where(['notification_id' => $notification_id])->get()->getResult();

    $users_to = [];
    foreach($results as $result) {
        if($result->data_type == "All") {

            $users = $userModel->where(['active' => 1])->orderBy('id','ASC')->get()->getResult();
            foreach($users as $user) {
                if(!in_array($user->id, $users_to))
                    array_push($users_to, $user->id);
            }
            break;
        }  else if($result->data_type == "Bank") {

            $users = $bankAccountModel->select("tb_autopay_user.id")->join("tb_autopay_company_user","tb_autopay_company_user.company_id=tb_autopay_bank_account.company_id","left")->join("tb_autopay_user","tb_autopay_user.id=tb_autopay_company_user.user_id","left")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_bank.id' => $result->data_id])->orderBy('tb_autopay_user.id','ASC')->groupBy('tb_autopay_user.id')->get()->getResult();

            foreach($users as $user) {
                if(!in_array($user->id, $users_to))
                    array_push($users_to, $user->id);
            }
            break;
        }
    }

    return $users_to;
}

function set_alert($type, $message, $keep=FALSE)
{
    $session = session();
    $session->setFlashdata('alert-' . $type, $message);

}

function get_month_by_billing_cycle($billing_cycle) {
    $b =  ['free' => 1,'monthly' => 1,'quarterly' => 3,'semi-annually' => 6,'annually' => 12,'biennially' =>24,'triennially' => 36];
    if(isset ($b[$billing_cycle]))
        return $b[$billing_cycle];
    else
        return FALSE;
}

function notify_sim_config($type, $user, $company, $planName, $billingCycle) {
    $simCount = model(\App\Models\SimCompanyModel::class)->where(['company_id' => $company->id])->countAllResults();

    if ($type == 'assign' && $simCount > 0) return;
    if ($type == 'unassign' && $simCount == 0) return;

    $simActionText = $type === 'assign' ? '➕ gắn' : '✖️ gỡ';
    $config = config(\App\Config\Billing::class);
    $message = '
------------------------------
‼️ Khách hàng đổi gói dịch vụ cần ' . $simActionText . ' SIM:

📦 Gói dịch vụ đã đổi: ' . $planName . ' (' . $billingCycle . ')

#️⃣ Thông tin KH:
    - Họ tên: ' . $user->lastname . ' ' . $user->firstname . '
    - Email: ' . $user->email . '
    - Tên công ty/tổ chức: ' . $company->full_name . ' (' .  $company->short_name . ')

⏰ Thực hiện lúc: ' . date("Y-m-d H:i:s") . '
------------------------------
                            ';

    $telegramQueueModel = model(\App\Models\NotificationTelegramQueueModel::class);
    $telegramQueueModel->insert([
        'chat_id' => $config->telegramChatId,
        'status' => 'Pending',
        'message' => $message
    ]);
}

function add_system_log($data) {
    if(!is_array($data))
        return FALSE;

    $systemLogModel = new \App\Models\SystemLogModel();

    $result = $systemLogModel->insert($data);

    return $result;
}

function get_ticket_status_badge($status) {
    if($status == "Open")
        $badge = "<span class='badge rounded-pill bg-primary'>Đang mở</span>";
    else if($status == "Answered")
        $badge = "<span class='badge rounded-pill bg-success'>Đã trả lời</span>";
    else if($status == "InProgress")
        $badge = "<span class='badge rounded-pill bg-danger'>Đang xử lý</span>";
    else if($status == "ClientReply")
        $badge = "<span class='badge rounded-pill bg-info'>Khách hàng trả lời</span>";
    else if($status == "Closed")
        $badge = "<span class='badge rounded-pill bg-dark'>Đã đóng</span>";
    else
        $badge = esc($status);
    return $badge;
}

function get_full_tax_info_address($id) {
    $taxInfoModel = new \App\Models\TaxInfoModel();

    $result = $taxInfoModel->select("tb_autopay_provinces.full_name as `province_name`, tb_autopay_districts.full_name as `district_name`, tb_autopay_wards.full_name as `ward_name`, tb_autopay_tax_info.address1")->join("tb_autopay_provinces","tb_autopay_provinces.code=tb_autopay_tax_info.province","left")->join("tb_autopay_districts","tb_autopay_districts.code=tb_autopay_tax_info.district","left")->join("tb_autopay_wards","tb_autopay_wards.code=tb_autopay_tax_info.ward","left")->where(['id' => $id])->get()->getRow();

    if(is_object($result))
        return esc($result->address1 . ', ' . $result->ward_name . ', ' . $result->district_name . ', ' . $result->province_name);
    else
        return '';

}

function slavable_model($modelClassName, $key)
{
    $optimizationConfig = config(\Config\Optimization::class);

    $availableModels = array_keys($optimizationConfig->slave['models']);

    if (in_array($modelClassName, $availableModels)
        && isset($optimizationConfig->slave['models'][$modelClassName][$key])
        && $optimizationConfig->slave['models'][$modelClassName][$key] === true) {
        $dbConfig = config(\Config\Database::class);
        $db = db_connect($dbConfig->read);

        if ($optimizationConfig->slave['debug']) {
            log_message('error', "[Optimization]: Query was routed into mysql slave {$modelClassName} - {$key} - " . json_encode(debug_backtrace()[0]));
        }

        return model($modelClassName, false, $db);
    }

    return model($modelClassName);
}
function xss_clean($str) {
    $str = preg_replace('/[^a-zA-Z0-9-.,\/àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ\s]/', '', $str);
    return $str;
}

function format_currency($value)
{
    if (is_numeric($value)) {
        return number_format($value, 0, ',', '.') . ' đ';
    } else {
        return $value;
    }
}

function get_order_payment_status_badge($status)
{
    switch($status) {
        case 'Unpaid':
            $badge = '<span class="badge bg-warning me-2">Chờ thanh toán</span>';
            break;
        case 'Paid':
            $badge = '<span class="badge bg-success me-2">Đã thanh toán</span>';
            break;
        case 'Refunded':
            $badge = '<span class="badge bg-danger me-2">Đã hoàn tiền</span>';
            break;
        default:
            $badge = '<span class="badge bg-secondary me-2">Không xác định</span>';
            break;
    }

    return $badge;
}

function get_configuration($setting, $company_id) {

    $configurationModel = new \App\Models\ConfigurationModel();

    $result = $configurationModel->where(['company_id' => $company_id, 'setting' => $setting])->get()->getRow();
    
    if(!is_object($result))
        return '';
    else
        return $result->value;
}