<?php

namespace App\Actions\Notification;

use App\Libraries\RabbitMQClient;
use PhpAmqpLib\Message\AMQPMessage;

class PushNotificationFcmByTokenQueueAction
{
    /**
     * @see $notificaiton https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages#Notification
     */
    public static function run($token, $notification, $data = null, $apns = null, $webpush = null, $deviceTokenId = null, $notificationId = null, $userId = null, $isBrowser = 0)
    {
        $rabbitmq = new RabbitMQClient;
        $rabbitmq->connect();

        $msg = new AMQPMessage(
            json_encode([
                'token' => $token,
                'notification' => $notification,
                'data' => $data,
                'apns' => $apns,
                'device_token_id' => $deviceTokenId,
                'notification_id' => $notificationId,
                'user_id' => $userId,
                'webpush' => $webpush, 
                'is_browser' => $isBrowser
            ]),
            array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
        );

        try {
            $rabbitmq->queueDeclare('noti_fcm_pushing');
            $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('noti_fcm_pushing', 'name'));
        } catch (Exception $e) {
            log_message('error', 'Notification FCM pushing queue failed: ' . $e->getMessage());
        }

        $rabbitmq->close();
    }
}
