<?php

namespace App\Libraries;

use CodeIgniter\HTTP\CURLRequest;
use Config\SInvoice as Config;
use Exception;

class SInvoice
{
    protected CURLRequest $client;

    protected static Config $config;

    protected const API_URL = 'https://api-vinvoice.viettel.vn/services/einvoiceapplication/api';

    public function __construct()
    {
        $this->client = ProxyCURLRequest::make()->setProxy('sinvoice');
    }

    public static function setConfig(Config $config): self
    {
        self::$config = $config;

        return new self();
    }

    public function createInvoice(array $data): array
    {
        $endpoint = self::$config->useDraftInvoice
            ? '/InvoiceAPI/InvoiceWS/createOrUpdateInvoiceDraft/'
            : '/InvoiceAPI/InvoiceWS/createInvoice/';

        if (! empty(self::$config->invoiceSeri)) {
            $invoiceSeri = self::$config->invoiceSeri;
            $templateCode = self::$config->templateCode;
        } else {
            $template = self::getLatestInvoiceTemplate();

            if (empty($template)) {
                throw new Exception('Không tìm thấy kí hiệu hóa đơn nào để sử dụng tạo hóa đơn');
            }

            $invoiceSeri = $template['invoiceSeri'];
            $templateCode = $template['templateCode'];
        }

        return $this->request('POST', $endpoint . self::$config->sellerInfo['taxCode'], [
            'generalInvoiceInfo' => [
                'invoiceType' => self::$config->invoiceType,
                'templateCode' => $templateCode,
                'invoiceSeries' => $invoiceSeri,
                'currencyCode' => 'VND',
                'paymentStatus' => $data['generalInvoiceInfo']['paymentStatus'],
                'invoiceIssuedDate' => $data['generalInvoiceInfo']['invoiceIssuedDate'],
            ],
            'sellerInfo' => [
                'sellerLegalName' => self::$config->sellerInfo['legalName'],
                'sellerTaxCode' => self::$config->sellerInfo['taxCode'],
                'sellerAddressLine' => self::$config->sellerInfo['addressLine'],
                'sellerPhoneNumber' => self::$config->sellerInfo['phoneNumber'],
                'sellerEmail' => self::$config->sellerInfo['email'],
                'sellerBankName' => self::$config->sellerInfo['bankName'],
                'sellerBankAccount' => self::$config->sellerInfo['bankAccount'],
                'sellerWebsite' => self::$config->sellerInfo['website'],
            ],
            'buyerInfo' => $data['buyerInfo'],
            'payments' => [
                [
                    'paymentMethod' => 8,
                    'paymentMethodName' => 'Tiền mặt/Chuyển khoản',
                ]
            ],
            'itemInfo' => $data['itemInfo'],
            'summarizeInfo' => $data['summarizeInfo'],
            'taxBreakdowns' => $data['taxBreakdowns'],
        ]);
    }

    protected function request(string $method, string $url, array $data = []): array
    {
        $response = $this->client->request($method, self::API_URL . $url, [
            'json' => $data,
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'auth' => [
                self::$config->username,
                self::$config->password,
            ],
            'http_errors' => false,
        ]);

        return json_decode($response->getBody(), true);
    }

    public function getInvoiceTemplates(string $type = 'all'): array
    {
        return $this->request('POST', '/InvoiceAPI/InvoiceUtilsWS/getAllInvoiceTemplates', [
            'taxCode' => self::$config->sellerInfo['taxCode'],
            'invoiceType' => $type,
        ]);
    }

    /**
     * @return array{templateCode: string, invoiceSeri: string}|null
     */
    public function getLatestInvoiceTemplate(): ?array
    {
        $templates = $this->getInvoiceTemplates(1)['template'];

        if (empty($templates)) {
            return null;
        }

        $pattern = self::$config->getInvoiceSeriPattern();

        foreach ($templates as $template) {
            if (preg_match($pattern, $template['invoiceSeri'])) {
                return [
                    'templateCode' => $template['templateCode'] ?? '',
                    'invoiceSeri' => $template['invoiceSeri'] ?? ''
                ];
            }
        }

        $latestTemplate = end($templates);

        return [
            'templateCode' => $latestTemplate['templateCode'] ?? '',
            'invoiceSeri' => $latestTemplate['invoiceSeri'] ?? ''
        ];
    }

    public function getInvoiceFile(
        string $invoiceNo,
        ?string $transactionUuid = null,
        string $fileType = 'PDF',
        ?bool $paid = null,
        ?string $startDate = null,
        ?string $endDate = null
    ): array {
        if (empty(self::$config->templateCode)) {
            $template = $this->getLatestInvoiceTemplate();

            if (empty($template)) {
                throw new Exception('Không tìm thấy kí hiệu hóa đơn nào để sử dụng tạo hóa đơn');
            }

            self::$config->templateCode = $template['templateCode'];
        }

        $data = [
            'supplierTaxCode' => self::$config->sellerInfo['taxCode'],
            'invoiceNo' => $invoiceNo,
            'templateCode' => self::$config->templateCode,
            'fileType' => $fileType,
        ];

        if ($transactionUuid) {
            $data['transactionUuid'] = $transactionUuid;
        }

        if ($paid !== null) {
            $data['paid'] = $paid;
        }

        if ($startDate) {
            $data['startDate'] = $startDate;
        }

        if ($endDate) {
            $data['endDate'] = $endDate;
        }

        return $this->request('POST', '/InvoiceAPI/InvoiceUtilsWS/getInvoiceRepresentationFile', $data);
    }
}
