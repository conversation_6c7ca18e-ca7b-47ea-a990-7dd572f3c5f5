<?php

namespace App\Libraries;

use CodeIgniter\HTTP\CURLRequest;
use Config\Ghtk;
use Config\Services;
use Exception;

class GhtkClient
{
    protected const BASE_URL = 'https://services.giaohangtietkiem.vn';

    protected const ENDPOINTS = [
        'TRACKING_STATUS' => '/services/shipment/v2/',
        'CREATE_ORDER' => '/services/shipment/order',
    ];

    protected CURLRequest $client;

    protected Ghtk $config;

    public function __construct()
    {
        $this->client = Services::curlrequest();
        $this->config = config(Ghtk::class);
    }

    public function trackingStatus(string $trackingCode)
    {
        return $this->request('GET', self::ENDPOINTS['TRACKING_STATUS'] . $trackingCode);
    }

    public function createOrder(array $orderData)
    {
        return $this->request('POST', self::ENDPOINTS['CREATE_ORDER'], $orderData);
    }
    
    public function printOrder(string $trackingCode, string $original = 'portrait', string $paperSize = 'A5')
    {
        return $this->request('GET', sprintf('/services/label/%s', $trackingCode), ['original' => $original, 'pager_size' => $paperSize], 'json', true);
    }

    protected function request(string $method, string $endpoint, array $data = [], string $contentType = 'json', bool $skipResponseFormat = false)
    {
        $url = self::BASE_URL . $endpoint;

        $headers = [
            'Token' => $this->config->apiToken,
        ];

        if (! empty($this->config->partnerCode)) {
            $headers['X-Client-Source'] = $this->config->partnerCode;
        }

        $options = ['headers' => $headers];

        if ($contentType === 'json') {
            $headers['Content-Type'] = 'application/json';
            $options['json'] = $data;
        } else {
            $headers['Content-Type'] = 'application/x-www-form-urlencoded';
            $options['form_params'] = $data;
        }
        
        if ($method === 'GET') {
            $options['query'] = $data;
        }

        try {
            $response = $this->client->request($method, $url, $options);
            
            if ($skipResponseFormat) {
                return $response;
            }
            
            $body = $response->getBody();

            return json_decode($body, true) ?: [
                'success' => false,
                'message' => 'Invalid response format',
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
            ];
        }
    }
}
