<?php

namespace App\Libraries;

use Config\App;
use Config\Services;
use CodeIgniter\HTTP\URI;
use CodeIgniter\HTTP\Response;
use CodeIgniter\HTTP\CURLRequest;
use CodeIgniter\HTTP\ResponseInterface;

class ProxyCURLRequest extends CURLRequest
{
    protected $proxy;

    public function setProxy($name = 'default')
    {
        $this->proxy = static::getProxy($name);

        return $this;
    }

    public static function getProxy($name = 'default')
    {
        $curlConfig = config(\Config\CURLRequest::class);
        $proxies = $curlConfig->proxies ?? [];

        return $proxies[$name] ?? null;
    }

    /**
     * Set CURL options
     *
     * @return array
     *
     * @throws InvalidArgumentException
     */
    protected function setCURLOptions(array $curlOptions = [], array $config = [])
    {
        $curlOptions = parent::setCURLOptions($curlOptions, $config);

        $curlOptions[CURLOPT_HTTPPROXYTUNNEL] = true;
        $curlOptions[CURLOPT_PROXY] = $this->proxy;

        return $curlOptions;
    }

    /**
     * Fires the actual cURL request.
     *
     * @see https://github.com/codeigniter4/CodeIgniter4/blob/develop/system/HTTP/CURLRequest.php#L354
     * @return ResponseInterface
     */
    public function send(string $method, string $url)
    {
        // Reset our curl options so we're on a fresh slate.
        $curlOptions = [];

        if (! empty($this->config['query']) && is_array($this->config['query'])) {
            // This is likely too naive a solution.
            // Should look into handling when $url already
            // has query vars on it.
            $url .= '?' . http_build_query($this->config['query']);
            unset($this->config['query']);
        }

        $curlOptions[CURLOPT_URL]            = $url;
        $curlOptions[CURLOPT_RETURNTRANSFER] = true;
        $curlOptions[CURLOPT_HEADER]         = true;
        $curlOptions[CURLOPT_FRESH_CONNECT]  = true;
        // Disable @file uploads in post data.
        $curlOptions[CURLOPT_SAFE_UPLOAD] = true;

        $curlOptions = $this->setCURLOptions($curlOptions, $this->config);
        $curlOptions = $this->applyMethod($method, $curlOptions);
        $curlOptions = $this->applyRequestHeaders($curlOptions);

        // Do we need to delay this request?
        if ($this->delay > 0) {
            usleep((int) $this->delay * 1_000_000);
        }

        $output = $this->sendRequest($curlOptions);

        // Set the string we want to break our response from
        $breakString = "\r\n\r\n";

        if (strpos($output, 'HTTP/1.1 100 Continue') === 0) {
            $output = substr($output, strpos($output, $breakString) + 4);
        }

        if (strpos($output, 'HTTP/1.1 200 Connection established') === 0) {
            $output = substr($output, strpos($output, $breakString) + 4);
        }

        // If request and response have Digest
        if (isset($this->config['auth'][2]) && $this->config['auth'][2] === 'digest' && strpos($output, 'WWW-Authenticate: Digest') !== false) {
            $output = substr($output, strpos($output, $breakString) + 4);
        }

        // Split out our headers and body
        $break = strpos($output, $breakString);

        if ($break !== false) {
            // Our headers
            $headers = explode("\n", substr($output, 0, $break));

            $this->setResponseHeaders($headers);

            // Our body
            $body = substr($output, $break + 4);
            $this->response->setBody($body);
        } else {
            $this->response->setBody($output);
        }

        return $this->response;
    }

    public static function make(array $options = [], ?ResponseInterface $response = null, ?App $config = null, bool $getShared = true)
    {
        $config ??= config('App');
        $response ??= new Response($config);

        $curl = new static(
            $config,
            new URI($options['base_uri'] ?? null),
            $response,
            $options
        );

        return $curl->setProxy();
    }
}
