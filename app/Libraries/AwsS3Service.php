<?php

namespace App\Libraries;

use CodeIgniter\HTTP\CURLRequest;
use Config\Services;
use Exception;

class AwsS3Service
{
    protected $bucket;
    protected $key;
    protected $secret;
    protected $endpoint;
    protected $expiresUrl;
    protected CURLRequest $client;

    public function __construct()
    {
        $config = config('AwsS3');

        $this->bucket = $config->bucket;
        $this->key = $config->key;
        $this->secret = $config->secret;
        $this->endpoint = $config->endpoint;
        $this->expiresUrl = $config->expiresUrl;
        $this->client = Services::curlrequest([
            'verify' => false,
            'http_errors' => false,
        ]);
    }

    public function upload($file, $key, $isContent = false, $contentType = null, $acl = 'private')
    {
        try {
            $key = ltrim($key, '/');
            $url = rtrim($this->endpoint, '/') . '/' . $this->bucket . '/' . $key;

            if ($isContent) {
                $fileContent = $file;
            } else {
                if (!file_exists($file)) {
                    throw new Exception("File does not exist: $file");
                }
                $fileContent = file_get_contents($file);
            }

            if (!$contentType && !$isContent) {
                $finfo = new \finfo(FILEINFO_MIME_TYPE);
                $contentType = $finfo->file($file);
            } elseif (!$contentType) {
                $contentType = 'application/octet-stream';
            }

            $date = gmdate('D, d M Y H:i:s T');
            $contentMD5 = base64_encode(md5($fileContent, true));
            $stringToSign = "PUT\n$contentMD5\n$contentType\n$date\nx-amz-acl:$acl\n/$this->bucket/$key";
            $signature = base64_encode(hash_hmac('sha1', $stringToSign, $this->secret, true));

            $headers = [
                'Host' => parse_url($this->endpoint, PHP_URL_HOST),
                'Date' => $date,
                'Content-Type' => $contentType,
                'Content-MD5' => $contentMD5,
                'Content-Length' => strlen($fileContent),
                'x-amz-acl' => $acl,
                'Authorization' => 'AWS ' . $this->key . ':' . $signature,
            ];

            $response = $this->client->request('PUT', $url, [
                'headers' => $headers,
                'body' => $fileContent,
            ]);

            $httpCode = $response->getStatusCode();

            if ($httpCode >= 400) {
                throw new Exception("Upload failed with code $httpCode. Response: " . $response->getBody());
            }

            return $this->getPresignedUrl($key);
        } catch (Exception $e) {
            log_message('error', 'S3 Upload Error: ' . $e->getMessage());
            throw $e;
        }
    }

    public function delete($key)
    {
        try {
            $key = ltrim($key, '/');
            $url = rtrim($this->endpoint, '/') . '/' . $this->bucket . '/' . $key;

            $date = gmdate('D, d M Y H:i:s T');
            $stringToSign = "DELETE\n\n\n$date\n/$this->bucket/$key";
            $signature = base64_encode(hash_hmac('sha1', $stringToSign, $this->secret, true));

            $headers = [
                'Host' => parse_url($this->endpoint, PHP_URL_HOST),
                'Date' => $date,
                'Authorization' => 'AWS ' . $this->key . ':' . $signature,
            ];

            $response = $this->client->request('DELETE', $url, [
                'headers' => $headers,
            ]);

            $httpCode = $response->getStatusCode();
            return $httpCode >= 200 && $httpCode < 300;
        } catch (Exception $e) {
            log_message('error', 'S3 Delete Error: ' . $e->getMessage());
            return false;
        }
    }

    public function exists($key)
    {
        try {
            $key = ltrim($key, '/');
            $url = rtrim($this->endpoint, '/') . '/' . $this->bucket . '/' . $key;

            $date = gmdate('D, d M Y H:i:s T');
            $stringToSign = "HEAD\n\n\n$date\n/$this->bucket/$key";
            $signature = base64_encode(hash_hmac('sha1', $stringToSign, $this->secret, true));

            $headers = [
                'Host' => parse_url($this->endpoint, PHP_URL_HOST),
                'Date' => $date,
                'Authorization' => 'AWS ' . $this->key . ':' . $signature,
            ];

            $response = $this->client->request('HEAD', $url, [
                'headers' => $headers,
            ]);

            return $response->getStatusCode() === 200;
        } catch (Exception $e) {
            log_message('error', 'S3 Exists Error: ' . $e->getMessage());
            return false;
        }
    }

    public function getMetadata($key)
    {
        try {
            $key = ltrim($key, '/');
            $url = rtrim($this->endpoint, '/') . '/' . $this->bucket . '/' . $key;

            $date = gmdate('D, d M Y H:i:s T');
            $stringToSign = "HEAD\n\n\n$date\n/$this->bucket/$key";
            $signature = base64_encode(hash_hmac('sha1', $stringToSign, $this->secret, true));

            $headers = [
                'Host' => parse_url($this->endpoint, PHP_URL_HOST),
                'Date' => $date,
                'Authorization' => 'AWS ' . $this->key . ':' . $signature,
            ];

            $response = $this->client->request('HEAD', $url, [
                'headers' => $headers,
            ]);

            if ($response->getStatusCode() !== 200) {
                return false;
            }

            $metadata = [];
            $headers = $response->getHeaderLine('Content-Type');
            if ($headers) {
                $metadata['mime-type'] = $headers;
            }

            $length = $response->getHeaderLine('Content-Length');
            if ($length) {
                $metadata['size'] = (int) $length;
            }

            $lastModified = $response->getHeaderLine('Last-Modified');
            if ($lastModified) {
                $metadata['timestamp'] = strtotime($lastModified);
            }

            return $metadata;
        } catch (Exception $e) {
            log_message('error', 'S3 GetMetadata Error: ' . $e->getMessage());
            return false;
        }
    }

    public function getPresignedUrl($key)
    {
        $key = ltrim($key, '/');
        $expiresAt = time() + $this->expiresUrl;
        $url = rtrim($this->endpoint, '/') . '/' . $this->bucket . '/' . $key;

        $stringToSign = "GET\n\n\n$expiresAt\n/$this->bucket/$key";
        $signature = base64_encode(hash_hmac('sha1', $stringToSign, $this->secret, true));

        return "$url?AWSAccessKeyId={$this->key}&Expires=$expiresAt&Signature=" . urlencode($signature);
    }
}
