<?php

namespace App\Libraries;

use Config\PrinterConfig;
use App\Libraries\ProxyCURLRequest;

class Printer
{
    public array $device;

    public int $timeout = 5;

    public function __construct(string $serviceName)
    {
        $config = config(PrinterConfig::class);

        $serviceDevices = $config->services[$serviceName] ?? null;
        
        if (!$serviceDevices) {
            throw new \InvalidArgumentException("Printer service '{$serviceName}' not found in configuration.");
        }
        
        $devices = array_filter($config->devices, function ($device) use ($serviceDevices) {
            return in_array($device['name'], $serviceDevices);
        });

        if (!count($devices)) {
            throw new \RuntimeException("No devices found for service '{$serviceName}'. Please check your configuration.");
        }

        $this->device = array_shift($devices);
    }

    protected function makeNewClient()
    {
        return ProxyCURLRequest::make()->setProxy('printer');
    }

    public function endpoint(string $path)
    {
        $endpoint = ltrim($path, '/');

        if (empty($this->device['base_url'])) {
            throw new \RuntimeException("Base URL for printer device is not set.");
        }

        return $this->device['base_url'] . '/' . $endpoint;
    }

    public function printBase64AsPng(string $base64)
    {
        $tmpFile = tempnam(sys_get_temp_dir(), 'printer_') . '.png';
        file_put_contents($tmpFile, base64_decode($base64));
        
        try {
            $response = $this->makeNewClient()->request('POST', $this->endpoint('/v1/windows/print'), [
                'auth' => [$this->device['basic_auth']['username'], $this->device['basic_auth']['password'], 'basic'],
                'multipart' => [
                    'file' => new \CURLFile($tmpFile, 'image/png', 'print.png'),
                    'printer_name' => $this->device['name'],
                ],
                'http_errors' => false,
                'timeout' => $this->timeout ?? 5
            ]);

            if ($response->getStatusCode() !== 200) {
                throw new \RuntimeException("Failed to print: " . $response->getBody());
            }

            return $response;
        } finally {
            register_shutdown_function(function () use ($tmpFile) {
                if (file_exists($tmpFile)) {
                    unlink($tmpFile);
                }
            });
        }
    }
}