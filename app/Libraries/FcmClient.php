<?php

namespace App\Libraries;

use Exception;
use Config\Mobile;
use Config\Services;
use Config\Notification;
use App\Libraries\ProxyCURLRequest;
use App\Models\FcmAccessTokenModel;

class FcmClient
{
    protected $projectId;
    protected $privateKey;
    protected $clientEmail;
    protected $accessToken;
    protected $accessTokenTtl;
    protected $timeout;

    public function __construct()
    {
        $notificationConfig = config(Notification::class);

        $this->loadConfig($notificationConfig);

        $this->accessToken = $this->authenticate();
    }

    protected function loadConfig($config)
    {
        $serviceAccountConfig = json_decode(file_get_contents($config->fcmCertPath));

        $this->projectId = $serviceAccountConfig->project_id;
        $this->privateKey = $serviceAccountConfig->private_key;
        $this->clientEmail = $serviceAccountConfig->client_email;
        $this->accessTokenTtl = $config->fcmAccessTokenTtl ?? 3600;
        $this->timeout = $config->timeout ?? 5;
    }

    protected function makeNewClient()
    {
        return ProxyCURLRequest::make()->setProxy('fcm');
    }

    protected function authenticate()
    {
        $fcmAccessTokenModel = model(FcmAccessTokenModel::class);
        $availableAccessToken = $fcmAccessTokenModel->where('expires_at >', date('Y-m-d H:i:s'))->first();

        if ($availableAccessToken) {
            return $availableAccessToken->access_token;
        }

        $response = $this->makeNewClient()->request('POST', 'https://oauth2.googleapis.com/token', [
            'query' => [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $this->makeJwtToken(),
            ],
            'http_errors' => false,
            'timeout' => $this->timeout
        ]);

        $body = $response->getBody();
        $body = json_decode($body, true);

        if (isset($body['error']) && isset($body['error_description'])) {
            throw new Exception('Unable authenticate FCM: ' . $body['error_description']);
        }

        $accessToken = $body['access_token'];

        $fcmAccessTokenModel->insert([
            'access_token' => $accessToken,
            'ttl' => $this->accessTokenTtl,
            'expires_at' => date('Y-m-d H:i:s', strtotime("+{$this->accessTokenTtl} seconds"))
        ]);

        return $accessToken;
    }

    protected function makeJwtToken()
    {
        $header = ['alg' => 'RS256', 'typ' => 'JWT'];

        $claimSet = [
            'iss' => $this->clientEmail,
            'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
            'aud' => 'https://oauth2.googleapis.com/token',
            'exp' => strtotime("+{$this->accessTokenTtl} seconds"),
            'iat' => time()
        ];

        $encodedHeader = $this->base64UrlEncode(json_encode($header));
        $encodedClaimSet = $this->base64UrlEncode(json_encode($claimSet));
        openssl_sign($encodedHeader . '.' . $encodedClaimSet, $signature, $this->privateKey, 'sha256WithRSAEncryption');
        $signature = $this->base64UrlEncode($signature);

        return $encodedHeader . '.' . $encodedClaimSet . '.' . $signature;
    }

    protected function base64UrlEncode($data)
    {
        return str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($data));
    }

    /**
     * @see $notification https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages#Notification
     */
    public function sendByToken($token, $notification, $data = null, $apns = null, $webpush = null)
    {
        $response = $this->makeNewClient()->request('POST', 'https://fcm.googleapis.com/v1/projects/' . $this->projectId  . '/messages:send', [
            'headers' => [
                'Authorization' => "Bearer {$this->accessToken}",
            ],
            'json' => [
                'message' => array_filter([
                    'token' => $token,
                    'notification' => $notification,
                    'data' => $data,
                    'apns' => $apns,
                    'webpush' => $webpush
                ])
            ],
            'http_errors' => false,
            'timeout' => $this->timeout
        ]);

        log_message('error', 'FCM payload log: ' . json_encode([
            'message' => [
                'token' => $token,
                'notification' => $notification,
                'data' => $data,
                'apns' => $apns,
            ]
        ]));

        return $response;
    }

    // "{\"applicationVersion\":\"3\",\"application\":\"com.sepay.trans\",\"authorizedEntity\":\"901043378302\",\"rel\":{\"topics\":{\"news\":{\"addDate\":\"2024-07-02\"},\"abc\":{\"addDate\":\"2024-07-03\"}}},\"platform\":\"ANDROID\"}"
    public function getTokenTopics($token)
    {
        $response = $this->makeNewClient()->request('POST', 'https://iid.googleapis.com/iid/info/' . $token . '?details=true', [
            'headers' => [
                'Authorization' => "Bearer {$this->accessToken}",
                'Content-Type' =>  'application/json',
                'access_token_auth' => 'true'
            ],
            'http_errors' => false,
            'timeout' => $this->timeout
        ]);

        return $response;
    }

    public function subscribeTokensToTopic($tokens, $topic)
    {
        $response = $this->makeNewClient()->request('POST', 'https://iid.googleapis.com/iid/v1:batchAdd', [
            'headers' => [
                'Authorization' => "Bearer {$this->accessToken}",
                'Content-Type' =>  'application/json',
                'access_token_auth' => 'true'
            ],
            'json' => [
                'to' => '/topics/' . $topic,
                'registration_tokens' => $tokens,
            ],
            'http_errors' => false,
            'timeout' => $this->timeout
        ]);

        return $response;
    }

    public function unsubscribeTokensToTopic($tokens, $topic)
    {
        $response = $this->makeNewClient()->request('POST', 'https://iid.googleapis.com/iid/v1:batchRemove', [
            'headers' => [
                'Authorization' => "Bearer {$this->accessToken}",
                'Content-Type' =>  'application/json',
                'access_token_auth' => 'true'
            ],
            'json' => [
                'to' => '/topics/' . $topic,
                'registration_tokens' => $tokens,
            ],
            'http_errors' => false,
            'timeout' => $this->timeout
        ]);

        return $response;
    }
}
