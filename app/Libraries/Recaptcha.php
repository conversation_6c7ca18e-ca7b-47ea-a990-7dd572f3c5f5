<?php

namespace App\Libraries;

/**
 * CodeIgniter reCAPTCHA V2 Library
 *
 * A library to implement Google reCAPTCHA V2
 * https://developers.google.com/recaptcha/intro
 *
 * You can get the keys from here:
 * https://www.google.com/recaptcha/admin
 *
 *
 * @package Codeigniter-recaptcha
 * @license MIT License
 * <AUTHOR>
 */

/**
 * Recaptcha class
 *
 * This class contains the methods that you need to create a reCAPTCHA box
 * And validate the response
 *
 * @category   Libraries
 * @package	   CodeIgniter
 * @subpackage Libraries
 * @license	MIT License
 */
class Recaptcha
{
	/**
	 * Site key provided by Google
	 *
	 * @var string
	 */
	private $_site_key;

	/**
	 * Secret key provided by Google
	 *
	 * @var string
	 */
	private $_secret_key;

	/**
	 * API endpoint
	 */
	const API = 'https://www.google.com/recaptcha/api/siteverify';

	/**
	 * CI instance
	 *
	 * @var object
	 */
	private $_ci;
    private $request;


	/**
	 * __construct
	 *
	 * @param array $config An array of options
	 *					  'site_key'   => Site key
	 *					  'secret_key' => Secret key
	 *					 )
	 *
	 * @return void
	 */
	public function __construct($options = NULL)
	{
		// Get CodeIgniter instance
		$config = new \Config\Recaptcha();

		// Load the config file
		//$this->_ci->config->load('recaptcha', FALSE, TRUE);
        $this->request = \Config\Services::request();

		// Get configs from the config file
		$config = array(
			'site_key'		=> $config->recaptchaSiteKey,
			'secret_key'	=> $config->recaptchaSecretKey,
		);

        //var_dump($config);

		if(is_array($options)){
			// Merge options with the config
			$config = array_merge($config, $options);
		}

		// Set keys
		$this->set_keys($config['site_key'], $config['secret_key']);

	 

		log_message('info', 'reCaptcha Class Initialized');
	}

	/**
	 * Set site and secret keys
	 *
	 * @param string $site   The reCAPTCHA site key
	 * @param string $secret The reCAPTCHA secret key
	 * 
	 * @return void
	 */
	public function set_keys($site, $secret)
	{
		$this->_site_key = $site;
		$this->_secret_key = $secret;

		log_message('info', 'reCaptcha Class: Keys were set');
	}

 

	/**
	 * Checks if the reCAPTCHA  was passed
	 *
	 * @param string $response The g-recaptcha-response submitted by the form
	 * @param string $ip	   User IP to send to Google
	 *						 FALSE  To not send the IP
	 *						 NULL   To get the IP automatically
	 * 
	 * @return array Response returned by Google's server
	 */
	public function is_valid($response = NULL, $ip = FALSE)
	{
		// Check if one of the keys is empty
		if(empty($this->_site_key) || empty($this->_secret_key))
		{
			// If it's a development environment
			if(ENVIRONMENT === 'development'){
				show_error('Please set both the Site key and Secret key for the reCAPTCHA library.', 500, 'reCAPTCHA library: Missing keys');
			}
			else
			{
				log_message('error', 'reCaptcha Class: No keys are set');
			}

			return array(
				'success' => FALSE,
			);
		}

		log_message('info', 'reCaptcha Class: Validating the response');

		// Prepare post data
		$post_data = array(
			'response' => $response
		);

		// If no response was passed get it from the post data
		if ($response === NULL) {
			$post_data['response'] = $this->request->getVar('g-recaptcha-response');
		}

		// If an IP was passed add it to post_data
		if( ! empty($ip) )
		{
			$post_data['remoteip'] = $ip;
		}
		elseif($ip === NULL)
		{
			$post_data['remoteip'] = $this->request->getIPAddress();
		}

		// If no response was set return fail
		if( empty($post_data['response']) ){
			return array(
				'success' => FALSE,
			);
		}

		// Pass the secret key
		$post_data['secret'] = $this->_secret_key;

		// Start the request
		$curl = curl_init();

		// Set cURL options
		// Return the response
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);

		// Set the URL
		curl_setopt($curl, CURLOPT_URL, self::API);

		// Set useragent
		curl_setopt($curl, CURLOPT_USERAGENT, 'CodeIgniter');

		// Send POST data
		curl_setopt($curl, CURLOPT_POST, TRUE);

		// Set POST data
		curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);

		// Stop if an error occurs
		curl_setopt($curl, CURLOPT_FAILONERROR, TRUE);

		// Force CURL to verify the certificate
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, TRUE);
		

		// Initiate the request and return the response
		$response = curl_exec($curl);

		// Check if there were any errors
		if($response === FALSE){
			// Log the error
			log_message('error', "reCAPTCHA library: cURL failed with error:". curl_error($curl));

			// Prepare data to return
			$return = array(
				'success' => FALSE,
				'error' => TRUE,
				'error_message' => curl_error($curl)
			);
		}else{
			// Parse the JSON response and prepare to return it
			$return = json_decode($response, TRUE);
		}

		// Close the cURL session
		curl_close($curl);

		//log_message('error', "reCAPTCHA library: cURL failed with error:". serialize($return));

		return $return;
	}
}