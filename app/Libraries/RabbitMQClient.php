<?php

namespace App\Libraries;

use PhpAmqpLib\Connection\AMQPStreamConnection;

class RabbitMQClient
{
    protected $config;
    protected $conn;
    public $channel;

    public function __construct()
    {
        $this->config = config(\App\Config\RabbitMQ::class);
    }

    public function connect()
    {
        try {
            $this->conn = AMQPStreamConnection::create_connection($this->config->connections, [
                'connection_timeout' => $this->config->connectionTimeout,
                'read_write_timeout' => $this->config->readWriteTimeout,
            ]);

            $this->channel = $this->conn->channel();

            return true;
        } catch (\Exception $e) {
            log_message('error', 'RabbitMQClient: ' . $e->getMessage());
            return false;
        }
    }

    public function getQueueDeclaration($key, $attribute = null)
    {
        if (is_null($attribute))
            return $this->config->queueDeclarations[$key];
        else
            return $this->config->queueDeclarations[$key][$attribute];
    }

    public function queueDeclare($key)
    {
        $queueDeclaration = $this->getQueueDeclaration($key);
        $durable = $queueDeclaration['durable'] ?? false;

        $this->channel->queue_declare($queueDeclaration['name'], false, $durable, false, false);
    }

    public function close()
    {
        $this->channel->close();
        $this->conn->close();
    }
}
