<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Filters\AuthFilter;

class AuthFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        
        $session = session();

        if (!$session->get('admin_logged_in')) {
            return redirect()->to(base_url('login'));
        }

        if(isset($session->get('admin_logged_in')['admin_id'])) {
            $admin_id = $session->get('admin_logged_in')['admin_id'];

            $adminModel = new \App\Models\AdminModel();
    
            $admin = $adminModel->where(['active'=> 1,'id'=>$admin_id])->first();
    
            if(!is_object($admin)) {
                $session->remove("admin_logged_in");
                return redirect()->to(base_url('login'));
            }

        } else {
            $session->remove("admin_logged_in");
            return redirect()->to(base_url('login'));
        }


        


    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        //
    }
}
