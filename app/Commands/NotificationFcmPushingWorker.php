<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use App\Libraries\FcmClient;
use App\Models\FcmDeviceTokenModel;
use CodeIgniter\CLI\BaseCommand;
use App\Libraries\RabbitMQClient;
use App\Models\NotificationFcmLogModel;

class NotificationFcmPushingWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Worker';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:noti-fcm-pushing';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'command:name [arguments] [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        helper('general');

        $rabbitmq = new RabbitMQClient();
        $connected = $rabbitmq->connect();

        if (!$connected) {
            CLI::error('Cannot connect to RabbitMQ.');
            return;
        }

        $rabbitmq->queueDeclare('noti_fcm_pushing');

        CLI::write("[*] Waiting for messages. To exit press CTRL+C\n");

        $callback = function ($msg) {
            $db = db_connect();

            CLI::write("[" . date('Y-m-d H:i:s') . "] Received " . $msg->getBody() . "\n");

            $data = json_decode($msg->getBody());

            $fcm = new FcmClient();

            $maxRetryAttemps = 7;
            $retries = 0;
            $fiboSeq = [1, 1];

            $status = null;

            while ($retries < $maxRetryAttemps) {
                try {
                    try {
                        $response = $fcm->sendByToken($data->token, $data->notification, $data->data, $data->apns);
                        $statusCode = $response->getStatusCode();

                        CLI::write($response->getBody());
                    } catch (\Exception $e) {
                        CLI::write($e->getMessage());
                        $response = null;
                        $statusCode = 504;
                    }

                    if ($statusCode == 429) {
                        $retryAfter = $response->getHeaderLine('retry-after');
                        sleep($retryAfter);
                        continue;
                    }

                    if ($statusCode >= 500) {
                        sleep(end($fiboSeq) * 60);
                        $retries++;
                        $nextFibo = $fiboSeq[count($fiboSeq) - 1] + $fiboSeq[count($fiboSeq) - 2];
                        log_message('error', "Notification FCM pushing worker retry #" . (count($fiboSeq) - 1) . ": " . json_encode($data));
                        array_push($fiboSeq, $nextFibo);
                        continue;
                    }

                    if ($statusCode == 404) {
                        model(FcmDeviceTokenModel::class)->where('token', $data->token)->set(['active' => 0])->update();
                        add_system_log(['company_id' => 0, 'data_type' => 'inactive_device_token', 'description' => "[PID: " . getmypid() . "] Vô hiệu hóa mã thiết bị #{$data->device_token_id} tại thông báo #{$data->notification_id}", 'level' => 'Info', 'by' => 'NotificationFcmPushingWorker']);

                        $status = 'invalid';
                        break;
                    }

                    if ($statusCode != 200) {
                        $body = json_decode($response->getBody(), true);

                        if (isset($body['error']['message']) && $body['error']['message'] === 'The registration token is not a valid FCM registration token') {
                            model(FcmDeviceTokenModel::class)->where('token', $data->token)->set(['active' => 0])->update();
                            add_system_log(['company_id' => 0, 'data_type' => 'inactive_device_token', 'description' => "[PID: " . getmypid() . "] Vô hiệu hóa mã thiết bị #{$data->device_token_id} tại thông báo #{$data->notification_id}", 'level' => 'Info', 'by' => 'NotficationFcmPushingWorker']);

                            $status = 'invalid';
                            break;
                        }

                        log_message('error', 'Notification FCM pushing worker failed: ' . json_encode($response->getBody()));
                        CLI::write("Send failed!");

                        $status = 'invalid';
                        break;
                    }

                    $status = 'success';
                    CLI::write("Sent!");
                    break;
                } catch (Exception $e) {
                    log_message('error', '[PID: ' . getmypid() . ' - Notification FCM worker debug] Send FCM failed: ' . json_encode($response->getBody()));
                }
            }

            if ($status) {
                try {
                    $fcmLogId = model(NotificationFcmLogModel::class)->insert([
                        'notification_id' => $data->notification_id,
                        'user_id' => $data->user_id,
                        'device_token_id' => $data->device_token_id,
                        'notification' => json_encode($data->notification),
                        'status' => $status,
                        'is_browser' => $data->is_browser
                    ]);
                } catch (\Exception $e) {
                    $fcmLogId = null;
                     
                }
            }

            log_message('error', '[PID: ' . getmypid() . ' - Notification FCM worker debug]: ' . ($fcmLogId ?? 'No ID'));

            $msg->ack();
            $db->close();
        };

        $rabbitmq->channel->basic_qos(null, 1, false);
        $rabbitmq->channel->basic_consume($rabbitmq->getQueueDeclaration('noti_fcm_pushing', 'name'), '', false, false, false, false, $callback);

        try {
            $rabbitmq->channel->consume();
        } catch (\Throwable $e) {
            log_message('error', 'Notification FCM pushing worker: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            CLI::error($e->getMessage() . ' - ' . $e->getTraceAsString());
        }

        $rabbitmq->close();
    }
}
