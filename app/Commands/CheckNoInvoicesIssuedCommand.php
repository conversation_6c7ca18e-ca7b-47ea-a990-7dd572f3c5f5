<?php

namespace App\Commands;

use App\Models\InvoiceModel;
use App\Models\NotificationTelegramQueueModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Config\SInvoice;
use Config\SubscriptionSInvoice;

class CheckNoInvoicesIssuedCommand extends BaseCommand
{
    protected $group = 'Invoices';

    protected $name = 'invoices:check-no-issued';

    protected $description = 'Kiểm tra và thông báo nếu không có hóa đơn nào được xuất trong 2 ngày liên tiếp';

    protected $lastAlertKey = 'last_no_invoice_alert';

    public function run(array $params)
    {
        CLI::write('Bắt đầu kiểm tra hóa đơn đã xuất...', 'yellow');

        $invoicesModel = model(InvoiceModel::class);

        $today = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));

        $issuedInvoices = $invoicesModel
            ->where('tax_issued', 1)
            ->where('tax_issued_id !=', '')
            ->groupStart()
            ->where('DATE(created_at)', $today)
            ->orWhere('DATE(created_at)', $yesterday)
            ->groupEnd()
            ->countAllResults();

        CLI::write("Số hóa đơn đã xuất trong 2 ngày qua: $issuedInvoices", 'yellow');

        $lastAlert = cache($this->lastAlertKey);
        CLI::write("Lần cảnh báo cuối: " . ($lastAlert ?: 'Chưa có'), 'yellow');

        if ($issuedInvoices === 0) {
            if (!$lastAlert || strtotime($lastAlert) < strtotime('-48 hours')) {
                $this->sendTelegramAlert();

                cache()->save($this->lastAlertKey, date('Y-m-d H:i:s'), 86400 * 7);

                CLI::write('Đã gửi cảnh báo qua Telegram!', 'green');
            } else {
                CLI::write('Đã gửi cảnh báo trước đó, bỏ qua để tránh spam.', 'yellow');
            }
        } else {
            CLI::write('Có hóa đơn được xuất trong 2 ngày qua, không cần gửi cảnh báo.', 'green');

            if ($lastAlert) {
                cache()->delete($this->lastAlertKey);
                CLI::write('Đã xóa cảnh báo gần nhất khỏi cache.', 'green');
            }
        }

        CLI::newLine();
        CLI::write('Hoàn thành kiểm tra!', 'green');
    }

    private function sendTelegramAlert()
    {
        $message = "‼️ CẢNH BÁO HỆ THỐNG ‼️\n\n";
        $message .= "Không có hóa đơn nào được xuất trong 2 ngày liên tiếp.\n";
        $message .= "Vui lòng kiểm tra hệ thống xuất hóa đơn.\n\n";
        $message .= "⏰ Thời gian kiểm tra: " . date('d/m/Y H:i:s') . "\n";
        $message .= "🔗 Truy cập: " . base_url('invoice');

        model(NotificationTelegramQueueModel::class)->sendTelegramMessage(
            $message,
            config(SubscriptionSInvoice::class)->noInvoiceAlertChatId,
            'html'
        );
    }
}
