<?php

namespace App\Commands;

use App\Models\InvoiceModel;
use App\Models\StransactionModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class UpdatePhysicalProductInvoiceDatepaidCommand extends BaseCommand
{
    protected $group = 'Invoice';

    protected $name = 'invoice:update-physical-datepaid';

    protected $description = 'Updates datepaid field for PhysicalProduct invoices from their latest transactions';

    public function run(array $params)
    {
        CLI::write('Starting to update PhysicalProduct invoice datepaid fields...', 'yellow');

        $invoiceModel = model(InvoiceModel::class);
        $transactionModel = model(StransactionModel::class);

        $invoices = $invoiceModel
            ->select('id')
            ->where('type', 'PhysicalProduct')
            ->where('datepaid', '0000-00-00 00:00:00')
            ->findAll();

        $totalInvoices = count($invoices);
        CLI::write("Found {$totalInvoices} invoices to process", 'yellow');

        $updatedCount = 0;
        foreach ($invoices as $invoice) {
            $latestTransaction = $transactionModel
                ->select('date')
                ->where('invoice_id', $invoice->id)
                ->orderBy('date', 'DESC')
                ->limit(1)
                ->first();

            if ($latestTransaction) {
                $invoiceModel->update($invoice->id, ['datepaid' => $latestTransaction->date]);
                $updatedCount++;
                CLI::write("Updated invoice #{$invoice->id} with date {$latestTransaction->date} to {$latestTransaction->date}", 'green');
            } else {
                CLI::write("No transaction found for invoice #{$invoice->id}", 'red');
            }
        }

        CLI::write("Completed! Updated {$updatedCount} out of {$totalInvoices} invoices", 'yellow');
    }
}
