<?php

namespace App\Commands;

use App\Libraries\GhtkClient;
use App\Models\InvoiceModel;
use App\Models\PhysicalOrderHistoryModel;
use App\Models\PhysicalOrderModel;
use App\Models\StransactionModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Exception;

class SyncOrderStatusCommand extends BaseCommand
{
    protected $group = 'Orders';

    protected $name = 'orders:sync-status';

    protected $description = 'Cập nhật trạng thái đơn hàng';

    public function run(array $params)
    {
        $physicalOrderModel = new PhysicalOrderModel();
        $ghtkClient = new GhtkClient();

        $orders = $physicalOrderModel
            ->select('id, order_code, status, tracking_code, payment_status, total_amount')
            ->where('shipping_company', 'GHTK')
            ->whereNotIn('status', ['Completed', 'Cancelled', 'Returned'])
            ->where('tracking_code IS NOT NULL')
            ->findAll();

        if (empty($orders)) {
            CLI::write('Không có đơn hàng GHTK cần cập nhật.', 'yellow');
            return;
        }

        CLI::write('Bắt đầu cập nhật trạng thái ' . count($orders) . ' đơn hàng...', 'green');

        foreach ($orders as $order) {
            try {
                $response = $ghtkClient->trackingStatus(urlencode($order->tracking_code));

                if ($response['success'] === false) {
                    CLI::write("Lỗi khi cập nhật đơn hàng {$order->order_code}: {$response['message']}", 'red');
                    continue;
                }

                $ghtkStatus = $response['order']['status'];
                $mappedStatus = $this->mapGhtkStatus($ghtkStatus);

                if ($mappedStatus && $mappedStatus !== $order->status) {
                    $physicalOrderModel->update($order->id, [
                        'status' => $mappedStatus,
                        'last_update_status' => date('Y-m-d H:i:s'),
                    ]);

                    if ($mappedStatus === 'Completed') {
                        $physicalOrderModel->update($order->id, [
                            'payment_status' => 'Paid',
                        ]);

                        $invoice = model(InvoiceModel::class)
                            ->select('tb_autopay_invoice.id')
                            ->join('tb_autopay_physical_invoices', 'tb_autopay_physical_invoices.id = tb_autopay_invoice.physical_invoice_id')
                            ->where('tb_autopay_physical_invoices.order_id', $order->id)
                            ->first();

                        if ($invoice) {
                            $totalPaid = model(StransactionModel::class)
                                ->where('invoice_id', $invoice->id)
                                ->selectSum('in')
                                ->first()
                                ->in ?? 0;

                            $now = date('Y-m-d H:i:s');

                            if ($totalPaid < $order->total_amount) {
                                model(StransactionModel::class)->insert([
                                    'invoice_id' => $invoice->id,
                                    'payment_method' => 'cod',
                                    'in' => $order->total_amount - $totalPaid,
                                    'description' => 'Đã thanh toán đủ tiền qua COD',
                                    'date' => $now,
                                    'type' => null,
                                ]);

                                model(InvoiceModel::class)->update($invoice->id, [
                                    'status' => 'Paid',
                                    'datepaid' => $now,
                                    'last_update_status_at' => $now,
                                ]);
                            }
                        }
                        $order->payment_status = 'Paid';
                    }

                    CLI::write("Đã cập nhật trạng thái đơn hàng {$order->order_code} từ {$order->status} thành {$mappedStatus}", 'green');

                    $order->status = $mappedStatus;

                    model(PhysicalOrderHistoryModel::class)->recordHistory(
                        $order,
                        "Cập nhật trạng thái đơn hàng tự động từ GHTK: " . $response['order']['status_text']
                    );
                } else {
                    CLI::write("Đơn hàng {$order->order_code} không cần cập nhật trạng thái", 'yellow');
                }
            } catch (Exception $e) {
                CLI::write("Lỗi khi xử lý đơn hàng {$order->order_code}: {$e->getMessage()}", 'red');
            }
        }
    }

    protected function mapGhtkStatus(string $ghtkStatus): ?string
    {
        $statusMap = [
            '-1' => 'Cancelled',
            '1' => 'WaitingShipper',
            '2' => 'WaitingShipper',
            '3' => 'Shipping',
            '4' => 'Shipping',
            '5' => 'Completed',
            '6' => 'Completed',
            '7' => 'Cancelled',
            '8' => 'WaitingShipper',
            '9' => 'Cancelled',
            '10' => 'WaitingShipper',
            '11' => 'Returned',
            '12' => 'WaitingShipper',
            '13' => 'Returned',
            '20' => 'Returned',
            '21' => 'Returned',
            '123' => 'WaitingShipper',
            '127' => 'Cancelled',
            '128' => 'WaitingShipper',
            '45' => 'Completed',
            '49' => 'Cancelled',
            '410' => 'Shipping',
        ];

        return $statusMap[$ghtkStatus] ?? null;
    }
}
