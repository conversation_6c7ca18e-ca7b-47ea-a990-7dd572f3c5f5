<?php

namespace App\Commands;

use App\Models\InvoiceModel;
use App\Models\NotificationTelegramQueueModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Config\SubscriptionSInvoice;
use Config\Services;

class ReportIssueInvoiceCommand extends BaseCommand
{
    protected $group = 'Invoices';

    protected $name = 'invoices:report-issue';

    protected $description = 'Báo cáo tình trạng xuất hóa đơn vào 12:00 và 17:00 hàng ngày';

    public function run(array $params)
    {
        $config = config(SubscriptionSInvoice::class);

        if (! $config->reportInvoiceTelegram) {
            return;
        }

        $currentHour = (int)date('H');

        if (! (($currentHour === 12) || ($currentHour === 17))) {
            return;
        }

        $isMorningReport = $currentHour === 12;
        $reportTime = $isMorningReport ? '12:00' : '17:00';
        $cacheKey = 'invoice_report_' . ($isMorningReport ? 'morning' : 'afternoon');

        $cache = Services::cache();
        $lastReportTime = $cache->get($cacheKey);
        
        if ($lastReportTime) {
            $lastReportDate = date('Y-m-d', strtotime($lastReportTime));
            $currentDate = date('Y-m-d');
            
            if ($lastReportDate === $currentDate) {
                CLI::write('Báo cáo đã được gửi trong khung giờ này!', 'yellow');
                return;
            }
        }

        $invoiceModel = model(InvoiceModel::class);
        $totalEligibleInvoices = $invoiceModel->getTotalEligibleInvoices();
        $totalIssuedInvoices = $invoiceModel->getTotalIssuedInvoices();

        $message = $this->generateTelegramMessage($totalEligibleInvoices, $totalIssuedInvoices, $reportTime);

        model(NotificationTelegramQueueModel::class)->sendTelegramMessage(
            $message,
            $config->reportInvoiceTelegramChatId,
            'html'
        );

        $cache->save($cacheKey, date('Y-m-d H:i:s'), 86400);

        CLI::write('Báo cáo xuất hóa đơn đã được gửi qua Telegram!', 'green');
    }

    private function generateTelegramMessage($totalEligibleInvoices, $totalIssuedInvoices, $reportTime)
    {
        $message = "<b>📊 Báo cáo xuất hóa đơn {$reportTime}</b>\n";
        $message .= "Thời gian báo cáo: " . date('d/m/Y H:i:s') . "\n\n";

        $message .= "<b>Thống kê tổng quan:</b>\n";
        $message .= "• Hóa đơn cần xử lý: <b>{$totalEligibleInvoices}</b>\n";
        $message .= "• Hóa đơn đã xuất: <b>{$totalIssuedInvoices}</b>\n\n";

        $completionRate = $totalEligibleInvoices > 0 
            ? round(($totalIssuedInvoices / $totalEligibleInvoices) * 100, 2) 
            : 0;
        $message .= "• Tỷ lệ hoàn thành: <b>{$completionRate}%</b>\n";

        return $message;
    }
}
