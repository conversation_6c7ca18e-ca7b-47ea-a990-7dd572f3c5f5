<?php

namespace App\Commands;

use App\Models\CompanySubscriptionModel;
use App\Models\CounterModel;
use App\Models\EmailLogModel;
use App\Models\InvoiceModel;
use App\Models\ProductModel;
use App\Models\UserModel;
use CodeIgniter\CLI\BaseCommand;
use Config\Services;
use Config\Subscription;

class CheckFreeUsageCommand extends BaseCommand
{
    protected $group = 'notifications';

    protected $name = 'notifications:check-free-usage';

    protected $description = 'Check usage for  free subscription users and send notifications if limit are near or exceeded.';

    protected $usage = 'notifications:check-free-usage';

    public function run(array $params)
    {
        $subscriptionModel = model(CompanySubscriptionModel::class);
        $counterModel = model(CounterModel::class);
        $config = config(Subscription::class);

        $subscriptions = $subscriptionModel
            ->select([
                'tb_autopay_company_subscription.id',
                'tb_autopay_company_subscription.company_id',
                'tb_autopay_company_subscription.plan_id',
                'tb_autopay_company_subscription.monthly_transaction_limit',
                'tb_autopay_company_subscription.last_notified_at',
                'tb_autopay_company_subscription.last_notification_type',
            ])
            ->join('tb_autopay_product', 'tb_autopay_product.id = tb_autopay_company_subscription.plan_id')
            ->where('tb_autopay_company_subscription.billing_cycle', 'free')
            ->where('tb_autopay_company_subscription.status', 'Active')
            ->where('tb_autopay_company_subscription.auto_renew', 1)
            ->where('tb_autopay_product.name', 'Free')
            ->findAll();

        foreach ($subscriptions as $subscription) {
            if (
                $subscription->last_notified_at
                && (time() - strtotime($subscription->last_notified_at) < $config->notificationInterval)
            ) {
                continue;
            }

            $lastExcessInvoice = model(InvoiceModel::class)
                ->select('date')
                ->where('company_id', $subscription->company_id)
                ->where('status', 'Paid')
                ->where('type', 'Excess')
                ->where('date >=', date('Y-m-01'))
                ->orderBy('date', 'desc')
                ->first();

            $usageCount = $counterModel->count_transaction_by_interval(
                $subscription->company_id,
                $lastExcessInvoice ? $lastExcessInvoice->date : date('Y-m-01'),
                date('Y-m-d')
            ) ?: 0;

            $usagePercentage = ($usageCount / $subscription->monthly_transaction_limit) * 100;

            if ($usagePercentage >= 80 && $usagePercentage < 100 && $subscription->last_notification_type !== 'near_limit') {
                $this->sendNotification($subscription, 'near_limit', $usageCount, $usagePercentage);
            } elseif ($usagePercentage >= 100 && $subscription->last_notification_type !== 'exceeded_limit') {
                $this->sendNotification($subscription, 'exceeded_limit', $usageCount, $usagePercentage);
            }
        }
    }

    protected function sendNotification($subscription, string $type, int $usageCount, float $usagePercentage): void
    {
        $subscriptionConfig = config(Subscription::class);

        $user = model(UserModel::class)
            ->select('tb_autopay_user.email, tb_autopay_user.firstname, tb_autopay_user.lastname')
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id=tb_autopay_user.id')
            ->where('tb_autopay_company_user.company_id', $subscription->company_id)
            ->where('tb_autopay_company_user.role', 'SuperAdmin')
            ->first();

        if (! $user) {
            return;
        }

        $product = model(ProductModel::class)->find($subscription->plan_id);

        if (($subscriptionConfig->writeLogOnly ?? true) === true) {
            log_message('error', "Company ID: $subscription->company_id, Subscription ID: $subscription->id, Type: $type, Usage: $usageCount/$subscription->monthly_transaction_limit, Percentage: $usagePercentage%");
            return;
        }

        $clientName = $user->lastname . ' ' . $user->firstname;

        if ($type === 'near_limit') {
            $subject = "Gói $product->name Sắp vượt giới hạn số lượng giao dịch trong tháng";
            $content = <<<EOD
                <h3>Thông báo: Sắp vượt giới hạn số lượng giao dịch trong tháng</h3> 
                <p style="font-size: 13px;">Xin chào Quý khách hàng $clientName,</p>         
                <p style="font-size: 13px;">Quý khách đã sử dụng <span style="color:#006fba">$usageCount/$subscription->monthly_transaction_limit</span> giao dịch trong tháng, đạt khoảng <span style="color:#006fba">$usagePercentage%</span> giới hạn sử dụng.</p> 
                <p style="font-size: 13px;">Gói dịch vụ của bạn: <strong>$product->name</strong> ($subscription->monthly_transaction_limit giao dịch/tháng)</p>
                <p style="font-size: 13px;">Quý khách vui lòng xem lại mức sử dụng và thanh toán nếu cần thiết trước khi đạt giới hạn.</p>
                <hr>
                <p style="text-align: center;">
                    <a href="https://my.sepay.vn/company/plans" style="text-decoration:none;color:#ffffff;font-size:14px;border-style:solid;border-color:#2d6bcf;border-width:10px 20px 10px 20px;display:inline-block;background:#2d6bcf;border-radius:30px;line-height:22px;width:auto;text-align:center" target="_blank">Xem chi tiết</a>
                </p>
            EOD;
        } elseif ($type === 'exceeded_limit') {
            $subject = "Gói $product->name đã vượt giới hạn sử dụng giao dịch trong tháng";
            $content = <<<EOD
                <h3>Thông báo: Đã vượt giới hạn sử dụng giao dịch trong tháng</h3>
                <p style="font-size: 13px;">Xin chào Quý khách hàng $clientName,</p>         
                <p style="font-size: 13px;">Quý khách đã vượt quá giới hạn sử dụng giao dịch trong tháng. Tổng số giao dịch đã sử dụng: <span style="color:#d9534f">$usageCount/$subscription->monthly_transaction_limit</span>.</p> 
                <p style="font-size: 13px;">Gói dịch vụ của bạn: <strong>$product->name</strong> ($subscription->monthly_transaction_limit giao dịch/tháng)</p>
                <p style="font-size: 13px;">Quý khách vui lòng thanh toán để duy trì dịch vụ của mình.</p>
                <hr>
                <p style="text-align: center;">
                    <a href="https://my.sepay.vn/company/plans" style="text-decoration:none;color:#ffffff;font-size:14px;border-style:solid;border-color:#2d6bcf;border-width:10px 20px 10px 20px;display:inline-block;background:#2d6bcf;border-radius:30px;line-height:22px;width:auto;text-align:center" target="_blank">Xem chi tiết</a>
                </p>
            EOD;
        }

        $from = '<EMAIL>';
        $to = $user->email;
        $message = $this->getEmailLayout($content);

        $sent = Services::email()
            ->initialize([
                'wordWrap' => true,
                'mailType' => 'html',
            ])
            ->setFrom($from, 'SePay')
            ->setTo($to)
            ->setSubject($subject)
            ->setMessage($message)
            ->send();

        model(EmailLogModel::class)->insert([
            'company_id' => $subscription->company_id,
            'email_type' => $type === 'near_limit' ? 'Subscription_Near_Limit' : 'Subscription_Exceeded_Limit',
            'data_id' => $subscription->id,
            'email_to' => $to,
            'email_from' => $from,
            'subject' => $subject,
            'message' => $message,
            'status' => $sent ? 'Sent' : 'Error',
        ]);

        if ($sent) {
            model(CompanySubscriptionModel::class)
                ->where('id', $subscription->id)
                ->set([
                    'last_notified_at' => date('Y-m-d H:i:s'),
                    'last_notification_type' => $type,
                ])
                ->update();
        }
    }

    protected function getEmailLayout(string $content): string
    {
        return <<<EOD
            <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                <html xmlns="http://www.w3.org/1999/xhtml">
                <head>
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                </head>
                <body style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; background-color: #f9f9fb; color: #303f4d; height: 100%; line-height: 1.4; margin: 0; width: 100% !important; -webkit-text-size-adjust: none;">
                    <style>
                        @media  only screen and (max-width: 600px) {
                            .inner-body {
                                width: 100% !important;
                            }
                
                            .footer {
                                width: 100% !important;
                            }
                        }
                
                        @media  only screen and (max-width: 500px) {
                            .button {
                                width: 100% !important;
                            }
                        }
                    </style>
                <table class="wrapper" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;"><tr>
                <td align="center" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">
                                <table class="content" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
                <tr>
                <td class="header" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 25px 0; text-align: center;">
                        <a href="https://sepay.vn" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #bbbfc3; font-size: 19px; font-weight: bold; text-decoration: none; text-shadow: 0 1px 0 white;">
                            <img width="100" src="https://my.sepay.vn/assets/images/logo/sepay-blue-359x116.png" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; max-width: 100%; border: none;"></a>
                    </td>
                </tr>
                <!-- Email Body --><tr>
                <td class="body" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
                                            <table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; background-color: #FFFFFF; margin: 0 auto; padding: 0; width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px;">
                <!-- Body content --><tr>
                <td class="content-cell" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 25px;">
                $content
                <!-- Subcopy -->
                <table class="subcopy" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; border-top: 1px solid #EDEFF2; margin-top: 25px; padding-top: 25px;"><tr>
                <td style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">


                    <p style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #303f4d; line-height: 1.5em; margin-top: 0; text-align: center; font-size: 12px;">Copyright © 2023 SePay - All rights reserved.</p>
                        </td>
                    </tr></table>
                </td>
                                                </tr>
                </table>
                </td>
                                    </tr>
                </table>
                </td>
                </tr>
                </table>
                </td>
                        </tr></table>
                </body>
                </html>
        EOD;
    }
}
