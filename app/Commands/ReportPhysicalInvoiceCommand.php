<?php

namespace App\Commands;

use App\Models\PhysicalInvoiceModel;
use App\Models\NotificationTelegramQueueModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Config\Services;
use Config\SInvoice;

class ReportPhysicalInvoiceCommand extends BaseCommand
{
    protected $group = 'Invoices';

    protected $name = 'invoices:report-issue-physical';

    protected $description = 'Báo cáo tình trạng xuất hóa đơn loa vào 12:00 và 17:00 hàng ngày';

    public function run(array $params)
    {
        $config = config(SInvoice::class);

        if (! $config->reportInvoiceTelegram) {
            return;
        }

        $currentHour = (int)date('H');

        if (! (($currentHour === 12) || ($currentHour === 17))) {
            return;
        }

        $isMorningReport = $currentHour === 12;
        $reportTime = $isMorningReport ? '12:00' : '17:00';
        $cacheKey = 'physical_invoice_report_' . ($isMorningReport ? 'morning' : 'afternoon');

        $cache = Services::cache();
        $lastReportTime = $cache->get($cacheKey);

        if ($lastReportTime) {
            $lastReportDate = date('Y-m-d', strtotime($lastReportTime));
            $currentDate = date('Y-m-d');

            if ($lastReportDate === $currentDate) {
                CLI::write('Báo cáo hóa đơn loa đã được gửi trong khung giờ này!', 'yellow');
                return;
            }
        }

        $physicalInvoiceModel = model(PhysicalInvoiceModel::class);

        $sevenDaysAgo = date('Y-m-d 00:00:00', strtotime('-7 days'));

        $totalEligibleInvoices = $physicalInvoiceModel->getTotalEligibleInvoices($sevenDaysAgo);
        $totalIssuedInvoices = $physicalInvoiceModel->getTotalIssuedInvoices($sevenDaysAgo);

        $message = $this->generateTelegramMessage($totalEligibleInvoices, $totalIssuedInvoices, $reportTime);

        model(NotificationTelegramQueueModel::class)->sendTelegramMessage(
            $message,
            $config->reportInvoiceTelegramChatId,
            'html'
        );

        $cache->save($cacheKey, date('Y-m-d H:i:s'), 86400);

        CLI::write('Báo cáo xuất hóa đơn loa đã được gửi qua Telegram!', 'green');
    }

    private function generateTelegramMessage($totalEligibleInvoices, $totalIssuedInvoices, $reportTime)
    {
        $message = "<b>📊 Báo cáo xuất hóa đơn loa {$reportTime}</b>\n";
        $message .= "Thời gian báo cáo: " . date('d/m/Y H:i:s') . "\n\n";

        $message .= "<b>Thống kê tổng quan 7 ngày gần nhất:</b>\n";
        $message .= "• Hóa đơn loa cần xử lý: <b>{$totalEligibleInvoices}</b>\n";
        $message .= "• Hóa đơn loa đã xuất: <b>{$totalIssuedInvoices}</b>\n\n";

        $completionRate = $totalEligibleInvoices > 0
            ? round(($totalIssuedInvoices / $totalEligibleInvoices) * 100, 2)
            : 0;
        $message .= "• Tỷ lệ hoàn thành: <b>{$completionRate}%</b>\n";

        return $message;
    }
}
