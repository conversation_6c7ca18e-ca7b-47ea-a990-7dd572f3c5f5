<?php

namespace App\Commands;

use App\Models\CompanySubscriptionModel;
use App\Services\SubscriptionUsageService;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Config\Subscription;

class CheckUsageLimitCommand extends BaseCommand
{
    protected $group = 'notifications';

    protected $name = 'notifications:check-usage-limit';

    protected $description = 'Check usage for paid subscription users and send notifications if limit are near or exceeded.';

    protected $usage = 'notifications:check-usage-limit [--execute]';

    protected $options = [
        'execute' => 'Execute real changes (default is dry run mode)'
    ];

    public function run(array $params)
    {
        $isExecute = CLI::getOption('execute') !== null;
        if (!$isExecute) {
            CLI::write("Running in DRY RUN mode - no actual changes will be made", 'yellow');
            CLI::write("Use --execute to perform real changes", 'yellow');
            CLI::newLine();
        }

        $subscriptionModel = model(CompanySubscriptionModel::class);
        $usageService = new SubscriptionUsageService();
        $config = config(Subscription::class);

        $subscriptions = $subscriptionModel
            ->select([
                'tb_autopay_company_subscription.id',
                'tb_autopay_company_subscription.company_id',
                'tb_autopay_company_subscription.plan_id',
                'tb_autopay_company_subscription.monthly_transaction_limit',
                'tb_autopay_company_subscription.last_notified_at',
                'tb_autopay_company_subscription.last_notification_type',
                'tb_autopay_company_subscription.billing_cycle',
                'tb_autopay_company_subscription.begin_date',
                'tb_autopay_company_subscription.end_date',
                'tb_autopay_company_subscription.recurring_payment',
                'tb_autopay_company_subscription.allow_exceed_limit',
                'tb_autopay_product.name as product_name',
            ])
            ->join('tb_autopay_product', 'tb_autopay_product.id = tb_autopay_company_subscription.plan_id')
            ->where('tb_autopay_company_subscription.status', 'Active')
            ->where('tb_autopay_company_subscription.monthly_transaction_limit >', 0)
            ->where('tb_autopay_company_subscription.billing_cycle !=', 'free')
            ->where('tb_autopay_product.name !=', 'Free')
            ->where('tb_autopay_company_subscription.auto_renew', true)
            ->findAll();

        CLI::write("Tìm thấy " . count($subscriptions) . " gói dịch vụ trả phí cần kiểm tra.", 'yellow');

        $notifiedCount = 0;
        $skippedCount = 0;
        $failedCount = 0;
        $nearLimitCount = 0;
        $exceedLimitCount = 0;
        $totalChecked = 0;

        CLI::write("Danh sách gói dịch vụ sắp vượt hoặc đã vượt giới hạn:", 'yellow');
        CLI::newLine();

        foreach ($subscriptions as $subscription) {
            $totalChecked++;
            $result = $usageService->checkSubscriptionUsage($subscription, $isExecute);

            if ($result['status'] === 'skipped' && (!isset($result['percentage']) || $result['percentage'] < 70)) {
                $skippedCount++;
                continue;
            }

            $formattedPercentage = number_format($result['percentage'], 1);
            $companyUrl = "https://ad.sepay.vn/company/details/{$subscription->company_id}";

            CLI::write("{$companyUrl} - {$result['usage']}/{$result['limit']} ({$formattedPercentage}%)", 'white');

            if (isset($result['percentage']) && $result['percentage'] >= 100) {
                $exceedLimitCount++;
            } elseif (isset($result['percentage']) && $result['percentage'] >= $config->nearLimitThreshold) {
                $nearLimitCount++;
            }

            switch ($result['status']) {
                case 'notified':
                    $notifiedCount++;
                    break;
                case 'skipped':
                    $skippedCount++;
                    break;
                case 'failed':
                    $failedCount++;
                    break;
            }
        }

        CLI::newLine();

        if ($nearLimitCount > 0 || $exceedLimitCount > 0) {
            CLI::write("Tổng kết:", 'yellow');
            if (!$isExecute) {
                CLI::write("[DRY RUN MODE - No actual changes were made]", 'yellow');
            }
            CLI::write("- Tổng gói đã kiểm tra: {$totalChecked}", 'white');
            CLI::write("- Gói sắp vượt giới hạn: {$nearLimitCount}", 'yellow');
            CLI::write("- Gói đã vượt giới hạn: {$exceedLimitCount}", 'red');
            CLI::write("- Gói đã thông báo: {$notifiedCount}", 'green');
            CLI::write("- Gói bỏ qua: {$skippedCount}", 'blue');
            CLI::write("- Gói thất bại: {$failedCount}", 'red');
        } else {
            CLI::write("Không có gói dịch vụ nào sắp vượt hoặc đã vượt giới hạn.", 'green');
        }
    }
}
