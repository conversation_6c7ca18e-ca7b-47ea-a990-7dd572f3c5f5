<?php

namespace App\Commands;

use App\Models\InvoiceModel;
use App\Models\StransactionModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class UpdateInvoiceDatepaid extends BaseCommand
{
    protected $group = 'Custom';

    protected $name = 'invoice:update-datepaid';

    protected $description = 'Update datepaid of invoices to latest transaction date';

    public function run(array $params)
    {
        $invoiceModel = model(InvoiceModel::class);
        $transactionModel = model(StransactionModel::class);

        $invoices = $invoiceModel
            ->select('tb_autopay_invoice.id, tb_autopay_physical_invoices.final_amount')
            ->where('tb_autopay_invoice.type', 'PhysicalProduct')
            ->where('tb_autopay_invoice.datepaid', '0000-00-00 00:00:00')
            ->join('tb_autopay_physical_invoices', 'tb_autopay_physical_invoices.id = tb_autopay_invoice.physical_invoice_id')
            ->findAll();

        foreach ($invoices as $invoice) {
            $totalPaid = $transactionModel
                ->where('invoice_id', $invoice->id)
                ->selectSum('in')
                ->first()
                ->in ?? 0;

            if ($totalPaid >= $invoice->final_amount) {
                $transaction = $transactionModel
                    ->where('invoice_id', $invoice->id)
                    ->orderBy('date', 'DESC')
                    ->first();

                if ($transaction) {
                    $invoiceModel->update($invoice->id, [
                        'datepaid' => $transaction->date,
                    ]);
                }
            }
        }
    }
}
