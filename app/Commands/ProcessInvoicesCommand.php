<?php

namespace App\Commands;

use App\Models\CompanyUserModel;
use App\Models\InvoiceCustomerInfoModel;
use App\Models\InvoiceModel;
use App\Models\TaxInfoModel;
use App\Services\CreateInvoice;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Config\SubscriptionSInvoice;
use Exception;

class ProcessInvoicesCommand extends BaseCommand
{
    protected $group = 'Invoices';

    protected $name = 'invoices:process';

    protected $description = 'Xử lý các hóa đơn dịch vụ chưa được xuất';

    public function run(array $params)
    {
        CLI::write('Bắt đầu xử lý hóa đơn...', 'yellow');

        $config = config(SubscriptionSInvoice::class);
        $companyUserModel = model(CompanyUserModel::class);
        $invoiceModel = model(InvoiceModel::class);
        $customerInfoModel = model(InvoiceCustomerInfoModel::class);
        $taxInfoModel = model(TaxInfoModel::class);

        $invoices = $invoiceModel
            ->select(['tb_autopay_invoice.id', 'tb_autopay_invoice.company_id'])
            ->join('tb_autopay_invoice_customer_info', 'tb_autopay_invoice_customer_info.invoice_id = tb_autopay_invoice.id', 'left')
            ->where('tb_autopay_invoice.tax_issued_id', '')
            ->where('tb_autopay_invoice.customer_info_id', null)
            ->whereIn('tb_autopay_invoice.type', ['NewOrder', 'Recurring', 'Excess', 'SubscriptionChange'])
            ->where('tb_autopay_invoice.created_at >=', $config->issueInvoiceFrom ?? date('Y-m-d'))
            ->whereIn('tb_autopay_invoice.status', ['Paid', 'Unpaid'])
            ->where('tb_autopay_invoice.total >', 0)
            ->groupBy('tb_autopay_invoice.id')
            ->findAll();

        CLI::write('Tìm thấy ' . count($invoices) . ' hóa đơn cần xử lý', 'yellow');
        CLI::newLine();

        helper('general');

        foreach ($invoices as $invoice) {
            CLI::write("Đang xử lý hóa đơn #{$invoice->id}...", 'yellow');

            $companyUser = $companyUserModel
                ->select('tb_autopay_company_user.company_id, tb_autopay_user.lastname, tb_autopay_user.firstname')
                ->join('tb_autopay_user', 'tb_autopay_user.id = tb_autopay_company_user.user_id')
                ->where('tb_autopay_company_user.company_id', $invoice->company_id)
                ->where('tb_autopay_company_user.role', 'SuperAdmin')
                ->first();

            if (! $companyUser) {
                CLI::write("Bỏ qua hóa đơn #{$invoice->id}: Không tìm thấy thông tin công ty", 'red');
                continue;
            }

            $customerInfo = $customerInfoModel->where('company_id', $companyUser->company_id)->first();

            if ($customerInfo) {
                $customerInfoId = $customerInfo->id;
            } else {
                $taxInfo = $taxInfoModel->where('company_id', $companyUser->company_id)->orderBy('created_at', 'DESC')->first();

                if ($taxInfo) {
                    $customerInfoId = $customerInfoModel->insert([
                        'company_id' => $taxInfo->company_id,
                        'name' => $taxInfo->type === 'Individual' ? $taxInfo->name : null,
                        'company_name' => $taxInfo->type === 'Organization' ? $taxInfo->name : null,
                        'email' => ! empty($taxInfo->email) ? json_encode([$taxInfo->email]) : null,
                        'tax_code' => $taxInfo->tax_code,
                        'address' => get_full_tax_info_address($taxInfo->id),
                    ]);

                    CLI::write("Đã tạo thông tin khách hàng mới #$customerInfoId từ thông tin thuế cho công ty {$companyUser->company_id}", 'green');
                }
            }

            if (! $customerInfoId) {
                $customerInfoId = $customerInfoModel->insert([
                    'company_id' => $companyUser->company_id,
                    'name' => $companyUser->firstname . ' ' . $companyUser->lastname,
                ]);

                CLI::write("Đã tạo thông tin khách hàng mới #$customerInfoId cho công ty {$companyUser->company_id}", 'green');
            }

            if ($customerInfoId) {
                $invoiceModel->update($invoice->id, [
                    'customer_info_id' => $customerInfoId,
                    'vat_invoice_requested_at' => date('Y-m-d H:i:s'),
                ]);
                CLI::write("Đã cập nhật thông tin khách hàng cho hóa đơn #{$invoice->id} ({$companyUser->company_id})", 'green');
            } else {
                CLI::write("Không thể cập nhật thông tin khách hàng cho hóa đơn #{$invoice->id} ({$companyUser->company_id})", 'red');
            }

            CLI::newLine();
        }

        $invoices = $invoiceModel
            ->select('tb_autopay_invoice.id, tb_autopay_invoice.last_update_status_at')
            ->join('tb_autopay_invoice_customer_info', 'tb_autopay_invoice_customer_info.id = tb_autopay_invoice.customer_info_id')
            ->where('tb_autopay_invoice.tax_issued_id', '')
            ->where('tb_autopay_invoice.customer_info_id IS NOT NULL')
            ->where('tb_autopay_invoice.status', 'Paid')
            ->where('tb_autopay_invoice.total >', 0)
            ->where('tb_autopay_invoice.created_at >=', $config->issueInvoiceFrom ?? date('Y-m-d'))
            ->where('tb_autopay_invoice.company_id !=', 0)
            ->where('tb_autopay_invoice.is_pushed_to_google_sheets', false)
            ->findAll();

        $createInvoiceService = new CreateInvoice();

        date_default_timezone_set('Asia/Ho_Chi_Minh');
        $currentHour = (int) date('H');
        $isLateNight = $currentHour >= 23 && $currentHour < 24;

        foreach ($invoices as $invoice) {
            if ($invoice->last_update_status_at) {
                $lastUpdateTime = strtotime($invoice->last_update_status_at);
                $shouldProcess = false;

                if ($isLateNight) {
                    $shouldProcess = true;
                } else if (time() >= $lastUpdateTime + HOUR) {
                    $shouldProcess = true;
                }

                if (! $shouldProcess) {
                    continue;
                }
            }

            try {
                CLI::write("Đang xuất hóa đơn #{$invoice->id}...", 'yellow');
                $result = $createInvoiceService->createInvoice($invoice->id);
                CLI::write("Đã xuất hóa đơn #{$invoice->id} - {$result['invoiceCode']}.", 'green');
            } catch (Exception $e) {
                CLI::write("Lỗi khi xử lý hóa đơn #{$invoice->id}: {$e->getMessage()}", 'red');
            }
        }

        CLI::newLine();
        CLI::write('Hoàn thành xử lý hóa đơn!', 'green');
    }
}
