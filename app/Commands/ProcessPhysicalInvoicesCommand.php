<?php

namespace App\Commands;

use App\Models\PhysicalInvoiceModel;
use App\Models\PhysicalOrderModel;
use App\Models\StransactionModel;
use App\Services\CreatePhysicalInvoice;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Config\SInvoice;
use Exception;

class ProcessPhysicalInvoicesCommand extends BaseCommand
{
    protected $group = 'Invoices';

    protected $name = 'physical-invoices:process';

    protected $description = 'Xử lý các hóa đơn chưa được xuất';

    public function run(array $params)
    {
        $orderModel = model(PhysicalOrderModel::class);
        $invoiceModel = model(PhysicalInvoiceModel::class);
        $stransactionModel = model(StransactionModel::class);
        $createInvoiceService = new CreatePhysicalInvoice();
        $config = config(SInvoice::class);

        $orders = $orderModel
            ->select('tb_autopay_physical_orders.id, tb_autopay_invoice.id as invoice_id, tb_autopay_physical_orders.customer_email')
            ->join('tb_autopay_physical_invoices', 'tb_autopay_physical_invoices.order_id = tb_autopay_physical_orders.id', 'left')
            ->join('tb_autopay_invoice', 'tb_autopay_invoice.physical_invoice_id = tb_autopay_physical_invoices.id', 'left')
            ->where('tb_autopay_physical_orders.status', 'Completed')
            ->where('tb_autopay_physical_orders.payment_status', 'Paid')
            ->whereIn('tb_autopay_physical_orders.shipping_company', ['GHTK', 'Self Delivery'])
            ->findAll();

        foreach ($orders as $order) {
            if (empty($order->invoice_id)) {
                $createInvoiceService->createInvoice($order->id, [
                    'invoice_email' => $order->customer_email ? [$order->customer_email] : [],
                ]);
            }
        }

        $pendingInvoices = $invoiceModel
            ->select('tb_autopay_physical_invoices.id, tb_autopay_invoice.total, tb_autopay_physical_orders.customer_email, tb_autopay_physical_orders.last_update_status, tb_autopay_physical_orders.shipping_company, tb_autopay_invoice.id as invoice_id')
            ->join('tb_autopay_physical_orders', 'tb_autopay_physical_orders.id = tb_autopay_physical_invoices.order_id')
            ->join('tb_autopay_invoice', 'tb_autopay_invoice.physical_invoice_id = tb_autopay_physical_invoices.id', 'left')
            ->whereIn('tb_autopay_physical_invoices.status', $config->useDraftInvoice ? ['pending'] : ['pending', 'draft'])
            ->where('tb_autopay_physical_orders.customer_type', 'retail')
            ->where('tb_autopay_physical_orders.payment_status', 'Paid')
            ->where('tb_autopay_physical_orders.status', 'Completed')
            ->whereIn('tb_autopay_physical_orders.shipping_company', ['GHTK', 'Self Delivery'])
            ->where('tb_autopay_physical_orders.created_at >=', $config->issueInvoiceFrom)
            ->findAll();

        if (empty($pendingInvoices)) {
            CLI::write('Không có hóa đơn nào cần xử lý.', 'yellow');
            return;
        }

        CLI::write('Bắt đầu xử lý ' . count($pendingInvoices) . ' hóa đơn...', 'green');

        date_default_timezone_set('Asia/Ho_Chi_Minh');
        $currentHour = (int) date('H');
        $isLateNight = $currentHour >= 23 && $currentHour < 24;

        foreach ($pendingInvoices as $invoice) {
            if ($invoice->last_update_status) {
                $lastUpdateTime = strtotime($invoice->last_update_status);
                $shouldProcess = false;

                if ($isLateNight) {
                    $shouldProcess = true;
                } else if (time() >= $lastUpdateTime + HOUR) {
                    $shouldProcess = true;
                }

                if (! $shouldProcess) {
                    continue;
                }
            }

            if ($invoice->shipping_company === 'Self Delivery') {
                if (empty($invoice->invoice_id)) {
                    CLI::write("Bỏ qua hóa đơn #{$invoice->id}: Chưa được xuất hóa đơn", 'yellow');
                    continue;
                }

                $totalPaid = $stransactionModel
                    ->where('invoice_id', $invoice->invoice_id)
                    ->selectSum('in')
                    ->first()
                    ->in ?? 0;

                if ($totalPaid < $invoice->total) {
                    CLI::write("Bỏ qua hóa đơn #{$invoice->id}: Chưa đủ số tiền thanh toán (Cần: {$invoice->total}, Đã thanh toán: {$totalPaid})", 'yellow');
                    continue;
                }
            }

            try {
                CLI::write("Đang xử lý hóa đơn #{$invoice->id}...", 'yellow');
                $createInvoiceService->issueInvoice($invoice->id);
                CLI::write("Đã xuất hóa đơn #{$invoice->id} thành công.", 'green');
            } catch (Exception $e) {
                CLI::write("Lỗi khi xử lý hóa đơn #{$invoice->id}: {$e->getMessage()}", 'red');
            }
        }

        CLI::write('Hoàn thành xử lý hóa đơn.', 'green');
    }
}
