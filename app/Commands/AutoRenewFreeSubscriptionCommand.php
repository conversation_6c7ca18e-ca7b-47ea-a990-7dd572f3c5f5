<?php

namespace App\Commands;

use App\Models\CompanySubscriptionModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use DateInterval;
use DateTime;

class AutoRenewFreeSubscriptionCommand extends BaseCommand
{
    protected $group = 'CodeIgniter';

    protected $name = 'subscription:auto-renew-free';

    protected $description = 'Automatically renews expired free subscriptions';

    protected $usage = 'subscription:auto-renew-free [--dry-run]';

    protected $options = [
        'dry-run' => 'Perform a dry-run of the command',
    ];

    public function run(array $params)
    {
        $subscriptionModel = model(CompanySubscriptionModel::class);

        $subscriptions = $subscriptionModel
            ->select([
                'tb_autopay_company_subscription.id',
                'tb_autopay_company_subscription.company_id',
                'tb_autopay_company_subscription.end_date',
            ])
            ->join('tb_autopay_product', 'tb_autopay_product.id = tb_autopay_company_subscription.plan_id')
            ->join('tb_autopay_company', 'tb_autopay_company.id = tb_autopay_company_subscription.company_id')
            ->where('tb_autopay_company_subscription.billing_cycle', 'free')
            ->where('tb_autopay_company_subscription.status', 'Active')
            ->where('tb_autopay_company_subscription.auto_renew', true)
            ->where('tb_autopay_product.name', 'Free')
            ->where('tb_autopay_company.active', true)
            ->findAll();

        if (empty($subscriptions)) {
            CLI::write('No free subscriptions to renew');
            return;
        }

        $dryRun = array_key_exists('dry-run', $params);
        $today = date('Y-m-d');
        $total = 0;

        foreach ($subscriptions as $subscription) {
            if (! $subscription->end_date || $subscription->end_date >= $today) {
                continue;
            }

            $total++;

            $end = new DateTime($subscription->end_date);
            $now = new DateTime($today);
            $interval = $end->diff($now);
            $months = $interval->m + ($interval->y * 12) + 1;

            $newEndDate = $end->add(new DateInterval("P{$months}M"))->format('Y-m-d');

            if ($dryRun) {
                $message = "Free subscription for company {$subscription->company_id} will be renewed from {$subscription->end_date} until {$newEndDate}";
            } else {
                $subscriptionModel->update($subscription->id, ['end_date' => $newEndDate]);
                $message = "Free subscription for company {$subscription->company_id} was renewed from {$subscription->end_date} until {$newEndDate}";
            }

            log_message('error', $message);
            CLI::write($message);
        }

        if ($dryRun) {
            CLI::write("Total free subscriptions to renew: {$total}");
        } else {
            CLI::write("Total free subscriptions renewed: {$total}");
        }
    }
}
