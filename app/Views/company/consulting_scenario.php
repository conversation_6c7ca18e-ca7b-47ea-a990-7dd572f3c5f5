<main class="content">
    <div class="container-fluid">

        <div>
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("company");?>">Công ty</a></li> 
                <li class="breadcrumb-item"><a href="<?= base_url("company/details/" . $company_details->id);?>">#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></a></li> 

                <li class="breadcrumb-item active" aria-current="page">Kịch bản tư vấn</li> 
                </ol>
            </nav>
            <!-- End Breadcrumb -->
        </div>
        
        <div class="row mt-3">

            <?php include(APPPATH . 'Views/company/inc_left_menu.php');?>


            <div class="col-md-9 col-xl-10">
                 
                <div class="row">
                    <div class="col-auto d-none d-sm-block">
                        <h4>#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></h4>
                    </div>

                    <div class="col-auto ms-auto text-end row">
                       

                    
                    </div>
                </div>
              

                <div class="mt-3">
                    <div class="card">
                        
                        <div class="card-body">
                             <div><p>Hãy tư vấn khách hàng dựa vào các thông tin sau</p></div>
                             <div>
                                <table class="table">
                                    <thead>
                                        <th>Loại</th>
                                        <th>Thông tin</th>
                                        <th>Cần tư vấn</th>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Gói dịch vụ</td>
                                            <td>
                                                <?php if(!is_object($subscription_details)) { ?>
                                                <span class='text-danger'>Chưa chọn gói dịch vụ</span>
                                                <?php  } else { ?>
                                                <span class='text-success'>Đã chọn gói dịch vụ</span>
                                                <?php } ?>
                                            </td>
                                            <td><?php if(!is_object($subscription_details)) { ?> Chọn gói dịch vụ và thanh toán <br><a href="<?php echo base_url('company/subscription/' . $company_details->id);?>">Xem gói dịch vụ</a> <?php } ?></td>
                                        </tr>

                                        
                                        <?php if(is_object($subscription_details) && is_object($invoice_new_order)) { ?>
                                        <tr>
                                            
                                            <td>Gói dịch vụ</td>
                                            <td>
                                                <?php if($invoice_new_order->status=='Unpaid') { ?>
                                                <span class='text-danger'>Đã chọn gói nhưng chưa thanh toán hoá đơn</span>
                                                <?php } else if($invoice_new_order->status=='Paid') {?>
                                                <span class='text-success'>Đã chọn gói và đã thanh toán hoá đơn</span>
                                            <?php } ?></td>
                                            <td><?php if($invoice_new_order->status=='Unpaid') { ?>Tư vấn để khách hàng thanh toán hoá đơn <br><a href="<?php echo base_url('company/invoice/' . $company_details->id);?>">Xem hoá đơn</a> <?php } ?></td>
                                        </tr>
                                        <?php } ?>
                                        
                                        <tr>
                                            
                                            <td>Tài khoản ngân hàng</td>
                                            <td>
                                            <?php if($count_bank_account == 0) { ?>    
                                                <span class='text-danger'>Chưa liên kết tài khoản nào</span> <?php } else { ?> <span class='text-success'>Đã liên kết tài khoản ngân hàng</span> <?php } ?></td>
                                            <td><?php if($count_bank_account == 0) { ?>   Tư vấn và hướng dẫn khách hàng liên kết tài khoản ngân hàng <?php } ?></td>
                                        </tr>
                                       
                                        <tr>
                                             
                                            <td>Giao dịch</td>
                                            <td><?php if($transaction_count == 0) { ?> <span class='text-danger'>Chưa có giao dịch </span><?php } else { ?> <span class='text-success'>Đã có giao dịch</span>  <?php } ?></td>
                                            <td><?php if($transaction_count == 0) { ?>Tư vấn thử nghiệm giao dịch chuyển vào VA nếu là liên kết qua API ngân hàng, chuyển vào tài khoản chính nếu qua SMS Banking  <?php } ?></td>
                                        </tr>

                                        <?php if(is_object($invoice_recurring) && $invoice_recurring->status == "Unpaid") { ?>
                                        <tr>
                                            
                                            <td>Hoá đơn</td>
                                            <td><span class='text-danger'>Có hoá đơn gia hạn chưa thanh toán</span></td>
                                            <td>Gọi nhắc khách thanh toán</td>
                                        </tr>
                                        <?php } ?>

                                        <?php if(is_object($invoice_excess) && $invoice_excess->status == "Unpaid") { ?>
                                        <tr>
                                            
                                            <td>Hoá đơn</td>
                                            <td><span class='text-danger'>Có hoá đơn vượt nhưng chưa thanh toán</span></td>
                                            <td>Gọi nhắc khách thanh toán</td>
                                        </tr>
                                        <?php } ?>

                                        <?php if( $count_trans_this_period > $limit_trans_this_period) { ?>
                                        
                                        <tr>
                                            
                                            <td>Gói dịch vụ</td>
                                            <td><span class='text-danger'>Đang vượt hạn mức</span></td>
                                            <td>
                                            Hạn mức: <?php echo number_format($limit_trans_this_period);?>
                                            <br>Đã sử dụng: <?php echo number_format($count_trans_this_period);?>
                                            <br>Tính toán và xem xét có cần tư vấn nâng gói không
                                        <br><a href="<?php echo base_url('subscription/edit/' . $subscription_details->id);?>">Xem Gói dịch vụ</a></td>
                                        </tr>

                                        <?php } ?>

                                        <?php if($count_telegram == 0 && $count_lark && $count_webhook) { ?>

                                        <tr>
                                             
                                            <td>Tích hợp</td>
                                            <td><span class='text-danger'>Không có bất kỳ tích hợp nào</a></td>
                                            <td>Xem xét tư vấn tích hợp chia sẻ biến động số dư (telegram, lark, google sheets..) hoặc webhook. Chỉ liên kết ngân hàng thôi thì chưa phục vụ mục đích nào cả.</td>
                                        </tr>
                                        <?php } ?>

                                        <?php if(is_object($subscription_details) && $subscription_details->billing_cycle && $subscription_details->plan_id == 7) { ?>
                                        <tr>
                                            <td>Gói dịch vụ</td>
                                            <td>
                                                <span class='text-danger'>Khách hàng đang sài gói Free</span>
                                                
                                            </td>
                                            <td>Tư vấn khách lên gói có phí <br><a href="<?php echo base_url('subscription/edit/' . $subscription_details->id);?>">Xem Gói dịch vụ</a></td>
                                        </tr>
                                        <?php } ?>
                                    </tbody>
                                </table>
                             </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>


 


<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>

 
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>


<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/app.js"></script>


<script>
 
 
        keyboard: false
    });



    function add_activity() {
        $('#activity_add_form')[0].reset();
        save_method = 'add';
        $('#activityAddModalLabel').text('Thêm hoạt động');
        $(".btn-activity-add").html('Thêm');
        addActivityModal.show();
    }


    function edit_activity(id) {
       

       save_method = 'update';
       $('#activity_add_form')[0].reset();

       //Ajax Load data from ajax
       $.ajax({
         url : "<?php echo base_url('crmactivity/ajax_get');?>/" + id,
         type: "GET",
         dataType: "JSON",
         success: function(data)
         {
           if(data.status == true) {
               console.log(data);
               $('#activity_add_form').find('input[name=id]').val(data.data.id);
               $('#activity_add_form').find('input[name=activity_at]').val(data.data.activity_at);
               $('#activity_add_form').find('textarea[name=content]').val(data.data.content);
              

               $('#activityAddModalLabel').text('Edit invoice item');
               $(".btn-activity-add").html('Lưu thay đổi');
               addActivityModal.show();

           } else {
               alert('Lỗi: ' + data.message);
           }
 
           
         },
         error: function (jqXHR, textStatus, errorThrown)
         {
             alert('Lỗi không thể lấy được thông tin invoice item này');
         }
     });
   }


   function save()
   {
       var url;
       if(save_method == 'add')
       {
           url = "<?php echo base_url('crmactivity/ajax_add');?>";

       } else  if(save_method == 'update')
       {
           url = "<?php echo base_url('crmactivity/ajax_update');?>";
       } 
       else
       {
           url = "<?php echo base_url('crmactivity');?>";
       } 

       $(".btn-activity-add").attr("disabled", true);
       $('#btn_activity_loading').html('');
       $(".btn-invoice-item-add").html('<div class="spinner-border text-light" role="status" id="btn_activity_loading"></div>');

       $.ajax({
           url : url,
           type: "POST",
           data: $('#activity_add_form').serialize(),
           dataType: "JSON",
           success: function(data)
           {
               //if success close modal and reload ajax table
               if(data.status == true) {
                    addActivityModal.hide();


                   if(save_method == 'add')
                   {
                       notyf.success({message:'Thêm thành công', dismissible: true});
                       $('#activities_table').DataTable().ajax.reload();

                   } else  if(save_method == 'update')
                   {
                       notyf.success({message:'Sửa thành công', dismissible: true});
                       $('#activities_table').DataTable().ajax.reload();

                   } 
               } else {
                   
                   $(".btn-activity-add").attr("disabled", false);
                   $('#btn_activity_loading').remove();
                   $(".btn-activity-add").html('Thêm');
                   alert('Lỗi: ' + data.message);

               }
           
           },
           error: function (jqXHR, textStatus, errorThrown)
           {
               alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
               $(".btn-activity-add").attr("disabled", false);
               $('#btn_activity_loading').remove();
               $(".btn-activity-add").html('Thêm');
           }
           
       });
   }

   function delete_activity(id) {
        if (confirm("Bạn có chắc chắn muốn xóa tích hợp này?")) {
            $.ajax({
            url : "<?php echo base_url('crmactivity/ajax_delete');?>",
            type: "POST",
            data: {id: id, "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
            dataType: "JSON",
            success: function(data)
            {
                //if success close modal and reload ajax table
                if(data.status == true) {
                    $('#activities_table').DataTable().ajax.reload();
                    notyf.success({message:'Xóa thành công', dismissible: true});

                } else {
                    
                    alert('Lỗi: ' + data.message);
 
                }
            
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
               
            }
            
        });
        }
        
    }

</script>