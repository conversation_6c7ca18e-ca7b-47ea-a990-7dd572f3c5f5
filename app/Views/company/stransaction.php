<main class="content">
    <div class="container-fluid">
        <div>
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("company");?>">Công ty</a></li> 
                <li class="breadcrumb-item"><a href="<?= base_url("company/details/" . $company_details->id);?>">#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></a></li> 

                <li class="breadcrumb-item active" aria-current="page">Giao dịch</li> 
                </ol>
            </nav>
            <!-- End Breadcrumb -->
        </div>
        <div class="row mt-3">

            <?php include(APPPATH . 'Views/company/inc_left_menu.php');?>


            <div class="col-md-9 col-xl-10">

                <h4>#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></h4>
                 
                <div class="mt-2">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6 col-md-3 text-center">
                                    <div><b class="text-success fs-3"><?= number_format($in_stransaction_count);?></b> Amount In </div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-success"> <?= number_format($in_stransaction_sum->total_amount);?> đ</span>
                                </div>
                           
                              
                                     
                                <div class="col-6 col-md-3 text-center">
                                    <div><b class="text-danger fs-3"><?= number_format($out_stransaction_count);?></b> Amount Out </div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-danger"> <?= number_format($out_stransaction_sum->total_amount);?> đ</span>
                                </div>
                                
                            </div>
                        </div>
                    </div>
                </div>

              

                <div class="mt-3">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"></h5>
                        </div>
                        <div class="card-body">
                            <div class="">
                                <table class="table table-hover table-striped table-bordered text-muted align-middle display nowrap" id="trans_table" style="width:100%">

                                    <thead class="table-light text-muted">
                                        <tr class="align-middle">
                                            <th></th>
                                            <th>ID Giao dịch</th>
                                            <th>Ngày</th>
                                            <th>Tiền vào</th>
                                            <th>Tiền ra</th>
                                            <th>Hoá đơn</th>
                                            <th></th>

                                         </tr>
                                    </thead>
                                    <tbody>
                
                                       
                                    </tbody>
                                </table>
                            </div>
                             
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

<!-- Payment Modal -->
<div class="modal fade" id="paymentAddModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="paymentAddModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title payment-modal-title" id="paymentAddModalLabel">Sửa giao dịch</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <?php echo form_open('',"id='payment_add_form' class='needs-validation form-add-payment' novalidate");?>
        <div class="modal-body m-lg-3">
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label" for="inputEmail4">Date</label>
                        <input class="form-control" type="datetime-local" name="date" required/>
                        <span class="validity"></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label" for="inputEmail4">Amount</label>
                        <input class="form-control" type="number" name="in" required/>
                        <span class="validity"></span>
                    </div>
                </div>
                <div class="col-12">
                    <div class="mb-3">
                        <label class="form-label" for="inputEmail4">Description</label>
                        <textarea name="description" class="form-control" placeholder="" rows="3"></textarea>
                        <span class="validity"></span>
                    </div>
                </div>
            </div>
        
        </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light me-3" data-bs-dismiss="modal">Đóng</button>
                <a class="btn btn-primary btn-payment-add" onclick="payment_save()">Cập nhật</a>
            </div>
        </form>
        </div>
    </div>
</div>
<!-- End Payment Modal -->

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>

 
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>


<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/app.js"></script>


<script>
 

 $(document).ready(function() {

      var  table = $('#trans_table').DataTable({ 
         rowReorder: {
             selector: 'td:nth-child(0)'
         },
         responsive: true,

      
          "processing": true,
          "serverSide": true,
          "order": [],
          "pageLength": 10,

          "ajax": {
              "url": "<?php echo base_url('company/stransaction_ajax_list/' . $company_details->id); ?>",
              "data": {"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
              "type": "POST"
          },


          "language": {
              "sProcessing":   "Đang xử lý...",
              "sLengthMenu":   "Xem _MENU_ mục",
              "sZeroRecords":  "Không tìm thấy dòng nào phù hợp",
              "sInfo":         "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
              "sInfoEmpty":    "Đang xem 0 đến 0 trong tổng số 0 mục",
              "sInfoFiltered": "(được lọc từ _MAX_ mục)",
              "sInfoPostFix":  "",
              "sSearch":       "Tìm:",
              "sUrl":          "",
              "oPaginate": {
                  "sFirst":    "Đầu",
                  "sPrevious": "Trước",
                  "sNext":     "Tiếp",
                  "sLast":     "Cuối"
              }
          },

          "columnDefs": [
             { responsivePriority: 1, targets: 1 },
             { responsivePriority: 2, targets: 2 },
              
              { 
                  "visible": false,
                  "targets": [ 0 ],
                  "orderable": false,
              },

              
          ],

          "drawCallback": function( settings ) {
             var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
             var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                 return new bootstrap.Tooltip(tooltipTriggerEl, {trigger : 'hover'})
             });
         },

          
      });

      table.on( 'responsive-display', function ( e, datatable, row, showHide, update ) {
         var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
         var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
             return new bootstrap.Tooltip(tooltipTriggerEl, {trigger : 'hover'})
         });

     } );

    

     table.on ('init', function () {
         $('*[type="search"][class="form-control form-control-sm"]').attr('style','max-width:120px');
         $('div.dataTables_filter').parent().attr('class','col-6');
         $('div.dataTables_length').parent().attr('class','col-6');
     })
 });

<?php if (has_permission('Stransaction',  'can_edit')): ?>
function edit_payment(id) {
    save_method = 'update';
    $('#payment_add_form')[0].reset();
    
    if ($('#payment_add_form').find('input[name=id]').length === 0) {
        $('#payment_add_form').append('<input type="hidden" name="id">');
    }

    $.ajax({
        url: "<?php echo base_url('stransaction/ajax_get');?>/" + id,
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            if(data.status == true) {
                $('#payment_add_form').find('input[name=id]').val(data.data.id);
                $('#payment_add_form').find('input[name=date]').val(formatDateTimeForInput(data.data.date));
                $('#payment_add_form').find('input[name=in]').val(parseInt(data.data.in));
                $('#payment_add_form').find('textarea[name=description]').val(data.data.description);
                
                $('.payment-modal-title').text('Sửa giao dịch');
                $(".btn-payment-add").html('Cập nhật');
                
                addPaymentModal.show();
            } else {
                alert('Lỗi: ' + data.message);
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Lỗi không thể lấy được thông tin giao dịch này');
        }
    });
}
var addPaymentModal = new bootstrap.Modal(document.getElementById('paymentAddModal'), {
    keyboard: false
});

function payment_save() {
    var url;
    if(save_method == 'update') {
        url = "<?php echo base_url('stransaction/ajax_update_by_invoice');?>";
    } else {
        return;
    }

    $(".btn-payment-add").attr("disabled", true);
    $(".btn-payment-add").html('<div class="spinner-border text-light" role="status"></div>');

    $.ajax({
        url: url,
        type: "POST",
        data: $('#payment_add_form').serialize(),
        dataType: "JSON",
        success: function(data) {
            if(data.status == true) {
                addPaymentModal.hide();
                $('#trans_table').DataTable().ajax.reload();
            } else {
                $(".btn-payment-add").attr("disabled", false);
                $(".btn-payment-add").html('Cập nhật');
                alert('Lỗi: ' + data.message);
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
            $(".btn-payment-add").attr("disabled", false);
            $(".btn-payment-add").html('Cập nhật');
        }
    });
}
<?php endif; ?>

<?php if (has_permission('Stransaction',  'can_delete')): ?>
function delete_payment(id) {
    if (!confirm("Bạn có chắc chắn muốn xóa giao dịch này?")) {
        return;
    }
    
    const deleteLink = $(`a[onclick="delete_payment(${id})"]`);
    const originalText = deleteLink.html();
    deleteLink.html('<i class="spinner-border spinner-border-sm"></i>');
    deleteLink.removeClass('text-danger').addClass('disabled');
    
    $.ajax({
        url: "<?php echo base_url('stransaction/ajax_delete');?>/" + id,
        type: "POST",
        data: {
            "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"
        },
        dataType: "JSON",
        success: function(data) {
            if (data.status === true) {
                notyf.success('Xóa giao dịch thành công');
                $('#trans_table').DataTable().ajax.reload();
            } else {
                deleteLink.html(originalText);
                deleteLink.addClass('text-danger').removeClass('disabled');
                
                alert('Lỗi: ' + (data.message || 'Không thể xóa giao dịch'));
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            deleteLink.html(originalText);
            deleteLink.addClass('text-danger').removeClass('disabled');
            
            alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
        }
    });
}
<?php endif; ?>

function formatDateTimeForInput(dateString) {
    const date = new Date(dateString);
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

</script>