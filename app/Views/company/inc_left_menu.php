<?php 
$uri = service('uri');
$segment1 = $uri->getSegment(1);
$segment2 = $uri->getSegment(2);
 

?>
<div class="col-md-3 col-xl-2">

    <div class="card">

        <div class="list-group list-group-flush">
            <a class="list-group-item list-group-item-action <?php if( $segment1 == 'company' && $segment2 == 'details') echo 'active';?>" href="<?php echo base_url('company/details/' . $company_details->id);?>"><i class="bi bi-buildings me-2"></i> <PERSON><PERSON> sơ công ty</a>
            <a class="list-group-item list-group-item-action <?php if($segment2 == 'subscription'||$segment1 == 'subscription') echo 'active';?>" href="<?php echo base_url('company/subscription/' . $company_details->id);?>"><i class="bi bi-layers me-2"></i> Subscription</a>  
            <a class="list-group-item list-group-item-action <?php if($segment2 == 'sim') echo 'active';?>" href="<?php echo base_url('company/sim/' . $company_details->id);?>"><i class="bi bi-sim me-2"></i> SIM</a>

            <?php if(has_permission('BankAccount', 'can_view_all')) { ?>
            <a class="list-group-item list-group-item-action <?php if($segment1 == 'company' && $segment2 == 'bankaccount') echo 'active';?>" href="<?php echo base_url('company/bankaccount/' . $company_details->id);?>"><i class="bi bi-bank me-2"></i> Ngân hàng</a>
            <?php } ?>

            <a class="list-group-item list-group-item-action <?php if($segment2 == 'invoice' || $segment1 == 'invoice') echo 'active';?>" href="<?php echo base_url('company/invoice/' . $company_details->id);?>"><i class="bi bi-receipt me-2"></i> Hóa đơn</a>

            <a class="list-group-item list-group-item-action <?php if($segment2 == 'tax_info' || ($segment1 == 'taxinfo')) echo 'active';?>" href="<?php echo base_url('company/tax_info/' . $company_details->id);?>"><i class="bi bi-card-list me-2"></i> Tax Info</a>


            <a class="list-group-item list-group-item-action <?php if($segment2 == 'stransaction') echo 'active';?>" href="<?php echo base_url('company/stransaction/' . $company_details->id);?>"><i class="bi bi-cash me-2"></i> Giao dịch</a>

            <a class="list-group-item list-group-item-action <?php if($segment2 == 'ticket') echo 'active';?>" href="<?php echo base_url('company/ticket/' . $company_details->id);?>"><i class="bi bi-life-preserver me-2"></i> Hỗ trợ</a>

            <a class="list-group-item list-group-item-action <?php if($segment2 == 'activity') echo 'active';?>" href="<?php echo base_url('company/activity/' . $company_details->id);?>"><i class="bi bi-telephone me-2"></i> CSKH</a>

            <a class="list-group-item list-group-item-action <?php if($segment2 == 'consulting_scenario') echo 'active';?>" href="<?php echo base_url('company/consulting_scenario/' . $company_details->id);?>"><i class="bi bi-book me-2"></i> Kịch bản tư vấn</a>

            <?php if (has_permission('EmailSent', 'can_view_all')): ?>
                <a class="d-flex align-items-center gap-2 list-group-item list-group-item-action <?php if($segment2 == 'email-sent') echo 'active';?>" href="<?php echo base_url('company/email-sent/' . $company_details->id);?>">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-envelope-arrow-down" viewBox="0 0 16 16">
                        <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v4.5a.5.5 0 0 1-1 0V5.383l-7 4.2-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h5.5a.5.5 0 0 1 0 1H2a2 2 0 0 1-2-1.99zm1 7.105 4.708-2.897L1 5.383zM1 4v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1"/>
                        <path d="M12.5 16a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7m.354-1.646a.5.5 0 0 1-.722-.016l-1.149-1.25a.5.5 0 1 1 .737-.676l.28.305V11a.5.5 0 0 1 1 0v1.793l.396-.397a.5.5 0 0 1 .708.708z"/>
                    </svg>
                    Email đã gửi
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>
