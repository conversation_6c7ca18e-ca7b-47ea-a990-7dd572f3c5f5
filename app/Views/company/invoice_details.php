<style>
.copyjs {
    cursor: pointer;
}
</style>
<main class="content">
    <div class="container-fluid">

        <div>
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("company");?>">Công ty</a></li> 
                <li class="breadcrumb-item"><a href="<?= base_url("company/details/" . $company_details->id);?>">#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></a></li> 
                <li class="breadcrumb-item"><a href="<?= base_url("company/invoice/" . $company_details->id);?>">Hoá đơn</a></li> 

                <li class="breadcrumb-item active" aria-current="page">Xem HD #<?= esc($invoice_details->id);?></li> 
                </ol>
            </nav>
            <!-- End Breadcrumb -->
        </div>

        <div class="row mt-3">

            <?php include(APPPATH . 'Views/company/inc_left_menu.php');?>


            <div class="col-md-9 col-xl-10">

                
                <div class="row">

                <div class="row mx-auto" style="max-width:800px">
            <h1 class="h3 my-3">Hóa đơn #<?= esc($invoice_details->id);?></h1>

            <div class="col-12">
                <div class="card">
                    <div class="card-body m-sm-3 m-md-5">
                        <div class="mb-4">
                            <img src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" class="img-fluid">
                        </div>

                        <div class="my-3">
                            <?php if($invoice_details->status == "Paid") { ?>
                            <h1 class="text-center"><span class="badge bg-success rounded-pill">Đã thanh toán</span>
                            </h1>

                            <?php } else if($invoice_details->status == "Unpaid") { ?>
                            <h1 class="text-center"><span class="badge bg-danger  rounded-pill">Chưa thanh toán</span>
                            </h1>

                            <?php } else if($invoice_details->status == "Cancelled") { ?>
                            <h1 class="text-center"><span class="badge bg-secondary  rounded-pill">Đã hủy</span>
                            </h1>

                            <?php } else if($invoice_details->status == "Refunded") { ?>
                            <h1 class="text-center"><span class="badge bg-secondary  rounded-pill">Đã hoàn tiền</span>
                            </h1>

                            <?php } ?>
                        </div>

                        <div class="row">
                            <div class="col-6">
                                <div class="text-muted">Hóa đơn số</div>
                                <strong>#<?= esc($invoice_details->id);?></strong>
                            </div>
                            <div class="col-6 text-md-right">
                                <div class="text-muted">Ngày tạo</div>
                                <strong><?= esc($invoice_details->date);?></strong>
                            </div>
                        </div>

                        <hr class="my-4" />

                        <div class="row mb-4">
                            <div class="col-8 col-md-6">
                                <div class="text-muted">Bên mua</div>
                                <strong>
                                    <?= esc($company_details->short_name);?>
                                </strong>
                                <p>
                                    <?= esc($company_details->full_name);?>
                                </p>
                            </div>
                            <div class="col-4  col-md-6 text-md-right">
                                <div class="text-muted">Bên bán</div>
                                <strong>
                                    SePay
                                </strong>

                            </div>
                        </div>

                        <?php if($invoice_details->status == "Unpaid"): ?>

                        <div class="p-3 border">
                            <h5 class="text-center">Hướng dẫn thanh toán</h5>
                            <div class="row">
                                <div class="col-md-5 text-center">

                                    <img src="<?= $qrcode;?>" class="img-fluid">
                                    <div><a download="qr-invoice-<?= esc($invoice_details->id);?>.png"
                                            href="<?= $qrcode;?>"><i class="bi bi-download"></i> Tải ảnh QR</a></div>

                                </div>
                                <div class="col-md-7">
                                    <table class="table table-borderless">
                                        <tbody>
                                            <tr>
                                                <td>Ngân hàng</td>
                                                <td class="fw-bold">MBBank</td>
                                            </tr>
                                            <tr>
                                                <td>Số tài khoản</td>
                                                <td class="fw-bold"><span id="vcb_id">*************</span> <i
                                                        class="bi bi-files copyjs" data-clipboard-target="#vcb_id"
                                                        id="i_vcb_id" data-bs-toggle="tooltip"
                                                        data-bs-title="Đã copy"></i></td>
                                            </tr>
                                            <tr>
                                                <td>Thụ hưởng</td>
                                                <td class="fw-bold">SEPAY JSC</td>
                                            </tr>

                                            <tr>
                                                <td>Nội dung CK</td>
                                                <td class="fw-bold"><span id="trans_content"><?= esc($paycode);?></span> <i class="bi bi-files copyjs" data-clipboard-target="#trans_content"
                                                        id="i_trans_content" data-bs-toggle="tooltip"
                                                        data-bs-title="Đã copy"></i>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Số tiền</td>
                                                <td class="fw-bold"><?= number_format($invoice_details->total);?> đ
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="2"><i class="bi bi-qr-code-scan"></i> Dùng ứng dụng ngân
                                                    hàng quét mã
                                                    QR để chuyển khoản</td>
                                            </tr>
                                        </tbody>
                                    </table>

                                </div>
                            </div>
                            <div class="alert alert-secondary alert-dismissible" role="alert">
                                <div class="alert-message">
                                    Vui lòng <strong>giữ nguyên nội dung chuyển khoản</strong>. Hệ thống sẽ tự nhận
                                    diện
                                    thanh toán trong vài giây
                                </div>
                            </div>
                        </div>

                        <?php endif;?>

                        <table class="table table-sm mt-5">
                            <thead>
                                <tr>
                                    <th>Mô tả</th>
                                    <th class="text-end">Giá tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($invoice_items as $item): ?>
                                <tr>
                                    <td><?= esc($item->description);?></td>

                                    <td class="text-end"><?= number_format($item->amount);?> đ</td>
                                </tr>
                                <?php endforeach; ?>

                                <tr>
                                    <th>Tổng </th>
                                    <th class="text-end"><?= number_format($invoice_details->total);?> đ</th>
                                </tr>
                            </tbody>
                        </table>


                        <div class="row mt-5">
                            <div class="col-6 text-start"> </div>
                            <div class="col-6 text-end">
                             <!--   <a onclick="window.print()">
                                <i class="bi bi-printer"></i> In
                            </a> -->
                        </div>
                           
                        </div>
                    </div>
                </div>
            </div>
        </div>
                  
                </div>
 
            

            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>




<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>

<script type="text/javascript" src="<?php echo base_url();?>/assets/DataTables/datatables.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/app.js"></script>


<script src="<?php echo base_url();?>/assets/clipboardjs/clipboard.min.js"></script>

<script>
tooltip = new bootstrap.Tooltip(document.getElementById('i_vcb_id'));
tooltip.disable();

tooltip = new bootstrap.Tooltip(document.getElementById('i_trans_content'));
tooltip.disable();


var clipboard = new ClipboardJS('.copyjs');

clipboard.on('success', function(e) {


    id = e.trigger.getAttribute('id');

    tooltip = new bootstrap.Tooltip(document.getElementById(id), {
        trigger: 'click'
    });
    tooltip.show();

    setTimeout(function() {
        tooltip.hide();
        tooltip.disable();

    }, 500);
});
 
</script>