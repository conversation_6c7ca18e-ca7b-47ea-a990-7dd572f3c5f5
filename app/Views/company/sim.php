<main class="content">
    <div class="container-fluid">
        <div>
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("company");?>">Công ty</a></li> 
                <li class="breadcrumb-item"><a href="<?= base_url("company/details/" . $company_details->id);?>">#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></a></li> 

                <li class="breadcrumb-item active" aria-current="page">Sim</li> 
                </ol>
            </nav>
            <!-- End Breadcrumb -->
        </div>
        <div class="row mt-3">

            <?php include(APPPATH . 'Views/company/inc_left_menu.php');?>


            <div class="col-md-9 col-xl-10">



                <div class="mt-3">
                    <div class="card">
                        <div class="card-header">
                        <a href="javascript:;" onclick="show_assign_sim()" class="btn btn-primary btn-sm float-end"><i class="bi bi-plus"></i> Gán SIM</a>   
                          
                            
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <th>SIM ID</th>
                                        <th>Số Điện thoại</th>
                                        <th>Device</th>
                                        <th>Trạng thái</th>
                                        <th>Ghi chú</th>
                                        <th>Ngày cấp</th>
                                        <th></th>

                                    </thead>
                                    <tbody>
                                        <?php foreach($sims as $sim): ?>
                                        <tr>
                                            <td><?= esc($sim->sim_id);?></td>
                                            <td><?= esc($sim->sim_phonenumber);?></td>
                                            <td><?= esc($sim->device_type);?></td>

                                            <td> <span class='fw-bold'>
                                                    <span
                                                        class='<?php if($sim->active == 1) echo 'text-success'; else  echo 'text-danger';?>'><?php if($sim->active == 1) echo 'Kích hoạt'; else  echo 'Tạm khóa';?></span>


                                                </span></td>
                                            <td><?= esc($sim->description);?></td>
                                            <td><?= esc($sim->created_at);?></td>
                                            <td><button onclick="revoke_sim(<?= esc($sim->id);?>)" class="btn btn-sm btn-danger"><i class="bi bi-trash"></i> Thu hồi</button></td>

                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>

                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>



  
<!-- Modal Assign Sim  -->
<div class="modal fade" id="simAssignModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="simAssignModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="simAssignModalLabel">Gán SIM</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <?php echo form_open('',"id='sim_assign_form' class='needs-validation form-add-sim' novalidate");?>
        <input type="hidden"name="company_id" value="<?= $company_details->id;?>">
        <div class="modal-body m-lg-3">
 

                    <div class="mb-3">
                        <label class="form-label"><b>Chọn SIM</b></label>
                        <select class="form-select" name="sim_id" aria-label="sim_id" required>
                            <option value="">Chọn SIM</option>

                            <?php foreach($availability_sims as $sim) { ?>
                            <option value="<?= esc($sim->id);?>">#<?= esc($sim->id);?> - <?= esc($sim->sim_phonenumber . ' - ' . $sim->description);?></option>
                            <?php } ?>
                            
                        </select>
                    </div>
                  
        
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary me-3" data-bs-dismiss="modal">Đóng</button>
                <a class="btn btn-primary btn-sim-add" onclick="save()">Gán</a>
            </div>
        </form>
        </div>
    </div>
</div>
<!-- Modal Assign Sim -->

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>

<script type="text/javascript" src="<?php echo base_url();?>/assets/DataTables/datatables.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/app.js"></script>

<script>


var assignSimModal = new bootstrap.Modal(document.getElementById('simAssignModal'), {
        keyboard: false
    });

    function revoke_sim(id) {

        if (confirm("Bạn có chắc chắn muốn gỡ sim này ra khỏi công ty?")) {
        
        url = "<?php echo base_url('sim/ajax_revoke_sim');?>";
    
        $.ajax({
            url : url,
            type: "POST",
            data: {id: id, "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
            dataType: "JSON",
            success: function(data)
            {
    
            
                //if success close modal and reload ajax table
                if(data.status == true) {
                    location.reload();
                }  else {
                    alert('Lỗi: ' + data.message);
                }
            
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                
            }
            
        });
    } 
}

function reset_sim_form() {
        $('#sim_assign_form')[0].reset();

        $('#btn_loading').html('');
        $(".btn-sim-add").html('Gán');
        $(".btn-sim-add").attr("disabled", false);

        $('[name="sim_phonenumber"]').prop("disabled", false);

    }

function show_assign_sim() {
        reset_sim_form();
        save_method = 'add';
        $('.modal-title').text('Gán Sim'); // Set title to Bootstrap modal title
        $(".btn-sim-add").html('Gán');
       // load_tom_select();
        assignSimModal.show();
    }

   

    function save()
    {

        var url;
        
        url = "<?php echo base_url('sim/ajax_assign_sim');?>";

         
        $(".btn-sim-add").attr("disabled", true);
        $('#btn_loading').html('');
        $(".btn-sim-add").html('<div class="spinner-border text-light" role="status" id="btn_loading"></div>');

        $.ajax({
            url : url,
            type: "POST",
            data: $('#sim_assign_form').serialize(),
            dataType: "JSON",
            success: function(data)
            {
                //if success close modal and reload ajax table
                if(data.status == true) {
                    assignSimModal.hide();
 
                    location.reload();
 
                } else {
                    
                    $(".btn-sim-add").attr("disabled", false);
                    $('#btn_loading').remove();
                    $(".btn-sim-add").html('Thêm');
                    alert('Lỗi: ' + data.message);
 
                }
            
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                $(".btn-sim-add").attr("disabled", false);
                $('#btn_loading').remove();
                $(".btn-sim-add").html('Thêm');
            }
            
        });
    }
</script>