<style>
    .dataTables_info {
        padding-top: 0 !important;
    }
</style>

<main class="content">
    <div class="container-fluid">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("company"); ?>">Công ty</a></li>
                <li class="breadcrumb-item"><a href="<?= base_url("company/details/" . $company_details->id); ?>">#<?= esc($company_details->id . ' - ' . $company_details->short_name); ?></a></li>
                <li class="breadcrumb-item active" aria-current="page">Email đã gửi</li>
            </ol>
        </nav>

        <div class="row mt-3">
            <?php include(APPPATH . 'Views/company/inc_left_menu.php'); ?>

            <div class="col-md-9 col-xl-10">
                <div class="card">
                    <div class="card-header pb-0">
                        <h5 class="card-title">Email đã gửi</h5>
                    </div>
                    <div class="card-body">
                        <table class="table" id="dataTables">
                            <thead>
                                <tr>
                                    <th>Subject</th>
                                    <th>Loại email</th>
                                    <th>Đối tượng</th>
                                    <th>Người nhận</th>
                                    <th>Trạng thái</th>
                                    <th>Ngày gửi</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php'); ?>

<script src="<?= base_url('assets/js/bootstrap.bundle.min.js'); ?>"></script>
<script src="<?= base_url('assets/js/jquery-3.6.0.min.js'); ?>"></script>
<script src="<?= base_url('assets/DataTables/datatables.min.js'); ?>"></script>
<script src="<?= base_url('assets/notyf/notyf.min.js'); ?>"></script>
<script src="<?= base_url('assets/js/app.js'); ?>"></script>

<script>
    $(() => {
        const dataTable = $('#dataTables').DataTable({
            processing: true,
            serverSide: true,
            pageLength: 25,
            responsive: true,
            ajax: {
                url: '<?= base_url('company/ajax_email_sent/' . $company_details->id) ?>',
                type: 'POST',
                data: function(d) {
                    d['<?= csrf_token() ?>'] = '<?= csrf_hash() ?>';
                },
            },
            language: {
                sProcessing: 'Đang xử lý...',
                sLengthMenu: 'Xem _MENU_ mục',
                sZeroRecords: 'Không tìm thấy dòng nào phù hợp',
                sInfo: 'Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục',
                sInfoEmpty: 'Đang xem 0 đến 0 trong tổng số 0 mục',
                sInfoFiltered: '(được lọc từ _MAX_ mục)',
                sInfoPostFix: '',
                sSearch: 'Tìm:',
                oPaginate: {
                    sFirst: 'Đầu',
                    sLast: 'Cuối',
                    sNext: 'Tiếp',
                    sPrevious: 'Trước',
                },
            },
            columnDefs: [],
            dom: `<'d-flex flex-wrap gap-2 justify-content-between'fB>tr<'d-flex flex-wrap gap-2 justify-content-center justify-content-md-between mt-3'<'d-flex flex-wrap justify-content-center align-items-center gap-3'li>p>`,
        });
    });
</script>
