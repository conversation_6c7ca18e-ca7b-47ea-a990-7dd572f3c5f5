<main class="content">
    <div class="container-fluid">

         <div>
             <!-- Breadcrumb -->
             <nav aria-label="breadcrumb">
              <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("company");?>">Công ty</a></li> 
                <li class="breadcrumb-item active" aria-current="page">#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></li> 
              </ol>
            </nav>
            <!-- End Breadcrumb -->
         </div>

        <div class="row mt-3">

            <?php include(APPPATH . 'Views/company/inc_left_menu.php');?>


            <div class="col-md-9 col-xl-10">

                <h4>#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></h4>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex flex-wrap gap-1">
                                <?php if(has_permission("LoginAsClient",  'can_view_all')): ?>
                                <a target="_blank"
                                    href="<?= base_url("company/login_as_client/". $user_details->id);?>" 
                                    class="btn btn-sm btn-outline-primary">Login as Client</a>
                                <?php endif; ?>

                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Set Service Status</button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" onclick="set_company_status('Pending')">Pending</a>
                                        <a class="dropdown-item" onclick="set_company_status('Active')">Active</a>
                                        <a class="dropdown-item" onclick="set_company_status('Suspended')">Suspended</a>
                                        <a class="dropdown-item" onclick="set_company_status('Terminated')">Terminated</a>
                                        <a class="dropdown-item" onclick="set_company_status('Cancelled')">Cancelled</a>
                                     </div>
                                </div>

                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Set Login Status</button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" onclick="set_login_status(1)">Active</a>
                                        <a class="dropdown-item" onclick="set_login_status(0)">Closed</a>
                                      
                                     </div>
                                </div>

                                <?php if ($admin_details->role == 'SuperAdmin' && !in_array($company_details->id, [1, 3, 8])): ?>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="bi bi-gear"></i> Bulk Status Update
                                        </button>
                                        <ul class="dropdown-menu">
                                            <?php if($company_details->status != 'Active' || $company_details->subscription_status != 'Active'): ?>
                                            <li>
                                                <a class="dropdown-item text-success" href="javascript:void(0)" onclick="directStatusUpdate('Active')">
                                                    <i class="bi bi-check-circle me-1"></i>
                                                    <strong>Activate</strong> <small class="text-muted">(Company & Subscription)</small>
                                                </a>
                                            </li>
                                            <?php endif; ?>
                                            <?php if($company_details->status != 'Suspended' || $company_details->subscription_status != 'Suspended'): ?>
                                            <li>
                                                <a class="dropdown-item text-warning" href="javascript:void(0)" onclick="directStatusUpdate('Suspended')">
                                                    <i class="bi bi-pause-circle me-1"></i>
                                                    <strong>Suspend</strong> <small class="text-muted">(Company & Subscription)</small>
                                                </a>
                                            </li>
                                            <?php endif; ?>
                                            <?php if($company_details->status != 'Suspended' || $company_details->subscription_status != 'Cancelled'): ?>
                                            <li>
                                                <a class="dropdown-item text-danger" href="javascript:void(0)" onclick="directStatusUpdate('Cancelled')">
                                                    <i class="bi bi-x-circle me-1"></i>
                                                    <strong>Cancel</strong> <small class="text-muted">(Company & Subscription & Invoices)</small>
                                                </a>
                                            </li>
                                            <?php endif; ?>
                                        </ul>
                                    </div>
                                <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-7">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Hồ sơ công ty</h5>
                            </div>
                            <div class="card-body">

                                <div class="row">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Tên công ty</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php echo esc($company_details->full_name);?></span>

                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Thương hiệu</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php echo esc($company_details->short_name);?></span>

                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Cho phép đăng nhập</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php if($company_details->active ==1){ ?>
                                            <span class='text-success'>Kích hoạt</span>
                                            <?php } else { ?>
                                            <span class='text-danger'>Tạm khóa</span>
                                            <?php } ?>

                                        </span>

                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Trạng thái dịch vụ</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'> 
                                            <span class="text-<?php if($company_details->status == "Active") echo 'success'; else echo 'warning';?>"><?= esc($company_details->status);?></span>
                                            

                                        </span>

                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Ngày đăng ký</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php echo esc($company_details->created_at);?></span>

                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Người giới thiệu</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php if(is_object($partner_details)) echo esc('#' .$partner_details->id . ' ' . $partner_details->lastname . ' ' . $partner_details->firstname); else echo 'N/A';?> <a href="#"  data-bs-toggle="modal" data-bs-target="#partnerModal" class="ms-3"><i class="bi bi-pencil"></i> Sửa</a></span>

                                    </div>
                                </div>

                                <?php if (!empty($company_details->tracking_source) || !empty($company_details->tracking_medium) || !empty($company_details->tracking_campaign) || !empty($company_details->tracking_term)): ?>
                                    <div class="row mt-3">
                                        <div class="col-md-4 col-6">
                                            <span class="text-muted">Thông tin tracking</span>
                                        </div>
                                        <div class="col-md-8 col-6">
                                            <div class="tracking-info">
                                                <?php
                                                $tracking_data = [
                                                    'tracking_source' => 'Nguồn',
                                                    'tracking_medium' => 'Kênh',
                                                    'tracking_campaign' => 'Chiến dịch',
                                                    'tracking_term' => 'Từ khóa',
                                                    'tracking_content' => 'Nội dung',
                                                    'tracking_referer' => 'Referrer'
                                                ];
                                                
                                                foreach ($tracking_data as $field => $label) {
                                                    if (!empty($company_details->$field)) {
                                                        echo '<div class="badge bg-light text-dark me-2 mb-1 p-2">';
                                                        echo '<strong>' . $label . ':</strong> ';
                                                        
                                                        if ($field === 'tracking_referer') {
                                                            $referer_text = esc($company_details->$field);
                                                            $display_text = strlen($referer_text) > 30 ? substr($referer_text, 0, 30) . '...' : $referer_text;
                                                            echo '<a href="' . esc($company_details->$field) . '" target="_blank" title="' . $referer_text . '">' . $display_text . '</a>';
                                                        } else {
                                                            echo esc($company_details->$field);
                                                        }
                                                        
                                                        echo '</div>';
                                                    }
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>  
                    <div class="col-md-5">

                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Người sở hữu (Super Admin)</h5>
                            </div>
                            <div class="card-body">

                                <div class="row">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Họ và tên</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span
                                            class='fw-bold'><?php echo esc($user_details->lastname . " " .  $user_details->firstname);?></span>

                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Email</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php echo esc($user_details->email);?></span>

                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Số điện thoại</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php echo esc($user_details->phonenumber);?></span>

                                    </div>
                                </div>


                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Ngày tạo</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php echo esc($user_details->created_at);?></span>

                                    </div>
                                </div>

                                <?php if(in_array($admin_details->role,['Admin','SuperAdmin'])) { ?>

                                <div class="mt-3">
                                <span class='fw-bold'><a href="#"  data-bs-toggle="modal" data-bs-target="#editUserModal" class="ms-3"><i class="bi bi-pencil"></i> Sửa</a></span>
                                </div>

                                <?php } ?>

                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-2">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6 col-md-3 text-center">
                                    <div><b class="text-<?php if($paid_invoice_count>0) echo 'primary'; else echo 'muted'; ?> fs-3"><?= number_format($paid_invoice_count);?></b> Invoices Paid</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($paid_invoice_count>0) echo 'success'; else echo 'muted'; ?>"> <?= number_format($paid_invoice_sum->total_amount);?> đ</span>
                                </div>
                           
                                <div class="col-6 col-md-3 text-center">
                                    <div><b class="text-<?php if($stransaction_count>0) echo 'primary'; else echo 'muted'; ?> fs-3"><?= number_format($stransaction_count);?></b> Transactions</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($stransaction_count>0) echo 'success'; else echo 'muted'; ?>"> <?= number_format($stransaction_sum->total_in);?> đ</span>
                                </div>
                                     
                                <div class="col-6 col-md-3 text-center">
                                    <div><b class="text-danger fs-3"><?= number_format($unpaid_invoice_count);?></b> Invoices Unpaid</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-danger"> <?= number_format($unpaid_invoice_sum->total_amount);?> đ</span>
                                </div>
                                <div class="col-6 col-md-3 text-center">
                                    <div>Users</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($bank_account_count>0) echo 'primary'; else echo 'muted'; ?>"> <?= number_format($user_count);?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-2">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6 col-md-3 text-center">
                                    <div>Bank Accounts</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($bank_account_count>0) echo 'primary'; else echo 'muted'; ?>"> <?= number_format($bank_account_count);?></span>
                                </div>
                           
                                <div class="col-6 col-md-3 text-center">
                                    <div>Bank Transactions</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($transaction_count>0) echo 'primary'; else echo 'muted'; ?>"> <?= number_format($transaction_count);?></span>
                                </div>

                                <div class="col-6 col-md-3 text-center">
                                    <div>Last Transaction</div>
                                     <span class="h5 d-inline-block mt-1 mb-3 text-primary"> <?php if(is_object($last_transaction)) echo timespan($last_transaction->last_transaction,2);?></span> 
 
                                </div>
                                     
                                <div class="col-6 col-md-3 text-center">
                                    <div>SIM</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($sim_count>0) echo 'primary'; else echo 'muted'; ?>"> <?= number_format($sim_count);?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-2">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6 col-md-3 text-center">
                                    <div>Tích hợp Webhooks</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($webhook_count>0) echo 'primary'; else echo 'muted'; ?>"> <?= number_format($webhook_count);?></span>
                                </div>
                           
                                <div class="col-6 col-md-3 text-center">
                                    <div>Tích hợp Telegram</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($telegram_count>0) echo 'primary'; else echo 'muted'; ?>"> <?= number_format($telegram_count);?></span>
                                </div>

                                <div class="col-6 col-md-3 text-center">
                                    <div>Tích hợp Lark</div>
                                     <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($lark_count>0) echo 'primary'; else echo 'muted'; ?>">  <?= number_format($lark_count);?></span> 
 
                                </div>
                                     
                                <div class="col-6 col-md-3 text-center">
                                    <div>Tin chat đã gửi</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($chat_count>0) echo 'primary'; else echo 'muted'; ?>"> <?= number_format($chat_count);?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Dịch vụ (Subscriptions)</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <th>ID</th>
                                        <th>Tên gói</th>
                                        <th>Trạng thái</th>
                                        <th>Bắt đầu</th>
                                        <th>Kết thúc</th>
                                        <th>First Payment</th>
                                        <th>Recurring Payment</th>
                                        <th>Billing Cycle</th>
                                    </thead>
                                    <tbody>
                                        <?php foreach($subscriptions as $subscription): ?>
                                        <tr>
                                            <td><a href="<?= base_url("subscription/edit/" . $subscription->id);?>"><?= esc($subscription->id);?></a></td>
                                            <td><?= esc($subscription->name);?></td>
                                            <td> <span class='fw-bold'>
                                                    <span
                                                        class='<?php if($subscription->status =='Active') echo 'text-success'; else if($subscription->status =='Suspended') echo 'text-warning';else if($subscription->status =='Pending') echo 'text-danger'; else echo 'text-secondary'; ?>'><?= esc($subscription->status);?></span>


                                                </span></td>
                                            <td><?= esc($subscription->begin_date);?></td>
                                            <td><?= esc($subscription->end_date);?></td>
                                            <td><?= number_format($subscription->first_payment);?> đ</td>
                                            <td><?= number_format($subscription->recurring_payment);?> đ</td>
                                            <td><?= esc(ucfirst($subscription->billing_cycle));?></td>

                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>

                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>



<!-- Partner Modal -->
<div class="modal fade" id="partnerModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="partnerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="partnerModalLabel">Sửa đối tác giới thiệu</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <?php echo form_open('',"id='partner_form' class='needs-validation form-add-payment' novalidate");?>
        <input type="hidden" name="company_id" value="<?=esc($company_details->id);?>">
        <div class="modal-body m-lg-3">

                    
            <div class="row">

               

                <div class="col-md-12">
                    <div class="mb-3">
                        <label class="form-label" for="inputEmail4">Chọn đối tác</label>
                        <select name="partner_id" class="form-control">
                        <option value="">Chọn đối tác</option>

                            <?php foreach($partners as $partner) { ?>
                            <option value="<?= esc($partner->id);?>" <?php if(is_object($partner_details) && $partner_details->id == $partner->id) echo 'selected';?>><?= esc('#' . $partner->id . ' '. $partner->lastname . ' ' . $partner->firstname);?></option>
                            <?php } ?>
                             
                        </select>
                        <span class="validity"></span>
                    </div>
                </div>

                
          
               
            </div>
        
        
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light me-3" data-bs-dismiss="modal">Đóng</button>
                <a class="btn btn-primary btn-partner-update" onclick="partner_save()">Lưu thay đổi</a>
            </div>
        </form>
        </div>
    </div>
</div>
<!-- Payment Modal -->

<?php if(in_array($admin_details->role,['Admin','SuperAdmin'])) { ?>


<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="editUserModalLabel">Sửa thông tin Người sở hữu (Super Admin)</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <?php echo form_open('',"id='user_form' class='needs-validation form-edit-user' novalidate");?>
        <input type="hidden" name="user_id" value="<?=esc($user_details->id);?>">

        <div class="modal-body m-lg-3">

                    
            <div class="row">

                <div class="col-md-12">
                    <div class="mb-3 form-group">
                        <label class="form-label">Email (cũng là tên đăng nhập)</label>
                        <input class="form-control" type="email" name="email" value="<?php echo esc($user_details->email);?>" placeholder="">
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="mb-3 form-group">
                        <label class="form-label">Số điện thoại</label>
                        <input class="form-control" type="number" name="phonenumber" value="<?php echo esc($user_details->phonenumber);?>" placeholder="">
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                
          
               
            </div>
        
        
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light me-3" data-bs-dismiss="modal">Đóng</button>
                <a class="btn btn-primary btn-user-update" onclick="user_save()">Lưu thay đổi</a>
            </div>
        </form>
        </div>
    </div>
</div>
<!-- Edit User Modal -->

<?php } ?>

<?php if (in_array($admin_details->role, ['Admin', 'SuperAdmin'])): ?>
    <div class="modal fade" id="setCompanySubscriptionStatus" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="setCompanySubscriptionStatusLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="setCompanySubscriptionStatusLabel">Cập nhật trạng thái công ty & dịch vụ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <?= form_open('company/update_status_bulk/' . $company_details->id, ['id' => 'company_status_form', 'class' => 'needs-validation', 'novalidate' => 'novalidate']) ?>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <div class="alert-message">
                            <strong>THÔNG BÁO:</strong> Hành động này sẽ:
                            <ul class="mb-0 ps-3">
                                <li>Cập nhật trạng thái của công ty thành <strong class="text-primary">[Trạng thái đã chọn]</strong></li>
                                <li>Cập nhật trạng thái của dịch vụ thành <strong class="text-primary">[Trạng thái đã chọn]</strong></li>
                                <li>Gửi email thông báo đến chủ sở hữu (<strong><?= esc($user_details->email); ?></strong>)</li>
                                <li>Lưu nhật ký hành động này trong lịch sử hệ thống</li>
                            </ul>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label" for="status">Trạng thái <span class="text-danger">*</span></label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="Active" <?= $company_details->status === 'Active' ? 'selected' : null ?>>Active</option>
                            <option value="Suspended" <?= $company_details->status === 'Suspended' ? 'selected' : null ?>>Suspended</option>
                            <option value="Cancelled" <?= $company_details->status === 'Cancelled' ? 'selected' : null ?>>Cancelled</option>
                        </select>
                        <div class="text-muted mt-1 mb-2" id="update-status-info" style="<?= $company_details->status === 'Cancelled' ? 'display: block;' : 'display: none;' ?>">
                            <small>
                                <i class="bi bi-info-circle me-1"></i>
                                Khi chọn trạng thái <strong class="text-danger">Cancelled</strong>, hệ thống sẽ:
                                <ul class="mb-0 mt-1">
                                    <li>Hủy dịch vụ đang hoạt động</li>
                                    <li>Hủy các hóa đơn chưa thanh toán</li>
                                    <li>Chuyển công ty sang trạng thái <strong class="text-warning">Suspended</strong></li>
                                </ul>
                            </small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label" for="reason">Lý do <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reason" name="reason" rows="2" required></textarea>
                        <div class="invalid-feedback">Vui lòng nhập lý do</div>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="send_notification" name="send_notification" value="1" checked>
                        <label class="form-check-label" for="send_notification">
                            Gửi thông báo qua email đến chủ sở hữu
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Hủy</button>
                    <button type="button" class="btn btn-success btn-unsuspend-action" onclick="changeCompanySubscriptionStatus()">
                        Xác nhận thay đổi
                    </button>
                </div>
                <?= form_close() ?>
            </div>
        </div>
    </div>
<?php endif; ?>

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>

<script type="text/javascript" src="<?php echo base_url();?>/assets/DataTables/datatables.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/app.js"></script>

<script>


function set_company_status(new_status) {
        
        url = "<?php echo base_url('company/ajax_status_update');?>";
    
        $.ajax({
            url : url,
            type: "POST",
            data: {id: <?= $company_details->id;?>, status: new_status, "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
            dataType: "JSON",
            success: function(data)
            {
    
            
                //if success close modal and reload ajax table
                if(data.status == true) {
                    location.reload();
                }  else {
                    alert('Lỗi: ' + data.message);
                }
            
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                
            }
            
        });
    }


    function set_login_status(new_status) {
        
        url = "<?php echo base_url('company/ajax_login_update');?>";
    
        $.ajax({
            url : url,
            type: "POST",
            data: {id: <?= $company_details->id;?>, active: new_status, "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
            dataType: "JSON",
            success: function(data)
            {
    
            
                //if success close modal and reload ajax table
                if(data.status == true) {
                    location.reload();
                }  else {
                    alert('Lỗi: ' + data.message);
                }
            
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                
            }
            
        });
    }
 


   function partner_save()
   {
       
        url = "<?php echo base_url('company/ajax_update_partner');?>";

       

       $(".btn-partner-update").attr("disabled", true);
       $('#btn_partner-update_loading').html('');
       $(".btn-partner-update").html('<div class="spinner-border text-light" role="status" id="btn_partner-update_loading"></div>');

       $.ajax({
           url : url,
           type: "POST",
           data: $('#partner_form').serialize(),
           dataType: "JSON",
           success: function(data)
           {
               //if success close modal and reload ajax table
               if(data.status == true) {
 
                    location.reload();

                 
               } else {
                   
                   $(".btn-partner-update-add").attr("disabled", false);
                   $('#btn_partner-update_loading').remove();
                   alert('Lỗi: ' + data.message);

               }
           
           },
           error: function (jqXHR, textStatus, errorThrown)
           {
               alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
               $(".btn-partner-update-add").attr("disabled", false);
                $('#btn_partner-update_loading').remove();
           }
           
       });
   }

   <?php if(in_array($admin_details->role,['Admin','SuperAdmin'])) { ?>

   function user_save()
   {
       
        url = "<?php echo base_url('company/ajax_user_update');?>";

       

       $(".btn-user-update").attr("disabled", true);
       $('#btn_user-update_loading').html('');
       $(".btn-user-update").html('<div class="spinner-border text-light" role="status" id="btn_user-update_loading"></div>');

       $.ajax({
           url : url,
           type: "POST",
           data: $('#user_form').serialize(),
           dataType: "JSON",
           success: function(data)
           {
               //if success close modal and reload ajax table
               if(data.status == true) {
 
                    location.reload();

                 
               } else {
                   
                   $(".btn-user-update-add").attr("disabled", false);
                   $('#btn_user-update_loading').remove();
                   alert('Lỗi: ' + data.message);

               }
           
           },
           error: function (jqXHR, textStatus, errorThrown)
           {
               alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
               $(".btn-user-update-add").attr("disabled", false);
                $('#btn_user-update_loading').remove();
           }
           
       });
   }

   <?php  } ?>

    <?php if ($admin_details->role == 'SuperAdmin' && !in_array($company_details->id, [1, 3, 8])): ?>
        function directStatusUpdate(status) {
            let reason = "";
            let confirmMessage = "";

            switch(status) {
                case 'Active':
                    reason = "Khôi phục hoạt động dịch vụ";
                    confirmMessage = "Xác nhận kích hoạt dịch vụ của công ty?";
                    break;
                case 'Suspended':
                    reason = "Tạm ngưng dịch vụ do quá hạn thanh toán";
                    confirmMessage = "Xác nhận tạm ngưng dịch vụ của công ty?";
                    break;
                case 'Cancelled':
                    reason = "Hủy dịch vụ theo yêu cầu";
                    confirmMessage = "Xác nhận hủy dịch vụ của công ty?";
                    break;
            }
            
            if (!confirm(confirmMessage)) {
                return;
            }

            const buttonText = {
                'Active': 'Active',
                'Suspended': 'Suspend',
                'Cancelled': 'Cancel'
            };
            
            $(`button:contains("${buttonText[status]}")`).prop('disabled', true).html(
                `<div class="spinner-border spinner-border-sm align-middle" role="status"></div> Processing...`
            );

            $.ajax({
                url: "<?= base_url('company/update_status_bulk/' . $company_details->id) ?>",
                type: "POST",
                data: {
                    status: status,
                    "<?= csrf_token() ?>": "<?= csrf_hash() ?>"
                },
                dataType: "JSON",
                success: function(data) {
                    if (data.status) {
                        window.location.reload();
                    } else {
                        alert('Lỗi: ' + (data.message || 'Có lỗi xảy ra! Vui lòng thử lại'));
                        location.reload();
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                    location.reload();
                }
            });
        }
    <?php endif; ?>
</script>
