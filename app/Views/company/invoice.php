<main class="content">
    <div class="container-fluid">

        <div>
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("company");?>">Công ty</a></li> 
                <li class="breadcrumb-item"><a href="<?= base_url("company/details/" . $company_details->id);?>">#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></a></li> 

                <li class="breadcrumb-item active" aria-current="page">Hoá đơn</li> 
                </ol>
            </nav>
            <!-- End Breadcrumb -->
        </div>
        
        <div class="row mt-3">

            <?php include(APPPATH . 'Views/company/inc_left_menu.php');?>


            <div class="col-md-9 col-xl-10">

                <h4>#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></h4>
                 
                <div class="mt-2">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6 col-md-3 text-center">
                                    <div><b class="text-success fs-3"><?= number_format($paid_invoice_count);?></b> Paid Invoices </div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-success"> <?= number_format($paid_invoice_sum->total_amount);?> đ</span>
                                </div>
                           
                              
                                     
                                <div class="col-6 col-md-3 text-center">
                                    <div><b class="text-danger fs-3"><?= number_format($unpaid_invoice_count);?></b> Unpaid Invoices </div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-danger"> <?= number_format($unpaid_invoice_sum->total_amount);?> đ</span>
                                </div>
                               
                                <div class="col-6 col-md-3 text-center">
                                    <div><b class="text-secondary fs-3"><?= number_format($cancelled_invoice_count);?></b> Cancelled Invoices </div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-secondary"> <?= number_format($cancelled_invoice_sum->total_amount);?> đ</span>
                                </div>
                               

                            </div>
                        </div>
                    </div>
                </div>

              

                <div class="mt-3">
                    <div class="card">
                        <div class="card-header">

                        <div class="text-end row">
                                <div class="mb-2">
                                    <a href="javascript:;" onclick="create_invoice_draft()" class="btn btn-primary btn-sm float-end"><i class="bi bi-plus"></i> Tạo Hoá đơn</a>

                                </div>

                        </div>
                        </div>
                        <div class="card-body">
                            <div class="">
                                <table class="table table-hover table-striped table-bordered text-muted align-middle display nowrap" id="invoices_table" style="width:100%">

                                    <thead class="table-light text-muted">
                                        <tr class="align-middle">
                                            <th></th>
                                            <th>Số hóa đơn</th>
                                            <th>Loại</th>
                                            <th>Trạng thái</th>
                                            <th>Tổng tiền</th>
                                            <th>HĐ VAT</th>
                                            <th>Ngày tạo</th>
                                            <th></th>

                                         </tr>
                                    </thead>
                                    <tbody>
                
                                       
                                    </tbody>
                                </table>
                            </div>
                             
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>




<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>

 
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>


<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/app.js"></script>


<script>
 

 $(document).ready(function() {

      var  table = $('#invoices_table').DataTable({ 
         rowReorder: {
             selector: 'td:nth-child(0)'
         },
         responsive: true,

      
          "processing": true,
          "serverSide": true,
          "order": [],
          "pageLength": 10,

          "ajax": {
              "url": "<?php echo base_url('company/invoice_ajax_list/' . $company_details->id); ?>",
              "data": {"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
              "type": "POST"
          },


          "language": {
              "sProcessing":   "Đang xử lý...",
              "sLengthMenu":   "Xem _MENU_ mục",
              "sZeroRecords":  "Không tìm thấy dòng nào phù hợp",
              "sInfo":         "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
              "sInfoEmpty":    "Đang xem 0 đến 0 trong tổng số 0 mục",
              "sInfoFiltered": "(được lọc từ _MAX_ mục)",
              "sInfoPostFix":  "",
              "sSearch":       "Tìm:",
              "sUrl":          "",
              "oPaginate": {
                  "sFirst":    "Đầu",
                  "sPrevious": "Trước",
                  "sNext":     "Tiếp",
                  "sLast":     "Cuối"
              }
          },

          "columnDefs": [
             { responsivePriority: 1, targets: 1 },
             { responsivePriority: 2, targets: 4 },
             { responsivePriority: 3, targets: 6 },
             { responsivePriority: 4, targets: 3 },

              { 
                  "visible": false,
                  "targets": [ 0 ],
                  "orderable": false,
              },

              
          ],

          "drawCallback": function( settings ) {
             var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
             var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                 return new bootstrap.Tooltip(tooltipTriggerEl, {trigger : 'hover'})
             });
         },

          
      });

      table.on( 'responsive-display', function ( e, datatable, row, showHide, update ) {
         var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
         var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
             return new bootstrap.Tooltip(tooltipTriggerEl, {trigger : 'hover'})
         });

     } );

    

     table.on ('init', function () {
         $('*[type="search"][class="form-control form-control-sm"]').attr('style','max-width:120px');
         $('div.dataTables_filter').parent().attr('class','col-6');
         $('div.dataTables_length').parent().attr('class','col-6');
     })

    
 });


 function create_invoice_draft()
{

    var url;
    
    url = "<?php echo base_url('invoice/ajax_create_draft');?>";
 
    $.ajax({
        url : url,
        type: "POST",
        data: {id: <?= $company_details->id;?>, invoice_owner: 'company', "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
        dataType: "JSON",
        success: function(data)
        {
            //if success close modal and reload ajax table
            if(data.status == true) {

                location.href = "<?php echo base_url('invoice/edit');?>/" + data.id;

            } else {
                
                alert('Lỗi: ' + data.message);

            }
        
        },
        error: function (jqXHR, textStatus, errorThrown)
        {
            alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
            
        }
        
    });
}

</script>