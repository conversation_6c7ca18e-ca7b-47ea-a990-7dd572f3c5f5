<main class="content">
    <div class="container-fluid">
        <div>
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("company");?>">Công ty</a></li> 
                <li class="breadcrumb-item"><a href="<?= base_url("company/details/" . $company_details->id);?>">#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></a></li> 

                <li class="breadcrumb-item active" aria-current="page">Subscription</li> 
                </ol>
            </nav>
            <!-- End Breadcrumb -->
        </div>
        <div class="row mt-3">

            <?php include(APPPATH . 'Views/company/inc_left_menu.php');?>


            <div class="col-md-9 col-xl-10">

                <h4>#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></h4>
                
                <div class="mt-3">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Dịch vụ (Subscriptions)</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <th>ID</th>
                                        <th>Tên gói</th>
                                        <th>Trạng thái</th>
                                        <th>Bắt đầu</th>
                                        <th>Kết thúc</th>
                                        <th>First Payment</th>
                                        <th>Recurring Payment</th>
                                        <th>Billing Cycle</th>
                                    </thead>
                                    <tbody>
                                        <?php foreach($subscriptions as $subscription): ?>
                                        <tr>
                                            <td><a href="<?= base_url("subscription/edit/" . $subscription->id);?>"><?= esc($subscription->id);?></a></td>
                                            <td><?= esc($subscription->name);?></td>
                                            <td> <span class='fw-bold'>
                                                    <span
                                                        class='<?php if($subscription->status =='Active') echo 'text-success'; else if($subscription->status =='Suspended') echo 'text-warning';else if($subscription->status =='Pending') echo 'text-danger'; else echo 'text-secondary'; ?>'><?= esc($subscription->status);?></span>


                                                </span></td>
                                            <td><?= esc($subscription->begin_date);?></td>
                                            <td><?= esc($subscription->end_date);?></td>
                                            <td><?= number_format($subscription->first_payment);?> đ</td>
                                            <td><?= number_format($subscription->recurring_payment);?> đ</td>
                                            <td><?= esc(ucfirst($subscription->billing_cycle));?></td>

                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>

                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>




<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>

<script type="text/javascript" src="<?php echo base_url();?>/assets/DataTables/datatables.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/app.js"></script>