<main class="content">
    <div class="container-fluid">

        <div>
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("company");?>">Công ty</a></li> 
                <li class="breadcrumb-item"><a href="<?= base_url("company/details/" . $company_details->id);?>">#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></a></li> 

                <li class="breadcrumb-item active" aria-current="page">CSKH</li> 
                </ol>
            </nav>
            <!-- End Breadcrumb -->
        </div>
        
        <div class="row mt-3">

            <?php include(APPPATH . 'Views/company/inc_left_menu.php');?>


            <div class="col-md-9 col-xl-10">
                 
                <div class="row">
                    <div class="col-auto d-none d-sm-block">
                        <h4>#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></h4>
                    </div>

                    <div class="col-auto ms-auto text-end row">
                        <div class="mb-2">
                            <a href="javascript:;" onclick="add_activity();" class="btn btn-primary btn-sm float-end"><i class="bi bi-plus"></i> Thêm hoạt động</a>   

                        </div>

                    
                    </div>
                </div>
              

                <div class="mt-3">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"></h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped table-bordered text-muted align-middle display " id="activities_table">

                                    <thead class="table-light text-muted">
                                        <tr class="align-middle">
                                            <th></th>
                                            <th>Ngày</th>
                                            <th>Nội dung</th>
                                            <th>Hình thức</th>
                                            <th>Bởi</th>
                                            <th></th>


                                         </tr>
                                    </thead>
                                    <tbody>
                
                                       
                                    </tbody>
                                </table>
                            </div>
                             
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>





<!-- Activity Modal -->
<div class="modal fade" id="activityAddModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="activityAddModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="activityAddModalLabel">Thêm Hoạt động</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <?php echo form_open('',"id='activity_add_form' class='needs-validation form-add-payment' novalidate");?>
        <input type="hidden" name="company_id" value="<?= $company_details->id;?>">
        <input type="hidden" name="id" value="">

        <div class="modal-body m-lg-3">

                    
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label" for="inputEmail4">Thời gian diễn ra</label>
                        <input class="form-control" type="datetime-local"  name="activity_at" value="<?= date("Y-m-d H:i:s");?>" required/>
                        <span class="validity"></span>
                    </div>
 
                </div>
                <div class="col-md-6">
                <div class="mb-3">
                        <label class="form-label" for="inputEmail4">Loại hoạt động</label>
                        <select name="type" class="form-control">
                            <option value="Call">Điện thoại</option>
                            <option value="Chat">Chat</option>
                            <option value="Meet">Gặp mặt</option>
                            <option value="Note">Ghi chú</option>
                        </select>
                        <span class="validity"></span>
                    </div>
                </div>
                
                <div class="col-12">
                    <div class="mb-3">
                        <label class="form-label" for="inputEmail4">Mô tả hoạt động</label>
                        <textarea name="content" class="form-control" placeholder="" rows="3"></textarea>
                        <span class="validity"></span>
                    </div>
                </div>
            </div>
        
        
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light me-3" data-bs-dismiss="modal">Đóng</button>
                <a class="btn btn-primary btn-activity-add" onclick="save()">Thêm</a>
            </div>
        </form>
        </div>
    </div>
</div>
<!-- Activity Modal -->


<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>

 
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>


<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/app.js"></script>


<script>
 

 $(document).ready(function() {

      var  table = $('#activities_table').DataTable({ 
         rowReorder: {
             selector: 'td:nth-child(0)'
         },
         responsive: false,

      
          "processing": true,
          "serverSide": true,
          "order": [],
          "pageLength": 10,

          "ajax": {
              "url": "<?php echo base_url('company/activity_ajax_list/' . $company_details->id); ?>",
              "data": {"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
              "type": "POST"
          },


          "language": {
              "sProcessing":   "Đang xử lý...",
              "sLengthMenu":   "Xem _MENU_ mục",
              "sZeroRecords":  "Không tìm thấy dòng nào phù hợp",
              "sInfo":         "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
              "sInfoEmpty":    "Đang xem 0 đến 0 trong tổng số 0 mục",
              "sInfoFiltered": "(được lọc từ _MAX_ mục)",
              "sInfoPostFix":  "",
              "sSearch":       "Tìm:",
              "sUrl":          "",
              "oPaginate": {
                  "sFirst":    "Đầu",
                  "sPrevious": "Trước",
                  "sNext":     "Tiếp",
                  "sLast":     "Cuối"
              }
          },

          "columnDefs": [
             { responsivePriority: 1, targets: 1 },
             { responsivePriority: 2, targets: 2 },
              
              { 
                  "visible": false,
                  "targets": [ 0 ],
                  "orderable": false,
              },

              
          ],

          "drawCallback": function( settings ) {
             var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
             var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                 return new bootstrap.Tooltip(tooltipTriggerEl, {trigger : 'hover'})
             });
         },

          
      });

      table.on( 'responsive-display', function ( e, datatable, row, showHide, update ) {
         var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
         var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
             return new bootstrap.Tooltip(tooltipTriggerEl, {trigger : 'hover'})
         });

     } );

    

     table.on ('init', function () {
         $('*[type="search"][class="form-control form-control-sm"]').attr('style','max-width:120px');
         $('div.dataTables_filter').parent().attr('class','col-6');
         $('div.dataTables_length').parent().attr('class','col-6');
     })

    
 });

 var addActivityModal = new bootstrap.Modal(document.getElementById('activityAddModal'), {
        keyboard: false
    });



    function add_activity() {
        $('#activity_add_form')[0].reset();
        save_method = 'add';
        $('#activityAddModalLabel').text('Thêm hoạt động');
        $(".btn-activity-add").html('Thêm');
        addActivityModal.show();
    }


    function edit_activity(id) {
       

       save_method = 'update';
       $('#activity_add_form')[0].reset();

       //Ajax Load data from ajax
       $.ajax({
         url : "<?php echo base_url('crmactivity/ajax_get');?>/" + id,
         type: "GET",
         dataType: "JSON",
         success: function(data)
         {
           if(data.status == true) {
               console.log(data);
               $('#activity_add_form').find('input[name=id]').val(data.data.id);
               $('#activity_add_form').find('input[name=activity_at]').val(data.data.activity_at);
               $('#activity_add_form').find('textarea[name=content]').val(data.data.content);
              

               $('#activityAddModalLabel').text('Edit invoice item');
               $(".btn-activity-add").html('Lưu thay đổi');
               addActivityModal.show();

           } else {
               alert('Lỗi: ' + data.message);
           }
 
           
         },
         error: function (jqXHR, textStatus, errorThrown)
         {
             alert('Lỗi không thể lấy được thông tin invoice item này');
         }
     });
   }


   function save()
   {
       var url;
       if(save_method == 'add')
       {
           url = "<?php echo base_url('crmactivity/ajax_add');?>";

       } else  if(save_method == 'update')
       {
           url = "<?php echo base_url('crmactivity/ajax_update');?>";
       } 
       else
       {
           url = "<?php echo base_url('crmactivity');?>";
       } 

       $(".btn-activity-add").attr("disabled", true);
       $('#btn_activity_loading').html('');
       $(".btn-invoice-item-add").html('<div class="spinner-border text-light" role="status" id="btn_activity_loading"></div>');

       $.ajax({
           url : url,
           type: "POST",
           data: $('#activity_add_form').serialize(),
           dataType: "JSON",
           success: function(data)
           {
               //if success close modal and reload ajax table
               if(data.status == true) {
                    addActivityModal.hide();


                   if(save_method == 'add')
                   {
                       notyf.success({message:'Thêm thành công', dismissible: true});
                       $('#activities_table').DataTable().ajax.reload();

                   } else  if(save_method == 'update')
                   {
                       notyf.success({message:'Sửa thành công', dismissible: true});
                       $('#activities_table').DataTable().ajax.reload();

                   } 
               } else {
                   
                   $(".btn-activity-add").attr("disabled", false);
                   $('#btn_activity_loading').remove();
                   $(".btn-activity-add").html('Thêm');
                   alert('Lỗi: ' + data.message);

               }
           
           },
           error: function (jqXHR, textStatus, errorThrown)
           {
               alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
               $(".btn-activity-add").attr("disabled", false);
               $('#btn_activity_loading').remove();
               $(".btn-activity-add").html('Thêm');
           }
           
       });
   }

   function delete_activity(id) {
        if (confirm("Bạn có chắc chắn muốn xóa tích hợp này?")) {
            $.ajax({
            url : "<?php echo base_url('crmactivity/ajax_delete');?>",
            type: "POST",
            data: {id: id, "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
            dataType: "JSON",
            success: function(data)
            {
                //if success close modal and reload ajax table
                if(data.status == true) {
                    $('#activities_table').DataTable().ajax.reload();
                    notyf.success({message:'Xóa thành công', dismissible: true});

                } else {
                    
                    alert('Lỗi: ' + data.message);
 
                }
            
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
               
            }
            
        });
        }
        
    }

</script>