<main class="content">
    <div class="container-fluid">
        

        <div class="row">
            <div class="col-md-7 d-flex">

                <div class="card flex-fill w-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><PERSON><PERSON>ng ký gần đây</h5>

                    </div>
                    <div class="card-body d-flex table-responsive">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Tên công ty</th>
                                    <th>Ngày tạo</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($recent_companies_register as $company): ?>
                                <tr>
                                    <td><a href="<?= base_url('company/details/' . $company->id);?>"><?= esc($company->id);?></a></td>
                                    <td><a href="<?= base_url('company/details/' . $company->id);?>"><?= esc(character_limiter($company->full_name,30,'..'));?></a></td>
                                    <td><?= timespan($company->created_at,1);?></td>
                                </tr>
                                <?php endforeach;?>


                            </tbody>
                        </table>

                    </div>
                    <div class="text-center pb-3"><a href="<?= base_url('company/');?>">Xem thêm <i class="bi bi-chevron-right"></i></a></div>

                </div>

            </div>
            <div class="col-md-5 d-flex">
            <div class="card flex-fill w-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Hóa đơn gần đây</h5>

                    </div>
                    <div class="card-body d-flex table-responsive">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Giá trị</th>
                                    <th>Trạng thái</th>
                                    <th>Ngày tạo</th>

                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($recent_invoices as $invoice): ?>
                                <tr>
                                    <td><a href="<?= base_url('invoice/details/' . $invoice->id);?>"><?= esc($invoice->id);?></a></td>
                                    <td><?= number_format($invoice->subtotal);?> đ</td>
                                    <td><span class="text-<?php if($invoice->status =="Paid") echo 'success'; else if($invoice->status =="Unpaid") echo 'danger'; else echo 'secondary'; ?>">
                                    <?= esc($invoice->status);?>
                                    </span>
                                    <td><?= timespan($invoice->created_at,1);?></td>
                                </tr>
                                <?php endforeach;?>


                            </tbody>
                        </table>
                    </div>
                    <div class="text-center pb-3"><a href="<?= base_url('invoice/');?>">Xem thêm <i class="bi bi-chevron-right"></i></a></div>

                </div>
            </div>
        </div>


    </div>
    <div class="mt-5"></div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>


<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js"></script>
<script>

</script>