<main class="content">
    <div class="container-fluid">
        <?php if (cache()->get('last_no_invoice_alert')): ?>
            <div class="alert alert-danger">
                <div class="alert-message">
                    <strong>Cảnh báo:</strong> <PERSON><PERSON><PERSON>ng có hóa đơn nào được xuất trong 2 ngày liên tiếp.
                </div>
            </div>
        <?php endif; ?>
        <div class="row row-cols-2 row-cols-md-3 row-cols-xxl-4 g-3 mb-3">
            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>MRR <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Monthly Recurring Revenue"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_mrr); ?> đ</span>
                    </div>
                </div>
            </div>

            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>Active & paid companies <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Công ty đang hoạt động và đã thanh toán"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($count_active_paid_companies); ?>
                        </span>
                    </div>
                </div>
            </div>

            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>Bank Transactions <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng số lượng giao dịch ngân hàng"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_bank_transactions); ?> </span>
                    </div>
                </div>
            </div>

            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>Chat message sent <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng số tin nhắn Chat đã gửi thành công"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_chat_sent); ?> </span>
                    </div>
                </div>
            </div>

            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>Webhooks sent <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng số webhooks đã gửi đi thành công"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_webhooks_sent); ?> </span>
                    </div>
                </div>
            </div>


            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>Client's cash inflow <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng số tiền vào phía khách hàng"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_amount_in); ?> </span>
                    </div>
                </div>
            </div>

            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>Bank Accounts Connected <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng tài khoản ngân hàng đã kết nối"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_bank_account_connected); ?>
                        </span>
                    </div>
                </div>
            </div>

            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>Webhooks verify payment success <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng Webhooks xác thực thanh toán thành công"></i>
                        </div>
                        <span
                            class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_webhooks_verify_payment_success); ?>
                        </span>
                    </div>
                </div>
            </div>

            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>Total Income <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng tiền vào (bao gồm VAT)"></i>
                        </div>
                        <span
                            class="h3 d-inline-block mt-1 mb-3"><?= number_format($sum_invoice_total); ?> đ
                        </span>
                    </div>
                </div>
            </div>

            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>Total Income 7 Days Ago <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng tiền vào (bao gồm VAT) 7 ngày gần nhất"></i>
                        </div>
                        <span
                            class="h3 d-inline-block mt-1 mb-3"><?= number_format($sum_invoice_7_days_ago); ?> đ
                        </span>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>Total Income This Month <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng tiền vào  (bao gồm VAT) tháng này"></i>
                        </div>
                        <span
                            class="h3 d-inline-block mt-1 mb-3"><?= number_format($sum_invoice_this_month); ?> đ
                        </span>
                    </div>
                </div>
            </div>


            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>Total Income This Year <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng tiền vào  (bao gồm VAT) năm này"></i>
                        </div>
                        <span
                            class="h3 d-inline-block mt-1 mb-3"><?= number_format($sum_invoice_this_year); ?> đ
                        </span>
                    </div>
                </div>
            </div>

            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>Hóa đơn đủ điều kiện xuất <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Tổng số hóa đơn đã thanh toán và đủ điều kiện để xuất hóa đơn"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_eligible_invoices); ?></span>
                    </div>
                </div>
            </div>

            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>
                            Hóa đơn đã xuất <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Tổng số hóa đơn đã được gọi API xuất hóa đơn"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_issued_invoices); ?></span>
                    </div>
                </div>
            </div>

            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>
                            Hóa đơn loa đủ điều kiện xuất <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Tổng số hóa đơn loa đã thanh toán và đủ điều kiện để xuất hóa đơn"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_eligible_physical_invoices); ?></span>
                    </div>
                </div>
            </div>

            <div class="col">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-body text-center">
                        <div>
                            Hóa đơn loa đã xuất <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Tổng số hóa đơn loa đã được xuất"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_issued_physical_invoices); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-3">
            <div class="col-md-7">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Đăng ký gần đây</h5>
                    </div>
                    <div class="card-body d-flex table-responsive">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Tên công ty</th>
                                    <th>Ngày tạo</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_companies_register as $company): ?>
                                    <tr>
                                        <td><a href="<?= base_url('company/details/' . $company->id); ?>"><?= esc($company->id); ?></a></td>
                                        <td><a href="<?= base_url('company/details/' . $company->id); ?>"><?= esc(character_limiter($company->full_name, 30, '..')); ?></a></td>
                                        <td><?= timespan($company->created_at, 1); ?></td>
                                    </tr>
                                <?php endforeach; ?>


                            </tbody>
                        </table>

                    </div>
                    <div class="text-center pb-3"><a href="<?= base_url('company/'); ?>">Xem thêm <i class="bi bi-chevron-right"></i></a></div>

                </div>



            </div>
            <div class="col-md-5">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Hóa đơn gần đây</h5>

                    </div>
                    <div class="card-body d-flex table-responsive">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Giá trị</th>
                                    <th>Trạng thái</th>
                                    <th>Ngày tạo</th>

                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_invoices as $invoice): ?>
                                    <tr>
                                        <td><a href="<?= base_url('invoice/edit/' . $invoice->id); ?>"><?= esc($invoice->id); ?></a></td>
                                        <td><?= number_format($invoice->subtotal); ?> đ</td>
                                        <td><span class="text-<?php if ($invoice->status == "Paid") echo 'success';
                                                                else if ($invoice->status == "Unpaid") echo 'danger';
                                                                else echo 'secondary'; ?>">
                                                <?= esc($invoice->status); ?>
                                            </span>
                                        <td><?= timespan($invoice->created_at, 1); ?></td>
                                    </tr>
                                <?php endforeach; ?>


                            </tbody>
                        </table>
                    </div>
                    <div class="text-center pb-3"><a href="<?= base_url('invoice/'); ?>">Xem thêm <i class="bi bi-chevron-right"></i></a></div>

                </div>


            </div>
            <div class="col-md-12">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Ticket gần đây</h5>
                    </div>
                    <div class="card-body d-flex table-responsive">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Tên</th>
                                    <th>Khách hàng</th>
                                    <th>Ngày tạo</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_tickets as $ticket): ?>
                                    <tr>
                                        <td><a href="<?= base_url('ticket/details/' . $ticket->id); ?>"><?= esc($ticket->id); ?></a></td>
                                        <td><a href="<?= base_url('ticket/details/' . $ticket->id); ?>"><?= esc(character_limiter($ticket->subject, 200, '..')); ?></a></td>
                                        <td><?= get_ticket_status_badge($ticket->status); ?></td>


                                        <td><a href="<?= base_url('company/details/' . $ticket->company_id); ?>"><?= esc(character_limiter($ticket->name, 30, '..')); ?></a></td>
                                        <td><?= timespan($ticket->created_at, 1); ?></td>
                                    </tr>
                                <?php endforeach; ?>


                            </tbody>
                        </table>

                    </div>
                    <div class="text-center pb-3"><a href="<?= base_url('ticket/'); ?>">Xem thêm <i class="bi bi-chevron-right"></i></a></div>

                </div>
            </div>
            <div class="col-md-6">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Thanh toán gần đây</h5>
                    </div>
                    <div class="card-body d-flex table-responsive">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Số tiền</th>
                                    <th>Ngày tạo</th>

                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_stransactions as $transaction): ?>
                                    <tr>
                                        <td><?= esc($transaction->id); ?></td>
                                        <td><?php if ($transaction->in > 0) { ?> <span class="text-success">+<?= esc(number_format($transaction->in)); ?></span> <?php } else { ?> <span class="text-danger">-<?= esc(number_format($transaction->out)); ?></span> <?php } ?></td>

                                        <td><?= timespan($transaction->date, 1); ?></td>
                                    </tr>
                                <?php endforeach; ?>


                            </tbody>
                        </table>
                    </div>


                </div>
            </div>
            <div class="col-md-6">
                <div class="card flex-fill mb-0 h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Tài khoản liên kết</h5>
                    </div>
                    <div class="card-body d-flex table-responsive">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Tài khoản</th>
                                    <th>Chủ TK</th>
                                    <th>Ngày tạo</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_bank_accounts as $bank_account): ?>
                                    <tr>
                                        <td><img class="img-fluid me-3" style="width:30px" src="https://my.sepay.vn/assets/images/banklogo/<?php echo $bank_account->icon_path; ?>"> <?= esc($bank_account->account_number); ?></td>
                                        <td><?= esc($bank_account->account_holder_name); ?></td>
                                        <td><?= timespan($bank_account->created_at, 1); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php'); ?>

<script src="<?php echo base_url(); ?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/jquery-3.6.0.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/app.js"></script>
