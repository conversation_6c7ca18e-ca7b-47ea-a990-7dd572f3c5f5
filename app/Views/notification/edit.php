 
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/select2/select2.min.css">

<style>
    .tox-promotion {
        display:none;
    }
    .tox-statusbar__branding {
        display:none;
    }
</style>
        <main class="content">
            <div class="container-fluid">


         <div>
             <!-- Breadcrumb -->
             <nav aria-label="breadcrumb">
              <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("notification");?>">Thông báo</a></li> 
                <li class="breadcrumb-item active" aria-current="page">Tạo thông báo</li> 
                
              </ol>
            </nav>
            <!-- End Breadcrumb -->
         </div>

                <div class="card mt-3" style="width:100%">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-danger border-bottom">
                                 <div class="my-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-1-circle" viewBox="0 0 16 16">
  <path d="M1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8Zm15 0A8 8 0 1 1 0 8a8 8 0 0 1 16 0ZM9.283 4.002V12H7.971V5.338h-.065L6.072 6.656V5.385l1.899-1.383h1.312Z"/>
</svg>
</div>
                            
                                <p>Soạn thông báo</p>
                            </div>
                            <div class="col-md-3 text-muted">
                            <div class="my-2">

                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-2-circle" viewBox="0 0 16 16">
  <path d="M1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8Zm15 0A8 8 0 1 1 0 8a8 8 0 0 1 16 0ZM6.646 6.24v.07H5.375v-.064c0-1.213.879-2.402 2.637-2.402 1.582 0 2.613.949 2.613 2.215 0 1.002-.6 1.667-1.287 2.43l-.096.107-1.974 2.22v.077h3.498V12H5.422v-.832l2.97-3.293c.434-.475.903-1.008.903-1.705 0-.744-.557-1.236-1.313-1.236-.843 0-1.336.615-1.336 1.306Z"/>
</svg></div>
                                <p>Xem trước nội dung</p>
                            </div>
                            <div class="col-md-3 text-muted">
                            <div class="my-2">

                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-3-circle" viewBox="0 0 16 16">
  <path d="M7.918 8.414h-.879V7.342h.838c.78 0 1.348-.522 1.342-1.237 0-.709-.563-1.195-1.348-1.195-.79 0-1.312.498-1.348 1.055H5.275c.036-1.137.95-2.115 2.625-2.121 1.594-.012 2.608.885 2.637 2.062.023 1.137-.885 1.776-1.482 1.875v.07c.703.07 1.71.64 1.734 1.917.024 1.459-1.277 2.396-2.93 2.396-1.705 0-2.707-.967-2.754-2.144H6.33c.059.597.68 1.06 1.541 1.066.973.006 1.6-.563 1.588-1.354-.006-.779-.621-1.318-1.541-1.318Z"/>
  <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0ZM1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8Z"/>
</svg></div>
                                <p>Gửi thông báo</p>
                            </div>

                            <div class="col-md-3 text-muted">
                            <div class="my-2">

                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-4-circle" viewBox="0 0 16 16">
  <path d="M7.519 5.057c.22-.352.439-.703.657-1.055h1.933v5.332h1.008v1.107H10.11V12H8.85v-1.559H4.978V9.322c.77-1.427 1.656-2.847 2.542-4.265ZM6.225 9.281v.053H8.85V5.063h-.065c-.867 1.33-1.787 2.806-2.56 4.218Z"/>
  <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0ZM1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8Z"/>
</svg></div>
                                <p>Xem kết quả</p>
                            </div>
                        </div>

                    </div>
                </div>
              
                <div class="card" style="width:100%">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-auto d-none d-sm-block">
                                <h3>Soạn thông báo</h3>
                            </div>

                            
                        </div>
                         
                        <div class="row">

                        <?php echo form_open('notification/ajax_add',"id='data_form' class='needs-validation data_form' novalidate");?>

                        
                            <div class="mb-1 mt-2">
                                <label class="form-label"><b>Gửi từ:</b> <EMAIL></label>
                                
                            </div>
                            <div class="mb-1">
                                <label class="form-label"><b>Gửi đến:</b></label>
                                <select id="send_to" name="send_to[]" class="form-control select2" data-toggle="select2"  multiple required>
             
            <?php foreach($groups_tree as $group) { ?>
              <option value="<?php echo $group['value'];?>" <?php if(in_array($group['value'], $noti_to)) echo 'selected';?>><?php echo $group['name'];?></option>
              <?php } ?>
             
          </select>
          <div class="invalid-feedback">
                                   Vui lòng chọn nhóm bạn muốn gửi đến.
                                </div>
                                
                            </div>
                            <div class="my-3">
                                <label class="form-label fw-bold">Tiêu đề <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" placeholder="" value="<?= $noti_details->title;?>" name="title" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Nội dung <span class="text-danger">*</span></label>
                                <textarea placeholder="" id="notify_body" name="body" required><?= $noti_details->body;?></textarea>
                                <div class="invalid-feedback">
                                   Vui lòng soạn nội dung thông báo.
                                </div>
                            </div>

                            <div class="mb-3 text-center">
                                <button type="button" class="btn btn-danger btn-lg btn-save" onclick="submit_data()"><i class="bi bi-arrow-right-circle"></i> Tiếp tục</button>
                            </div>
                           
                            </form>
                            
                        </div>
                    </div>
                </div>
                
            </div>
        </main>

        <?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>


        </div>
    </div>

 

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>

<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
    
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>
<script src="<?php echo base_url();?>/assets/tinymce/tinymce.min.js"></script>
<script src="<?php echo base_url();?>/assets/select2/select2.min.js"></script>

<script>

document.addEventListener("DOMContentLoaded", function() {
    // Select2
    $(".select2").each(function() {
        $(this)
            .wrap("<div class=\"position-relative\"></div>")
            .select2({
                placeholder: "Chọn",
                dropdownParent: $(this).parent()
            });
    })

});
  tinymce.init({
  selector: 'textarea#notify_body',
      plugins: 'print preview paste importcss searchreplace autolink autosave save directionality code visualblocks visualchars fullscreen image link media template codesample table charmap hr pagebreak nonbreaking anchor toc insertdatetime advlist lists wordcount imagetools textpattern noneditable help charmap quickbars emoticons',
   
  image_caption: true,
  quickbars_selection_toolbar: 'bold italic | quicklink h2 h3 blockquote quickimage quicktable',
  noneditable_noneditable_class: 'mceNonEditable',
  toolbar_mode: 'sliding',
  contextmenu: 'link image imagetools table',
  
  });

  /*
(() => {
  'use strict'

  const forms = document.querySelectorAll('.needs-validation')


  Array.from(forms).forEach(form => {
    form.addEventListener('submit', event => {
        event.preventDefault()
        event.stopPropagation()
      if (form.checkValidity()) {
         
        submit_data();
      }  

      form.classList.add('was-validated')
    }, false)
  })
})()
*/



function submit_data() {
      
    url = "<?php echo base_url('notification/ajax_edit');?>";
    //  post_data = $('#data_form').serialize();

    var ed = tinyMCE.get('notify_body');

    var send_to = $('#send_to').val();

    // convert array to json
    var json_send_to = JSON.stringify(send_to);    
    
    var title = $("input[name=title]").val();

    var data_save = [];
    var body = ed.getContent();


    data_save.push({
        name: "id",
        value: <?= $noti_details->id;?>
    });
    data_save.push({
        name: "body",
        value: body
    });
    data_save.push({
        name: "send_to",
        value: json_send_to
    });

    data_save.push({
        name: "title",
        value: title
    });

    data_save.push({
        name: "<?php echo csrf_token() ?>",
        value: "<?php echo csrf_hash() ?>"
    });  

      $(".btn-save").attr("disabled", true);
      $('#btn_loading').html('');
      $(".btn-save").html('<div class="spinner-border text-light" role="status" id="btn_loading"></div>');
  
      $.ajax({
          url : url,
          type: "POST",
          data: data_save,
          dataType: "JSON",
          success: function(data)
          {
  
              $(".btn-save").attr("disabled", false);
              $('#btn_loading').remove();
              $(".btn-save").html('<i class="bi bi-arrow-right-circle"></i> Tiếp tục');
              
              //if success close modal and reload ajax table
              if(data.status == true) {
                  location.href="<?= base_url('notification/preview/');?>"+data.id;
              } else {
                  alert('Lỗi: ' + data.message);
              }
          
          },
          error: function (jqXHR, textStatus, errorThrown)
          {
              alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
              $(".btn-save").attr("disabled", false);
              $('#btn_loading').remove();
              $(".btn-save").html('<i class="bi bi-arrow-right-circle"></i> Tiếp tục');
          }
          
      });
      }
  
  
</script>
