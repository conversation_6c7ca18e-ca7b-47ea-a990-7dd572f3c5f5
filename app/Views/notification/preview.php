 
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
<style>
    .tox-promotion {
        display:none;
    }
    .tox-statusbar__branding {
        display:none;
    }
</style>
        <main class="content">
            <div class="container-fluid">

            <div>
             <!-- Breadcrumb -->
             <nav aria-label="breadcrumb">
              <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("notification");?>">Thông báo</a></li> 
                <li class="breadcrumb-item active" aria-current="page">Xem trước thông báo #<?= esc($noti_details->id);?></li> 
                
              </ol>
            </nav>
            <!-- End Breadcrumb -->
         </div>

         <div class="card mt-3" style="width:100%">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-muted">
                                 <div class="my-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-1-circle" viewBox="0 0 16 16">
  <path d="M1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8Zm15 0A8 8 0 1 1 0 8a8 8 0 0 1 16 0ZM9.283 4.002V12H7.971V5.338h-.065L6.072 6.656V5.385l1.899-1.383h1.312Z"/>
</svg>
</div>
                            
                                <p>Soạn thông báo</p>
                            </div>
                            <div class="col-md-3 text-danger  border-bottom">
                            <div class="my-2">

                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-2-circle" viewBox="0 0 16 16">
  <path d="M1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8Zm15 0A8 8 0 1 1 0 8a8 8 0 0 1 16 0ZM6.646 6.24v.07H5.375v-.064c0-1.213.879-2.402 2.637-2.402 1.582 0 2.613.949 2.613 2.215 0 1.002-.6 1.667-1.287 2.43l-.096.107-1.974 2.22v.077h3.498V12H5.422v-.832l2.97-3.293c.434-.475.903-1.008.903-1.705 0-.744-.557-1.236-1.313-1.236-.843 0-1.336.615-1.336 1.306Z"/>
</svg></div>
                                <p>Xem trước nội dung</p>
                            </div>
                            <div class="col-md-3 text-muted">
                            <div class="my-2">

                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-3-circle" viewBox="0 0 16 16">
  <path d="M7.918 8.414h-.879V7.342h.838c.78 0 1.348-.522 1.342-1.237 0-.709-.563-1.195-1.348-1.195-.79 0-1.312.498-1.348 1.055H5.275c.036-1.137.95-2.115 2.625-2.121 1.594-.012 2.608.885 2.637 2.062.023 1.137-.885 1.776-1.482 1.875v.07c.703.07 1.71.64 1.734 1.917.024 1.459-1.277 2.396-2.93 2.396-1.705 0-2.707-.967-2.754-2.144H6.33c.059.597.68 1.06 1.541 1.066.973.006 1.6-.563 1.588-1.354-.006-.779-.621-1.318-1.541-1.318Z"/>
  <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0ZM1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8Z"/>
</svg></div>
                                <p>Gửi thông báo</p>
                            </div>

                            <div class="col-md-3 text-muted">
                            <div class="my-2">
<?php if($noti_details->step == 1) { ?>  <a class="" href="<?= base_url('notification/result/' . $noti_details->id);?>"> <?php } ?>
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-4-circle" viewBox="0 0 16 16">
  <path d="M7.519 5.057c.22-.352.439-.703.657-1.055h1.933v5.332h1.008v1.107H10.11V12H8.85v-1.559H4.978V9.322c.77-1.427 1.656-2.847 2.542-4.265ZM6.225 9.281v.053H8.85V5.063h-.065c-.867 1.33-1.787 2.806-2.56 4.218Z"/>
  <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0ZM1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8Z"/>
</svg></div>
                                <p>Xem kết quả</p>

<?php if($noti_details->step == 1) { ?> </a> <?php } ?>

                            </div>
                        </div>

                    </div>
                </div>
              
                <div class="card" style="width:100%">
                    <div class="card-body">
                    <p class="">Vui lòng kiểm tra đối tượng gửi đến và nội dung đúng với yêu cầu của bạn!</p>

                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <td class="fw-bold">Gửi đến</td>
                                    <td><?= esc($noti_to_text);?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold" style="width:100px">Tiêu đề</td>
                                    <td><?= esc($noti_details->title);?></td>
                                </tr>
                               
                                <tr>
                                    <td class="fw-bold">Nội dung</td>
                                    <td><?= $noti_details->body;?></td>
                                </tr>
                            </tbody>
                        </table>

                        <?php if($noti_details->step == 0) { ?> 

                        <div class="row my-3">
                            <div class="col-6 text-end">
                                <a href="<?= base_url('notification/edit/' . $noti_details->id);?>" class="btn btn-secondary"><i class="bi bi-arrow-left-circle"></i> Quay lại sửa</a>
                            </div>
                            <div class="col-6">
                            <a class="btn btn-danger btn-lg" href="<?= base_url('notification/send/' . $noti_details->id);?>"><i class="bi bi-arrow-right-circle"></i> Tiếp tục</a>

                            </div>
                        </div>
                        <?php } ?>
                       
                    </div>
                </div>
                
            </div>
        </main>

        <?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>


        </div>
    </div>

 

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>

<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
    
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>
<script src="<?php echo base_url();?>/assets/tinymce/tinymce.min.js"></script>

<script> 
  
  
</script>