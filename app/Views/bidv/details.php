<main class="content">
    <div class="container-fluid">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("bankaccount"); ?>">Tài khoản ngân hàng</a></li>
                <li class="breadcrumb-item active" aria-current="page">#<?= esc($bank_account_details->id . ' - ' . $bank_account_details->account_number); ?></li>
            </ol>
        </nav>

        <div class="row mt-3">
            <div class="col-12">
                <h4 class="d-flex align-items-center">#<?= esc($bank_account_details->id) . ' - <img src="https://my.sepay.vn/assets/images/banklogo/bidv-icon.png" class="mx-1" style="width: 24px; height: 24px;">' . esc($bank_account_details->account_number); ?></h4>

                <div class="row">
                    <div class="col-12 col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Thông tin tài khoản ngân hàng</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 col-6">
                                        <span class="text-muted">Ngân hàng</span>
                                    </div>
                                    <div class="col-md-8 col-6">
                                        <img src="https://my.sepay.vn/assets/images/banklogo/bidv-icon.png" class="me-1" style="width: 24px; height: 24px;"> <span class='fw-bold'>BIDV</span>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-4 col-6">
                                        <span class="text-muted">Số tài khoản</span>
                                    </div>
                                    <div class="col-md-8 col-6">
                                        <span class="fw-bold"><?= esc($bank_account_details->account_number) ?></span>
                                    </div>
                                </div>
                                <?php if ($bank_account_details->bank_api): ?>
                                <div class="row mt-3">
                                    <div class="col-md-4 col-6">
                                        <span class="text-muted">Loại tài khoản</span>
                                    </div>
                                    <div class="col-md-8 col-6">
                                        <span class="fw-bold">
                                            <?php if ($enterprise_account_details): ?>
                                                Doanh nghiệp
                                            <?php else: ?>
                                                Cá nhân
                                            <?php endif ?>
                                        </span>
                                    </div>
                                </div>
                                <?php endif ?>
                                <div class="row mt-3">
                                    <div class="col-md-4 col-6">
                                        <span class="text-muted">Tên thụ hưởng</span>
                                    </div>
                                    <div class="col-md-8 col-6">
                                        <span class="fw-bold"><?= esc($bank_account_details->account_holder_name) ?></span>
                                        <a href="#" data-bs-toggle="modal" data-bs-target="#editAccountHolderNameModal" class="ms-3"><i class="bi bi-pencil"></i> Sửa</a></span>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-4 col-6">
                                        <span class="text-muted">Trạng thái kết nối</span>
                                    </div>
                                    <div class="col-md-8 col-6">
                                        <div class="d-flex align-items-center">
                                            <?php if ($bank_account_details->bank_api): ?>
                                                <span class="text-warning"><i class="bi bi-lightning-charge-fill"></i> API Banking</span>
                                            <?php else: ?>
                                                <span class="text-primary"><i class="bi bi-chat-left-text-fill"></i> SMS Banking</span>
                                            <?php endif ?>
                                            <?php if ($bank_account_details->bank_api_connected || $bank_account_details->bank_sms_connected): ?>
                                                <span class="badge bg-success ms-2">Đã kết nối</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary ms-2">Chưa kết nối</span>
                                            <?php endif ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php if ($enterprise_account_details && $bank_account_details->bank_api): ?>
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Cấu hình tài khoản doanh nghiệp</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 col-6">
                                            <span class="text-muted">Cho phép tùy chỉnh tên thụ hưởng VA</span>
                                        </div>
                                        <div class="col-md-8 col-6">
                                            <?= form_open(base_url('bidv/ajax_edit_enterprise_bank_account'), 'id="form-switch-custom-va-name"') ?>
                                                <input type="hidden" name="bank_account_id" value="<?= esc($bank_account_details->id) ?>" ?>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" role="switch" name="custom_va_name" id="switcher-custom-va-name" <?= $enterprise_account_details->custom_va_name ? 'checked' : '' ?>>
                                                </div>
                                            <?= form_close() ?>
                                        </div>
                                    </div>

                                    <div class="row mt-3">
                                        <div class="col-md-4 col-6">
                                            <span class="text-muted">Tiền tố VA</span>
                                        </div>
                                        <div class="col-md-8 col-6">
                                            <table class="table table-bordered table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Mã đầu</th>
                                                        <th>ID VA</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><?= esc($enterprise_account_details->va_prefix) ?><?= strlen($enterprise_account_details->prefix_id) ?></td>
                                                        <td><?= esc($enterprise_account_details->prefix_id) ?></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        <?php endif ?>
                    </div>
                    <div class="col-12 col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Thông tin công ty</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 col-6">
                                        <span class="text-muted">Tên công ty</span>
                                    </div>
                                    <div class="col-md-8 col-6">
                                        <a href="<?= base_url('company/details/' . $company_details->id) ?>">#<?= esc($company_details->id) ?> - <?= esc($company_details->full_name) ?></a>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-4 col-6">
                                        <span class="text-muted">Thương hiệu</span>
                                    </div>
                                    <div class="col-md-8 col-6">
                                        <?= esc($company_details->short_name) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php'); ?>

</div>
</div>

<div class="modal fade" id="editAccountHolderNameModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="editAccountHolderNameModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAccountHolderNameModalLabel">Sửa tên thụ hưởng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('bidv/ajax_edit_bank_account'), 'id="form-edit-bank-account"'); ?>
                <input type="hidden" name="bank_account_id" value="<?= esc($bank_account_details->id) ?>">
                <div class="modal-body m-lg-3">
                    <div class="form-group">
                        <input type="text" class="form-control" id="account_holder_name" name="account_holder_name" style="text-transform: uppercase" maxlength="70" value="<?= esc($bank_account_details->account_holder_name) ?>">
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light me-1" data-bs-dismiss="modal">Đóng</button>
                    <button type="submit" class="btn btn-primary btn-save d-flex align-items-center" style="gap: 0.25rem">
                        <div class="spinner-border loader" style="width: 16px; height: 16px; display: none;" role="status"></div> Lưu thay đổi
                    </button>
                </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script src="<?php echo base_url(); ?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="<?php echo base_url(); ?>/assets/DataTables/datatables.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/app.js"></script>

<script>
$('#form-edit-bank-account').submit((e) => {
    e.preventDefault();

    const form = '#form-edit-bank-account';

    $(form).find('.btn-save').prop('disabled', true)
    $(form).find('.btn-save .loader').show();

    $.ajax({
        url: $(form).attr('action'),
        method: 'POST',
        data: $(form).serialize(),
        success: (res) => {
            $(form).find('.btn-save').prop('disabled', false)
            $(form).find('.btn-save').find('.loader').hide();

            window.location.reload();
        },
        error: (err) => {
            $(form).find('.btn-save').prop('disabled', false)
            $(form).find('.btn-save').find('.loader').hide();

            if (err.status === 400) {
                for (const field in err.responseJSON.messages) {
                    $(`*[name=${field}]`).addClass('is-invalid')
                    $(`*[name=${field}]`).parents('.form-group').children('.invalid-feedback').show().html(err.responseJSON.messages[field])
                }
                return;
            }

            if (err.status === 403) {
                notyf.error({
                    message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                    dismissible: true
                });
                return;
            }

            notyf.error({
                message: err.responseJSON.messages.error,
                dismissible: true
            });
        },
    })
})

$('#switcher-custom-va-name').on('change', () => {
    const checked = $('#switcher-custom-va-name').prop('checked');

    $('#form-switch-custom-va-name').submit()
})

$('#form-switch-custom-va-name').submit(e => {
    e.preventDefault();

    const form = '#form-switch-custom-va-name';

    $.ajax({
        url: $(form).attr('action'),
        method: 'POST',
        data: $(form).serialize(),
        success: (res) => {
            notyf.success({message: 'Lưu thay đổi thành công', dismissible: true});
        },
        error: (err) => {
            if (err.status === 403) {
                notyf.error({
                    message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                    dismissible: true
                });
                return;
            }

            notyf.error({
                message: err.responseJSON.messages.error,
                dismissible: true
            });
        },
    })
}) 
</script>