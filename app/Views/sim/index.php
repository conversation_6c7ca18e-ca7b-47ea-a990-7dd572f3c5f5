 
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
        <main class="content">
            <div class="container-fluid">
              
                <div class="card mt-3" style="width:100%">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-auto d-none d-sm-block">
                                <h3>Danh sách SIM</h3>
                            </div>

                            <div class="col-auto ms-auto text-end row">
                                <div class="mb-2">
                                    <a href="javascript:;" onclick="show_add_sim()" class="btn btn-primary btn-sm float-end"><i class="bi bi-plus"></i> Thêm SIM</a>   

                                </div>

                            
                            </div>
                        </div>
                         
                        <div class="row">
                           
                           
                                <div class="">
                                <table class="table table-hover table-striped table-bordered text-muted align-middle display nowrap" id="sim_table" style="width:100%">
                                    <thead class="table-light text-muted">
                                        <tr class="align-middle">
                                            <th></th>
                                            <th>SIM ID</th>
                                            <th>SĐT</th>
                                            <th>Device</th>
                                            <th>Ghi chú</th>
                                            <th>Công ty</th>
                                            <th>Ngân hàng</th>
                                            <th>Last SMS</th>
                                            <th>Trạng thái</th>
                                            <th>Tạo lúc</th>
                                            <th></th>

                                         </tr>
                                    </thead>
                                    <tbody>
                
                                       
                                    </tbody>
                                </table>
                                </div>
                             
                            
                        </div>
                    </div>
                </div>
                
            </div>
        </main>

        <?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>


        </div>
    </div>


  
<!-- Modal -->
<div class="modal fade" id="simAddModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="simAddModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="simAddModalLabel">Thêm SIM</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <?php echo form_open('',"id='sim_add_form' class='needs-validation form-add-sim' novalidate");?>
        <input type="hidden"name="id" value="">
        <div class="modal-body m-lg-3">

                  

                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Số điện thoại <span class="text-danger">(*)</span></label>
                        <input type="number" class="form-control sim_phonenumber" name="sim_phonenumber" minlength="5" maxlength="20" placeholder="" required>
                    </div>
                
                    
                    <div class="mb-3">
                        <label for="exampleFormControlInput2" class="form-label">Mô tả <span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control description" name="description" minlength="5" maxlength="100" placeholder="" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label"><b>Là SIM chia sẻ?</b></label>
                        <select class="form-select active" name="is_shared" aria-label="is_shared" required>
                            <option value="0" selected>Không</option>
                            <option value="1">Có</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label"><b>Là Sim riêng?</b></label>
                        <select class="form-select active" name="is_primary" aria-label="is_primary" required>
                            <option value="1" selected>Có</option>
                            <option value="0">Không</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label"><b>Loại thiết bị</b></label>
                        <select class="form-select active" name="device_type" aria-label="is_primary" required>
                            <option value="USB" selected>USB</option>
                            <option value="Dinstar">Dinstar</option>
                            <option value="None">None</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Dinstar IMSI</label>
                        <input type="number" class="form-control" name="dinstar_imsi" placeholder="" required>
                    </div>
                

                  
                    <div class="mb-3">
                        <label class="form-label"><b>Trạng thái</b></label>
                        <select class="form-select active" name="active" aria-label="Trạng thái" required>
                            <option value="1" selected>Kích hoạt</option>
                            <option value="0">Tạm ngưng</option>
                        </select>
                    </div>
                 
        
        
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary me-3" data-bs-dismiss="modal">Đóng</button>
                <a class="btn btn-primary btn-sim-add" onclick="save()">Thêm</a>
            </div>
        </form>
        </div>
    </div>
</div>
<!-- Modal -->

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>

<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
    
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

<script>



$(document).ready(function() {

var  table = $('#sim_table').DataTable({ 
   rowReorder: {
       selector: 'td:nth-child(0)'
   },
   responsive: true,


    "processing": true,
    "serverSide": true,
    "order": [],
    "pageLength": 20,

    "ajax": {
        "url": "<?php echo base_url('sim/ajax_list'); ?>",
        "data": {"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
        "type": "POST"
    },


    "language": {
        "sProcessing":   "Đang xử lý...",
        "sLengthMenu":   "Xem _MENU_ mục",
        "sZeroRecords":  "Không tìm thấy dòng nào phù hợp",
        "sInfo":         "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
        "sInfoEmpty":    "Đang xem 0 đến 0 trong tổng số 0 mục",
        "sInfoFiltered": "(được lọc từ _MAX_ mục)",
        "sInfoPostFix":  "",
        "sSearch":       "Tìm:",
        "sUrl":          "",
        "oPaginate": {
            "sFirst":    "Đầu",
            "sPrevious": "Trước",
            "sNext":     "Tiếp",
            "sLast":     "Cuối"
        }
    },

    "columnDefs": [
       { responsivePriority: 1, targets: 1 },
       { responsivePriority: 2, targets: 2 },
       { responsivePriority: 3, targets: 6 },
       { responsivePriority: 4, targets: 8 },

        { 
            "visible": false,
            "targets": [ 0 ],
            "orderable": false,
        },

        { 
            "targets": [ 5,6],
            "orderable": false,
        } 
        
    ],

    
});

table.on ('init', function () {
   $('*[type="search"][class="form-control form-control-sm"]').attr('style','max-width:120px');
   $('div.dataTables_filter').parent().attr('class','col-6');
   $('div.dataTables_length').parent().attr('class','col-6');
});


});

 

var addSimModal = new bootstrap.Modal(document.getElementById('simAddModal'), {
        keyboard: false
    });

    function show_add_sim() {
        reset_sim_form();
        save_method = 'add';
        $('.modal-title').text('Thêm Sim'); // Set title to Bootstrap modal title
        $(".btn-sim-add").html('Thêm');
       // load_tom_select();
        addSimModal.show();
    }

    function reset_sim_form() {
        $('#sim_add_form')[0].reset();

        $('#btn_loading').html('');
        $(".btn-sim-add").html('Thêm');
        $(".btn-sim-add").attr("disabled", false);

        $('[name="sim_phonenumber"]').prop("disabled", false);

    }

    function save()
    {

        var url;
        if(save_method == 'add')
        {
            url = "<?php echo base_url('sim/ajax_sim_add');?>";

        } else  if(save_method == 'update')
        {
            url = "<?php echo base_url('sim/ajax_sim_update');?>";
        } 
        else
        {
            url = "<?php echo base_url('sim');?>";
        } 

        $(".btn-sim-add").attr("disabled", true);
        $('#btn_loading').html('');
        $(".btn-sim-add").html('<div class="spinner-border text-light" role="status" id="btn_loading"></div>');

        $.ajax({
            url : url,
            type: "POST",
            data: $('#sim_add_form').serialize(),
            dataType: "JSON",
            success: function(data)
            {
                //if success close modal and reload ajax table
                if(data.status == true) {
                    addSimModal.hide();
 
                    $('#sim_table').DataTable().ajax.reload();

                    if(save_method == 'add')
                    {
                        notyf.success({message:'Thêm thành công', dismissible: true});

                    } else  if(save_method == 'update')
                    {
                        notyf.success({message:'Sửa thành công', dismissible: true});
                    } 
                } else {
                    
                    $(".btn-sim-add").attr("disabled", false);
                    $('#btn_loading').remove();
                    $(".btn-sim-add").html('Thêm');
                    alert('Lỗi: ' + data.message);
 
                }
            
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                $(".btn-sim-add").attr("disabled", false);
                $('#btn_loading').remove();
                $(".btn-sim-add").html('Thêm');
            }
            
        });
    }

    function delete_sim(id) {
        if (confirm("Bạn có chắc chắn muốn xóa SIM này?")) {
            $.ajax({
            url : "<?php echo base_url('sim/ajax_sim_delete');?>",
            type: "POST",
            data: {id: id, "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
            dataType: "JSON",
            success: function(data)
            {
                //if success close modal and reload ajax table
                if(data.status == true) {
                    $('#transactions_table').DataTable().ajax.reload();
                    notyf.success({message:'Đã xóa tài khoản ngân hàng', dismissible: true});

                } else {
                    
                    alert('Lỗi: ' + data.message);
 
                }
            
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
               
            }
            
        });
        }
        
    }

    function edit_sim(id)
      {
        reset_sim_form(); // reset form on modals
  
        save_method = 'update';

        //Ajax Load data from ajax
        $.ajax({
          url : "<?php echo base_url('sim/ajax_get_sim');?>/" + id,
          type: "GET",
          dataType: "JSON",
          success: function(data)
          {
            if(data.status == true) {
                $('[name="id"]').val(data.data.id);
                $('[name="sim_phonenumber"]').val(data.data.sim_phonenumber);
                $('[name="sim_phonenumber"]').prop("disabled", true);

                $('[name="seri"]').val(data.data.seri);
                $('[name="location"]').val(data.data.location);
                $('[name="description"]').val(data.data.description);
                $('[name="is_primary"]').val(data.data.is_primary);
                $('[name="is_shared"]').val(data.data.is_shared);
                $('[name="active"]').val(data.data.active);
                $('[name="dinstar_imsi"]').val(data.data.dinstar_imsi);
                $('[name="device_type"]').val(data.data.device_type);

                $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                $('.modal-title').text('Sửa SIM'); // Set title to Bootstrap modal title
                $(".btn-sim-add").html('Cập nhật');
                addSimModal.show();

            } else {
                alert('Lỗi: ' + data.message);
            }
  
            
          },
          error: function (jqXHR, textStatus, errorThrown)
          {
              alert('Lỗi không thể lấy được thông tin');
          }
      });
    } 

</script>
