<link rel="stylesheet" href="<?= base_url('assets/css/dataTables.bootstrap5.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/css/rowReorder.bootstrap5.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/css/responsive.dataTables.min.css') ?>">

<main class="content">
    <div class="container-fluid">
        <div class="card mt-3">
            <div class="card-header">
                <h3>Ngân hàng</h3>
            </div>
            <div class="card-body">
                <table class="table table-hover table-striped table-bordered" id="bank_table" style="width: 100%;">
                    <thead>
                        <tr>
                            <th></th>
                            <th>Ngân hàng</th>
                            <th>Trạng thái</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</main>

<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php') ?>

<script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery-3.5.1.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery.dataTables.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.bootstrap5.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.rowReorder.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.responsive.min.js') ?>"></script>
<script src="<?= base_url('assets/notyf/notyf.min.js') ?>"></script>
<script src="<?= base_url('assets/js/app.js?v=1') ?>"></script>

<script>
    $('#bank_table').DataTable({
        rowReorder: {
            selector: 'td:nth-child(0)',
        },
        responsive: true,
        processing: true,
        serverSide: true,
        order: [],
        pageLength: 50,
        ajax: {
            "url": "<?= base_url('bank/ajax_list') ?>",
            "data": {
                "<?= csrf_token() ?>": "<?= csrf_hash() ?>"
            },
            "type": "POST"
        },
        language: {
            "sProcessing": "Đang xử lý...",
            "sLengthMenu": "Xem _MENU_ mục",
            "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
            "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
            "sInfoEmpty": "Đang xem 0 đến 0 trong tổng số 0 mục",
            "sInfoFiltered": "(được lọc từ _MAX_ mục)",
            "sInfoPostFix": "",
            "sSearch": "Tìm:",
            "sUrl": "",
            "oPaginate": {
                "sFirst": "Đầu",
                "sPrevious": "Trước",
                "sNext": "Tiếp",
                "sLast": "Cuối"
            }
        },
        columnDefs: [{
                responsivePriority: 1,
                targets: 1
            },
            // {
            //     responsivePriority: 2,
            //     targets: 2
            // },
            // {
            //     responsivePriority: 3,
            //     targets: 3
            // },
            {
                "visible": false,
                "targets": [0],
                "orderable": false,
            },
        ],
    });
</script>