<style>
    .tox-promotion,
    .tox-statusbar__branding {
        display: none;
    }
</style>

<main class="content">
    <div class="container-fluid" style="width: 760px; max-width: 100%;">
        <div class="card mt-3">
            <div class="card-header pb-0">
                <h3 class="card-title d-flex align-items-center gap-2">
                    Chỉnh sửa
                    <span>
                        <img src="<?= "https://my.sepay.vn/assets/images/banklogo/" . $bank->icon_path ?>" alt="<?= $bank->brand_name ?>" class="img-fluid" style="max-width: 18px;">
                        <span class="fw-medium"><?= $bank->brand_name ?></span>
                    </span>
                </h3>
            </div>
            <div class="card-body pt-2">
                <?= form_open('', ['id' => 'form-edit-bank']) ?>
                    <ul class="nav nav-tabs" role="tablist" id="accountType">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="invididual-tab" data-bs-toggle="tab" data-bs-target="#invididual" type="button" role="tab" aria-controls="invididual" aria-selected="false">Cá nhân</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="enterprise-tab" data-bs-toggle="tab" data-bs-target="#enterprise" type="button" role="tab" aria-controls="enterprise" aria-selected="false">Doanh nghiệp</button>
                        </li>
                    </ul>

                    <div class="tab-content mt-3" id="accountTypeContent">
                        <?php foreach (['invididual', 'enterprise'] as $type): ?>
                            <div class="tab-pane fade show <?= $type === 'invididual' ? 'active' : '' ?>" id="<?= $type ?>" role="tabpanel" aria-labelledby="<?= $type ?>-tab">
                                <div class="mb-4">
                                    <div>
                                        <label for="<?= $type ?>_api_connection" class="form-label fw-bold">Kết nối API</label>
                                        <div>
                                            <label class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="<?= $type ?>_api_connection" name="<?= $type ?>_api_connection" value="1" <?= $bank->{$type . '_api_connection'} ? 'checked' : '' ?>>
                                                <span class="form-check-label">Kích hoạt</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div style="<?= $bank->{$type . '_api_connection'} ? '' : 'display: none;' ?>" id="<?= $type ?>_api_connection_content">
                                    <fieldset class="mb-3 border bg-body border-1 rounded p-3">
                                        <legend class="fs-4">Thông tin tại danh sách ngân hàng</legend>
                                        <div class="mb-4">
                                            <label for="<?= $type ?>_speed" class="form-label fw-bold">Tốc độ</label>
                                            <textarea type="text" class="form-control" id="<?= $type ?>_speed" name="<?= $type ?>_speed"><?= $bank->{$type . '_speed'} ?></textarea>
                                        </div>

                                        <div class="mb-4">
                                            <label for="<?= $type ?>_connection_procedure" class="form-label fw-bold">Thủ tục</label>
                                            <textarea type="text" class="form-control" id="<?= $type ?>_connection_procedure" name="<?= $type ?>_connection_procedure"><?= $bank->{$type . '_connection_procedure'} ?></textarea>
                                        </div>

                                        <div class="mb-4">
                                            <label for="<?= $type ?>_features" class="form-label fw-bold">Hỗ trợ</label>
                                            <div>
                                                <label class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="<?= $type ?>_features" name="<?= $type ?>_features[]" value="va" <?= in_array('va', $bank->{$type . '_features'}) ? 'checked' : '' ?>>
                                                    <span class="form-check-label">Tài khoản ảo (VA)</span>
                                                </label>
                                                <label class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="<?= $type ?>_features" name="<?= $type ?>_features[]" value="amount-in" <?= in_array('amount-in', $bank->{$type . '_features'}) ? 'checked' : '' ?>>
                                                    <span class="form-check-label">Tiền vào</span>
                                                </label>
                                                <label class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="<?= $type ?>_features" name="<?= $type ?>_features[]" value="amount-out" <?= in_array('amount-out', $bank->{$type . '_features'}) ? 'checked' : '' ?>>
                                                    <span class="form-check-label">Tiền ra</span>
                                                </label>
                                                <label class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="<?= $type ?>_features" name="<?= $type ?>_features[]" value="balance" <?= in_array('balance', $bank->{$type . '_features'}) ? 'checked' : '' ?>>
                                                    <span class="form-check-label">Số dư</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div>
                                            <label for="<?= $type ?>_promotion" class="form-label fw-bold">Mở tài khoản</label>
                                            <textarea class="form-control" id="<?= $type ?>_promotion" name="<?= $type ?>_promotion"><?= $bank->{$type . '_promotion'} ?></textarea>
                                        </div>
                                    </fieldset>

                                    <div class="mb-4">
                                        <label for="<?= $type ?>_description" class="form-label fw-bold">Mô tả</label>
                                        <textarea class="form-control" id="<?= $type ?>_description" name="<?= $type ?>_description"><?= $bank->{$type . '_description'} ?></textarea>
                                        <small class="text-muted">Mô tả chi tiết về ngân hàng trên thanh bên khi nhấn chọn vào ngân hàng</small>
                                    </div>

                                    <fieldset class="mb-3 border bg-body border-1 rounded p-3">
                                        <legend class="fs-4">Khuyến mãi</legend>
                                        <div class="mb-4">
                                            <label for="<?= $type ?>_promotion_short_description" class="form-label fw-bold">Mô tả ngắn</label>
                                            <textarea class="form-control" id="<?= $type ?>_promotion_short_description" name="<?= $type ?>_promotion_short_description"><?= $bank->{$type . '_promotion_short_description'} ?></textarea>
                                        </div>

                                        <div class="mb-4">
                                            <label for="<?= $type ?>_promotion_button_label" class="form-label fw-bold">Nhãn nút</label>
                                            <input type="text" class="form-control" id="<?= $type ?>_promotion_button_label" name="<?= $type ?>_promotion_button_label" value="<?= $bank->{$type . '_promotion_button_label'} ?>">
                                        </div>

                                        <div>
                                            <label for="<?= $type ?>_promotion_description" class="form-label fw-bold">Mô tả</label>
                                            <textarea class="form-control" id="<?= $type ?>_promotion_description" name="<?= $type ?>_promotion_description"><?= $bank->{$type . '_promotion_description'} ?></textarea>
                                        </div>
                                    </fieldset>

                                    <div class="mb-4">
                                        <label for="<?= $type ?>_order" class="form-label fw-bold">Thứ tự</label>
                                        <input type="number" class="form-control" id="<?= $type ?>_order" name="<?= $type ?>_order" value="<?= $bank->{$type . '_order'} ?>">
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <button type="submit" class="btn btn-primary">Cập nhật</button>
                    <a href="<?= base_url('bank') ?>" class="btn btn-link">Quay lại</a>
                <?= form_close() ?>
            </div>
        </div>
    </div>
</main>

<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php') ?>

<script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery-3.5.1.js') ?>"></script>
<script src="<?= base_url('assets/notyf/notyf.min.js') ?>"></script>
<script src="<?= base_url('assets/tinymce/tinymce.min.js') ?>"></script>
<script src="<?= base_url('assets/js/app.js?v=1') ?>"></script>

<script>
    const initTinymce = (element, height = 300) => {
        tinymce.init({
            height: height,
            selector: element,
            plugins: 'preview importcss searchreplace autolink autosave save directionality code visualblocks visualchars fullscreen image link media codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount help charmap quickbars emoticons',
            image_caption: true,
            quickbars_selection_toolbar: 'bold italic | quicklink h3 h4 h5 blockquote quickimage quicktable',
            noneditable_noneditable_class: 'mceNonEditable',
            toolbar_mode: 'sliding',
            contextmenu: 'link image imagetools table',
        });
    };

    $(() => {
        $(document).on('submit', '#form-edit-bank', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.prop('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true);
                    button.prepend('<div class="spinner-border spinner-border-sm me-1 align-middle" role="status"></div>');
                },
                success: function(response) {
                    if (response.success) {
                        notyf.success(response.message);
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại sau');
                },
                complete: function() {
                    button.prop('disabled', false);
                    button.find('.spinner-border').remove();
                },
            });
        });

        <?php foreach (['invididual', 'enterprise'] as $type): ?>
            initTinymce('#<?= $type ?>_speed', 200);
            initTinymce('#<?= $type ?>_connection_procedure', 200);
            initTinymce('#<?= $type ?>_promotion', 200);
            initTinymce('#<?= $type ?>_description', 500);
            initTinymce('#<?= $type ?>_promotion_short_description', 200);
            initTinymce('#<?= $type ?>_promotion_description', 500);
        <?php endforeach; ?>
    });

    $('#invididual_api_connection').on('change', function() {
        $('#invididual_api_connection_content').toggle();
    });

    $('#enterprise_api_connection').on('change', function() {
        $('#enterprise_api_connection_content').toggle();
    });
</script>