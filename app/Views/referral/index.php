<link rel="stylesheet" href="<?= base_url('assets/css/dataTables.bootstrap5.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/css/rowReorder.bootstrap5.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/css/responsive.dataTables.min.css') ?>">

<main class="content">
    <div class="container-fluid p-0 p-md-3">
        <div class="row mt-2 mb-3 mt-md-0">
            <div class="col-auto">
                <h3>Chương trình giới thiệu</h3>
            </div>
        </div>

        <div class="row row-cols-1 row-cols-sm-2 row-cols-lg-3 row-cols-xl-4 g-3 mb-4">
            <div class="col d-flex">
                <div class="card flex-fill mb-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col mt-0">
                                <h5 class="card-title">
                                    Số giao dịch đã tặng
                                    <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Tổng số giao dịch mà hệ thống đã tặng cho người dùng thông qua chương trình giới thiệu."></i>
                                </h5>
                            </div>
                        </div>
                        <span class="h1 d-inline-block mt-1 mb-3">
                            <i class="fas fa-hand-holding-usd me-1"></i>
                            <?= number_format($stats->total_transactions) ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col d-flex">
                <div class="card flex-fill mb-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col mt-0">
                                <h5 class="card-title">
                                    Tổng số người được giới thiệu
                                    <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Tổng số người đã đăng ký tài khoản thông qua lời giới thiệu."></i>
                                </h5>
                            </div>
                        </div>
                        <span class="h1 d-inline-block mt-1 mb-3">
                            <i class="fas fa-users me-1"></i>
                            <?= number_format($stats->total_referrals) ?>
                        <span>
                    </div>
                </div>
            </div>
            <div class="col d-flex">
                <div class="card flex-fill mb-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col mt-0">
                                <h5 class="card-title">
                                    Tổng số người được giới thiệu đang chờ
                                    <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Những người được giới thiệu đã đăng ký nhưng chưa kết nối tài khoản ngân hàng."></i>
                                </h5>
                            </div>
                        </div>
                        <span class="h1 d-inline-block mt-1 mb-3 text-warning">
                            <i class="fas fa-exclamation-circle me-1"></i>
                            <?= number_format($stats->total_pending_referrals) ?>
                        <span>
                    </div>
                </div>
            </div>
            <div class="col d-flex">
                <div class="card flex-fill mb-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col mt-0">
                                <h5 class="card-title">
                                    Tổng số người được giới thiệu hợp lệ
                                    <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Những người được giới thiệu đã hoàn tất liên kết tài khoản ngân hàng."></i>
                                </h5>
                            </div>
                        </div>
                        <span class="h1 d-inline-block mt-1 mb-3 text-success">
                            <i class="fas fa-check-circle me-1"></i>
                            <?= number_format($stats->total_successful_referrals) ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col d-flex">
                <div class="card flex-fill mb-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col mt-0">
                                <h5 class="card-title">
                                    Tổng ngân hàng đã liên kết
                                    <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Tổng số tài khoản ngân hàng đã được liên kết thông qua chương trình."></i>
                                <h5>
                            </div>
                        </div>
                        <span class="h1 d-inline-block mt-1 mb-3">
                            <i class="fas fa-university me-1"></i>
                            <?= number_format($stats->total_bank_account_connected) ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col d-flex">
                <div class="card flex-fill mb-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col mt-0">
                                <h5 class="card-title">
                                    Số khách hàng có trả tiền
                                    <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Tổng số khách hàng đã trả phí dịch vụ thông qua chương trình."></i>
                                </h5>
                            </div>
                        </div>
                        <span class="h1 d-inline-block mt-1 mb-3">
                            <i class="fas fa-hand-holding-usd me-1"></i>
                            <?= number_format($stats->total_paid_invoices) ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col d-flex">
                <div class="card flex-fill mb-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col mt-0">
                                <h5 class="card-title">
                                    Tổng giao dịch đã sử dụng
                                    <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Tổng số giao dịch đã được người dùng sử dụng trong tài khoản của họ."></i>
                                </h5>
                            </div>
                        </div>
                        <span class="h1 d-inline-block mt-1 mb-3">
                            <i class="fas fa-hand-holding-usd me-1"></i>
                            <?= number_format($stats->total_used_transactions) ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col d-flex">
                <div class="card flex-fill mb-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col mt-0">
                                <h5 class="card-title">
                                    Số người được giới thiệu tháng này
                                    <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Tổng số người được giới thiệu trong tháng này."></i>
                                </h5>
                            </div>
                        </div>
                        <span class="h1 d-inline-block mt-1 mb-3">
                            <i class="fas fa-users me-1"></i>
                            <?= number_format($stats->referrals_this_month) ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header pb-0">
                <h5 class="card-title">Danh sách giới thiệu gần đây</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table" id="dataTables">
                        <thead>
                            <tr>
                                <th></th>
                                <th>Người GT</th>
                                <th>Người được GT</th>
                                <th>Giao dịch nhận</th>
                                <th>Hình thức</th>
                                <th>Hợp lệ</th>
                                <th>Thời gian</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header pb-0">
                        <h5 class="card-title">Top người giới thiệu tháng này (<?= date('m/Y') ?>)</h5>
                    </div>
                    <div class="card-body">
                        <table class="table text-center">
                            <thead>
                            <tr>
                                <th>Thứ hạng</th>
                                <th>Họ tên</th>
                                <th>Giới thiệu</th>
                            </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($topReferralsThisMonth)): ?>
                                    <tr>
                                        <td colspan="3">Tháng này không có ai giới thiệu</td>
                                    </tr>
                                <?php endif ?>
                                <?php foreach ($topReferralsThisMonth as $index => $referral): ?>
                                    <tr>
                                        <td><?= $index + 1 ?></td>
                                        <td>
                                            <a href="<?= base_url("company/details/$referral->company_id") ?>" target="_blank">
                                                <img src="<?= get_gravatar($referral->email) ?>" class="rounded-pill me-1" style="max-width: 24px; max-height: 24px;">
                                                <?= $referral->fullname ?>
                                            </a>
                                        </td>
                                        <td><strong><?= number_format($referral->total_uses) ?></strong></td>
                                    </tr>
                                <?php endforeach ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header pb-0">
                        <h5 class="card-title">Top người giới thiệu tháng trước (<?= date('m/Y', strtotime('-1 month')) ?>)</h5>
                    </div>
                    <div class="card-body">
                        <table class="table text-center">
                            <thead>
                            <tr>
                                <th>Thứ hạng</th>
                                <th>Họ tên</th>
                                <th>Giới thiệu</th>
                            </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($topReferralsLastMonth)): ?>
                                    <tr>
                                        <td colspan="3">Tháng trước không có ai giới thiệu</td>
                                    </tr>
                                <?php endif ?>
                                <?php foreach ($topReferralsLastMonth as $index => $referral): ?>
                                    <tr>
                                        <td><?= $index + 1 ?></td>
                                        <td>
                                            <a href="<?= base_url("company/details/$referral->company_id") ?>" target="_blank">
                                                <img src="<?= get_gravatar($referral->email) ?>" class="rounded-pill me-1" style="max-width: 24px; max-height: 24px;">
                                                <?= $referral->fullname ?>
                                            </a>
                                        </td>
                                        <td><strong><?= number_format($referral->total_uses) ?></strong></td>
                                    </tr>
                                <?php endforeach ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery-3.5.1.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery.dataTables.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.bootstrap5.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.rowReorder.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.responsive.min.js') ?>"></script>
<script src="<?= base_url('assets/notyf/notyf.min.js') ?>"></script>
<script src="<?= base_url('assets/js/app.js?v=1') ?>"></script>

<script>
    $(function() {
        const table = $('#dataTables').DataTable({
            processing: true,
            serverSide: true,
            responsive: true,
            order: [],
            pageLength: 10,
            ajax: {
                url: '<?= base_url('referral/ajax_referral_uses_list') ?>',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                },
                type: 'POST',
            },
            language: {
                sProcessing: 'Đang xử lý...',
                sLengthMenu: 'Xem _MENU_ mục',
                sZeroRecords: 'Không tìm thấy dòng nào phù hợp',
                sInfo: 'Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục',
                sInfoEmpty: 'Đang xem 0 đến 0 trong tổng số 0 mục',
                sInfoFiltered: '(được lọc từ tổng số _MAX_ mục)',
                sInfoPostFix: '',
                sSearch: 'Tìm:',
                sUrl: '',
                oPaginate: {
                    sFirst: 'Đầu',
                    sPrevious: 'Trước',
                    sNext: 'Tiếp',
                    sLast: 'Cuối'
                }
            },
            columnDefs: [
                {
                    targets: [0],
                    orderable: false,
                    visible: false,
                },
            ],
        });

        table.on('init', function() {
            $('*[type="search"][class="form-control form-control-sm"]').attr('style', 'max-width:120px');
            $('div.dataTables_filter').parent().attr('class', 'col-6');
            $('div.dataTables_length').parent().attr('class', 'col-6');
        });
    });
</script>