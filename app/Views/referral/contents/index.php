<style>
    .card .card-body p {
        margin-bottom: 0;
    }
</style>

<main class="content">
    <div class="container-fluid p-0 p-md-3">
        <div class="row row-cols-1 row-cols-md-2 g-3">
            <div class="col">
                <div class="bg-light border rounded-lg p-3">
                    <div class="row align-items-center mb-3 mt-2 mt-md-0">
                        <div class="col-auto">
                            <h3 class="mb-0">Hướng dẫn giới thiệu</h3>
                        </div>
                        <div class="col-auto ms-auto text-end">
                            <div class="dropdown d-inline-block">
                                <a href="/referral/contents/create?type=manual" class="btn btn-primary btn-sm">Thêm mới</a>
                            </div>
                        </div>
                    </div>
                    <?php if (empty($manuals)) : ?>
                        <div class="d-flex flex-column align-items-center justify-content-center py-5">
                            <i class="fas fa-book fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted mb-0">Chưa có hướng dẫn giới thiệu nào!</h4>
                        </div>
                    <?php else : ?>
                        <div class="row row-cols-1 row-cols-xl-2 g-2 g-md-3" id="sortable-manual-container">
                            <?php foreach ($manuals as $index => $manual): ?>
                                <?php $index++; ?>

                                <div class="col">
                                    <div class="h-100 card mb-0" data-id="<?= $manual->id ?>">
                                        <div class="card-header pb-0 cursor-pointer">
                                            <div class="card-actions float-end">
                                                <div class="dropdown position-relative">
                                                    <a href="#" data-bs-toggle="dropdown" data-bs-display="static" aria-expanded="false" class="">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="more-horizontal" class="lucide lucide-more-horizontal align-middle">
                                                            <circle cx="12" cy="12" r="1"></circle>
                                                            <circle cx="19" cy="12" r="1"></circle>
                                                            <circle cx="5" cy="12" r="1"></circle>
                                                        </svg>
                                                    </a>
                                                    <div class="dropdown-menu dropdown-menu-end">
                                                        <a class="dropdown-item" href="<?= base_url('referral/contents/edit/' . $manual->id) ?>">Chỉnh sửa</a>
                                                        <button class="dropdown-item text-danger" onclick="deletemanual(<?= $manual->id ?>)">Xóa</button>
                                                    </div>
                                                </div>
                                            </div>
                                            <h5 class="card-title mb-0">
                                                <?= esc($manual->title) ?>
                                                <span class="badge fs-7 <?= $manual->active ? 'bg-success' : 'bg-secondary' ?>">
                                                    <?= $manual->active ? 'Đang hiện' : 'Đã ẩn' ?>
                                                </span>
                                            </h5>
                                        </div>
                                        <div class="card-body pt-3">
                                            <?= $manual->description ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col">
                <div class="bg-light border rounded-lg p-3">
                    <div class="row align-items-center mb-3 mt-2 mt-md-0">
                        <div class="col-auto">
                            <h3 class="mb-0">Câu hỏi thường gặp</h3>
                        </div>
                        <div class="col-auto ms-auto text-end">
                            <div class="dropdown d-inline-block">
                                <a href="/referral/contents/create?type=faq" class="btn btn-primary btn-sm">Thêm mới</a>
                            </div>
                        </div>
                    </div>
                    <?php if (empty($faqs)) : ?>
                        <div class="d-flex flex-column align-items-center justify-content-center py-5">
                            <i class="fas fa-question-circle fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted mb-0">Chưa có câu hỏi thường gặp nào!</h4>
                        </div>
                    <?php else: ?>
                        <div class="row row-cols-1 g-2 g-md-3" id="sortable-faq-container">
                            <?php foreach ($faqs as $faq): ?>
                                <div class="col">
                                    <div class="card mb-0" data-id="<?= $faq->id ?>">
                                        <div class="card-header pb-0 cursor-pointer">
                                            <div class="card-actions float-end">
                                                <div class="dropdown position-relative">
                                                    <a href="#" data-bs-toggle="dropdown" data-bs-display="static" aria-expanded="false" class="">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="more-horizontal" class="lucide lucide-more-horizontal align-middle">
                                                            <circle cx="12" cy="12" r="1"></circle>
                                                            <circle cx="19" cy="12" r="1"></circle>
                                                            <circle cx="5" cy="12" r="1"></circle>
                                                        </svg>
                                                    </a>
                                                    <div class="dropdown-menu dropdown-menu-end">
                                                        <a class="dropdown-item" href="<?= base_url('referral/contents/edit/' . $faq->id) ?>">Chỉnh sửa</a>
                                                        <button class="dropdown-item text-danger" onclick="deletemanual(<?= $faq->id ?>)">Xóa</button>
                                                    </div>
                                                </div>
                                            </div>
                                            <h5 class="card-title mb-0">
                                                <?= esc($faq->title) ?>
                                                <span class="badge fs-7 <?= $faq->active ? 'bg-success' : 'bg-secondary' ?>">
                                                    <?= $faq->active ? 'Đang hiện' : 'Đã ẩn' ?>
                                                </span>
                                            </h5>
                                        </div>
                                        <div class="card-body pt-3">
                                            <?= $faq->description ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</main>

<script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery-3.5.1.js') ?>"></script>
<script src="<?= base_url('assets/notyf/notyf.min.js') ?>"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.0/Sortable.min.js"></script>
<script src="<?= base_url('assets/js/app.js?v=1') ?>"></script>

<script>
    function deletemanual(id) {
        if (!confirm('Bạn có chắc chắn muốn xóa hướng dẫn giới thiệu này không?')) {
            return;
        }

        $.ajax({
            url: '<?= base_url('referral/contents/delete') ?>' + '/' + id,
            type: 'POST',
            data: {
                _method: 'DELETE',
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
            },
            success: function(response) {
                if (response.status) {
                    location.reload();
                } else {
                    notyf.error(response.message);
                }
            }
        });
    }

    function initSortable(selector) {
        const sortable = new Sortable(document.getElementById(selector), {
            handle: '.card-header',
            animation: 150,
            onEnd(evt) {
                const sortedIds = Array.from(evt.from.children).map(item => item.querySelector('.card').dataset.id);
                updateSortOrder(sortedIds);
            }
        });
    }

    initSortable('sortable-manual-container');
    initSortable('sortable-faq-container');

    function updateSortOrder(sortedIds) {
        $.ajax({
            url: '<?= base_url('referral/contents/update-order') ?>',
            type: 'POST',
            data: {
                order: sortedIds,
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
            },
            success: function(response) {
                if (response.status) {
                    notyf.success('Danh sách đã được sắp xếp lại.');
                } else {
                    notyf.error(response.message);
                }
            }
        });
    }
</script>
