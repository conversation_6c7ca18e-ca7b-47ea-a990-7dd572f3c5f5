<style>
    .tox-promotion {
        display: none;
    }

    .tox-statusbar__branding {
        display: none;
    }
</style>

<main class="content">
    <div class="container-fluid p-0 p-md-3">
        <div class="row align-items-center mb-3 mt-2 mt-md-0">
            <div class="col-auto">
                <h3 class="mb-0">Thêm <?= $page_title ?> mới</h3>
            </div>
        </div>

        <div class="container mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Thông tin <?= $page_title ?>
                </div>
                <div class="card-body">
                    <?= form_open('referral/contents/create') ?>
                        <input type="hidden" name="type" value="<?= $type ?>">
                        <div class="mb-3 row">
                            <label for="title" class="col-form-label col-sm-2 text-sm-right">Tiêu đ<PERSON></label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <label for="content" class="col-form-label col-sm-2 text-sm-right">Mô tả</label>
                            <div class="col-sm-10">
                                <textarea class="form-control" id="description" name="description"></textarea>
                            </div>
                        </div>
                        <?php if ($type === 'manual'): ?>
                            <div class="mb-3 row">
                                <label for="description" class="col-form-label col-sm-2 text-sm-right">Nội dung</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" id="content" name="content"></textarea>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div class="mb-3 row">
                            <div class="col-sm-10 offset-sm-2">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="active" name="active" checked>
                                    <label class="form-check-label" for="active">Kích hoạt</label>
                                </div>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="<?= base_url('referral/contents') ?>" class="btn btn-link text-muted">Quay lại</a>
                            <button type="submit" class="btn btn-primary">Thêm mới</button>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</main>

<script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery-3.5.1.js') ?>"></script>
<script src="<?= base_url('assets/notyf/notyf.min.js') ?>"></script>
<script src="<?= base_url('assets/tinymce/tinymce.min.js') ?>"></script>
<script src="<?= base_url('assets/js/app.js?v=1') ?>"></script>

<script>
    function initTinyMCE(selector, height) {
        tinymce.init({
            selector: selector,
            height: height,
            plugins: 'preview importcss searchreplace autolink autosave save directionality code visualblocks visualchars fullscreen image link media codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount help charmap quickbars emoticons',
            image_caption: true,
            quickbars_selection_toolbar: 'bold italic | quicklink h2 h3 blockquote quickimage quicktable',
            noneditable_noneditable_class: 'mceNonEditable',
            toolbar_mode: 'sliding',
            contextmenu: 'link image imagetools table',
        });
    }

    initTinyMCE('textarea#description', 250);
    
    <?php if ($type === 'manual'): ?>
        initTinyMCE('textarea#content', 500);
    <?php endif; ?>

    $(document).ready(function() {
        $('form').submit(function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.prop('action'),
                type: 'POST',
                data: new FormData(this),
                contentType: false,
                processData: false,
                beforeSend: function() {
                    button.prop('disabled', true).prepend('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>');
                },
                success: function(response) {
                    if (response.status) {
                        notyf.success(response.message);
                        window.location.href = '<?= base_url('referral/contents/edit') ?>' + '/' + response.data.id;
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    notyf.error(xhr.responseJSON.message);
                },
                complete: function() {
                    button.prop('disabled', false).find('.spinner-border').remove();
                }
            });
        });
    });
</script>
