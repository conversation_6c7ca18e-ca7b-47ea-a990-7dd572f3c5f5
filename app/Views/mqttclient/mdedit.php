<div class="modal fade" id="editClientModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="editClientModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addDeviceModalLabel">Chỉnh sửa thông tin </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editClientForm" action="javascript:;" class="needs-validation" novalidate method="post" accept-charset="utf-8">
                <div class="modal-body">
                    <fieldset class="border p-3 mt-3">
                    <input type="text" hidden class="form-control" id="editid" name="id" placeholder="" >
                    <div class="mb-3 form-group">
                            <label for="edit_mqtt_server_id" class="form-label"><b>Server MQTT <span class="text-danger">*</span></b></label>
                            <select class="form-select" id="edit_mqtt_server_id" name="mqtt_server_id" >
                                <option data-user-mqtt="" value="">Chọn server MQTT</option>
                                <?php if(!empty($data_server)) { ?>
                                    <?php foreach($data_server as $val){ ?>
                                        <option value="<?= $val['id']?>"><?= $val['hostname']?> - <?= $val['port']?> - <?= $val['username_server']??""?></option>
                                    <?php } ?>
                                <?php } ?>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="mb-3 form-group">
                            <label for="editusername" class="form-label"><b>User Name <span class="text-danger">*</span></b></label>
                            <input type="text" class="form-control" id="editusername" name="username" placeholder="Nhập username client" >
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="mb-3 form-group">
                            <label for="editpassword" class="form-label"><b>Pass Word <span class="text-danger">*</span></b></label>
                            <input type="text" class="form-control" id="editpassword" name="password" placeholder="password client" >
                            <div class="invalid-feedback"></div>
                        </div>

                       
                    </fieldset>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary me-auto" data-bs-dismiss="modal">Đóng</button>
                    <button type="submit" class="btn btn-primary" id="saveDeviceButton">
                        <div class="spinner-border text-light loader me-2" style="width: 20px; height: 20px; display: none;" role="status"></div>
                        Cập nhật
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
