<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">

<meta name="csrf-token" content="<?= csrf_hash() ?>">
<link rel="stylesheet" href="/assets/sweealert2/sweealert2.min.css">
<script src="/assets/sweealert2/sweealert2.min.js"></script>
<!-- Initialize Bootstrap tooltips -->
 <style>
    .active-btn {
    background-color: var(--bs-btn-active-bg);
    border-color: var(--bs-btn-active-border-color);
    color: var(--bs-btn-active-color);
}
.select2-container {
    width: 100% !important; /* Đặt chiều rộng thành 100% */
}

.select2-container--default .select2-selection--single {
    height: 38px; /* Chiều cao của Select2 */
    border: 1px solid #ced4da; /* Biên giống như Bootstrap */
    border-radius: 0.375rem; /* Đường viền bo góc giống Bootstrap */
    padding: 0.375rem 0.75rem; /* Padding giống Bootstrap */
}
.select2-container--default .select2-search--dropdown .select2-search__field {
    background: #fff;
    color: black;

}
:focus-visible {
    outline: none; /* Bỏ hiệu ứng outline */
}

 </style>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        var tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(function (tooltip) {
            new bootstrap.Tooltip(tooltip);
        });
    });
</script>
<main class="content">
   <div class="container-fluid">
      <div class="card mt-3" style="width:100%">
         <div class="card-body">
            <div class="row">
               <div class="col-auto d-none d-sm-block">
                  <h3>Danh sách Client</h3>
               </div>
               <div class="col-auto ms-auto text-end row">
                              <div class="mb-2">
                                 <a href="javascipt:;" data-bs-toggle="modal" data-bs-target="#addClientModal" target="_blank" class="btn btn-primary btn-sm float-end"><i class="bi bi-plus"></i> Thêm tài khoản</a>   
                              </div>
                           </div>
            </div>
            <div class="row">
               <div class="filter-item mb-3">
                  <button class="btn btn-filter btn-outline-primary"  onclick="filterData('All')">
                  Tất cả
                  <span class="badge bg-danger" id="count-all" >0</span>
                  </button>
                  <button class="btn btn-filter  btn-outline-primary" onclick="filterData('Đã liên kết')">
                  Đã liên kết
                  <span class="badge bg-danger" id="count-approved">0</span>
                  </button>
                  <button class="btn btn-filter  btn-outline-primary"  onclick="filterData('Chưa liên kết')">
                  Chưa liên kết
                  <span class="badge bg-danger" id="count-pending" >0</span>
                  </button>
                 
               </div>
               <div class="">
                  <table class="table dt-responsive table-hover table-striped table-bordered text-muted align-middle display nowrap" id="partner_table" style="width:100%">
                     <thead class="table-light text-muted">
                        <tr class="align-middle">
                        </tr>
                     </thead>
                     <tbody>
                     </tbody>
                  </table>
               </div>
            </div>
         </div>
      </div>
   </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>
<?php include(APPPATH . 'Views/mqttclient/mdadd.php');?>
<?php include(APPPATH . 'Views/mqttclient/mdedit.php');?>
</div>
</div>
<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

<script src="<?= base_url(); ?>assets/js/jquery.validate.min.js"></script>
<script src="<?= base_url(); ?>assets/js/jquery.validate.additional-methods.min.js"></script>



<script>
   function showConfirmation(title,text) {
        return Swal.fire({
            title: title,
            text: text,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Đồng ý",
            cancelButtonText: "Trở lại",
            customClass: {
            popup: 'small-swal-popup',  // Class tùy chỉnh
            title: 'small-swal-title',
            icon: 'small-swal-icon',
            content: 'small-swal-content',
            confirmButton: 'small-swal-confirm-button',
            cancelButton: 'small-swal-cancel-button'
        }
        });
    }

   let table;
   $(document).ready(function() {
       table = $('#partner_table').DataTable({
           paging: true,
           scrollX: false,
           autoWidth: false,
           language: {
               processing: "Message khi đang tải dữ liệu",
               search: "Tìm kiếm",
               lengthMenu: "Hiển thị _MENU_",
               info: "Hiển thị trang _PAGE_ / _PAGES_",
               infoEmpty: "Hiển thị trang _PAGE_ / _PAGES_ / _TOTAL_",
               loadingRecords: "",
               infoFiltered: "(Được lọc trong  _MAX_ record dữ liệu / _TOTAL_)",
               zeroRecords: "Không tìm kiếm được giá trị",
               emptyTable: "Không có dữ liệu",
               paginate: {
                   first: " Đầu",
                   previous: " Trước",
                   next: " Sau",
                   last: " Cuối",
               },
           },
        ajax: {
            url: `/mqtt_client/all-data`,
            method: 'GET',
            dataSrc: "data",
            
        },
           columns: [{
                   data: "id",
                   title: "short",
                   className: "d-none"
               },
              
               {
                   data: null,
                   title: "Thông tin MQTT Server",
                   render: function(data, type, row, meta) {
                       
                    return `
                    <div class="d-flex flex-column">
                        <span class="fw-bold">Host server: <span class="text-primary">${row.hostname}</span></span>
                        <span class="fw-bold">User name: <span class="text-primary">${row.username_server}</span></span>
                    </div>`;
                   }
               },
               
               
               {
                    data: null,
                    title: "Thông tin MQTT Client",
                    render: function(data, type, row, meta) {
                        return `
                        <div class="d-flex flex-column">
                            <span class="fw-bold">Username Client: <span class="text-primary">${row.username}</span></span>
                            <span class="fw-bold">Password Client: <span class="text-primary">${row.password}</span></span>
                        </div>`;
                    }
                },

                {
                   data: null,
                   title: "Trạng thái",
                   render: function(data, type, row, meta) {
                        return `
                        <div class="d-flex flex-column align-items-start ${row.serial_number > 1 ?" text-success":" text-danger"}">
                            <span class="my-1">${row.type}</span>
                            <span class="my-1 fw-bold ${row.serial_number > 1 ?"":"d-none"}">S/N: <span class="">${row.serial_number}</span></span>
                            <span class="my-1 fw-bold ${row.serial_number > 1 ?"":"d-none"}">Vendor: <span class="">${row.vendor}</span></span>
                        </div>`;
                   }
               },
             
              
               {
                data: null,
                title: "Tác vụ",
                className: "w-fix",
                render: function(data, type, row, meta) {
                    // Tạo nội dung HTML cho ô <td>
                    return `
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-soft-danger btn-sm dropdown bg-danger text-white fw-bold" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi-three-dots-vertical align-middle"></i> <!-- BS5 icon for more options -->
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li class="cursor-pointer" onclick="editClient('${row.id}')">
                                    <span class="dropdown-item edit-item-btn">
                                        <i class="bi-pencil align-bottom me-2 text-muted"></i> Sửa <!-- BS5 pencil icon -->
                                    </span>
                                </li>
                                <li class="cursor-pointer ${row.serial_number > 1 ? "d-none" : ""}" onclick="removeClient('${row.id}')">
                                    <span class="dropdown-item edit-item-btn">
                                        <i class="bi-trash align-bottom me-2 text-muted"></i> Xóa <!-- BS5 trash icon -->
                                    </span>
                                </li>
                            </ul>
                        </div>                              
                    `;
                }
            },

           ],
           order: [
               [0, 'desc']
           ], // Sắp xếp theo cột ID từ lớn đến bé
          
       });
          // Cập nhật đếm số bản ghi khi bảng khởi tạo
        table.on('init.dt', function() {
            let data = table.ajax.json().data;
            updateRecordCounts(); 
       
        });

        // render User MQTT

        
       
   });

   

  
  
   function filterData(str){
       // Loại bỏ class active-btn từ tất cả các nút
       $('.btn-filter').removeClass('active-btn');
   
       // Thêm class active-btn vào nút hiện tại
       $(event.target).addClass("active-btn");
   
       if(str == "All"){
           table.search('').columns().search('').draw();
           return;
       }
       
       if(str == "Đã liên kết"){
           table.column(3).search('Đã liên kết').draw();
           return;
       }
       if(str == "Chưa liên kết"){
           table.column(3).search('Chưa liên kết').draw();
           return;
       }
       table.search('').columns().search('').draw();
   }
   
   async function removeClient(id){
        const confirm = await showConfirmation("Bạn có chắc chắn xóa ?","Mọi thông tin liên quan sẽ bị xóa!")
        
        if(confirm.isConfirmed){
            $.post(`/mqtt_client/delete/${id}`,function(res){
                console.log(res);
                
                if(res.code==200){
                    notyf.success({
                        message:"Xóa thành công!"
                    })
                    reloadTable()
                }else{
                    notyf.error({
                        message:"Lỗi xóa hãy liên hệ kỹ thuật!"
                    })
                }
            })
        }
   }
 

   function editClient(id){
        $.get(`/mqtt_client/edit/${id}`,function(res){
                if(res.code==200){
                    console.log(res.data);
                    if(res.data.length==0){
                        notyf.error({
                        message:"Lỗi lấy dữ liệu thiết bị!"
                        })
                        return
                    }
                    renderDataEdit(res.data)
                    
                }else{
                    notyf.error({
                        message:"Lỗi lấy dữ liệu thiết bị!"
                    })
                }
        })
   }

   function renderDataEdit(data){
        
        $("#editClientModal").modal("show");
        // Gán dữ liệu vào các input tương ứng
       
        $("#editusername").val(data.username); // Model thiết bị
        $("#editpassword").val(data.password); // Vendor thiết bị
        $("#editid").val(data.id); // Vendor thiết bị

        // Set value cho select2 cho MQTT Server
        $("#edit_mqtt_server_id").val(data.mqtt_server_id).trigger('change'); // Chọn giá trị trong Select2

   }
   
</script>
<!-- validate modal add -->
<script>
$(document).ready(function(){
   
    // Lấy CSRF token từ meta tag
    let csrfToken = $('meta[name="csrf-token"]').attr('content');
             
    // Cấu hình jQuery để thêm CSRF token vào tất cả các yêu cầu AJAX
        $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': csrfToken
        }
    });
    // Validate add device form
    $("#addClientForm").validate({
        errorElement: 'div',
        rules: {
            
            "username": {
                "required": true,
                "maxlength": 80
            },
            "password": {
                "required": true,
                "maxlength": 80
            },
            "mqtt_server_id": {
                "required": true
            },
           
        },
        messages: {
          
            username: {
                required: 'Username thiết bị là bắt buộc',
                maxlength: 'Username thiết bị không vượt quá 80 ký tự'
            },
            password: {
                required: 'Password thiết bị là bắt buộc',
                maxlength: 'Password thiết bị không vượt quá 80 ký tự'
            },
            mqtt_server_id: {
                required: 'Vui lòng chọn Server MQTT'
            },
           
        },
        highlight: function(input) {
            $(input).addClass('is-invalid');
        },
        unhighlight: function(input) {
            $(input).removeClass('is-invalid');
        },
        errorPlacement: function(error, input) {
            // Append error inside the invalid-feedback div
            $(input).siblings('.invalid-feedback').append(error);
        },
        submitHandler: function(form, e) {
            // Prevent default form submission
            e.preventDefault();

            // Call function to save data
            saveData(form, e);
        }
    });
    // Validate add device form
    $("#editClientForm").validate({
        errorElement: 'div',
        rules: {
            
            "username": {
                "required": true,
                "maxlength": 80
            },
            "password": {
                "required": true,
                "maxlength": 80
            },
            "mqtt_server_id": {
                "required": true
            },
           
        },
        messages: {
          
            username: {
                required: 'Username thiết bị là bắt buộc',
                maxlength: 'Username thiết bị không vượt quá 80 ký tự'
            },
            password: {
                required: 'Password thiết bị là bắt buộc',
                maxlength: 'Password thiết bị không vượt quá 80 ký tự'
            },
            mqtt_server_id: {
                required: 'Vui lòng chọn Server MQTT'
            },
           
        },
        highlight: function(input) {
            $(input).addClass('is-invalid');
        },
        unhighlight: function(input) {
            $(input).removeClass('is-invalid');
        },
        errorPlacement: function(error, input) {
            // Append error inside the invalid-feedback div
            $(input).siblings('.invalid-feedback').append(error);
        },
        submitHandler: function(form, e) {
            // Prevent default form submission
            e.preventDefault();

            // Call function to save data
            saveDataEdit(form, e);
        }
    });

    
});

// Hàm cập nhật số lượng bản ghi
function updateRecordCounts() {
    let data = table.ajax.json().data;
    // Loại bỏ class active-btn từ tất cả các nút
    $('.btn-filter').removeClass('active-btn');

    // Thêm class active-btn vào nút hiện tại
    $(event.target).addClass("active-btn");
    // Lọc ra các bản ghi có trạng thái "Pending"
    let pendingCount = data.filter(function(record) {
        return record.type === 'Chưa liên kết';
    }).length;

    // Lọc ra các bản ghi có trạng thái "Approved"
    let approvedCount = data.filter(function(record) {
        return record.type === 'Đã liên kết';
    }).length;

    // Tổng số bản ghi
    let totalCount = data.length;

    $("#count-all").text(totalCount);         // Số lượng tất cả
    $("#count-approved").text(approvedCount);  // Số lượng Approved
    $("#count-pending").text(pendingCount);    // Số lượng Pending
}

// Hàm reloadTable (giả định)
function reloadTable() {
    table.ajax.reload(function() {
        updateRecordCounts();  // Cập nhật lại số lượng bản ghi sau khi reload
    });
}


function saveData(form, e) {
    const btn = $(e.originalEvent.submitter)
    btn.prop('disabled', true)
    btn.find('.loader').show();
    
    
    $.ajax({
        url: "/mqtt_client/add",
        type: "POST",
        dataType: "JSON",
        data: $(form).serialize(), // Serialize form data
        success: function(res, textStatus, jqXHR) {
            if (res.code == 200) {
                console.log(res.message);
                notyf.success({
                    message: res.message,
                })
                $('#addClientModal').modal('hide');
            }
            reloadTable()
           

        },
        error: function(err, textStatus, errorThrown) {
            if (err.status == 403) {
                notyf.error({
                    message: "Hãy nhấn F5 tải lại trang để thử lại!"
                })
            }

            if (err.status == 404) {
                notyf.error({
                    message: err.responseJSON.message
                })

            }
            console.log(err.responseJSON.message);

            if (err.status == 422) {
                notyf.error({
                    message: err.responseJSON.data.name || "Lỗi dữ liệu!"
                })
            }
            if (err.status == 423) {
                notyf.error({
                    message: err.responseJSON.message || "Lỗi dữ liệu!"
                })
            }

            if (err.status == 500) {
                notyf.error({
                    message: "Lỗi hệ thống, Hãy liên hệ kỹ thuật!"
                })

            }
        },
        complete: (xhr, textStatus) => {
            let newCsrfToken = xhr.getResponseHeader('X-CSRF-TOKEN');

            if (newCsrfToken) {
                updateCsrfToken(newCsrfToken);
            }
            btn.prop('disabled', false)
            btn.find('.loader').hide();

        }
    })
}


function saveDataEdit(form, e) {
    const btn = $(e.originalEvent.submitter)
    btn.prop('disabled', true)
    btn.find('.loader').show();
    
    
    $.ajax({
        url: "/mqtt_client/update",
        type: "POST",
        dataType: "JSON",
        data: $(form).serialize(), // Serialize form data
        success: function(res, textStatus, jqXHR) {
            if (res.code == 200) {
                console.log(res.message);
                notyf.success({
                    message: res.message,
                })
                $('#editClientModal').modal('hide');
            }
            reloadTable()
           

        },
        error: function(err, textStatus, errorThrown) {
            if (err.status == 403) {
                notyf.error({
                    message: "Hãy nhấn F5 tải lại trang để thử lại!"
                })
            }

            if (err.status == 404) {
                notyf.error({
                    message: err.responseJSON.message
                })

            }
            console.log(err.responseJSON.message);

            if (err.status == 422) {
                notyf.error({
                    message: err.responseJSON.data.name || "Lỗi dữ liệu!"
                })
            }
            if (err.status == 423) {
                notyf.error({
                    message: err.responseJSON.message || "Lỗi dữ liệu!"
                })
            }

            if (err.status == 500) {
                notyf.error({
                    message: "Lỗi hệ thống, Hãy liên hệ kỹ thuật!"
                })

            }
        },
        complete: (xhr, textStatus) => {
            let newCsrfToken = xhr.getResponseHeader('X-CSRF-TOKEN');

            if (newCsrfToken) {
                updateCsrfToken(newCsrfToken);
            }
            btn.prop('disabled', false)
            btn.find('.loader').hide();

        }
    })
}


function updateCsrfToken(csrfToken) {
    $('meta[name="csrf-token"]').attr('content', csrfToken);
    $.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': csrfToken
    }
    });
} 
</script>