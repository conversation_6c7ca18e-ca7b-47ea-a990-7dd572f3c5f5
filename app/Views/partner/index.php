 
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
        <main class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-md-4 col-6 d-flex">
                        <div class="card flex-fill">
                            <div class="card-body  text-center">
                                <div>Tổng hoa hồng <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Số dư đã được duyệt" data-bs-original-title="" title=""></i>
                                </div>
                                <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_commission);?> đ</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-6 d-flex">
                        <div class="card flex-fill">
                            <div class="card-body  text-center">
                                <div>Hoa hồng đã rút <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Hoa hồng đã chuyển khoản cho đối tác" data-bs-original-title="" title=""></i>
                                </div>
                                <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_withdraw);?> đ</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-6 d-flex">
                        <div class="card flex-fill">
                            <div class="card-body  text-center">
                                <div>Hoa hồng có thể rút <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Tổng hoa hồng đối tác có thể rút" data-bs-original-title="" title=""></i>
                                </div>
                                <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($pending_withdraw_commission);?> đ</span>
                            </div>
                        </div>
                    </div>
                </div>
              
                <div class="card mt-3" style="width:100%">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-auto d-none d-sm-block">
                                <h3>Danh sách đối tác</h3>
                            </div>

                            <div class="col-auto ms-auto text-end row">
                                <div class="mb-2">
                                    <a href="https://doitac.sepay.vn/register" target="_blank" class="btn btn-primary btn-sm float-end"><i class="bi bi-plus"></i> Thêm đối tác</a>   

                                </div>

                            
                            </div>
                        </div>
                         
                        <div class="row">
                           
                           
                                <div class="">
                                <table class="table table-hover table-striped table-bordered text-muted align-middle display nowrap" id="partner_table" style="width:100%">
                                    <thead class="table-light text-muted">
                                        <tr class="align-middle">
                                            <th></th>
                                            <th>ID</th>
                                            <th>Tên</th>
                                            <th>Email</th>
                                            <th>SĐT</th>
                                            <th>Giới thiệu</th>
                                            <th>HH đã duyệt</th>
                                            <th>HH đã rút</th>
                                            <th>Số dư</th>
                                            <?php if(has_permission('LoginAsPartner', 'can_view_all')) { ?>
                                            <th>Login</th>
                                            <?php } ?>
                                            <th>Tạo lúc</th>

                                         </tr>
                                    </thead>
                                    <tbody>
                
                                       
                                    </tbody>
                                </table>
                                </div>
                             
                            
                        </div>
                    </div>
                </div>
                
            </div>
        </main>

        <?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>


        </div>
    </div>


  

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>

<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
    
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

<script>



$(document).ready(function() {

var  table = $('#partner_table').DataTable({ 
   rowReorder: {
       selector: 'td:nth-child(0)'
   },
   responsive: true,


    "processing": true,
    "serverSide": true,
    "order": [],
    "pageLength": 20,

    "ajax": {
        "url": "<?php echo base_url('partner/ajax_list'); ?>",
        "data": {"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
        "type": "POST"
    },


    "language": {
        "sProcessing":   "Đang xử lý...",
        "sLengthMenu":   "Xem _MENU_ mục",
        "sZeroRecords":  "Không tìm thấy dòng nào phù hợp",
        "sInfo":         "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
        "sInfoEmpty":    "Đang xem 0 đến 0 trong tổng số 0 mục",
        "sInfoFiltered": "(được lọc từ _MAX_ mục)",
        "sInfoPostFix":  "",
        "sSearch":       "Tìm:",
        "sUrl":          "",
        "oPaginate": {
            "sFirst":    "Đầu",
            "sPrevious": "Trước",
            "sNext":     "Tiếp",
            "sLast":     "Cuối"
        }
    },

    "columnDefs": [
       { responsivePriority: 1, targets: 1 },
       { responsivePriority: 2, targets: 2 },
       
        { 
            "visible": false,
            "targets": [ 0 ],
            "orderable": false,
        },

        { 
            "targets": [ 5,6],
            "orderable": false,
        } 
        
    ],

    
});

table.on ('init', function () {
   $('*[type="search"][class="form-control form-control-sm"]').attr('style','max-width:120px');
   $('div.dataTables_filter').parent().attr('class','col-6');
   $('div.dataTables_length').parent().attr('class','col-6');
});


});


</script>