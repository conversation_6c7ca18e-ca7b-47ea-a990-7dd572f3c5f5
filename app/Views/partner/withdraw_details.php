 
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
        <main class="content">
            <div class="container-fluid">
              
                <div class="card mt-3" style="width:100%">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-auto d-none d-sm-block">
                                <h3>Rút tiền</h3>
                            </div>

                           
                        </div>
                         
                        <div class="row">
                            <div class="col-md-8">
                            <table class="table">
                                    <tbody>
                                        <tr>
                                            <td style="width:30%" class="fw-bold">ID yêu cầu:</td>
                                            <td>#<?= esc($withdraw_details->id);?></td>
                                        </tr>
                                        <tr>
                                            <td style="width:30%" class="fw-bold">Đối tác:</td>
                                            <td>#<?= esc($withdraw_details->partner_id .' ' . $partner_details->lastname . ' ' . $partner_details->firstname);?>
                                        <?php if($partner_details->active == 0) { ?> <span class='text-danger'>Cảnh báo: Tài khoả đối tác này đã bị tạm khoá</span> <?php } ?>
                                        </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Số tiền:</td>
                                            <td class="text-success"><?= number_format(esc($withdraw_details->amount));?>đ</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Tài khoản nhận tiền:</td>
                                            <td><?= nl2br(esc($withdraw_details->body));?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Trạng thái xử lý:</td>
                                            <td class=""><?php if($withdraw_details->status == "Completed")
                $status = "<span class='text-success'>Hoàn tất</span>";
            else if($withdraw_details->status == "Pending")
                $status = "<span class='text-danger'>Chờ xử lý</span>";
            else if($withdraw_details->status == "InProgress")
                $status = "<span class='text-warning'>Đang xử lý</span>";
            else if($withdraw_details->status == "Rejected")
                $status = "<span class='text-secondary'>Bị từ chối</span>";
            else if($withdraw_details->status == "Closed")
                $status = "<span class='text-secondary'>Đã đóng</span>";
            else
                $status = esc($withdraw_details->status); echo $status; ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Hành động:</td>
                                            <td><a class="btn btn-warning me-4" onclick="edit_withdraw()"><i class="bi bi-pencil"></i> Sửa</a> <?php if($withdraw_details->status == "Pending" || $withdraw_details->status == "InProgress") { ?> <a class="btn btn-success me-2" onclick="make_withdraw_complete()"><i class="bi bi-check2-circle"></i> Đánh dấu đã chuyển tiền</a> <?php } ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Trả lời từ admin:</td>
                                            <td><?= nl2br(esc($withdraw_details->admin_answer));?></td>
                                        </tr>
                                    </tbody>
                                    
                                </table>
                             
</div>
<div class="col-md-4">
<div class="p-2 border rounded border-info">
                                    <h4 class="mt-3 ms-2">Hướng dẫn</h4>
                                    <ul class="lh-lg">
                                        <li>Chọn <b>Đánh dấu đã chuyển tiền</b> nếu đã chuyển tiền xong cho đối tác.</li>
                                        <li>Set trạng thái sang <b>Đóng</b> nếu huỷ yêu cầu</li>
                                        <li>Chọn <b>Sửa</b> nếu muốn sửa số tiền hoặc điền câu trả lời cho khách hàng.</li>

                                    </ul>

                                </div>


</div>
                        </div>
                    </div>
                </div>
                
            </div>
        </main>

        <?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>


        </div>
    </div>


  
<!-- Modal -->
<div class="modal fade" id="withdrawAddModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="withdrawAddModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="withdrawAddModalLabel">Sửa yêu cầu Rút tiền</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <?php echo form_open('',"id='withdraw_add_form' class='needs-validation form-add-withdraw' novalidate");?>
        <input type="hidden"name="id" value="">
        <div class="modal-body m-lg-3">

                  

                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Số tiền <span class="text-danger">(*)</span></label>
                        <input type="number" class="form-control withdraw_phonenumber" name="amount" placeholder="" required>
                    </div>
                
                    
                    <div class="mb-3">
                        <label for="exampleFormControlInput2" class="form-label">Tài khoản nhận tiền <span class="text-danger">(*)</span></label>
                        <textarea class="form-control" name="body" rows="3"></textarea>
                    </div>


                  
                    <div class="mb-3">
                        <label for="exampleFormControlInput2" class="form-label">Trả lời từ admin</label>
                        <textarea class="form-control" name="admin_answer" rows="3"></textarea>
                    </div>

                  

                    <div class="mb-3">
                        <label class="form-label"><b>Trạng thái xử lý</b></label>
                        <select class="form-select active" name="status" aria-label="status" required>
                            <option value="Pending">Chờ</option>
                            <option value="InProgress">Đang xử lý</option>
                            <?php if($withdraw_details->status =="Completed") { ?>
                            <option value="Completed">Hoàn tất</option>
                            <?php } ?>
                            <option value="Closed">Đóng</option>
                        
                        </select>
                    </div>
                 

                  
                  
        
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary me-3" data-bs-dismiss="modal">Đóng</button>
                <a class="btn btn-primary btn-withdraw-add" onclick="save()">Cập nhật</a>
            </div>
        </form>
        </div>
    </div>
</div>
<!-- Modal -->

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>

<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
    
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

<script>

var addWithdrawModal = new bootstrap.Modal(document.getElementById('withdrawAddModal'), {
        keyboard: false
    });

function edit_withdraw() {
  
        save_method = 'update';

        //Ajax Load data from ajax
        $.ajax({
          url : "<?php echo base_url('partner/ajax_get_withdraw/' . $withdraw_details->id);?>",
          type: "GET",
          dataType: "JSON",
          success: function(data)
          {
            if(data.status == true) {
                $('[name="id"]').val(data.data.id);
                $('[name="amount"]').val(parseInt(data.data.amount));
 
                $('[name="body"]').val(data.data.body);
                $('[name="status"]').val(data.data.status);
                $('[name="admin_answer"]').val(data.data.admin_answer);
                $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                $(".btn-withdraw-add").html('Cập nhật');
                addWithdrawModal.show();

            } else {
                alert('Lỗi: ' + data.message);
            }
  
            
          },
          error: function (jqXHR, textStatus, errorThrown)
          {
              alert('Lỗi không thể lấy được thông tin');
          }
      });
} 


function make_withdraw_complete()
    {

        if (confirm("Bạn có chắc chắn ĐÃ CHUYỂN TIỀN cho đối tác này không?") == true) {

            url = "<?php echo base_url('partner/ajax_make_withdraw_complete');?>";
        
           
            $.ajax({
                url : url,
                type: "POST",
                data: {id: <?php echo $withdraw_details->id;?>, "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
                dataType: "JSON",
                success: function(data)
                {
                    //if success close modal and reload ajax table
                    if(data.status == true) {    
                        location.reload();
                    } else {
                       
                        alert('Lỗi: ' + data.message);
    
                    }
                
                },
                error: function (jqXHR, textStatus, errorThrown)
                {
                    alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                    
                }
                
            });
        }
    }




    function save()
    {

       
        url = "<?php echo base_url('partner/ajax_withdraw_update');?>";
       
        $(".btn-withdraw-add").attr("disabled", true);
        $('#btn_loading').html('');
        $(".btn-withdraw-add").html('<div class="spinner-border text-light" role="status" id="btn_loading"></div>');

        $.ajax({
            url : url,
            type: "POST",
            data: $('#withdraw_add_form').serialize(),
            dataType: "JSON",
            success: function(data)
            {
                //if success close modal and reload ajax table
                if(data.status == true) {
                    addWithdrawModal.hide();
 
                     location.reload();
                } else {
                    
                    $(".btn-withdraw-add").attr("disabled", false);
                    $('#btn_loading').remove();
                    $(".btn-withdraw-add").html('Cập nhật');
                    alert('Lỗi: ' + data.message);
 
                }
            
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                $(".btn-withdraw-add").attr("disabled", false);
                $('#btn_loading').remove();
                $(".btn-withdraw-add").html('Cập nhật');
            }
            
        });
    }


</script>