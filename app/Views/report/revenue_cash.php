<main class="content">
    <div class="container-fluid">
        

        <div class="row">
            <div class="col-md-12 d-flex">

                <div class="card flex-fill w-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Doanh thu không phân bổ năm <?php echo $year;?></h5>

                    </div>
                    <div class="card-body d-flex table-responsive">
                        
                    <div class="col-xl-12">

                    <?php 
                    $total_new_revenue = 0;
                    $total_recurring_revenue = 0;
                    $total_physical_product_revenue = 0;
                    ?>
<div class="card-box">
    <h4 class="header-title m-t-0 m-b-30"></h4>
    <div class="table-responsive">
    <table class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">
        <thead>
            <tr> 
                <th colspan="2"></th>
                <?php foreach($revenue as $key=>$value) { ?>
                <th>T<?php echo $key;?></th>
                <?php } ?>
            </tr>
            
        </thead>
        <tbody>
            <tr>
                <td rowspan="3"><b>New</b></td>
                <td>Monthly Revenue</td>    
                <?php foreach($revenue as $key=>$value) { 
                    $total_new_revenue = $total_new_revenue + $value['new'];
                    ?>
                <td  class="fw-bold"><?php echo number_format($value['new']);?></td>
                <?php } ?> 
                
            </tr>
            <tr>
               <td>Invoice</td>   
               <?php foreach($revenue as $key=>$value) { 
                    ?>
                <td><?php echo number_format($value['new_invoice_count']);?></td>
                <?php } ?> 
            </tr>
            <tr>
                <td>Avg Revenue/ Invoice</td>    
                <?php foreach($revenue as $key=>$value) { 
                    ?>
                <td><?php if($value['new_invoice_count'] > 0) echo number_format($value['new']/ $value['new_invoice_count']); else echo 0; ?></td>
                <?php } ?> 
            </tr>

            <tr>
                <td rowspan="3"><b>Recurring</b></td>
                <td>Monthly Revenue</td>     
                <?php foreach($revenue as $key=>$value) { 
                    $total_recurring_revenue = $total_recurring_revenue + $value['recurring'];
                    ?>
                <td  class="fw-bold"><?php echo number_format($value['recurring']);?></td>
                <?php } ?> 
            </tr>
            <tr>
               <td>Invoice</td>   
               <?php foreach($revenue as $key=>$value) { 
                    ?>
                <td><?php echo number_format($value['recurring_invoice_count']);?></td>
                <?php } ?> 
            </tr>
            <tr>
                <td>Avg Revenue/ Invoice</td>    
                <?php foreach($revenue as $key=>$value) { 
                    ?>
                <td><?php if($value['recurring_invoice_count'] > 0) echo number_format($value['recurring']/ $value['recurring_invoice_count']); ?></td>
                <?php } ?> 
            </tr>

            <tr>
                <td rowspan="3"><b>Physical Product</b></td>
                <td>Monthly Revenue</td>     
                <?php foreach($revenue as $key=>$value) { 
                    $total_physical_product_revenue = $total_physical_product_revenue + $value['physical_product'];
                    ?>
                <td  class="fw-bold"><?php echo number_format($value['physical_product']);?></td>
                <?php } ?> 
            </tr>
            <tr>
               <td>Invoice</td>   
               <?php foreach($revenue as $key=>$value) { 
                    ?>
                <td><?php echo number_format($value['physical_product_invoice_count']);?></td>
                <?php } ?> 
            </tr>
            <tr>
                <td>Avg Revenue/ Invoice</td>    
                <?php foreach($revenue as $key=>$value) { 
                    ?>
                <td><?php if($value['physical_product_invoice_count'] > 0) echo number_format($value['physical_product']/ $value['physical_product_invoice_count']); else echo 0; ?></td>
                <?php } ?> 
            </tr>

            <tr>
                <td rowspan="3"><b>Total</b></td>
                <td>Monthly Revenue</td>     
                <?php foreach($revenue as $key=>$value) { 
                    ?>
                <td class="fw-bold text-danger"><?php echo number_format($value['new'] + $value['recurring'] + $value['physical_product']);?></td>
                <?php } ?> 
            </tr>
            <tr>
               <td>Invoice</td>   
               <?php foreach($revenue as $key=>$value) { 
                    ?>
                <td><?php echo number_format($value['recurring_invoice_count'] + $value['new_invoice_count'] + $value['physical_product_invoice_count']);?></td>
                <?php } ?> 
            </tr>
            <tr>
                <td>Avg Revenue/ Invoice</td>    
                <?php foreach($revenue as $key=>$value) { 
                    ?>
                <td><?php if(($value['recurring_invoice_count'] + $value['new_invoice_count'] + $value['physical_product_invoice_count']) > 0 ) echo number_format(($value['new'] + $value['recurring'] + $value['physical_product'])/ ($value['recurring_invoice_count'] + $value['new_invoice_count'] + $value['physical_product_invoice_count']));?></td>
                <?php } ?> 
            </tr>
           
           
        </tbody>
    </table>

    <ul class="mt-5">
        <li>Total New Invoice: <b class="text-danger"><?php echo number_format(array_sum(array_column($revenue, 'new_invoice_count'))); ?></b></li>
        <li>Total New: <b class="text-danger"><?= number_format($total_new_revenue);?></b></li>
        <li>Total Recurring: <b class="text-danger"><?= number_format($total_recurring_revenue);?></b></li>
        <li>Total Physical Product Invoice: <b class="text-danger"><?php echo number_format(array_sum(array_column($revenue, 'physical_product_invoice_count'))); ?></b></li>
        <li>Total Physical Product: <b class="text-danger"><?= number_format($total_physical_product_revenue);?></b></li>
        <li>Total: <b class="text-danger"><?php echo number_format($total_new_revenue + $total_recurring_revenue + $total_physical_product_revenue);?></b></li>
    </ul>

    </div>

</div>

</div>


                    </div>

                </div>

            </div>
            <div class="col-md-5 d-flex">
             
            </div>
        </div>


    </div>
    <div class="mt-5"></div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>


<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js"></script>
<script>

</script>
