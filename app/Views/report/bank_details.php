<main class="content">
    <div class="container-fluid">
        <div class="row">
             

            <div class="col-md-3 col-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body  text-center">
                        <div>Tài khoản ngân hàng <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng tài khoản ngân hàng đã kết nối"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_bank_account_connected);?>
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-md-3 col-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body  text-center">
                        <div>Số lượng giao dịch <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng số lượng giao dịch tiền vào lẫn tiền ra"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($count_trans);?>
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-md-3 col-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body  text-center">
                        <div>GD tiền vào <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng giao dịch tiền vào"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($sum_amount_in);?>đ
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-md-3 col-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body  text-center">
                        <div>GD tiền ra <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng giao dịch tiền ra"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($sum_amount_out);?>đ
                        </span>
                    </div>
                </div>
            </div>

             
            <div class="col-md-3 col-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body  text-center">
                        <div>TK ngân hàng 30 ngày <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="TK ngân hàng liên kết 30 ngày gần đây"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_bank_account_connected_30days);?>
                        </span>
                    </div>
                </div>
            </div>


            <div class="col-md-3 col-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body  text-center">
                        <div>SL giao dịch 30 ngày gần đây<i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng số lượng giao dịch tiền vào lẫn tiền ra 30 ngày gần nhất"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($count_trans_30days);?>
                        </span>
                    </div>
                </div>
            </div>


            <div class="col-md-3 col-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body  text-center">
                        <div>GD tiền vào 30 ngày gần đây<i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng giao dịch tiền vào 30 ngày gần nhất"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($sum_amount_in_30days);?>đ
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-md-3 col-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body  text-center">
                        <div>GD tiền ra 30 ngày gần đây<i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng giao dịch tiền ra 30 ngày gần nhất"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($sum_amount_out_30days);?>đ
                        </span>
                    </div>
                </div>
            </div>


        </div>

        <div class="row">
            <div class="col-md-12 d-flex">

                <div class="card flex-fill w-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">TK <?= esc($bank_details->brand_name);?> gần đây</h5>

                    </div>
                    <div class="card-body d-flex table-responsive">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Số TK</th>
                                    <th>Chủ TK</th>
                                    <th>GD gần nhất</th>
                                    <th>Tạo lúc</th>
                                </tr>
                            </thead>
                            <tbody>
                                
                            <?php foreach($recent_banks as $bank):?>

                                <tr>
                                    <td><img class="img-fluid me-3" style="width:30px" src="https://my.sepay.vn/assets/images/banklogo/<?php echo $bank_details->icon_path;?>"><?= esc($bank->account_number);?></td>
                                    <td><a href="<?php if($bank->company_id > 0) echo base_url('company/details/' . $bank->company_id); else if($bank->merchant_id > 0) echo base_url('merchant/details/' . $bank->merchant_id);?>"><?= esc($bank->account_holder_name);?></a></td>
                                    <td><?= esc($bank->last_transaction);?></td>
                                    <td><?= esc($bank->created_at);?></td>

                                </tr>

                            <?php endforeach;?>


                            </tbody>
                        </table>

                    </div>

                </div>

            </div>
            
        </div>


    </div>
    <div class="mt-5"></div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>


<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js"></script>
<script>

</script>