<main class="content">
    <div class="container-fluid">
        

        <div class="row">
            <div class="col-md-12 d-flex">

                <div class="card flex-fill w-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Doanh thu phân bổ năm <?php echo $year;?></h5>

                    </div>
                    <div class="card-body d-flex table-responsive">
                        
                    <div class="col-xl-12">

                    <?php 
                    $total_new_revenue = 0;
                    $total_recurring_revenue = 0;
                    ?>
<div class="card-box">
    <h4 class="header-title m-t-0 m-b-30"></h4>
    <div class="table-responsive">
    <table class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">
        <thead>
            <tr> 
                <th colspan="2"></th>
                <?php foreach($revenue as $key=>$value) { ?>
                <th>T<?php echo $key;?></th>
                <?php } ?>
            </tr>
            
        </thead>
        <tbody>
            <tr>
                <td rowspan="3"><b>New</b></td>
                <td>Monthly Revenue</td>    
                <?php foreach($revenue as $key=>$value) { 
                    $total_new_revenue = $total_new_revenue + $value['new'];
                    ?>
                <td  class="fw-bold"><?php echo number_format($value['new']);?></td>
                <?php } ?> 
                
            </tr>
            <tr>
               <td>Subscription</td>   
                
            </tr>
            <tr>
                <td>Avg Revenue/ Subscription</td>    
                
            </tr>

            <tr>
                <td rowspan="3"><b>Recurring</b></td>
                <td>Monthly Revenue</td>     
                <?php foreach($revenue as $key=>$value) { 
                    $total_recurring_revenue = $total_recurring_revenue + $value['recurring'];
                    ?>
                <td  class="fw-bold"><?php echo number_format($value['recurring']);?></td>
                <?php } ?> 
            </tr>
            <tr>
               <td>Subscription</td>   
               
            </tr>
            <tr>
                <td>Avg Revenue/ Subscription</td>    
                
            </tr>

            <tr>
                <td rowspan="3"><b>Total</b></td>
                <td>Monthly Revenue</td>     
                <?php foreach($revenue as $key=>$value) { 
                    ?>
                <td  class="fw-bold text-danger"><?php echo number_format($value['new'] + $value['recurring']);?></td>
                <?php } ?> 
            </tr>
            <tr>
               <td>Subscription</td>   
                
            </tr>
            <tr>
                <td>Avg Revenue/ Subscription</td>    
                 
            </tr>
           
           
        </tbody>
    </table>

    <ul class="mt-5">
        <li>Total New Subscription: <b class="text-danger"></b></li>
        <li>Total New: <b class="text-danger"><?= number_format($total_recurring_revenue);?></b></li>
        <li>Total Recurring: <b class="text-danger"><?= number_format($total_new_revenue);?></b></li>
        <li>Total: <b class="text-danger"><?php echo number_format($total_new_revenue + $total_recurring_revenue);?></b></li>
    </ul>

    </div>

</div>

</div>


                    </div>

                </div>

            </div>
            <div class="col-md-5 d-flex">
             
            </div>
        </div>


    </div>
    <div class="mt-5"></div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>


<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js"></script>
<script>

</script>
