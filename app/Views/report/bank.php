<main class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-3 col-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body  text-center">
                        <div>Tổng số dư <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Monthly Recurring Revenue"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_accumulated);?> đ</span>
                    </div>
                </div>
            </div>

          

            <div class="col-md-3 col-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body  text-center">
                        <div>Số lượng giao dịch <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng số lượng giao dịch ngân hàng"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_bank_transactions);?> </span>
                    </div>
                </div>
            </div>
 

            <div class="col-md-3 col-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body  text-center">
                        <div>Tài khoản ngân hàng <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng tài khoản ngân hàng đã kết nối"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3"><?= number_format($total_bank_account_connected);?>
                        </span>
                    </div>
                </div>
            </div>

             


        </div>

        <div class="row">
            <div class="col-md-6 d-flex">

                <div class="card flex-fill w-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Số dư theo ngân hàng</h5>

                    </div>
                    <div class="card-body d-flex table-responsive">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Ngân hàng</th>
                                    <th>Số dư</th>
                                </tr>
                            </thead>
                            <tbody>
                                
                            <?php foreach($top_banks_accumulated as $bank):?>

                                <tr>
                                    <td><img class="img-fluid me-3" style="width:30px" src="https://my.sepay.vn/assets/images/banklogo/<?php echo $bank->icon_path;?>"><?= esc($bank->brand_name);?></td>
                                    <td><?= number_format($bank->total_accumulated);?> đ</td>

                                </tr>

                            <?php endforeach;?>


                            </tbody>
                        </table>

                    </div>

                </div>

            </div>
            <div class="col-md-5 d-flex">
             
            </div>
        </div>


    </div>
    <div class="mt-5"></div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>


<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js"></script>
<script>

</script>