<main class="content">
    <div class="container-fluid">
        

        <div class="row">
            <div class="col-md-12 d-flex">

                <div class="card flex-fill w-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Số dư theo tà<PERSON></h5>

                    </div>
                    <div class="card-body d-flex table-responsive">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Kh<PERSON>ch hàng</th>
                                    <th>Tài khoản</th>
                                    <th><PERSON><PERSON> hàng</th>
                                    <th>Số dư</th>
                                </tr>
                            </thead>
                            <tbody>
                                
                            <?php foreach($results as $account):
                                
                               ?>

                                <tr>
                                    <td><a href="<?php echo base_url('company/details/' . $account->company_id);?>">#<?= esc($account->company_id . " - " . $account->full_name);?></a></td>
                                    <td><?= esc($account->account_number);?></td>
                                    <td><?php echo $account->brand_name;?></td>

                                    <td><?php echo number_format($account->accumulated);?> đ</td>

                                </tr>

                            <?php endforeach;?>


                            </tbody>
                        </table>

                    </div>

                </div>

            </div>
            <div class="col-md-5 d-flex">
             
            </div>
        </div>


    </div>
    <div class="mt-5"></div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>


<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js"></script>
<script>

</script>