<style>
.copyjs {
    cursor: pointer;
}
</style>
<link rel="stylesheet" href="<?= base_url('assets/tom-select/tom-select.bootstrap5.css');?>">
<main class="content">
    <div class="container-fluid">

        <div>
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-light mb-0">
                    <li class="breadcrumb-item"><a href="<?= base_url("company");?>">Công ty</a></li>
                    <li class="breadcrumb-item"><a
                            href="<?= base_url("company/details/" . $company_details->id);?>">#<?= esc($company_details->id . ' - ' . $company_details->short_name);?></a>
                    </li>
                    <li class="breadcrumb-item"><a
                            href="<?= base_url("company/tax_info/" . $company_details->id);?>">Tax Info</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Tạo mới</li>
                </ol>
            </nav>
            <!-- End Breadcrumb -->
        </div>

        <div class="row mt-3">

            <?php include(APPPATH . 'Views/company/inc_left_menu.php');?>


            <div class="col-md-9 col-xl-10">


                <div class="">




                    <div class="">
                        <div class="card">

                            <div class="card-body">
                            <?php echo form_open('taxinfo/ajax_add',"id='data_form' class='needs-validation data_form' novalidate");?>
                                <input type="hidden" name="company_id" value="<?= $company_details->id;?>">
                                <input type="hidden" name="merchant_id" value="0">

                                <div class="row">
                                    <h4>Thêm thông tin xuất xuất hoá đơn</h4>

                                    <div class="my-3">

                                        <label for="exampleFormControlInput2" class="form-label  fw-bold">Loại thông
                                            tin</label>
                                        <select name="type" id="type" class="form-select" aria-label="" required>
                                            <option value="" selected>Chọn</option>
                                            <option value="Individual">Cá nhân (Individual)</option>
                                            <option value="Organization">Công ty (Organization)</option>

                                        </select>
                                        <div class="invalid-feedback">
                                            Vui lòng chọn loại
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="exampleFormControlInput2" class="form-label  fw-bold">Tên</label>
                                        <input type="text" name="name" class="form-control"
                                            id="exampleFormControlInput2" placeholder="" value="" required>
                                        <div class="invalid-feedback">
                                            Vui lòng điền tên công ty/ cá nhân
                                        </div>
                                    </div>


                                    <div class="mb-3">
                                        <label for="exampleFormControlInput2" class="form-label fw-bold">Mã số
                                            thuế</label>
                                        <input type="text" name="tax_code" class="form-control"
                                            id="exampleFormControlInput2" placeholder="" value="">
                                        <div class="invalid-feedback">
                                            Vui lòng điền mã số thuế nếu là công ty
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="exampleFormControlInput2" class="form-label  fw-bold">Email nhận hoá
                                            đơn</label>
                                        <input type="email" name="email" class="form-control"
                                            id="exampleFormControlInput2" placeholder="" value="" required>
                                        <div class="invalid-feedback">
                                            Vui lòng điền email
                                        </div>
                                    </div>

                                    <label class="mb-2  fw-bold">Địa chỉ xuất hoá đơn</label>
                                    <div class="ms-3">

                                        <div class="mb-3">

                                            <label for="exampleFormControlInput2" class="form-label">Tỉnh/ Thành</label>
                                            <select name="province" id="province" class="form-select" aria-label=""  required>
                                                <option value="" selected>Chọn Tỉnh/ Thành</option>
                                                <?php foreach($provinces as $province): ?>
                                                <option value="<?= esc($province->code);?>"><?= esc($province->name);?>
                                                </option>
                                                <?php endforeach; ?>

                                            </select>
                                            <div class="invalid-feedback">
                                                Vui lòng chọn Tỉnh/ Thành phố
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="exampleFormControlInput2" class="form-label">Quận/ Huyện</label>
                                            <select name="district" id="district" class="form-select" aria-label=""  required>
                                                <option value="" selected>Chọn Quận/ Huyện</option>


                                            </select>
                                            <div class="invalid-feedback">
                                                Vui lòng chọn Quận/ Huyện
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="exampleFormControlInput2" class="form-label">Phường/ Xã</label>
                                            <select name="ward" id="ward" class="form-select" aria-label=""  required>
                                                <option value="" selected>Chọn Phường/ Xã</option>


                                            </select>
                                            <div class="invalid-feedback">
                                                Vui lòng chọn Phường/ Xã
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="exampleFormControlInput2" class="form-label">Số nhà, đường/
                                                thôn, ấp</label>
                                            <input type="text" name="address1" class="form-control"
                                                id="exampleFormControlInput2" placeholder="Số nhà, đường/ thôn, ấp"
                                                value=""  required>
                                            <div class="invalid-feedback">
                                                Vui lòng điền địa chỉ cụ thể. Bao gồm Số nhà, đường, thôn, ấp
                                            </div>
                                        </div>
                                    </div>


                                    <div class="mb-3">
                                        <label for="exampleFormControlInput2" class="form-label  fw-bold">Ghi chú</label>
                                        <input type="text" name="note" class="form-control"
                                            id="exampleFormControlInput2" placeholder="" value="">
                                        
                                    </div>



                                </div>

                                <div class="mt-3">
                                <button type="submit" name="btn_submit" class="btn btn-info btn-save">Thêm</button>
                                </div>
                                <?php echo form_close();?>
                                <hr class="my-4">


                            </div>
                        </div>
                    </div>



                </div>




            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>




<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>

<script type="text/javascript" src="<?php echo base_url();?>/assets/DataTables/datatables.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/app.js"></script>


<script src="<?php echo base_url();?>/assets/clipboardjs/clipboard.min.js"></script>
<script src="<?= base_url('assets/tom-select/tom-select.complete.min.js');?>"></script>


<script>
var tomselect_settings = {
    searchInDropdown: true,
    plugins: [
        'change_listener',
        'dropdown_input'
    ],
    render: {
        'option': function(data, escape) {
            return data.optionTemplate || `<div>${data.text}</div>>`
        },
        'item': function(data, escape) {
            return data.optionTemplate || `<div>${data.text}</div>>`
        }
    },
    hideSelected: false,
    sortField: {
        direction: "asc"
    },
    addPrecedence: false
};

let tomselect = new TomSelect('#province', tomselect_settings);



(() => {
    'use strict'

    // Fetch all the forms we want to apply custom Bootstrap validation styles to
    const forms = document.querySelectorAll('.needs-validation')

    // Loop over them and prevent submission
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            event.preventDefault()
            event.stopPropagation()
            if (form.checkValidity()) {
                submit_data();
            }

            form.classList.add('was-validated')
        }, false)
    })
})()





function district_reset() {
    $("select[name='district']").find('option')
        .remove()
        .end()
        .append('<option value="" selected="">Chọn Quận/ Huyện</option>')
}

function ward_reset() {
    $("select[name='ward']").find('option')
        .remove()
        .end()
        .append('<option value="" selected="">Chọn Phường/ Xã</option>')
}

function get_districts_by_province(province_code) {
    $.ajax({
        url: "<?php echo base_url('taxinfo/ajax_get_districts');?>/" + province_code,
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            if (data.status == true) {

                $.each(data.data, function(i, item) {
                    $("select[name='district']").append($('<option>', {
                        value: item.code,
                        text: item.full_name
                    }));
                });

            } else {
                alert('Lỗi: ' + data.message);
            }


        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Lỗi không thể lấy danh sách Quận/ Huyện');
        }
    });
}


function get_wards_by_district(district_code) {
    $.ajax({
        url: "<?php echo base_url('taxinfo/ajax_get_wards');?>/" + district_code,
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            if (data.status == true) {
                console.log(data.data);

                $.each(data.data, function(i, item) {
                    console.log(item.full_name);
                    $("select[name='ward']").append($('<option>', {
                        value: item.code,
                        text: item.full_name
                    }));
                });

            } else {
                alert('Lỗi: ' + data.message);
            }


        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Lỗi không thể lấy danh sách Quận/ Huyện');
        }
    });
}


$("select[name='province']").change(function(e) {
    var selected = this.value;

    if (selected == "") {
        district_reset();
        ward_reset();
    } else {
        district_reset();
        ward_reset();
        get_districts_by_province(selected);
    }

});

$("select[name='district']").change(function(e) {
    var selected = this.value;

    if (selected == "") {
        ward_reset();
    } else {
        ward_reset();
        get_wards_by_district(selected);
    }

});



function submit_data() {
      
      url = "<?php echo base_url('taxinfo/ajax_add');?>";
      post_data = $('#data_form').serialize();
  
  
      $(".btn-save").attr("disabled", true);
      $('#btn_loading').html('');
      $(".btn-save").html('<div class="spinner-border text-light" role="status" id="btn_loading"></div>');
  
      $.ajax({
          url : url,
          type: "POST",
          data: post_data,
          dataType: "JSON",
          success: function(data)
          {
  
              $(".btn-save").attr("disabled", false);
              $('#btn_loading').remove();
              $(".btn-save").html('Thêm');
              
              //if success close modal and reload ajax table
              if(data.status == true) {
  
                 notyf.success({message:'Thêm thông tin thành công', dismissible: true});
                location.href="<?= base_url('company/tax_info/' . $company_details->id);?>";
              } else {
                  alert('Lỗi: ' + data.message);
              }
          
          },
          error: function (jqXHR, textStatus, errorThrown)
          {
              alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
              $(".btn-save").attr("disabled", false);
              $('#btn_loading').remove();
              $(".btn-save").html('Thêm');
          }
          
      });
      }
  
  

</script>