 
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
        <main class="content">
            <div class="container-fluid">
              
                <div class="card mt-3" style="width:100%">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-auto d-none d-sm-block">
                                <h3>Danh sách Product</h3>
                            </div>

                            <div class="col-auto ms-auto text-end row">
                                <div class="mb-2">
                                    <a href="javascript:;" onclick="show_add_product()" class="btn btn-primary btn-sm float-end"><i class="bi bi-plus"></i> Thêm Product</a>   

                                </div>

                            
                            </div>
                        </div>
                         
                        <div class="row">
                           
                           
                                <div class="">
                                <table class="table table-hover table-striped table-bordered text-muted align-middle display nowrap" id="product_table" style="width:100%">
                                    <thead class="table-light text-muted">
                                        <tr class="align-middle">
                                            <th></th>
                                            <th>ID</th>
                                            <th>Tên</th>
                                            <th>Giao Dịch</th>
                                            <th>Sử dụng</th>
                                            <th>Giá tháng</th>
                                            <th>Giá năm</th>
                                            <th>Billing Type</th>
                                            <th>Thứ tự</th>
                                            <th>Công khai</th>
                                            <th>Trạng thái</th>
                                           
                                            <th></th>

                                         </tr>
                                    </thead>
                                    <tbody>
                
                                       
                                    </tbody>
                                </table>
                                </div>
                             
                            
                        </div>
                    </div>
                </div>
                
            </div>
        </main>

        <?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>


        </div>
    </div>


  
<!-- Modal -->
<div class="modal fade" id="productAddModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="productAddModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="productAddModalLabel">Thêm Product</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <?php echo form_open('',"id='product_add_form' class='needs-validation form-add-product' novalidate");?>
        <input type="hidden"name="id" value="">
        <div class="modal-body m-lg-3">

                  

                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Tên Product<span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" name="name" placeholder="" required>
                    </div>
                
                    
                    <div class="mb-3">
                        <label for="exampleFormControlInput2" class="form-label">Mô tả (HTML)<span class="text-danger">(*)</span></label>
                        <textarea name="description" class="form-control" placeholder="" rows="5" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label"><b>sms_allow?</b></label>
                        <select class="form-select active" name="sms_allow" aria-label="sms_allow" required>
                            <option value="0" selected>Không</option>
                            <option value="1">Có</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label"><b>Cấp dedicated_sim?</b></label>
                        <select class="form-select active" name="dedicated_sim" aria-label="dedicated_sim" required>
                            <option value="0" selected>Không</option>
                            <option value="1">Có</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">monthly_transaction_limit<span class="text-danger">(*)</span></label>
                        <input type="number" class="form-control" name="monthly_transaction_limit" placeholder="" required>
                    </div>
                     
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">price_monthly<span class="text-danger">(*)</span></label>
                        <input type="number" class="form-control" name="price_monthly" placeholder="" required>
                    </div>

                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">price_annually<span class="text-danger">(*)</span></label>
                        <input type="number" class="form-control" name="price_annually" placeholder="" required>
                    </div>

                    
                     

                    <div class="mb-3">
                        <label class="form-label"><b>billing_type</b></label>
                        <select class="form-select billing_type" name="billing_type" aria-label="billing_type" required>
                            <option value="Recurring">Recurring</option>
                            <option value="Onetime">Onetime</option>
                            <option value="Free">Free</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label"><b>hidden?</b></label>
                        <select class="form-select active" name="hidden" aria-label="hidden" required>
                            <option value="0">Không</option>
                            <option value="1">Có</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label"><b>active?</b></label>
                        <select class="form-select active" name="active" aria-label="active" required>
                            <option value="1">Có</option>
                            <option value="0" >Không</option>

                        </select>
                    </div>
                 
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">sort_order<span class="text-danger">(*)</span></label>
                        <input type="number" class="form-control" name="sort_order" placeholder="" required>
                    </div>
                     
        
        
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary me-3" data-bs-dismiss="modal">Đóng</button>
                <a class="btn btn-primary btn-product-add" onclick="save()">Thêm</a>
            </div>
        </form>
        </div>
    </div>
</div>
<!-- Modal -->

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>

<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
    
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

<script>



$(document).ready(function() {

var  table = $('#product_table').DataTable({ 
   rowReorder: {
       selector: 'td:nth-child(0)'
   },
   responsive: true,


    "processing": true,
    "serverSide": true,
    "order": [],
    "pageLength": 20,

    "ajax": {
        "url": "<?php echo base_url('product/ajax_list'); ?>",
        "data": {"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
        "type": "POST"
    },


    "language": {
        "sProcessing":   "Đang xử lý...",
        "sLengthMenu":   "Xem _MENU_ mục",
        "sZeroRecords":  "Không tìm thấy dòng nào phù hợp",
        "sInfo":         "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
        "sInfoEmpty":    "Đang xem 0 đến 0 trong tổng số 0 mục",
        "sInfoFiltered": "(được lọc từ _MAX_ mục)",
        "sInfoPostFix":  "",
        "sSearch":       "Tìm:",
        "sUrl":          "",
        "oPaginate": {
            "sFirst":    "Đầu",
            "sPrevious": "Trước",
            "sNext":     "Tiếp",
            "sLast":     "Cuối"
        }
    },

    "columnDefs": [
       { responsivePriority: 1, targets: 1 },
       { responsivePriority: 2, targets: 2 },

       
        { 
            "visible": false,
            "targets": [ 0 ],
            "orderable": false,
        },
 
        
    ],

    
});

table.on ('init', function () {
   $('*[type="search"][class="form-control form-control-sm"]').attr('style','max-width:120px');
   $('div.dataTables_filter').parent().attr('class','col-6');
   $('div.dataTables_length').parent().attr('class','col-6');
});


});

 

var addSimModal = new bootstrap.Modal(document.getElementById('productAddModal'), {
        keyboard: false
    });

    function show_add_product() {
        reset_product_form();
        save_method = 'add';
        $('.modal-title').text('Thêm Sim'); // Set title to Bootstrap modal title
        $(".btn-product-add").html('Thêm');
       // load_tom_select();
        addSimModal.show();
    }

    function reset_product_form() {
        $('#product_add_form')[0].reset();

        $('#btn_loading').html('');
        $(".btn-product-add").html('Thêm');
        $(".btn-product-add").attr("disabled", false);


    }

    function save()
    {

        var url;
        if(save_method == 'add')
        {
            url = "<?php echo base_url('product/ajax_product_add');?>";

        } else  if(save_method == 'update')
        {
            url = "<?php echo base_url('product/ajax_product_update');?>";
        } 
        else
        {
            url = "<?php echo base_url('product');?>";
        } 

        $(".btn-product-add").attr("disabled", true);
        $('#btn_loading').html('');
        $(".btn-product-add").html('<div class="spinner-border text-light" role="status" id="btn_loading"></div>');

        $.ajax({
            url : url,
            type: "POST",
            data: $('#product_add_form').serialize(),
            dataType: "JSON",
            success: function(data)
            {
                //if success close modal and reload ajax table
                if(data.status == true) {
                    addSimModal.hide();
 
                    $('#product_table').DataTable().ajax.reload();

                    if(save_method == 'add')
                    {
                        notyf.success({message:'Thêm thành công', dismissible: true});

                    } else  if(save_method == 'update')
                    {
                        notyf.success({message:'Sửa thành công', dismissible: true});
                    } 
                } else {
                    
                    $(".btn-product-add").attr("disabled", false);
                    $('#btn_loading').remove();
                    $(".btn-product-add").html('Thêm');
                    alert('Lỗi: ' + data.message);
 
                }
            
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                $(".btn-product-add").attr("disabled", false);
                $('#btn_loading').remove();
                $(".btn-product-add").html('Thêm');
            }
            
        });
    }
 
    function edit_product(id)
      {
        reset_product_form(); // reset form on modals
  
        save_method = 'update';

        //Ajax Load data from ajax
        $.ajax({
          url : "<?php echo base_url('product/ajax_get_product');?>/" + id,
          type: "GET",
          dataType: "JSON",
          success: function(data)
          {
            if(data.status == true) {
                $('[name="id"]').val(data.data.id);
                $('[name="name"]').val(data.data.name);
                $('[name="description"]').val(data.data.description);
                $('[name="sms_allow"]').val(data.data.sms_allow);
                $('[name="dedicated_sim"]').val(data.data.dedicated_sim);
                $('[name="active"]').val(data.data.active);
                $('[name="monthly_transaction_limit"]').val(data.data.monthly_transaction_limit);
                $('[name="price_monthly"]').val(data.data.price_monthly);
                $('[name="price_annually"]').val(data.data.price_annually);
                $('[name="billing_type"]').val(data.data.billing_type);
                $('[name="hidden"]').val(data.data.hidden);
                $('[name="active"]').val(data.data.active);
                $('[name="sort_order"]').val(data.data.sort_order);

                $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                $('.modal-title').text('Sửa PRODUCT'); // Set title to Bootstrap modal title
                $(".btn-product-add").html('Cập nhật');
                addSimModal.show();

            } else {
                alert('Lỗi: ' + data.message);
            }
  
            
          },
          error: function (jqXHR, textStatus, errorThrown)
          {
              alert('Lỗi không thể lấy được thông tin');
          }
      });
    } 

</script>