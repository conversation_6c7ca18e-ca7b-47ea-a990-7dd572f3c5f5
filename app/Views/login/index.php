<!doctype html>
<html lang="vi">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Bootstrap CSS -->
    <link href="<?php echo base_url();?>assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?php echo base_url();?>assets/font/bootstrap-icons.css" rel="stylesheet">
    <link href="<?php echo base_url();?>assets/css/123host.css" rel="stylesheet">
    <link rel="shortcut icon" href="<?php echo base_url();?>assets/images/favicon.png" type="image/x-icon" />

    <title>Admin - Đăng nhập - SePay</title>
    <meta name="description" content="Đăng nhập vào SePay Admin"/>
    <meta name="robots" content="noindex">

</head>

<body data-theme="colored" data-layout="fluid" data-sidebar-position="left" data-sidebar-behavior="sticky">
    <div class="main d-flex justify-content-center w-100">
        <main class="content d-flex p-0">
            <div class="container d-flex flex-column">
                <div class="row mt-5">
                    <div class="col-sm-10 col-md-8 col-lg-6 mx-auto d-table h-100">
                        <div class="d-table-cell align-middle">

                            <div class="text-center mt-4">
                            <h1> 
                    <img src="<?= base_url('assets/images/logo/sepay-blue-359x116.png');?>" style="max-width:200px" class="img-fluid">
                    </h1>
                                <p class="lead text-white">
                                    
                                </p>
                            </div>

                            <div class="card">
                                <div class="card-body">
                                <h3 class="text-center">Khu vực Admin</h3>
                               
                                    <div class="m-sm-4">
                                    <div id="login_message"></div>

                                    <?php echo form_open('/login',array('id'=>'login_form'));?>
                                    <input type="hidden" id="g-recaptcha-response" name="g-recaptcha-response">

                                            <div class="mb-3">
                                                <label class="form-label">Email</label>
                                                <input class="form-control form-control-lg" type="email" name="email"
                                                    placeholder="Điền email">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Mật khẩu</label>
                                                <input class="form-control form-control-lg" type="password"
                                                    name="password" placeholder="Điền mật khẩu">
                                                   
                                            </div>
                                            
                                            <div class="text-center mt-3">
                                            <a class="btn btn-lg btn-primary  btn-login" onclick="do_login();">Đăng nhập</a>
                                            </div>
                                        </form>
                                               
                                                        
                                        </div>
                                        

                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>


    <script src="<?php echo base_url();?>assets/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo base_url();?>assets/js/jquery-3.6.0.min.js"></script>
    <script src="<?php echo base_url();?>assets/js/app.js"></script>
    <script src="https://www.google.com/recaptcha/api.js?render=6LcGE_8kAAAAAD3Kt7q8SOT-4SM76tFxzhUgX8ZE"></script>

<script>
   function captcha_run() {
        grecaptcha.execute('6LcGE_8kAAAAAD3Kt7q8SOT-4SM76tFxzhUgX8ZE', {action:'validate_captcha'})
                  .then(function(token) {
            // add token value to form
            document.getElementById('g-recaptcha-response').value = token;
            $(".btn-login").show();

        });
    }
    grecaptcha.ready(function() {
    // do request for recaptcha token
    // response is promise with passed token
        captcha_run();
    });
</script>  
    <script>

    function do_login()
    {

        $(".btn-login").attr("disabled", true);
        $('#login_loading').html('');
        $(".btn-login").html('<div class="spinner-border text-light" role="status" id="login_loading"></div>');

        
        // ajax adding data to database
        $.ajax({
            url : "<?php echo base_url('login/do_login');?>",
            type: "POST",
            data: $('#login_form').serialize(),
            dataType: "JSON",
            success: function(data)
            {
                //if success close modal and reload ajax table
                if(data.status == true) {
                    location.href=('<?php echo base_url();?>');
                } else {
                    $('#login_message').html("<div class='alert alert-danger'>"+data.message+"</div>");
                    $(".btn-login").attr("disabled", false);
                    $('#login_loading').remove();
                    $(".btn-login").html('Đăng nhập');
                    captcha_run();
 
                }
            
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Có lỗi xảy ra! Vui lòng liên hệ SePay để được hỗ trợ');
                $(".btn-login").attr("disabled", false);
                $('#login_loading').remove();
                $(".btn-login").html('Đăng nhập');
            }
            
        });
    }
    
    </script>


</body>

</html>