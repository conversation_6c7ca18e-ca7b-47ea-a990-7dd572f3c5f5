<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
<style>
    .tox-promotion {
        display: none;
    }
    .tox-statusbar__branding {
        display: none;
    }
</style>
        <main class="content">
            <div class="container-fluid">
              
                <div class="card mt-3" style="width:100%">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-auto d-none d-sm-block">
                                <h3>Danh sách loại tích hợp</h3>
                            </div>
                            <div class="col-auto ms-auto text-end row">
                                <div class="mb-2">
                                    <a href="javascript:;" onclick="show_add_type()" class="btn btn-primary btn-sm float-end"><i class="bi bi-plus"></i> Thêm loại tích hợp</a>   
                                </div>
                            
                            </div>
                        </div>
                         
                        <div class="row">
                                <div class="">
                                <table class="table table-hover table-striped table-bordered text-muted align-middle display nowrap" id="type_table" style="width:100%">
                                    <thead class="table-light text-muted">
                                        <tr class="align-middle">
                                            <th>ID</th>
                                            <th>Tên</th>
                                            <th>Đường dẫn</th>
                                            <th>Vị trí</th>
                                            <th>Ngày tạo</th>
                                            <th>Ngày cập nhật</th>
                                            <th>Hành động</th>
                                         </tr>
                                    </thead>
                                    <tbody>
                
                                       
                                    </tbody>
                                </table>
                                </div>
                             
                            
                        </div>
                    </div>
                </div>
                
            </div>
        </main>
        <?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>
        </div>
    </div>
  
<!-- Modal -->
<div class="modal fade" id="typeAddModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="typeAddModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="typeAddModalLabel">Thêm loại tích hợp</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <?php echo form_open('',"id='type_add_form' class='needs-validation form-add-type' novalidate");?>
        <input type="hidden"name="id" value="">
        <div class="modal-body m-lg-3">
                    <input type="hidden" name="id" value="">
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Tên loại tích hợp<span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" placeholder="Chia sẻ biến động số dư" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Đường dẫn<span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" name="slug" placeholder="Chia sẻ biến động số dư" required>
                    </div>
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Vị trí<span class="text-danger">(*)</span></label>
                        <input type="number" class="form-control" name="position" placeholder="1">
                    </div>
        
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary me-3" data-bs-dismiss="modal">Đóng</button>
                <a class="btn btn-primary btn-type-add" onclick="save()">Thêm</a>
            </div>
        </form>
        </div>
    </div>
</div>
<!-- Modal -->
<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/tinymce-jquery.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
    
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>
<script>
$(document).ready(function() {
var  table = $('#type_table').DataTable({ 
   rowReorder: {
       selector: 'td:nth-child(0)'
   },
   responsive: true,
    "processing": true,
    "serverSide": true,
    "ajax": {
        "url": "<?php echo base_url('otherintergation/ajax_get_list_type'); ?>",
        "data": {"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
        "type": "POST"
    },
    "language": {
        "sProcessing":   "Đang xử lý...",
        "sLengthMenu":   "Xem _MENU_ mục",
        "sZeroRecords":  "Không tìm thấy loại tích hợp phù hợp",
        "sInfo":         "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
        "sInfoEmpty":    "Đang xem 0 đến 0 trong tổng số 0 mục",
        "sInfoFiltered": "(được lọc từ _MAX_ mục)",
        "sInfoPostFix":  "",
        "sSearch":       "Tìm:",
        "sUrl":          "",
        "oPaginate": {
            "sFirst":    "Đầu",
            "sPrevious": "Trước",
            "sNext":     "Tiếp",
            "sLast":     "Cuối"
        }
    },
    "columnDefs": [
       { responsivePriority: 1, targets: 1 },
       { responsivePriority: 2, targets: 2 },
    ],
    "order": [],
});
table.on ('init', function () {
   $('*[type="search"][class="form-control form-control-sm"]').attr('style','max-width:120px');
   $('div.dataTables_filter').parent().attr('class','col-6');
   $('div.dataTables_length').parent().attr('class','col-6');
});
});
 
var typeAddModal = new bootstrap.Modal(document.getElementById('typeAddModal'), {
        keyboard: false
    });
    function show_add_type() {
        reset_bank_form();
        save_method = 'add';
        $('.modal-title').text('Thêm loại tích hợp');
        $(".btn-type-add").html('Thêm');
        typeAddModal.show();
    }
    function reset_bank_form() {
        $('#type_add_form')[0].reset();
        $('#btn_loading').html('');
        $(".btn-type-add").html('Thêm');
        $(".btn-type-add").attr("disabled", false);
    }
    function save()
    {
        var url;
        if(save_method == 'add')
        {
            url = "<?php echo base_url('otherintergation/ajax_add_type');?>";
        } else  if(save_method == 'update')
        {
            url = "<?php echo base_url('otherintergation/ajax_update_type');?>";
        } 
        else
        {
            url = "<?php echo base_url('otherintergation/type');?>";
        } 
        $(".btn-type-add").attr("disabled", true);
        $('#btn_loading').html('');
        $(".btn-type-add").html('<div class="spinner-border text-light" role="status" id="btn_loading"></div>');
        $.ajax({
            url : url,
            type: "POST",
            data: $('#type_add_form').serialize(),
            dataType: "JSON",
            success: function(data)
            {
                //if success close modal and reload ajax table
                if(data.status == true) {
                    typeAddModal.hide();
 
                    $('#type_table').DataTable().ajax.reload();
                    if(save_method == 'add')
                    {
                        notyf.success({message:'Thêm loại tích hợp thành công', dismissible: true});
                    } else  if(save_method == 'update')
                    {
                        notyf.success({message:'Sửa loại tích hợp thành công', dismissible: true});
                    } 
                } else {
                    $(".btn-type-add").attr("disabled", false);
                    $('#btn_loading').remove();
                    $(".btn-type-add").html('Thêm');
                    alert('Lỗi: ' + data.message);
                }
            
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                $(".btn-type-add").attr("disabled", false);
                $('#btn_loading').remove();
                $(".btn-type-add").html('Thêm');
            }
            
        });
    }
 
    function edit_type(id)
      {
        reset_bank_form(); // reset form on modals
  
        save_method = 'update';
        //Ajax Load data from ajax
        $.ajax({
          url : "<?php echo base_url('otherintergation/ajax_get_type');?>/" + id,
          type: "GET",
          dataType: "JSON",
          success: function(data)
          {
            if(data.status == true) {
                $('[name="id"]').val(data.data.id);
                $('[name="name"]').val(data.data.name);
                $('[name="slug"]').val(data.data.slug);
                $('[name="position"]').val(data.data.position);
                $('#modal_form').modal('show');
                $('.modal-title').text('Cập nhật loại tích hợp');
                $(".btn-type-add").html('Cập nhật');
                typeAddModal.show();
            } else {
                alert('Lỗi: ' + data.message);
            }
          },
          error: function (jqXHR, textStatus, errorThrown)
          {
              alert('Lỗi không thể lấy được thông tin');
          }
      });
    } 
</script>