<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
<style>
    .tox-promotion {
        display: none;
    }
    .tox-statusbar__branding {
        display: none;
    }
</style>
        <main class="content">
            <div class="container-fluid">
              
                <div class="card mt-3" style="width:100%">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-auto d-none d-sm-block">
                                <h3>Danh sách ngân hàng</h3>
                            </div>
                            <div class="col-auto ms-auto text-end row">
                                <div class="mb-2">
                                    <a href="javascript:;" onclick="show_add_bank()" class="btn btn-primary btn-sm float-end"><i class="bi bi-plus"></i> Thêm ngân hàng</a>   
                                </div>
                            
                            </div>
                        </div>
                         
                        <div class="row">
                                <div class="">
                                <table class="table table-hover table-striped table-bordered text-muted align-middle display nowrap" id="bank_table" style="width:100%">
                                    <thead class="table-light text-muted">
                                        <tr class="align-middle">
                                            <th>ID</th>
                                            <th>Tên thương hiệu</th>
                                            <th>Tên đầy đủ</th>
                                            <th>Đường dẫn icon</th>
                                            <th>Trạng thái</th>
                                            <th>Giao dịch <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-title="Số lượng giao dịch được cộng thêm khi liên kết với ngân hàng"></i></th>
                                            <th>Đường dẫn chuyển hướng <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-title="Đường dẫn chuyển hướng của trang liên kết tặng giao dịch của mỗi ngân hàng"></i></th>
                                            <th>Hành động</th>
                                         </tr>
                                    </thead>
                                    <tbody>
                
                                       
                                    </tbody>
                                </table>
                                </div>
                             
                            
                        </div>
                    </div>
                </div>
                
            </div>
        </main>
        <?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>
        </div>
    </div>
  
<!-- Modal -->
<div class="modal fade" id="bankAddModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="bankAddModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="bankAddModalLabel">Thêm ngân hàng</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <?php echo form_open('',"id='bank_add_form' class='needs-validation form-add-bank' novalidate");?>
        <input type="hidden"name="id" value="">
        <div class="modal-body m-lg-3">
                    <input type="hidden" name="id" value="">
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Tên thương hiệu<span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" name="brand_name" placeholder="Vietcombank" required>
                    </div>
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Tên viết tắt<span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" name="short_name" placeholder="Vietcombank" required>
                    </div>
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Tên đầy đủ<span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" name="full_name" placeholder="Ngân hàng TMCP Ngoại Thương Việt Nam" required>
                    </div>
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Mã bin</label>
                        <input type="text" class="form-control" name="bin" placeholder="970436" required>
                    </div>
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Mã code</label>
                        <input type="text" class="form-control" name="code" placeholder="VCB" required>
                    </div>
                     
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">logo_path<span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" name="logo_path" placeholder="vietcombank.png" required>
                    </div>
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">icon_path<span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" name="icon_path" placeholder="vietcombank-icon.png" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><b>Trạng thái</b></label>
                        <select class="form-select active" name="active" aria-label="active" required>
                            <option value="1" selected>Kích hoạt</option>
                            <option value="0">Tạm khoá</option>
                        </select>
                    </div>
                 
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Giao dịch tặng <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-title="Giao dịch được tặng khi liên kết mới ngân hàng"></i></label>
                        <input type="number" class="form-control" name="bank_bonus" placeholder="" required>
                    </div>
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Đường dẫn chuyển hướng (vietcombank/step1) <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-title="Đường dẫn chuyển hướng của trang liên kết tặng giao dịch của mỗi ngân hàng"></i></label>
                        <input type="text" class="form-control" name="bank_path" placeholder="vietcombank/step1">
                    </div>
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Nhập mã giới thiệu</label>
                        <input type="text" class="form-control" name="referral_code" placeholder="ABCDEF">
                    </div>
                     
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Hướng dẫn liên kết</label>
                        <textarea type="text" id="guide" class="form-control" name="guide"></textarea>
                    </div>
        
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary me-3" data-bs-dismiss="modal">Đóng</button>
                <a class="btn btn-primary btn-bank-add" onclick="save()">Thêm</a>
            </div>
        </form>
        </div>
    </div>
</div>
<!-- Modal -->
<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url();?>/assets/tinymce/tinymce.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/tinymce-jquery.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
    
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>
<script>
$(document).ready(function() {
var  table = $('#bank_table').DataTable({ 
   rowReorder: {
       selector: 'td:nth-child(0)'
   },
   responsive: true,
    "processing": true,
    "serverSide": true,
    "order": [[0, 'desc']],
    "ajax": {
        "url": "<?php echo base_url('bankbonus/ajax_list_bank'); ?>",
        "data": {"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
        "type": "POST"
    },
    "language": {
        "sProcessing":   "Đang xử lý...",
        "sLengthMenu":   "Xem _MENU_ mục",
        "sZeroRecords":  "Không tìm thấy ngân hàng phù hợp",
        "sInfo":         "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
        "sInfoEmpty":    "Đang xem 0 đến 0 trong tổng số 0 mục",
        "sInfoFiltered": "(được lọc từ _MAX_ mục)",
        "sInfoPostFix":  "",
        "sSearch":       "Tìm:",
        "sUrl":          "",
        "oPaginate": {
            "sFirst":    "Đầu",
            "sPrevious": "Trước",
            "sNext":     "Tiếp",
            "sLast":     "Cuối"
        }
    },
    "columnDefs": [
       { responsivePriority: 1, targets: 1 },
       { responsivePriority: 2, targets: 2 },
       { targets : 7, orderable : false , searchable : false}
    ],
    
});
table.on ('init', function () {
   $('*[type="search"][class="form-control form-control-sm"]').attr('style','max-width:120px');
   $('div.dataTables_filter').parent().attr('class','col-6');
   $('div.dataTables_length').parent().attr('class','col-6');
});
});
 
var bankAddModal = new bootstrap.Modal(document.getElementById('bankAddModal'), {
        keyboard: false
    });
    function show_add_bank() {
        reset_bank_form();
        save_method = 'add';
        $('.modal-title').text('Thêm ngân hàng');
        $(".btn-bank-add").html('Thêm');
        $('[name="brand_name"]').attr('readonly', false);
        $('[name="short_name"]').attr('readonly', false);
        bankAddModal.show();
    }
    function reset_bank_form() {
        $('#bank_add_form')[0].reset();
        $('#btn_loading').html('');
        $(".btn-bank-add").html('Thêm');
        $(".btn-bank-add").attr("disabled", false);
    }
    function save()
    {
        var url;
        if(save_method == 'add')
        {
            url = "<?php echo base_url('bankbonus/ajax_bank_add');?>";
        } else  if(save_method == 'update')
        {
            url = "<?php echo base_url('bankbonus/ajax_bank_update');?>";
        } 
        else
        {
            url = "<?php echo base_url('bankbonus/bank');?>";
        } 
        $(".btn-bank-add").attr("disabled", true);
        $('#btn_loading').html('');
        $(".btn-bank-add").html('<div class="spinner-border text-light" role="status" id="btn_loading"></div>');
        $.ajax({
            url : url,
            type: "POST",
            data: $('#bank_add_form').serialize(),
            dataType: "JSON",
            success: function(data)
            {
                //if success close modal and reload ajax table
                if(data.status == true) {
                    bankAddModal.hide();
 
                    $('#bank_table').DataTable().ajax.reload();
                    if(save_method == 'add')
                    {
                        notyf.success({message:'Thêm ngân hàng thành công', dismissible: true});
                    } else  if(save_method == 'update')
                    {
                        notyf.success({message:'Sửa ngân hàng thành công', dismissible: true});
                    } 
                } else {
                    $(".btn-bank-add").attr("disabled", false);
                    $('#btn_loading').remove();
                    $(".btn-bank-add").html('Thêm');
                    alert('Lỗi: ' + data.message);
                }
            
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                $(".btn-bank-add").attr("disabled", false);
                $('#btn_loading').remove();
                $(".btn-bank-add").html('Thêm');
            }
            
        });
    }
 
    function edit_bank(id)
      {
        reset_bank_form(); // reset form on modals
  
        save_method = 'update';
        //Ajax Load data from ajax
        $.ajax({
          url : "<?php echo base_url('bankbonus/ajax_get_bank');?>/" + id,
          type: "GET",
          dataType: "JSON",
          success: function(data)
          {
            if(data.status == true) {
                $('[name="id"]').val(data.data.id);
                $('[name="brand_name"]').attr('readonly', true).val(data.data.brand_name);
                $('[name="short_name"]').attr('readonly', true).val(data.data.short_name);
                $('[name="full_name"]').val(data.data.full_name);
                $('[name="bin"]').val(data.data.bin);
                $('[name="code"]').val(data.data.code);
                $('[name="logo_path"]').val(data.data.logo_path);
                $('[name="icon_path"]').val(data.data.icon_path);
                $('[name="active"]').val(data.data.active);
                $('[name="bank_bonus"]').val(data.data.bank_bonus);
                $('[name="bank_path"]').val(data.data.bank_path);
                $('[name="referral_code"]').val(data.data.referral_code);
                $('[name="guide"]').val(data.data.guide);
                $('#modal_form').modal('show');
                $('.modal-title').text('Cập nhật ngân hàng');
                $(".btn-bank-add").html('Cập nhật');
                bankAddModal.show();
            } else {
                alert('Lỗi: ' + data.message);
            }
          },
          error: function (jqXHR, textStatus, errorThrown)
          {
              alert('Lỗi không thể lấy được thông tin');
          }
      });
    } 
    $('textarea#guide').tinymce({
        height: 300,
        menubar: true,
        plugins: [
          'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
          'anchor', 'searchreplace', 'visualblocks', 'fullscreen',
          'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | bold italic backcolor | ' +
          'alignleft aligncenter alignright alignjustify | ' +
          'bullist numlist outdent indent | removeformat | help'
      });
      document.addEventListener('focusin', function (e) { 
            if (e.target.closest('.tox-tinymce-aux, .moxman-window, .tam-assetmanager-root') !== null) {
            e.stopImmediatePropagation();
            } 
        });
</script>