<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
<style>
    .dtr-data{
        white-space: normal;
    }
</style>
<main class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-auto ms-auto mb-3 mt-2" style="width : 200px">
                <input class="form-control border-primary" type="text" name="daterange" id="daterange" value="" />
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 col-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body  text-center">
                        <div>Tổng số giao dịch <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tống số giao dịch được tặng khi liên kết"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3" id="total_transactions"><?= number_format($total_transactions)?></span>
                    </div>
                </div>
            </div>

            <div class="col-md-4 col-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body text-center">
                        <div>Tổng số ngân hàng <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng số ngân hàng đã liên kết để nhận giao dịch"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3" id="total_banks"><?= number_format($total_banks)?></span>
                    </div>
                </div>
            </div>

            <div class="col-md-4 col-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body  text-center">
                        <div>Tổng số công ty <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                data-bs-title="Tổng công ty đã liên kết với ngân hàng để nhận giao dịch"></i>
                        </div>
                        <span class="h3 d-inline-block mt-1 mb-3" id="total_companies"><?= number_format($total_companies)?></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Danh sách công ty được tặng giao dịch</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-hover table-striped table-bordered text-muted align-middle display nowrap" id="bank_bonus">
                            <thead>
                                <tr>
                                    <th>STT</th>
                                    <th>Tên công ty</th>
                                    <th>Tên người dùng</th>
                                    <th>Ngân hàng</th>
                                </tr>
                            </thead>
                            <tbody>

                            </tbody>
                        </table>
                    </div>
                   

                </div>
            </div>

        </div>


    </div>
    <div class="mt-5"></div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

<script>

$('#daterange').daterangepicker({
    timePicker: false,
    startDate: moment().startOf('month'),
    endDate: moment().endOf('month'),
    locale: {
        format: 'DD/MM/YYYY',
        separator: ' - ',
        applyLabel: 'Áp dụng',
        cancelLabel: 'Hủy',
        fromLabel: 'Từ',
        toLabel: 'Đến',
        customRangeLabel: 'Tùy chỉnh',
        weekLabel: 'T',
        daysOfWeek: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
        monthNames: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
        firstDay: 1
    },
    ranges: {
        'Hôm nay': [moment().startOf('day'), moment().endOf('day')],
        'Hôm qua': [moment().subtract(1, 'days').startOf('day'), moment().subtract(1, 'days').endOf('day')],
        '7 ngày qua': [moment().subtract(6, 'days').startOf('day'), moment().endOf('day')],
        '30 ngày qua': [moment().subtract(29, 'days').startOf('day'), moment().endOf('day')],
        'Tháng này': [moment().startOf('month'), moment().endOf('month')],
        'Tháng trước': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
        'Năm nay': [moment().startOf('year'), moment().endOf('year')],
        'Năm trước': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')]
    }
});

$('#daterange').val(moment().startOf('month').format('DD/MM/YYYY') + ' - ' + moment().endOf('month').format('DD/MM/YYYY'));

var table = $('#bank_bonus').DataTable({ 
    rowReorder: {
        selector: 'td:nth-child(0)'
    },
    responsive: true,
    order : [],

    "processing": true,
    "serverSide": true,

    "ajax": {
        "url": "<?php echo base_url('bankbonus/ajax_bank_bonus'); ?>",
        "data": {"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
        "type": "POST"
    },

    "language": {
        "sProcessing":   "Đang xử lý...",
        "sLengthMenu":   "Xem _MENU_ mục",
        "sZeroRecords":  "Không tìm thấy dòng nào phù hợp",
        "sInfo":         "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
        "sInfoEmpty":    "Đang xem 0 đến 0 trong tổng số 0 mục",
        "sInfoFiltered": "(được lọc từ _MAX_ mục)",
        "sInfoPostFix":  "",
        "sSearch":       "Tìm:",
        "sUrl":          "",
        "oPaginate": {
            "sFirst":    "Đầu",
            "sPrevious": "Trước",
            "sNext":     "Tiếp",
            "sLast":     "Cuối"
        }
    },

    "columnDefs": [
       { orderable: false, targets: 0 },
       { responsivePriority: 1, targets: 1 },
       { responsivePriority: 2, targets: 2 },
       { responsivePriority: 3, targets: 3 },
    ],
});

table.on ('init', function () {
   $('*[type="search"][class="form-control form-control-sm"]').attr('style','max-width:120px');
   $('div.dataTables_filter').parent().attr('class','col-6');
   $('div.dataTables_length').parent().attr('class','col-6');
});

function loadSummary() {
    var dates = $('#daterange').val().split(' - ');
    
    $.ajax({
        url: `<?= base_url('bankbonus/ajax_summary')?>`,
        type: 'GET',
        data: {
            start_date: moment(dates[0], 'DD/MM/YYYY').format('YYYY-MM-DD'),
            end_date: moment(dates[1], 'DD/MM/YYYY').format('YYYY-MM-DD')
        },
        success: function(response) {
            $('#total_transactions').text(response.data.total_transactions);
            $('#total_banks').text(response.data.total_banks);
            $('#total_companies').text(response.data.total_companies);
        }
    });
}

$('#daterange').on('apply.daterangepicker', function(ev, picker) {
    loadSummary();
});

</script>