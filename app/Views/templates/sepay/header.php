<?php 
$uri = service('uri');
$segment1 = $uri->getSegment(1);
$segments = $uri->getSegments();
if($admin_details->theme == 'auto') {
    if(date('H') > 5 && date('H') < 17)
      $theme_mode = 'light';
    else
      $theme_mode = 'dark';
} else 
    $theme_mode = $admin_details->theme;


?>
<!doctype html>
<html lang="vi">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <meta name="color-scheme" content="light dark">

    <!-- Bootstrap CSS -->    
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <?php if($theme_mode == 'dark') { ?>
    <link href="<?php echo base_url();?>/dist/css/dark.css" rel="stylesheet">
    <?php } else { ?>
    <link href="<?php echo base_url();?>/dist/css/light.css" rel="stylesheet">
    <?php } ?>
    <link rel="stylesheet" href="<?php echo base_url();?>/assets/notyf/notyf.min.css">

    <link rel="shortcut icon" href="<?php echo base_url();?>/assets/images/favicon.png" type="image/x-icon" />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap" rel="stylesheet">

    <title><?php if(isset($page_title)) echo esc($page_title) . ' - ';?>SePay</title>
    <style>
        .content {
            padding: 0.5rem 0.5rem 1.5rem;
        }
       
        .thumb-xl {
            height: 120px;
            width: 120px;
        }

        body[data-theme=dark] .sidebar-cta-content {
            background: unset !important;
        }
        .sidebar-cta-content {
            padding: 1rem;
        }

        .fs-7 {
            font-size: .725rem!important;
        }
 
    </style>

</head>


<body  data-theme="<?php if($theme_mode == 'dark') echo 'dark'; else echo 'colored';?>" data-layout="fluid" data-sidebar-position="left" data-sidebar-behavior="<?php echo esc($admin_details->sidebar_behavior);?>">
    <div class="wrapper">
        <nav id="sidebar" class="sidebar <?php if($admin_details->sidebar_toggle == 1 && !is_mobile()) echo 'collapsed';?>">
            <div class="sidebar-content js-simplebar" data-simplebar="init">
                <a class="sidebar-brand" href="<?php echo base_url();?>">
                    
                    <img src="<?= base_url('assets/images/logo/autopay-30x30-white-icon.png');?>">
                    <span class="align-middle me-3">SePay</span>
                    <p style="font-size:10px;" class="text-center mb-0"></p>
                </a>
                <ul class="sidebar-nav">
                    <li class="sidebar-item <?php if($segment1 == '') echo 'active';?>">
                        
                        <a href="<?php echo base_url();?>" class="sidebar-link "><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-speedometer" viewBox="0 0 16 16">
  <path d="M8 2a.5.5 0 0 1 .5.5V4a.5.5 0 0 1-1 0V2.5A.5.5 0 0 1 8 2zM3.732 3.732a.5.5 0 0 1 .707 0l.915.914a.5.5 0 1 1-.708.708l-.914-.915a.5.5 0 0 1 0-.707zM2 8a.5.5 0 0 1 .5-.5h1.586a.5.5 0 0 1 0 1H2.5A.5.5 0 0 1 2 8zm9.5 0a.5.5 0 0 1 .5-.5h1.5a.5.5 0 0 1 0 1H12a.5.5 0 0 1-.5-.5zm.754-4.246a.389.389 0 0 0-.527-.02L7.547 7.31A.91.91 0 1 0 8.85 8.569l3.434-4.297a.389.389 0 0 0-.029-.518z"/>
  <path fill-rule="evenodd" d="M6.664 15.889A8 8 0 1 1 9.336.11a8 8 0 0 1-2.672 15.78zm-4.665-4.283A11.945 11.945 0 0 1 8 10c2.186 0 4.236.585 6.001 1.606a7 7 0 1 0-12.002 0z"/>
</svg> <span class="align-middle">Tổng quan</span></a>
                    </li>
                    
                    <?php if(has_permission('Company', 'can_view_all')) { ?>
                    <li class="sidebar-item  <?php if ($segment1 == 'company') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('company');?>" class="sidebar-link"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-buildings" viewBox="0 0 16 16">
  <path d="M14.763.075A.5.5 0 0 1 15 .5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5V14h-1v1.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V10a.5.5 0 0 1 .342-.474L6 7.64V4.5a.5.5 0 0 1 .276-.447l8-4a.5.5 0 0 1 .487.022ZM6 8.694 1 10.36V15h5V8.694ZM7 15h2v-1.5a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 .5.5V15h2V1.309l-7 3.5V15Z"/>
  <path d="M2 11h1v1H2v-1Zm2 0h1v1H4v-1Zm-2 2h1v1H2v-1Zm2 0h1v1H4v-1Zm4-4h1v1H8V9Zm2 0h1v1h-1V9Zm-2 2h1v1H8v-1Zm2 0h1v1h-1v-1Zm2-2h1v1h-1V9Zm0 2h1v1h-1v-1ZM8 7h1v1H8V7Zm2 0h1v1h-1V7Zm2 0h1v1h-1V7ZM8 5h1v1H8V5Zm2 0h1v1h-1V5Zm2 0h1v1h-1V5Zm0-2h1v1h-1V3Z"/>
</svg> <span class="align-middle">Công ty</span></a>
                    </li>
                    <?php } ?>

					<?php if (in_array($admin_details->role, ['Admin', 'SuperAdmin'])): ?>
						<li class="sidebar-item <?= $segment1 === 'bank' ? 'active' : null; ?>">
							<a href="<?= base_url('bank') ?>" class="sidebar-link">
								<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-bank" viewBox="0 0 16 16">
									<path d="m8 0 6.61 3h.89a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5H15v7a.5.5 0 0 1 .485.38l.5 2a.498.498 0 0 1-.485.62H.5a.498.498 0 0 1-.485-.62l.5-2A.5.5 0 0 1 1 13V6H.5a.5.5 0 0 1-.5-.5v-2A.5.5 0 0 1 .5 3h.89zM3.777 3h8.447L8 1zM2 6v7h1V6zm2 0v7h2.5V6zm3.5 0v7h1V6zm2 0v7H12V6zM13 6v7h1V6zm2-1V4H1v1zm-.39 9H1.39l-.25 1h13.72z"/>
								</svg>
								<span class="align-middle">Ngân hàng</span>
							</a>
						</li>
                    <?php endif; ?>

                    <?php if(in_array($admin_details->role,['Admin','SuperAdmin'])) { ?>
                    <li class="sidebar-item  <?php if ($segment1 == 'bankaccount') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('bankaccount');?>" class="sidebar-link"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-bank" viewBox="0 0 16 16">
  <path d="m8 0 6.61 3h.89a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5H15v7a.5.5 0 0 1 .485.38l.5 2a.498.498 0 0 1-.485.62H.5a.498.498 0 0 1-.485-.62l.5-2A.5.5 0 0 1 1 13V6H.5a.5.5 0 0 1-.5-.5v-2A.5.5 0 0 1 .5 3h.89zM3.777 3h8.447L8 1zM2 6v7h1V6zm2 0v7h2.5V6zm3.5 0v7h1V6zm2 0v7H12V6zM13 6v7h1V6zm2-1V4H1v1zm-.39 9H1.39l-.25 1h13.72z"/>
</svg> <span class="align-middle">Tài khoản ngân hàng</span></a>
                    </li>
                    <?php } ?>


                    <li class="sidebar-item  <?php if ($segment1 == 'notificationCenter') echo 'active'; ?>">
                      <a href="<?= base_url('notificationCenter') ?>" class="sidebar-link d-flex align-items-center">
                        <i class="bi bi-app-indicator" style="font-size: 18px;"></i> <span class="align-middle">Trung tâm thông báo</span>
                      </a>
                    </li>

                    <?php if(has_permission('CrmActivity', 'can_view_all')) { ?>
                    <li class="sidebar-item  <?php if ($segment1 == 'crmactivity') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('crmactivity');?>" class="sidebar-link">
<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-telephone" viewBox="0 0 16 16">
  <path d="M3.654 1.328a.678.678 0 0 0-1.015-.063L1.605 2.3c-.483.484-.661 1.169-.45 1.77a17.6 17.6 0 0 0 4.168 6.608 17.6 17.6 0 0 0 6.608 4.168c.601.211 1.286.033 1.77-.45l1.034-1.034a.678.678 0 0 0-.063-1.015l-2.307-1.794a.68.68 0 0 0-.58-.122l-2.19.547a1.75 1.75 0 0 1-1.657-.459L5.482 8.062a1.75 1.75 0 0 1-.46-1.657l.548-2.19a.68.68 0 0 0-.122-.58zM1.884.511a1.745 1.745 0 0 1 2.612.163L6.29 2.98c.329.423.445.974.315 1.494l-.547 2.19a.68.68 0 0 0 .178.643l2.457 2.457a.68.68 0 0 0 .644.178l2.189-.547a1.75 1.75 0 0 1 1.494.315l2.306 1.794c.829.645.905 1.87.163 2.611l-1.034 1.034c-.74.74-1.846 1.065-2.877.702a18.6 18.6 0 0 1-7.01-4.42 18.6 18.6 0 0 1-4.42-7.009c-.362-1.03-.037-2.137.703-2.877z"/>
</svg>

<span class="align-middle">CSKH</span></a>
                    </li>
                    <?php } ?>


                    <?php if(has_permission('Ticket', 'can_view_all')) { ?>
                    <li class="sidebar-item  <?php if ($segment1 == 'ticket') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('ticket');?>" class="sidebar-link"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-life-preserver" viewBox="0 0 16 16">
  <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m6.43-5.228a7.03 7.03 0 0 1-3.658 3.658l-1.115-2.788a4 4 0 0 0 1.985-1.985zM5.228 14.43a7.03 7.03 0 0 1-3.658-3.658l2.788-1.115a4 4 0 0 0 1.985 1.985zm9.202-9.202-2.788 1.115a4 4 0 0 0-1.985-1.985l1.115-2.788a7.03 7.03 0 0 1 3.658 3.658m-8.087-.87a4 4 0 0 0-1.985 1.985L1.57 5.228A7.03 7.03 0 0 1 5.228 1.57zM8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6"/>
</svg> <span class="align-middle">Hỗ trợ</span></a>
                    </li>
                    <?php } ?>

                    <?php if(has_permission('Subscription', 'can_view_all')) { ?>
                    <li class="sidebar-item  <?php if ($segment1 == 'subscription') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('subscription');?>" class="sidebar-link"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-hdd-stack" viewBox="0 0 16 16">
  <path d="M14 10a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h12zM2 9a2 2 0 0 0-2 2v1a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-1a2 2 0 0 0-2-2H2z"/>
  <path d="M5 11.5a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0zm-2 0a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0zM14 3a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h12zM2 2a2 2 0 0 0-2 2v1a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H2z"/>
  <path d="M5 4.5a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0zm-2 0a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0z"/>
</svg> <span class="align-middle">Dịch vụ</span></a>
                    </li>
                    <?php } ?>

                    <?php if(has_permission('Invoice', 'can_view_all')) { ?>
                    <li class="sidebar-item  <?php if ($segment1 == 'invoice') {
                        echo 'active';
                    }?>">
                    <a data-bs-target="#invoice" data-bs-toggle="collapse" class="sidebar-link collapsed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-receipt" viewBox="0 0 16 16">
  <path d="M1.92.506a.5.5 0 0 1 .434.14L3 1.293l.646-.647a.5.5 0 0 1 .708 0L5 1.293l.646-.647a.5.5 0 0 1 .708 0L7 1.293l.646-.647a.5.5 0 0 1 .708 0L9 1.293l.646-.647a.5.5 0 0 1 .708 0l.646.647.646-.647a.5.5 0 0 1 .708 0l.646.647.646-.647a.5.5 0 0 1 .801.13l.5 1A.5.5 0 0 1 15 2v12a.5.5 0 0 1-.053.224l-.5 1a.5.5 0 0 1-.8.13L13 14.707l-.646.647a.5.5 0 0 1-.708 0L11 14.707l-.646.647a.5.5 0 0 1-.708 0L9 14.707l-.646.647a.5.5 0 0 1-.708 0L7 14.707l-.646.647a.5.5 0 0 1-.708 0L5 14.707l-.646.647a.5.5 0 0 1-.708 0L3 14.707l-.646.647a.5.5 0 0 1-.801-.13l-.5-1A.5.5 0 0 1 1 14V2a.5.5 0 0 1 .053-.224l.5-1a.5.5 0 0 1 .367-.27zm.217 1.338L2 2.118v11.764l.137.274.51-.51a.5.5 0 0 1 .707 0l.646.647.646-.646a.5.5 0 0 1 .708 0l.646.646.646-.646a.5.5 0 0 1 .708 0l.646.646.646-.646a.5.5 0 0 1 .708 0l.646.646.646-.646a.5.5 0 0 1 .708 0l.646.646.646-.646a.5.5 0 0 1 .708 0l.509.509.137-.274V2.118l-.137-.274-.51.51a.5.5 0 0 1-.707 0L12 1.707l-.646.647a.5.5 0 0 1-.708 0L10 1.707l-.646.647a.5.5 0 0 1-.708 0L8 1.707l-.646.647a.5.5 0 0 1-.708 0L6 1.707l-.646.647a.5.5 0 0 1-.708 0L4 1.707l-.646.647a.5.5 0 0 1-.708 0l-.509-.51z"/>
  <path d="M3 4.5a.5.5 0 0 1 .5-.5h6a.5.5 0 1 1 0 1h-6a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h6a.5.5 0 1 1 0 1h-6a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h6a.5.5 0 1 1 0 1h-6a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm8-6a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5z"/>
</svg> <span class="align-middle">Hoá đơn</span></a>

						<ul id="invoice" class="sidebar-dropdown list-unstyled collapse <?php if($segment1 == 'invoice') echo 'show';?>" data-bs-parent="#sidebar">
							<li class="sidebar-item <?php if($segment1 == 'invoice' && !isset($segments[1]) ) echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('invoice');?>">Danh sách</a></li>
							<li class="sidebar-item <?php if($segment1 == 'invoice' && isset($segments[1]) && $segments[1] == "overdue") echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('invoice/overdue');?>">Hóa đơn trễ hạn</a></li>
						</ul>
                    </li>
                    <?php } ?>

                    <?php if (has_permission('Order', 'can_view_all')): ?>
                        <li class="sidebar-item <?= $segment1 === 'orders' ? 'active' : null ?>">
                            <a href="<?= base_url('orders') ?>" class="sidebar-link collapsed" data-bs-target="#orders" data-bs-toggle="collapse">
                                <i class="bi bi-cart" style="font-size: 18px;"></i>
                                <span class="align-middle">Đơn hàng</span>
                            </a>
                            <ul id="orders" class="sidebar-dropdown list-unstyled collapse <?= $segment1 === 'orders' ? 'show' : null ?>" data-bs-parent="#sidebar">
                                <li class="sidebar-item <?= $segment1 === 'orders' && !isset($segments[1]) ? 'active' : null ?>">
                                    <a href="<?= base_url('orders') ?>" class="sidebar-link">Danh sách</a>
                                </li>
                                <li class="sidebar-item <?= $segment1 === 'orders' && isset($segments[1]) && $segments[1] === 'report' ? 'active' : null ?>">
                                    <a href="<?= base_url('orders/report') ?>" class="sidebar-link">Báo cáo</a>
                                </li>
                            </ul>
                        </li>
                    <?php endif; ?>

                    <?php if(has_permission('Stransaction', 'can_view_all')) { ?>
                    <li class="sidebar-item  <?php if ($segment1 == 'stransaction') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('stransaction');?>" class="sidebar-link"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-cash" viewBox="0 0 16 16">
  <path d="M8 10a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/>
  <path d="M0 4a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V4zm3 0a2 2 0 0 1-2 2v4a2 2 0 0 1 2 2h10a2 2 0 0 1 2-2V6a2 2 0 0 1-2-2H3z"/>
</svg> <span class="align-middle">Giao dịch</span></a>
                    </li>
                    <?php } ?>

                    <?php if(has_permission('Outputdevice', 'can_view_all')) { ?>
                    <li class="sidebar-item  <?php if ($segment1 == 'mqtt_client') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('mqtt_client');?>" class="sidebar-link"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-server" viewBox="0 0 16 16">
                        <path d="M1.333 2.667C1.333 1.194 4.318 0 8 0s6.667 1.194 6.667 2.667V4c0 1.473-2.985 2.667-6.667 2.667S1.333 5.473 1.333 4z"/>
                        <path d="M1.333 6.334v3C1.333 10.805 4.318 12 8 12s6.667-1.194 6.667-2.667V6.334a6.5 6.5 0 0 1-1.458.79C11.81 7.684 9.967 8 8 8s-3.809-.317-5.208-.876a6.5 6.5 0 0 1-1.458-.79z"/>
                        <path d="M14.667 11.668a6.5 6.5 0 0 1-1.458.789c-1.4.56-3.242.876-5.21.876-1.966 0-3.809-.316-5.208-.876a6.5 6.5 0 0 1-1.458-.79v1.666C1.333 14.806 4.318 16 8 16s6.667-1.194 6.667-2.667z"/>
                      </svg> <span class="align-middle">MQTT Client</span></a>
                    </li>
                    <?php } ?>

                    <?php if(has_permission('Outputdevice', 'can_view_all')) { ?>
                    <li class="sidebar-item  <?php if ($segment1 == 'outputdevice') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('outputdevice');?>" class="sidebar-link"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-volume-up" viewBox="0 0 16 16">
                      <path d="M11.536 14.01A8.47 8.47 0 0 0 14.026 8a8.47 8.47 0 0 0-2.49-6.01l-.708.707A7.48 7.48 0 0 1 13.025 8c0 2.071-.84 3.946-2.197 5.303z"/>
                      <path d="M10.121 12.596A6.48 6.48 0 0 0 12.025 8a6.48 6.48 0 0 0-1.904-4.596l-.707.707A5.48 5.48 0 0 1 11.025 8a5.48 5.48 0 0 1-1.61 3.89z"/>
                      <path d="M10.025 8a4.5 4.5 0 0 1-1.318 3.182L8 10.475A3.5 3.5 0 0 0 9.025 8c0-.966-.392-1.841-1.025-2.475l.707-.707A4.5 4.5 0 0 1 10.025 8M7 4a.5.5 0 0 0-.812-.39L3.825 5.5H1.5A.5.5 0 0 0 1 6v4a.5.5 0 0 0 .5.5h2.325l2.363 1.89A.5.5 0 0 0 7 12zM4.312 6.39 6 5.04v5.92L4.312 9.61A.5.5 0 0 0 4 9.5H2v-3h2a.5.5 0 0 0 .312-.11"/>
                    </svg> <span class="align-middle">Thiết bị Loa</span></a>
                    </li>
                    <?php } ?>

                    <?php if(has_permission('Partner', 'can_view_all')) { ?>

                    <li class="sidebar-item <?php if($segment1 == 'partner') echo 'active';?>">
                    
                        <a data-bs-target="#partner" data-bs-toggle="collapse" class="sidebar-link collapsed"> <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-person-vcard" viewBox="0 0 16 16">
  <path d="M5 8a2 2 0 1 0 0-4 2 2 0 0 0 0 4Zm4-2.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5ZM9 8a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4A.5.5 0 0 1 9 8Zm1 2.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5Z"/>
  <path d="M2 2a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H2ZM1 4a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H8.96c.026-.163.04-.33.04-.5C9 10.567 7.21 9 5 9c-2.086 0-3.8 1.398-3.984 3.181A1.006 1.006 0 0 1 1 12V4Z"/>
</svg> <span class="align-middle">Affiliates</span></a>

                    <ul id="partner" class="sidebar-dropdown list-unstyled collapse <?php if($segment1 == 'partner') echo 'show';?>" data-bs-parent="#sidebar">
 							          <li class="sidebar-item <?php if($segment1 == 'partner' && !isset($segments[1]) ) echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('partner');?>">Danh sách</a></li>
                         <li class="sidebar-item <?php if($segment1 == 'partner' && isset($segments[1]) && $segments[1] == "withdraw") echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('partner/withdraw');?>">Yêu cầu Rút tiền</a></li>

                     </ul>
                    </li>
                    <?php } ?>

                    <?php if(has_permission('ChannelPartner', 'can_view_all')) { ?>
    <li class="sidebar-item <?php if($segment1 == 'channelpartner') echo 'active';?>">
        <a data-bs-target="#channelpartner" data-bs-toggle="collapse" class="sidebar-link collapsed">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-person-vcard" viewBox="0 0 16 16">
                <path d="M5 8a2 2 0 1 0 0-4 2 2 0 0 0 0 4Zm4-2.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5ZM9 8a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4A.5.5 0 0 1 9 8Zm1 2.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5Z"/>
                <path d="M2 2a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H2ZM1 4a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H8.96c.026-.163.04-.33.04-.5C9 10.567 7.21 9 5 9c-2.086 0-3.8 1.398-3.984 3.181A1.006 1.006 0 0 1 1 12V4Z"/>
            </svg>
            <span class="align-middle">Channel Partner</span>
        </a>

        <ul id="channelpartner" class="sidebar-dropdown list-unstyled collapse <?php if($segment1 == 'channelpartner') echo 'show';?>" data-bs-parent="#sidebar">
            <li class="sidebar-item">
                <a data-bs-target="#ocb" data-bs-toggle="collapse" class="sidebar-link collapsed">
                    OCB
                </a>

                <ul id="ocb" class="sidebar-dropdown list-unstyled collapse <?php if(isset($segments[0]) && $segments[0] == 'channelpartner' &&  $segments[1] == 'ocb') echo 'show';?>">
                    <li class="sidebar-item <?php if(isset($segments[2]) && $segments[2] == 'partner_list' &&  $segments[1] == 'ocb') echo 'active';?>">
                        <a class="sidebar-link" href="<?php echo base_url('channelpartner/ocb/partner_list');?>">Danh sách đối tác</a>
                    </li>
                    <li class="sidebar-item <?php if(isset($segments[2]) && $segments[2] == 'commission' &&  $segments[1] == 'ocb') echo 'active';?>">
                        <a class="sidebar-link" href="<?php echo base_url('channelpartner/ocb/commission');?>">Danh sách hoa hồng</a>
                    </li>
                    <li class="sidebar-item <?php if(isset($segments[2]) && $segments[2] == 'withdraw' &&  $segments[1] == 'ocb') echo 'active';?>">
                        <a class="sidebar-link" href="<?php echo base_url('channelpartner/ocb/withdraw');?>">Yêu cầu rút tiền</a>
                    </li>
                </ul>
            </li>
            <li class="sidebar-item">
                <a data-bs-target="#vietinbank" data-bs-toggle="collapse" class="sidebar-link collapsed">
                    VietinBank
                </a>

                <ul id="vietinbank" class="sidebar-dropdown list-unstyled collapse <?php if(isset($segments[0]) && $segments[0] == 'channelpartner' &&  $segments[1] == 'vietinbank') echo 'show';?>">
                    <li class="sidebar-item <?php if(isset($segments[2]) && $segments[2] == 'partner_list' &&  $segments[1] == 'vietinbank') echo 'active';?>">
                        <a class="sidebar-link" href="<?php echo base_url('channelpartner/vietinbank/partner_list');?>">Danh sách đối tác</a>
                    </li>
                    <li class="sidebar-item <?php if(isset($segments[2]) && $segments[2] == 'commission' &&  $segments[1] == 'vietinbank') echo 'active';?>">
                        <a class="sidebar-link" href="<?php echo base_url('channelpartner/vietinbank/commission');?>">Danh sách hoa hồng</a>
                    </li>
                    <li class="sidebar-item <?php if(isset($segments[2]) && $segments[2] == 'withdraw' &&  $segments[1] == 'vietinbank') echo 'active';?>">
                        <a class="sidebar-link" href="<?php echo base_url('channelpartner/vietinbank/withdraw');?>">Yêu cầu rút tiền</a>
                    </li>
                </ul>
            </li>
        </ul>
    </li>
<?php } ?>


					<?php if (has_permission('Referral', 'can_view_all')): ?>
						<li class="sidebar-item <?= $segment1 === 'referral' ? 'active' : null ?>">
							<a data-bs-target="#referral" data-bs-toggle="collapse" class="sidebar-link collapsed">
								<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-person-add" viewBox="0 0 16 16">
									<path d="M12.5 16a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7m.5-5v1h1a.5.5 0 0 1 0 1h-1v1a.5.5 0 0 1-1 0v-1h-1a.5.5 0 0 1 0-1h1v-1a.5.5 0 0 1 1 0m-2-6a3 3 0 1 1-6 0 3 3 0 0 1 6 0M8 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4"/>
									<path d="M8.256 14a4.5 4.5 0 0 1-.229-1.004H3c.001-.246.154-.986.832-1.664C4.484 10.68 5.711 10 8 10q.39 0 .74.025c.226-.341.496-.65.804-.918Q8.844 9.002 8 9c-5 0-6 3-6 4s1 1 1 1z"/>
								</svg>
								<span class="align-middle">Giới thiệu</span>
							</a>
							<ul id="referral" class="sidebar-dropdown list-unstyled collapse <?= $segment1 === 'referral' ? 'show' : null ?>" data-bs-parent="#sidebar">
								<li class="sidebar-item <?= $segment1 === 'referral' && !isset($segments[1]) ? 'active' : null ?>">
									<a class="sidebar-link" href="<?= base_url('referral') ?>">Tổng quan</a>
								</li>
								<li class="sidebar-item <?= $segment1 === 'referral' && isset($segments[1]) && $segments[1] === 'contents' ? 'active' : null ?>">
									<a class="sidebar-link" href="<?= base_url('referral/contents') ?>">Tinh chỉnh nội dung</a>
								</li>
							</ul>
						</li>
					<?php endif; ?>

          <?php if(has_permission('BankBonus', 'can_view_all')) { ?>
              <li class="sidebar-item <?php if($segment1 == 'bankbonus') echo 'active';?>">
                  <a data-bs-target="#bankbonus" data-bs-toggle="collapse" class="sidebar-link collapsed"> <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-person-fill-gear" viewBox="0 0 16 16">
                <path d="M11 5a3 3 0 1 1-6 0 3 3 0 0 1 6 0m-9 8c0 1 1 1 1 1h5.256A4.5 4.5 0 0 1 8 12.5a4.5 4.5 0 0 1 1.544-3.393Q8.844 9.002 8 9c-5 0-6 3-6 4m9.886-3.54c.18-.613 1.048-.613 1.229 0l.043.148a.64.64 0 0 0 .921.382l.136-.074c.561-.306 1.175.308.87.869l-.075.136a.64.64 0 0 0 .382.92l.149.045c.612.18.612 1.048 0 1.229l-.15.043a.64.64 0 0 0-.38.921l.074.136c.305.561-.309 1.175-.87.87l-.136-.075a.64.64 0 0 0-.92.382l-.045.149c-.18.612-1.048.612-1.229 0l-.043-.15a.64.64 0 0 0-.921-.38l-.136.074c-.561.305-1.175-.309-.87-.87l.075-.136a.64.64 0 0 0-.382-.92l-.148-.045c-.613-.18-.613-1.048 0-1.229l.148-.043a.64.64 0 0 0 .382-.921l-.074-.136c-.306-.561.308-1.175.869-.87l.136.075a.64.64 0 0 0 .92-.382zM14 12.5a1.5 1.5 0 1 0-3 0 1.5 1.5 0 0 0 3 0"/>
              </svg> <span class="align-middle">Mở TK nhận giao dịch</span></a>
              <ul id="bankbonus" class="sidebar-dropdown list-unstyled collapse <?php if($segment1 == 'bankbonus') echo 'show';?>" data-bs-parent="#sidebar">
                  <li class="sidebar-item <?php if($segment1 == 'bankbonus' && !isset($segments[1]) ) echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('bankbonus');?>">Danh sách</a></li>
                  <li class="sidebar-item <?php if($segment1 == 'bankbonus' && isset($segments[1]) && $segments[1] == "bank") echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('bankbonus/bank');?>">Ngân hàng</a></li>
              </ul>
              </li>
            <?php } ?>

            <?php if(has_permission('OtherIntergation', 'can_view_all')) { ?>
              <li class="sidebar-item <?php if($segment1 == 'otherintergation') echo 'active';?>">
                  <a data-bs-target="#otherintergation" data-bs-toggle="collapse" class="sidebar-link collapsed"> 
                  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 50 50" width="18px" height="18px"><circle style="fill:none;stroke:currentColor;stroke-width:2;stroke-miterlimit:10;" cx="41" cy="9" r="6"/><circle style="fill:none;stroke:currentColor;stroke-width:2;stroke-miterlimit:10;" cx="41" cy="41" r="6"/><circle style="fill:none;stroke:currentColor;stroke-width:2;stroke-miterlimit:10;" cx="9" cy="25" r="6"/><polyline style="fill:none;stroke:currentColor;stroke-width:2;stroke-miterlimit:10;" points="14,25 28,25 37,14 "/><g id="XMLID_1_"><g><line style="fill:none;stroke:currentColor;stroke-width:2;stroke-miterlimit:10;" x1="28" y1="25" x2="36.41" y2="36.14"/></g></g></svg> 
                  <span class="align-middle">Tất cả tích hợp</span></a>
                  <ul id="otherintergation" class="sidebar-dropdown list-unstyled collapse <?php if($segment1 == 'otherintergation') echo 'show';?>" data-bs-parent="#sidebar">
                      <li class="sidebar-item <?php if($segment1 == 'otherintergation' && isset($segments[1]) && $segments[1] == "type") echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('otherintergation/type');?>">Loại tích hợp</a></li>
                      <li class="sidebar-item <?php if($segment1 == 'otherintergation' && !isset($segments[1]) ) echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('otherintergation');?>">Danh sách tích hợp</a></li>
                  </ul>
              </li>
            <?php } ?>

                    <?php if(has_permission('Merchant', 'can_view_all')) { ?>

<li class="sidebar-item <?php if($segment1 == 'merchant') echo 'active';?>">

    <a data-bs-target="#merchant" data-bs-toggle="collapse" class="sidebar-link collapsed"> <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-building-gear" viewBox="0 0 16 16">
  <path d="M2 1a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6.5a.5.5 0 0 1-1 0V1H3v14h3v-2.5a.5.5 0 0 1 .5-.5H8v4H3a1 1 0 0 1-1-1z"/>
  <path d="M4.5 2a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5zm3 0a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5zm3 0a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5zm-6 3a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5zm3 0a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5zm3 0a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5zm-6 3a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5zm3 0a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5zm4.386 1.46c.18-.613 1.048-.613 1.229 0l.043.148a.64.64 0 0 0 .921.382l.136-.074c.561-.306 1.175.308.87.869l-.075.136a.64.64 0 0 0 .382.92l.149.045c.612.18.612 1.048 0 1.229l-.15.043a.64.64 0 0 0-.38.921l.074.136c.305.561-.309 1.175-.87.87l-.136-.075a.64.64 0 0 0-.92.382l-.045.149c-.18.612-1.048.612-1.229 0l-.043-.15a.64.64 0 0 0-.921-.38l-.136.074c-.561.305-1.175-.309-.87-.87l.075-.136a.64.64 0 0 0-.382-.92l-.148-.045c-.613-.18-.613-1.048 0-1.229l.148-.043a.64.64 0 0 0 .382-.921l-.074-.136c-.306-.561.308-1.175.869-.87l.136.075a.64.64 0 0 0 .92-.382zM14 12.5a1.5 1.5 0 1 0-3 0 1.5 1.5 0 0 0 3 0"/>
</svg> <span class="align-middle">Merchants</span></a>

<ul id="merchant" class="sidebar-dropdown list-unstyled collapse <?php if($segment1 == 'merchant') echo 'show';?>" data-bs-parent="#sidebar">
     <li class="sidebar-item <?php if($segment1 == 'merchant' && !isset($segments[1]) ) echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('merchant');?>">Danh sách</a></li>
    <!-- <li class="sidebar-item <?php if($segment1 == 'merchant' && isset($segments[1]) && $segments[1] == "invoice") echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('merchant/invoice');?>">Hoá đơn</a></li>
     <li class="sidebar-item <?php if($segment1 == 'merchant' && isset($segments[1]) && $segments[1] == "transaction") echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('merchant/transaction');?>">Giao dịch</a></li> -->

 </ul>
</li>
<?php } ?>




                    <?php if(has_permission('Notification', 'can_view_all')) { ?>

                    <li class="sidebar-item  <?php if ($segment1 == 'notification') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('notification');?>" class="sidebar-link"> <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-envelope" viewBox="0 0 16 16">
                      <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z"/>
                    </svg><span class="align-middle">Thông báo (Legacy)</span></a>
                    </li>
                    <?php }?>

					<?php if (has_permission('CampaignAnalytic', 'can_view_all')): ?>
						<li class="sidebar-item <?= $segment1 == 'campaign-analytics' ? 'active' : '' ?>">
							<a href="<?= base_url('campaign-analytics') ?>" class="sidebar-link">
								<i class="bi bi-bar-chart-line" style="font-size: 18px;"></i>
								<span class="align-middle">Thống kê Chiến dịch</span>
							</a>
						</li>
					<?php endif; ?>

                    <?php if(has_permission('Product', 'can_view_all')) { ?>
                    <li class="sidebar-item  <?php if ($segment1 == 'product') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('product');?>" class="sidebar-link"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-boxes" viewBox="0 0 16 16">
  <path d="M7.752.066a.5.5 0 0 1 .496 0l3.75 2.143a.5.5 0 0 1 .252.434v3.995l3.498 2A.5.5 0 0 1 16 9.07v4.286a.5.5 0 0 1-.252.434l-3.75 2.143a.5.5 0 0 1-.496 0l-3.502-2-3.502 2.001a.5.5 0 0 1-.496 0l-3.75-2.143A.5.5 0 0 1 0 13.357V9.071a.5.5 0 0 1 .252-.434L3.75 6.638V2.643a.5.5 0 0 1 .252-.434zM4.25 7.504 1.508 9.071l2.742 1.567 2.742-1.567zM7.5 9.933l-2.75 1.571v3.134l2.75-1.571zm1 3.134 2.75 1.571v-3.134L8.5 9.933zm.508-3.996 2.742 1.567 2.742-1.567-2.742-1.567zm2.242-2.433V3.504L8.5 5.076V8.21zM7.5 8.21V5.076L4.75 3.504v3.134zM5.258 2.643 8 4.21l2.742-1.567L8 1.076zM15 9.933l-2.75 1.571v3.134L15 13.067zM3.75 14.638v-3.134L1 9.933v3.134z"/>
</svg> <span class="align-middle">Product</span></a>
                    </li>
                    <?php } ?>


                    <?php if(has_permission('Sim', 'can_view_all')) { ?>
                    <li class="sidebar-item  <?php if ($segment1 == 'sim') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('sim');?>" class="sidebar-link"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-sim" viewBox="0 0 16 16">
  <path d="M2 1.5A1.5 1.5 0 0 1 3.5 0h7.086a1.5 1.5 0 0 1 1.06.44l1.915 1.914A1.5 1.5 0 0 1 14 3.414V14.5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 14.5v-13zM3.5 1a.5.5 0 0 0-.5.5v13a.5.5 0 0 0 .5.5h9a.5.5 0 0 0 .5-.5V3.414a.5.5 0 0 0-.146-.353l-1.915-1.915A.5.5 0 0 0 10.586 1H3.5z"/>
  <path d="M5.5 4a.5.5 0 0 0-.5.5V6h2.5V4h-2zm3 0v2H11V4.5a.5.5 0 0 0-.5-.5h-2zM11 7H5v2h6V7zm0 3H8.5v2h2a.5.5 0 0 0 .5-.5V10zm-3.5 2v-2H5v1.5a.5.5 0 0 0 .5.5h2zM4 4.5A1.5 1.5 0 0 1 5.5 3h5A1.5 1.5 0 0 1 12 4.5v7a1.5 1.5 0 0 1-1.5 1.5h-5A1.5 1.5 0 0 1 4 11.5v-7z"/>
</svg> <span class="align-middle">SIM</span></a>
                    </li>
                    <?php } ?>

                    <?php if(has_permission('Delay', 'can_view_all')) { ?>
                    <li class="sidebar-item  <?php if ($segment1 == 'home') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('home/delay_report');?>" class="sidebar-link"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-hourglass-bottom" viewBox="0 0 16 16">
  <path d="M2 1.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-1v1a4.5 4.5 0 0 1-2.557 4.06c-.29.139-.443.377-.443.59v.7c0 .213.154.451.443.59A4.5 4.5 0 0 1 12.5 13v1h1a.5.5 0 0 1 0 1h-11a.5.5 0 1 1 0-1h1v-1a4.5 4.5 0 0 1 2.557-4.06c.29-.139.443-.377.443-.59v-.7c0-.213-.154-.451-.443-.59A4.5 4.5 0 0 1 3.5 3V2h-1a.5.5 0 0 1-.5-.5zm2.5.5v1a3.5 3.5 0 0 0 1.989 3.158c.533.256 1.011.791 1.011 1.491v.702s.18.149.5.149.5-.15.5-.15v-.7c0-.701.478-1.236 1.011-1.492A3.5 3.5 0 0 0 11.5 3V2h-7z"/>
</svg> <span class="align-middle">Delay</span></a>
                    </li>
                    <?php } ?>

                    <?php if(has_permission('ReportBank', 'can_view_all') || has_permission('ReportPaidInvoice', 'can_view_all')) { ?>
                    <li class="sidebar-item <?php if($segment1 == 'report') {
                        echo 'active';
                    }?>">
                    
                    <a data-bs-target="#statistics" data-bs-toggle="collapse" class="sidebar-link collapsed"> <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-bar-chart-line" viewBox="0 0 16 16">
<path d="M11 2a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v12h.5a.5.5 0 0 1 0 1H.5a.5.5 0 0 1 0-1H1v-3a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v3h1V7a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v7h1V2zm1 12h2V2h-2v12zm-3 0V7H7v7h2zm-5 0v-3H2v3h2z"/>
</svg> <span class="align-middle">Report</span></a>

                <ul id="statistics" class="sidebar-dropdown list-unstyled collapse <?php if($segment1 == 'report') {
                    echo 'show';
                }?>" data-bs-parent="#sidebar">

<?php if(has_permission('ReportBank', 'can_view_all') ) { ?>

                <li class="sidebar-item <?php if($segment1 == 'report' && isset($segments[1]) && $segments[1] == "bank") {
                    echo 'active';
                }?>"><a class="sidebar-link" href="<?php echo base_url('report/bank');?>">CASA Ngân hàng</a></li>

<li class="sidebar-item <?php if($segment1 == 'report' && isset($segments[1]) && $segments[1] == "bank_count") {
                    echo 'active';
                }?>"><a class="sidebar-link" href="<?php echo base_url('report/bank_count');?>">Thống kê TK</a></li>

                      <?php } ?>
                  
                  <?php if(has_permission('ReportPaidInvoice', 'can_view_all') ) { ?>

                <li class="sidebar-item <?php if($segment1 == 'report' && isset($segments[1]) && $segments[1] == "invoice_item_paid") {
                    echo 'active';
                }?>"><a class="sidebar-link" href="<?php echo base_url('report/invoice_item_paid');?>">Hoá đơn Paid</a></li>
                      <?php } ?>

              <?php if(has_permission('RecurringRevenue', 'can_view_all') ) { ?>

            <li class="sidebar-item <?php if($segment1 == 'report' && isset($segments[1]) && $segments[1] == "revenue_allocation") {
                echo 'active';
            }?>"><a class="sidebar-link" href="<?php echo base_url('report/revenue_allocation');?>">Doanh thu phân bổ</a></li>
                  <?php } ?>  


                  <?php if(has_permission('CashRevenue', 'can_view_all') ) { ?>

<li class="sidebar-item <?php if($segment1 == 'report' && isset($segments[1]) && $segments[1] == "revenue_cash") {
    echo 'active';
}?>"><a class="sidebar-link" href="<?php echo base_url('report/revenue_cash');?>">Doanh thu Không PB</a></li>
      <?php } ?>  

              </ul>
                    </li>
                    <?php } ?>
                  

                    <li class="sidebar-item mt-3 text-center">  <a  class="sidebar-link"  onclick="sidebar_behavior()" id="sidebar_behavior_icon">
                        <?php if($admin_details->sidebar_behavior =='fixed') { ?>
                        <i  class="bi bi-chevron-left"></i> Thu gọn
                        <?php } else { ?>
                        <i  class="bi bi-chevron-right"></i>
                        <?php } ?>
                        </a>
                      </li>
                      
                    
                </ul>
                

              


            </div>
        </nav>
        <div class="main">
        <div id="top-alert"></div>
        <nav class="navbar navbar-expand navbar-light navbar-bg">
            <a class="sidebar-toggle" <?php if(!is_mobile()) { ?> onclick="sidebar_toggle()" <?php } ?>>
                <i class="hamburger align-self-center"></i>
            </a>
            <h3 class="mb-0 text-truncate"><?php echo $page_title;?> </h3>

            <ul class="navbar-nav">
  <li id="quick_menu" class="nav-item px-2 dropdown">
    <a class="nav-link dropdown-toggle" href="#" id="servicesDropdown" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
      Quick menu
    </a>
    <div class="dropdown-menu dropdown-menu-start dropdown-mega" aria-labelledby="servicesDropdown">
      <div class="d-md-flex align-items-start justify-content-start">
        <div class="dropdown-mega-list">
           
          <a class="dropdown-item" href="<?php echo base_url();?>">Tổng quan</a>
          <?php if(has_permission('Company', 'can_view_all')) { ?>
          <a class="dropdown-item" href="<?php echo base_url('company');?>">Công ty</a>
          <?php } ?>
          <?php if(has_permission('Ticket', 'can_view_all')) { ?>
          <a class="dropdown-item" href="<?php echo base_url('ticket');?>">Hỗ trợ</a>
          <?php } ?>
          <?php if(has_permission('Subscription', 'can_view_all')) { ?>
          <a class="dropdown-item" href="<?php echo base_url('subscription');?>">Dịch vụ</a>
          <?php } ?>
          <?php if(has_permission('Invoice', 'can_view_all')) { ?>
          <a class="dropdown-item" href="<?php echo base_url('invoice');?>">Hoá đơn</a>
          <?php } ?>
          <?php if(has_permission('Stransaction', 'can_view_all')) { ?>
          <a class="dropdown-item" href="<?php echo base_url('stransaction');?>">Giao dịch</a>
          <?php } ?>
          <?php if(has_permission('Partner', 'can_view_all')) { ?>
          <a class="dropdown-item" href="<?php echo base_url('partner');?>">Affiliates</a>
          <?php } ?>
          <?php if(has_permission('Sim', 'can_view_all')) { ?>
          <a class="dropdown-item" href="<?php echo base_url('sim');?>">SIM</a>
          <?php } ?>
          <?php if(has_permission('Notification', 'can_view_all')) { ?>
          <a class="dropdown-item" href="<?php echo base_url('notification');?>">Thông báo</a>
          <?php } ?>
          <?php if(has_permission('Delay', 'can_view_all')) { ?>
          <a class="dropdown-item" href="<?php echo base_url('home/delay_report');?>">Delay</a>
          <?php } ?>
           
        </div>
         
      </div>
    </div>
  </li>
</ul>

<div class="navbar-collapse collapse">
                <ul class="navbar-nav navbar-align">
<?php 
  if($admin_details->theme == 'auto') {
    if(date('H') >= 6 && date('H') <= 18)
      $theme_mode = 'light';
    else
      $theme_mode = 'dark';
  } else 
    $theme_mode = $admin_details->theme;
   
?>            
                <li class="nav-item dropdown">
							<a class="nav-flag dropdown-toggle" href="#" id="languageDropdown" data-bs-toggle="dropdown">
              <?php if($admin_details->theme == 'light') { ?>
                <i class="bi bi-brightness-high-fill"></i>
              <?php } else if($admin_details->theme == 'dark') { ?>
                <i class="bi bi-moon-stars-fill"></i>
              <?php } else if($admin_details->theme == 'auto') {  ?>
                <i class="bi bi-circle-half"></i>
                <?php } ?>
              </a>
							<div class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
								<a class="dropdown-item d-flex align-items-center <?php if($admin_details->theme == 'light') echo 'active'; ?>" onclick="set_theme('light')"  href="javascript:;">
                <i class="bi bi-brightness-high-fill me-2"></i>
                  <span class="align-middle">Sáng</span>
                </a>
								<a class="dropdown-item d-flex align-items-center <?php if($admin_details->theme == 'dark') echo 'active'; ?>" onclick="set_theme('dark')" href="javascript:;">
                <i class="bi bi-moon-stars-fill  me-2"></i>
                  <span class="align-middle">Tối</span>
                </a>
                <a class="dropdown-item d-flex align-items-center <?php if($admin_details->theme == 'auto') echo 'active'; ?>" onclick="set_theme('auto')" href="javascript:;">
                <i class="bi bi-circle-half me-2"></i>
                  <span class="align-middle">Tự động</span>
                </a>
								 
							</div>
						</li>

                
                <li class="nav-item dropdown">
							<a class="nav-icon dropdown-toggle d-inline-block d-sm-none" href="#" data-bs-toggle="dropdown" aria-expanded="false">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-settings align-middle"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>
              </a>

							<a class="nav-link dropdown-toggle d-none d-sm-inline-block" href="<?php echo base_url('admin/my_profile');?>" data-bs-toggle="dropdown">
                <img src="<?php echo esc(get_gravatar($admin_details->email,32));?>" class="avatar img-fluid rounded-circle me-1" alt=""> <span class="text-dark"><?php echo esc(character_limiter( $admin_details->lastname . ' ' .$admin_details->firstname, 15, '..'));?></span>
              </a>
							<div class="dropdown-menu dropdown-menu-end">
								<a class="dropdown-item" href="<?php echo base_url('admin/my_profile');?>"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-user align-middle me-1"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg> Tài khoản</a>
                                
								<div class="dropdown-divider"></div>
								
								<a class="dropdown-item" href="<?php echo base_url('logout');?>"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-box-arrow-right align-middle me-1" viewBox="0 0 16 16">
  <path fill-rule="evenodd" d="M10 12.5a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v2a.5.5 0 0 0 1 0v-2A1.5 1.5 0 0 0 9.5 2h-8A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-2a.5.5 0 0 0-1 0v2z"/>
  <path fill-rule="evenodd" d="M15.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708.708L14.293 7.5H5.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"/>
</svg> Đăng xuất</a>
							</div>
						</li>


                </ul>
            </div> 
</nav>
