<script>

function set_theme(mode) {
  
    $.ajax({
      url : "<?php echo base_url('admin/update_theme');?>",
      type: "POST",
      data: {theme: mode, "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
      dataType: "JSON",
      success: function(data)
      {
          //if success close modal and reload ajax table
          if(data.status == true) {
              location.reload();

          } else {
              
              alert('Lỗi: ' + data.message);

          }
      
      },
      error: function (jqXHR, textStatus, errorThrown)
      {
          alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
         
      }
      
  });
  
}

function sidebar_toggle() {
  
  $.ajax({
    url : "<?php echo base_url('admin/sidebar_toggle');?>",
    type: "POST",
    data: {"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
    dataType: "JSON",
    success: function(data)
    {
        //if success close modal and reload ajax table
        if(data.status == true) {
           // location.reload();
           console.log(data);

        }  
    
    } 
    
});

}

function sidebar_behavior() {
  
  var current_sidebar_behavior = $("body").attr('data-sidebar-behavior');
  if(current_sidebar_behavior == 'fixed') {
    var sidebar_behavior = 'compact';  
    var sidebar_icon = "bi bi-chevron-right"; 
    $("#sidebar_behavior_icon").html("<i class='bi bi-chevron-right'></i>");

  } 
  else {
    var sidebar_behavior = 'fixed';
    var sidebar_icon = "bi bi-chevron-left";
    $("#sidebar_behavior_icon").html("<i class='bi bi-chevron-left'></i> <span>Thu gọn</span>");

  }

  $("body").attr('data-sidebar-behavior',sidebar_behavior);

 // $("#sidebar_behavior_icon").attr('class',sidebar_icon);


  $.ajax({
    url : "<?php echo base_url('admin/sidebar_behavior');?>",
    type: "POST",
    data: {sidebar_behavior: sidebar_behavior,"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
    dataType: "JSON",
    success: function(data)
    {
        //if success close modal and reload ajax table
        if(data.status == true) {
           // location.reload();
          

        }  
    
    } 
    
});
}

// alert message
<?php
$session = session();
$alert = FALSE;
if($session->getFlashdata('alert-success') != NULL) {
  $alert = "success";
  $message = $session->getFlashdata('alert-success');
} else if($session->getFlashdata('alert-error') != NULL) {
  $alert = "error";
  $message = $session->getFlashdata('alert-error');
} else if($session->getFlashdata('alert-warning') != NULL) {
  $alert = "warning";
  $message = $session->getFlashdata('alert-warning');
}

if($alert)
  echo "notyf.".$alert."({message:'" . $message . "', dismissible: true})";

?>
 
</script>

</body>

</html>