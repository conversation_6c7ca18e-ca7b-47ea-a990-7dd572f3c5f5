 
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
        <main class="content">
            <div class="container-fluid">
              
                <div class="card mt-3" style="width:100%">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-auto d-none d-sm-block">
                                <h3>Thông báo đã tạo</h3>
                            </div>

                            <div class="col-auto ms-auto text-end row">
                                <div class="mb-2 d-flex align-items-center" style="gap: 0.5rem;">
                                    <?php if (has_permission('Notification', 'can_edit')): ?>
                                        <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#modal-sort-popup"><i class="bi bi-sort-numeric-down me-2"></i> Sắp xếp vị trí Popup</button>
                                    <?php endif ?>
                                    <a href="<?= base_url('notificationCenter/step1');?>" class="btn btn-primary btn-sm"><i class="bi bi-pencil me-2"></i> Tạo thông báo</a>   
                                </div>

                            
                            </div>
                        </div>
                         
                        <div class="row">
                           
                           
                                <div class="table-responsive">
                                <table class="table table-hover table-striped table-bordered text-muted align-middle display nowrap" id="data_table" style="width:100%">
                                    <thead class="table-light text-muted">
                                        <tr class="align-middle">
                                            <th></th>
                                            <th>ID</th>
                                            <th>Tiêu đề</th>
                                            <th>Trạng thái gửi</th>
                                            <th>Trạng thái hiển thị</th>
                                            <th>Ngày tạo</th>
                                         </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                                </div>
                             
                            
                        </div>
                    </div>
                </div>
                
            </div>
        </main>

        <?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>


        </div>
    </div>

<?php if (has_permission('Notification', 'can_edit')): ?>
<div class="modal fade" id="modal-sort-popup" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5">Sắp xếp vị trí Popup</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('notificationCenter/ajax_sort_popup'), 'method="post" id="form-sort-popup"') ?>
            <div class="modal-body">
                <?php if (count($popup_notification_list)): ?>
                    <ul class="list-group"></ul>
                <?php else: ?>
                    <div class="alert alert-info mb-0">
                        <div class="alert-message">Hiện chưa có Popup nào khả dụng (hết ngày hiển thị hoặc bị ẩn)</div>
                    </div>
                <?php endif ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <?php if (count($popup_notification_list)): ?>
                    <button type="submit" class="btn btn-primary">Lưu thay đổi</button>
                <?php endif ?>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>
<?php endif ?>
   
<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>

<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
    
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

<script>



$(document).ready(function() {

var  table = $('#data_table').DataTable({ 
 

    "processing": true,
    "serverSide": true,
    "order": [],
    "pageLength": 20,

    "ajax": {
        "url": "<?php echo base_url('notificationCenter/ajax_list'); ?>",
        "data": {"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
        "type": "POST"
    },


    "language": {
        "sProcessing":   "Đang xử lý...",
        "sLengthMenu":   "Xem _MENU_ mục",
        "sZeroRecords":  "Không tìm thấy dòng nào phù hợp",
        "sInfo":         "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
        "sInfoEmpty":    "Đang xem 0 đến 0 trong tổng số 0 mục",
        "sInfoFiltered": "(được lọc từ _MAX_ mục)",
        "sInfoPostFix":  "",
        "sSearch":       "Tìm:",
        "sUrl":          "",
        "oPaginate": {
            "sFirst":    "Đầu",
            "sPrevious": "Trước",
            "sNext":     "Tiếp",
            "sLast":     "Cuối"
        }
    },

    "columnDefs": [
       { responsivePriority: 1, targets: 1 },
       { responsivePriority: 2, targets: 2 },
        
        { 
            "visible": false,
            "targets": [ 0 ],
            "orderable": false,
        },
 
        
    ],

    
});

table.on ('init', function () {
   $('*[type="search"][class="form-control form-control-sm"]').attr('style','max-width:120px');
   $('div.dataTables_filter').parent().attr('class','col-6');
   $('div.dataTables_length').parent().attr('class','col-6');
});


});

<?php if (has_permission('Notification', 'can_edit')): ?>
let notiPopupPos = <?= json_encode(array_map(function($item) {
    return [
        'id' => $item->id,
        'title' => $item->title
    ];
}, $popup_notification_list)) ?>

function drawNotiPopupPos() {
    if (notiPopupPos.length < 1) return;

    $('#form-sort-popup .list-group').html('')

    notiPopupPos.forEach((item, index) => $('#form-sort-popup .list-group').append(`
        <li class="list-group-item d-flex align-items-center p-2">
            ${index > 0 ? `<button type="button" class="btn btn-light btn-sm me-1 btn-up" data-id="${item.id}"><i class="bi bi-caret-up-fill"></i></button>` : (notiPopupPos.length > 1 ? '<button type="button" class="btn btn-light btn-sm me-1" disabled style="opacity: 0;"><i class="bi bi-caret-up-fill"></i></button>' : '')}
            ${index < notiPopupPos.length - 1 ? `<button type="button" class="btn btn-light btn-sm me-1 btn-down" data-id="${item.id}"><i class="bi bi-caret-down-fill"></i></button>` : (notiPopupPos ? '<button type="button" class="btn btn-light btn-sm me-1" disabled style="opacity: 0;"><i class="bi bi-caret-down-fill"></i></button>' : '')}
            <span class="ms-1">${item.title}</span>
            <input type="hidden" value="${item.id}" name="pos[]" />
        </li>
    `))
}

$(document).ready(() => {
    drawNotiPopupPos()
})

$(document).on('click', '#form-sort-popup .btn-down', (e) => {
    const id = $(e.currentTarget).data('id')
    const item = $(e.currentTarget).parent('.list-group-item')
    const pos = item.index()
    const nextId = item.next().data('id')
    const nextPos = pos + 1

    if (nextPos > notiPopupPos.length - 1 || nextPos < 0) return

    const temp = notiPopupPos[pos]
    notiPopupPos[pos] = notiPopupPos[pos + 1]
    notiPopupPos[pos + 1] = temp

    drawNotiPopupPos()
})

$(document).on('click', '#form-sort-popup .btn-up', (e) => {
    const id = $(e.currentTarget).data('id')
    const item = $(e.currentTarget).parent('.list-group-item')
    const pos = item.index()
    const prevId = item.prev().data('id')
    const prevPos = pos - 1

    if (prevPos > notiPopupPos.length - 1 || prevPos < 0) return;

    const temp = notiPopupPos[pos]
    notiPopupPos[pos] = notiPopupPos[pos - 1]
    notiPopupPos[pos - 1] = temp

    drawNotiPopupPos()
})

$('#form-sort-popup').submit((e) => {
    e.preventDefault()

    $.ajax({
        method: 'post',
        url: $('#form-sort-popup').attr('action'),
        data: $('#form-sort-popup').serialize(),
        success: (res) => {
            if (res.status) {
                    notyf.success({
                    message: 'Đã lưu thay đổi',
                    dismissible: true
                });
            } else {
                notyf.error({
                    message: res.message,
                    dismissible: true
                });
            }
        },
        error: (err) => {
            notyf.error({
                message: err.responseJSON.messages.error,
                dismissible: true
            });
        }
    })
})
<?php endif ?>
</script>
