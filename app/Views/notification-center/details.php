<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">

<main class="content">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-light mb-0">
            <li class="breadcrumb-item"><a href="<?= base_url('notificationCenter') ?>">Trung tâm thông báo</a></li> 
            <li class="breadcrumb-item active" aria-current="page">#<?= $notification_details->id ?></li> 
        </ol>
    </nav>
    <div class="container-sm mt-4">
        <div class="row mx-0 mb-4" style="--bs-gutter-x: 0px">
   
             <div class="col-12 col-lg border border-sm-end-0">
                <div class="p-3 bg-white">
                    <h3 id="mail-counter"><?= $user_count ?></h3>
                    <p class="mb-0">người dùng nhận thông báo</p>
                    <?php if ($user_count > 0): ?>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-info" data-toggle="tooltip" data-placement="top" title="<?= $seen_user_count ?> đã xem (<?= round($seen_user_count * 100 / $user_count ) ?>%)" role="progressbar" style="width: <?= round($seen_user_count * 100 / $user_count ) ?>%"></div>
                        <div class="progress-bar bg-light" role="progressbar" data-toggle="tooltip" data-placement="top" title="<?= ($user_count - $seen_user_count) ?> chưa xem (<?= round(($user_count - $seen_user_count) * 100 / $user_count ) ?>%)" style="width: <?= round(($user_count - $seen_user_count) * 100 / $user_count ) ?>%"></div>
                    </div>
                    <?php endif ?>
                </div>
            </div>

            <?php if (in_array('Mail', $notification_details->channels)): ?>
            <div class="col-12 bg-white col-lg border border-sm-end-0">
                <div class="p-3">
                    <h3 id="mail-counter"><?= $mail_count ?></h3>
                    <p class="mb-0">địa chỉ email</p>
                    <?php if ($mail_count > 0): ?>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-success" data-toggle="tooltip" data-placement="top" title="<?= $seen_mail_count ?> đã xem (<?= round($seen_mail_count * 100 / $mail_count ) ?>%)" role="progressbar" style="width: <?= round($seen_mail_count * 100 / $mail_count ) ?>%"></div>
                        <div class="progress-bar bg-primary" data-toggle="tooltip" data-placement="top" title="<?= $sent_mail_count ?> đã gửi (<?= round($sent_mail_count * 100 / $mail_count ) ?>%)" role="progressbar" style="width: <?= round($sent_mail_count * 100 / $mail_count ) ?>%"></div>
                        <div class="progress-bar bg-info" role="progressbar" data-toggle="tooltip" data-placement="top" title="<?= $pending_mail_count ?> đang chờ (<?= round($pending_mail_count * 100 / $mail_count ) ?>%)" style="width: <?= round($pending_mail_count * 100 / $mail_count ) ?>%"></div>
                        <div class="progress-bar bg-danger" role="progressbar" data-toggle="tooltip" data-placement="top" title="<?= $failed_mail_count ?> thất bại (<?= round($failed_mail_count * 100 / $mail_count ) ?>%)" style="width: <?= round($failed_mail_count * 100 / $mail_count ) ?>%"></div>
                    </div>
                    <?php endif ?>
                </div>
            </div>
            <?php endif ?>

            <?php if (in_array('Mobile', $notification_details->channels)): ?>
            <div class="col-12 col-lg border border-sm-end-0 bg-white">
                <div class="p-3">
                    <h3 id="mobile-counter"><?= $mobile_count ?></h3>
                    <p class="mb-0">thiết bị di động</p>
                    <?php if ($mobile_count > 0): ?>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-success" data-toggle="tooltip" data-placement="top" title="<?= $sent_mobile_count ?> đã gửi (<?= round($sent_mobile_count * 100 / $mobile_count ) ?>%)" role="progressbar" style="width: <?= round($sent_mobile_count * 100 / $mobile_count ) ?>%"></div>
                        <div class="progress-bar bg-info" role="progressbar" data-toggle="tooltip" data-placement="top" title="<?= $pending_mobile_count ?> đang chờ (<?= round($pending_mobile_count * 100 / $mobile_count ) ?>%)" style="width: <?= round($pending_mobile_count * 100 / $mobile_count ) ?>%"></div>
                        <div class="progress-bar bg-light" role="progressbar" data-toggle="tooltip" data-placement="top" title="<?= $invalid_mobile_count ?> bỏ qua (<?= round($invalid_mobile_count * 100 / $mobile_count ) ?>%)" style="width: <?= round($invalid_mobile_count * 100 / $mobile_count ) ?>%"></div>
                    </div>
                    <?php endif ?>
                </div>
            </div>
            <?php endif ?>
            
            <?php if (in_array('Desktop', $notification_details->channels)): ?>
            <div class="col-12 col-lg border bg-white">
                <div class="p-3">
                    <h3 id="desktop-counter"><?= $desktop_count ?></h3>
                    <p class="mb-0">thiết bị máy tính</p>
                    <?php if ($desktop_count > 0): ?>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-success" data-toggle="tooltip" data-placement="top" title="<?= $sent_desktop_count ?> đã gửi (<?= round($sent_desktop_count * 100 / $desktop_count ) ?>%)" role="progressbar" style="width: <?= round($sent_desktop_count * 100 / $desktop_count ) ?>%"></div>
                        <div class="progress-bar bg-info" role="progressbar" data-toggle="tooltip" data-placement="top" title="<?= $pending_desktop_count ?> đang chờ (<?= round($pending_desktop_count * 100 / $desktop_count ) ?>%)" style="width: <?= round($pending_desktop_count * 100 / $desktop_count ) ?>%"></div>
                        <div class="progress-bar bg-light" role="progressbar" data-toggle="tooltip" data-placement="top" title="<?= $invalid_desktop_count ?> bỏ qua (<?= round($invalid_desktop_count * 100 / $desktop_count ) ?>%)" style="width: <?= round($invalid_desktop_count * 100 / $desktop_count ) ?>%"></div>
                    </div>
                    <?php endif ?>
                </div>
            </div>
            <?php endif ?>

            
            <?php if (in_array('Popup', $notification_details->channels)): ?>
            <div class="col-12 col-lg border bg-white">
                <div class="p-3">
                    <h3 id="desktop-counter"><?= $popup_visible_days ? ($popup_visible_days > 0 ? $popup_visible_days : 0) : '—' ?></h3>
                    <p class="mb-0">ngày còn lại hiển thị Popup</p>
                </div>
            </div>
            <?php endif ?>
        </div>
    
        <div class="card">
            <?= form_open(base_url('notificationCenter/ajax_edit/' . $notification_details->id), 'method="post" id="form-edit-noti"') ?>
            <div class="card-body py-0">
                <div class="py-3 border-bottom border-light">
                    <p class="fw-bold mb-1">Ẩn thông báo</p>
                    <p class="text-muted mb-2 text-sm">Ẩn hiển thị ở Popup, thông báo của người dùng tại my.sepay.vn</p>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" role="switch" name="hidden" <?= $notification_details->hidden ? 'checked' : '' ?>>
                    </div>
                </div>

                <div class="py-3 border-bottom border-light">
                    <p class="fw-bold mb-2">Tiêu đề</p>
                    <input type="text" name="title" value='<?= esc($notification_details->title, 'attr') ?>' class="form-control" />
                </div>

                <div class="py-3 border-bottom border-light">
                    <p class="fw-bold mb-2">Bộ lọc người dùng</p>
                    <p class="mb-0"><?= array_values(array_filter($user_filters, function($item) use ($notification_details) { return $item['name'] == $notification_details->user_filter; }))[0]['label'] ?? '&mdash;' ?></p>
                </div>  
                
                <div class="py-3 border-bottom border-light">
                    <p class="fw-bold mb-2">Phân loại</p>
                    <select class="form-select" name="notificable_type">
                        <?php foreach (array_filter($notificable_type_options) as $notificable_type_option): ?>
                            <option value="<?= $notificable_type_option['value'] ?>" <?= $notification_details->notificable_type == $notificable_type_option['value'] ? 'selected' : '' ?>><?= $notificable_type_option['label'] ?></option>
                        <?php endforeach ?>
                    </select>
                </div>

                <div class="py-3 border-bottom border-light">
                    <p class="fw-bold mb-2">Mô tả</p>
                    <textarea class="form-control" name="description" rows="3"><?= $notification_details->description ?></textarea>
                </div>

                <div class="py-3 border-bottom border-light">
                    <p class="fw-bold mb-2">Nội dung</p>
                    <textarea name="body" id="body"><?= $notification_details->body ?? '' ?></textarea>
                </div>

                <?php if ($popup_notification_details): ?>
                    <div>
                        <h5 style="position: sticky; top: 0;" class="py-3 mb-0 bg-white">Cấu hình Popup</h5>
                        <div class="py-3 border-bottom border-light thumbnail-url-input-group">
                            <label class="form-label fw-bold d-block">URL ảnh mô tả</label>

                            <div style="width: 760px; max-width: 100%;">
                                <div class="rounded-sm bg-light mb-2 d-flex align-items-center justify-content-center preview overflow-hidden" style="aspect-ratio: 16/9;">
                                    <?php if ($popup_notification_details->thumbnail_url): ?>
                                        <img src="<?= $popup_notification_details->thumbnail_url ?>" style="max-width: 100%; max-height: 100%; margin: auto;" />
                                    <?php endif ?>
                                </div>

                                <input type="url" class="form-control" name="popup_thumbnail_url" placeholder="Dán URL ảnh mô tả tại đây" value="<?= esc($popup_notification_details->thumbnail_url) ?>">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>

                        <div class="py-3 border-bottom border-light form-group">
                            <label class="form-label fw-bold d-block">URL nút hành động</label>
                            <input type="url" class="form-control" name="popup_cta_url" value="<?= esc($popup_notification_details->cta_url) ?>">
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="py-3 border-bottom border-light form-group">
                            <label class="form-label fw-bold d-block">Nhãn nút hành động</label>
                            <input type="text" class="form-control" name="popup_cta_text" value="<?= esc($popup_notification_details->cta_text) ?>">
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="py-3 border-bottom border-light form-group">
                            <label class="form-label fw-bold d-block mb-0">Hiển thị đến hết ngày</label>
                            <p class="mb-2"><small>(để trống nếu muốn hiển thị vĩnh viễn)</small></p>
                            <input type="date" class="form-control" name="popup_hidden_at" value="<?= $popup_notification_details->hidden_at ? date('Y-m-d', strtotime($popup_notification_details->hidden_at)) : '' ?>">
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                <?php endif ?>

                <div class="py-3">
                    <div class="alert alert-info text-primary"><div class="alert-message"><i class="bi bi-info-square-fill"></i> Các thay đổi trên chỉ có hiệu lực với kênh thông báo Popup và mục thông báo ở my.sepay.vn</div></div>
                    <button type="submit" class="btn btn-primary btn-save d-flex align-items-center" style="gap: 0.25rem"><div class="spinner-border text-light loader" style="width: 16px; height: 16px; display: none;" role="status"></div> Lưu thay đổi</button>
                </div>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</main>

<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>


</div>
</div>

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url();?>/assets/tinymce/tinymce.min.js"></script>

<script src="<?= base_url(); ?>assets/js/jquery.validate.min.js"></script>
<script src="<?= base_url(); ?>assets/js/jquery.validate.additional-methods.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

<script>
$(function () {
  $('[data-toggle="tooltip"]').tooltip()
})

tinymce.init({
    selector: 'textarea#body',
    plugins: 'print preview paste importcss searchreplace autolink autosave save directionality code visualblocks visualchars fullscreen image link media template codesample table charmap hr pagebreak nonbreaking anchor toc insertdatetime advlist lists wordcount imagetools textpattern noneditable help charmap quickbars emoticons',
    image_caption: true,
    quickbars_selection_toolbar: 'bold italic | quicklink h2 h3 blockquote quickimage quicktable',
    noneditable_noneditable_class: 'mceNonEditable',
    toolbar_mode: 'sliding',
    contextmenu: 'link image imagetools table',
});

$(document).ready(() => {
    $('#form-edit-noti').validate({
        submitHandler: function(form) {
            $(form).find('.btn-save').prop('disabled', true)
            $(form).find('.btn-save .loader').show();

            $.ajax({
                url: $(form).attr('action'),
                method: 'POST',
                data: $(form).serialize(),
                dataType: "JSON",
                success: (res, status, request) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (res.status) {
                        window.location.reload()
                    } else {
                        notyf.error({
                            message: res.message,
                            dismissible: true
                        });
                    }
                },
                error: (err) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (err.status === 400) {
                        for (const field in err.responseJSON.messages) {
                            $(`*[name=${field}]`).addClass('is-invalid')
                            $(`*[name=${field}]`).parents('.form-group').addClass('is-invalid')
                                .children('.invalid-feedback').show().html(`
                                    <div id="${field}-error" class="error">${err.responseJSON.messages[field]}</div>
                                `)
                        }
                        return;
                    }

                    if (err.status === 403) {
                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                            dismissible: true
                        });
                        return;
                    }

                    notyf.error({
                        message: err.responseJSON.messages.error,
                        dismissible: true
                    });
                }
            })
        },
        errorElement: 'div',
        rules: {
            "title": {
                "required": true,
                "maxlength": 100,
            },
            "body": {
                "required": true,
            }
        },
        messages: {
            title: {
                required: 'Trường tiêu đề là bắt buộc',
                maxlength: 'Trường tiêu đề vượt quá 100 ký tự',
            },
            body: {
                required: 'Trường nội dung là bắt buộc'
            }
        },
        highlight: function (input) {
            $(input).addClass('is-invalid');
            $(input).parent('.input-group').addClass('is-invalid');
        },
        unhighlight: function (input) {
            $(input).removeClass('is-invalid');
            $(input).parent('.input-group').removeClass('is-invalid');
        },
        errorPlacement: function (error, input) {
            $(error).show()
            $(input).parents('.form-group').children('.invalid-feedback').append(error);
        }
    })
})

$('.thumbnail-url-input-group input[type=url]').on('input change paste', (e) => {
    let image = new Image
    image.src = e.target.value.trim()
    image.onload = () => {
        $(e.target).parent().find('.preview').html(`<img src="${image.src}" style="max-width: 100%; max-height: 100%; margin: auto;" />`)
    }
    image.onerror = () => {
        $(e.target).parent().find('.preview').html('')
    }
})
</script>
