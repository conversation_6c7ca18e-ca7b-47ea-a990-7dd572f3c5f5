<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">

<main class="content">
    <div class="container-sm" style="width: 760px; max-width: 100%;">
        <?= form_open(base_url('notificationCenter/ajax_step1'), 'id="form-add-noti"') ?>
            <div class="card mt-3 overflow-hidden">
                <div class="row mx-0 border-bottom">
                    <div class="col-12 col-sm-4 border-top p-3 border-4 text-primary border-primary">
                        <i class="bi bi-1-circle-fill fs-1"></i>
                        <p class="mb-0">Soạn thông báo</p>
                    </div>
                   
                    <div class="col-12 col-sm-4 border-top p-3 border-4">
                        <i class="bi bi-2-circle-fill fs-1"></i>
                        <p class="mb-0">Cấu hình kênh gửi thông báo</p>
                    </div>

                    <div class="col-12 col-sm-4 border-top p-3 border-4">
                        <i class="bi bi-3-circle-fill fs-1"></i>
                        <p class="mb-0">Chọn đối tượng nhận thông báo</p>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($notification_details): ?>
                        <input type="hidden" name="id" value="<?= $notification_details->id ?>">
                    <?php endif ?>

                    <div class="mb-3 form-group">
                        <label class="form-label fw-bold d-block">Tiêu đề <span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" name="title" value='<?= esc($notification_details->title ?? '', 'attr') ?>'>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3 form-group">
                        <label class="form-label fw-bold d-block">Mô tả ngắn</label>
                        <textarea class="form-control" name="description"><?= $notification_details->description ?? '' ?></textarea>
                        <div class="invalid-feedback"></div>
                    </div>
        
                     <div class="mb-3 form-group">
                        <label class="form-label fw-bold d-block">Phân loại</label>
                        <select name="notificable_type" class="form-control">
                            <?php foreach ($notificable_type_options as $option): ?>
                                <option value="<?= $option['value'] ?>" <?= $notification_details && $notification_details->notificable_type == $option['value'] ? 'selected' : '' ?>><?= $option['label'] ?></option>
                            <?php endforeach ?>
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3 form-group">
                        <label class="form-label fw-bold d-block">Nội dung <span class="text-danger">(*)</span></label>
                        <textarea name="body" id="body"><?= $notification_details->body ?? '' ?></textarea>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
                <div class="card-footer border-top d-flex align-items-center justify-content-end" style="gap: 0.5rem;">
                    <button type="submit" class="btn btn-primary btn-save d-flex align-items-center" style="gap: 0.25rem"><div class="spinner-border text-light loader" style="width: 16px; height: 16px; display: none;" role="status"></div> Tiếp tục</button>
                </div>
            </div>
        <?= form_close() ?>
    </div>
</main>

<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>


</div>
</div>

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url();?>/assets/tinymce/tinymce.min.js"></script>

<script src="<?= base_url(); ?>assets/js/jquery.validate.min.js"></script>
<script src="<?= base_url(); ?>assets/js/jquery.validate.additional-methods.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

<script>
tinymce.init({
    selector: 'textarea#body',
    plugins: 'print preview paste importcss searchreplace autolink autosave save directionality code visualblocks visualchars fullscreen image link media template codesample table charmap hr pagebreak nonbreaking anchor toc insertdatetime advlist lists wordcount imagetools textpattern noneditable help charmap quickbars emoticons',
    image_caption: true,
    quickbars_selection_toolbar: 'bold italic | quicklink h2 h3 blockquote quickimage quicktable',
    noneditable_noneditable_class: 'mceNonEditable',
    toolbar_mode: 'sliding',
    contextmenu: 'link image imagetools table',
});

$(document).ready(() => {
    $('#form-add-noti').validate({
        submitHandler: function(form) {
            $(form).find('.btn-save').prop('disabled', true)
            $(form).find('.btn-save .loader').show();

            $.ajax({
                url: $(form).attr('action'),
                method: 'POST',
                data: $(form).serialize(),
                dataType: "JSON",
                success: (res, status, request) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (res.status) {
                        window.location = '<?= base_url('notificationCenter/step2') ?>' + '/' + res.id
                    } else {
                        notyf.error({
                            message: res.message,
                            dismissible: true
                        });
                    }
                },
                error: (err) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (err.status === 400) {
                        for (const field in err.responseJSON.messages) {
                            $(`*[name=${field}]`).addClass('is-invalid')
                            $(`*[name=${field}]`).parents('.form-group').addClass('is-invalid')
                                .children('.invalid-feedback').show().html(`
                                    <div id="${field}-error" class="error">${err.responseJSON.messages[field]}</div>
                                `)
                        }
                        return;
                    }

                    if (err.status === 403) {
                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                            dismissible: true
                        });
                        return;
                    }

                    notyf.error({
                        message: err.responseJSON.messages.error,
                        dismissible: true
                    });
                }
            })
        },
        errorElement: 'div',
        rules: {
            "title": {
                "required": true,
                "maxlength": 100,
            },
            "body": {
                "required": true,
            }
        },
        messages: {
            title: {
                required: 'Trường tiêu đề là bắt buộc',
                maxlength: 'Trường tiêu đề vượt quá 100 ký tự',
            },
            body: {
                required: 'Trường nội dung là bắt buộc'
            }
        },
        highlight: function (input) {
            $(input).addClass('is-invalid');
            $(input).parent('.input-group').addClass('is-invalid');
        },
        unhighlight: function (input) {
            $(input).removeClass('is-invalid');
            $(input).parent('.input-group').removeClass('is-invalid');
        },
        errorPlacement: function (error, input) {
            $(error).show()
            $(input).parents('.form-group').children('.invalid-feedback').append(error);
        }
    })
})
</script>
