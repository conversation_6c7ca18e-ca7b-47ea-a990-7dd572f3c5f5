<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">


<main class="content">
    <div class="container-sm" style="width: 760px; max-width: 100%;">
        <?= form_open(base_url('notificationCenter/ajax_step3/' . $notification_details->id), 'id="form-add-noti"') ?>
            <div class="card mt-3 overflow-hidden">
                <div class="row mx-0 border-bottom">
                    <div class="col-12 col-sm-4 border-top p-3 border-4 text-primary border-primary">
                        <i class="bi bi-1-circle-fill fs-1"></i>
                        <p class="mb-0">Soạn thông báo</p>
                    </div>
                    <div class="col-12 col-sm-4 border-top p-3 border-4 text-primary border-primary">
                        <i class="bi bi-2-circle-fill fs-1"></i>
                        <p class="mb-0">Cấu hình kênh gửi thông báo</p>
                    </div>

                    <div class="col-12 col-sm-4 border-top p-3 border-4 text-primary border-primary">
                        <i class="bi bi-3-circle-fill fs-1"></i>
                        <p class="mb-0">Chọn đối tượng nhận thông báo</p>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3 form-group">
                        <label class="form-label fw-bold d-block">Bộ lọc người dùng <span class="text-danger">(*)</span></label>
                        <select class="form-control" name="user_filter">
                            <option value="" disabled selected>&mdash; Chọn một bộ lọc &mdash;</option>
                            <?php foreach ($user_filters as $filter): ?>
                                <option value="<?= $filter['name'] ?>"><?= $filter['label'] ?></option>
                            <?php endforeach ?>
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>

                    <?php if ($admin_details->role === 'Admin'): ?>
                    <div id="raw-sql" class="mb-3 d-none">
                        <p class="mb-2 fw-bold">Debug SQL</p>
                        <div class="font-monospace bg-light text-code code p-2 rounded-lg"></div>
                    </div>
                    <?php endif ?>

                    <div>
                        <p class="mb-2 fw-bold">Thống kê dự kiến</p>

                        <div class="row mx-0" style="--bs-gutter-x: 0px">
                            <div class="col-12 col-md border border-sm-end-0">
                                <div class="p-3">
                                    <h3 id="user-counter">N/A</h3>
                                    <p class="mb-0">người dùng</p>
                                </div>
                            </div>

                            <?php if (in_array('Mail', $notification_details->channels)): ?>
                            <div class="col-12 col-md border border-sm-end-0">
                                <div class="p-3">
                                    <h3 id="mail-counter">N/A</h3>
                                    <p class="mb-0">địa chỉ email</p>
                                </div>
                            </div>
                            <?php endif ?>

                            <?php if (in_array('Mobile', $notification_details->channels)): ?>
                            <div class="col-12 col-md border border-sm-end-0">
                                <div class="p-3">
                                    <h3 id="mobile-counter">N/A</h3>
                                    <p class="mb-0">thiết bị di động</p>
                                </div>
                            </div>
                            <?php endif ?>

                            <?php if (in_array('Desktop', $notification_details->channels)): ?>
                            <div class="col-12 col-md border">
                                <div class="p-3">
                                    <h3 id="desktop-counter">N/A</h3>
                                    <p class="mb-0">thiết bị máy tính</p>
                                </div>
                            </div>
                            <?php endif ?>
                        </div>
                    </div>
                </div>
                <div class="card-footer border-top d-flex align-items-center justify-content-end" style="gap: 0.5rem;">
                    <a href="<?= base_url('notificationCenter/step2/' . $notification_details->id) ?>" class="btn btn-light">Quay lại</a>
                    <button type="submit" class="btn btn-primary btn-save d-flex align-items-center" disabled style="gap: 0.25rem"><div class="spinner-border text-light loader" style="width: 16px; height: 16px; display: none;" role="status"></div> Gửi thông báo</button>
                </div>
            </div>
        <?= form_close() ?>
    </div>
</main>

<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>


</div>
</div>

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url();?>/assets/tinymce/tinymce.min.js"></script>
<script src="<?= base_url(); ?>assets/js/jquery.validate.min.js"></script>
<script src="<?= base_url(); ?>assets/js/jquery.validate.additional-methods.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

<script>
$('select[name=user_filter]').change((e) => {
    $('#form-add-noti').removeAttr('disabled');

    $.ajax({
        method: 'GET',
        url: '<?= base_url('notificationCenter/ajax_statistic/') . $notification_details->id ?>' + '/' + e.target.value.trim(),
        success: (res) => {
            $('#user-counter').html(res.user);
            $('#mail-counter').html(res.mail);
            $('#mobile-counter').html(res.mobile);
            $('#desktop-counter').html(res.desktop);
            $('#raw-sql').removeClass('d-none');
            $('#raw-sql .code').html(res.raw_sql);

            if (res.sendable) {
                $('#form-add-noti button[type=submit]').removeAttr('disabled');
            } else {
                $('#form-add-noti button[type=submit]').attr('disabled');
            }
        },
    })
})

$(document).ready(() => {
    $('#form-add-noti').validate({
        submitHandler: function(form) {
            $(form).find('.btn-save').prop('disabled', true)
            $(form).find('.btn-save .loader').show();

            const formData = $(form).serializeArray();

            $.ajax({
                url: $(form).attr('action'),
                method: 'POST',
                data: formData,
                dataType: "JSON",
                success: (res, status, request) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (res.status) {
                        window.location = '<?= base_url('notificationCenter/details/' . $notification_details->id) ?>'
                    } else {
                        notyf.error({
                            message: res.message,
                            dismissible: true
                        });
                    }
                },
                error: (err) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (err.status === 400) {
                        for (const field in err.responseJSON.messages) {
                            $(`*[name=${field}]`).addClass('is-invalid')
                            $(`*[name=${field}]`).parents('.form-group').addClass('is-invalid')
                                .children('.invalid-feedback').show().html(`
                                    <div id="${field}-error" class="error">${err.responseJSON.messages[field]}</div>
                                `)
                        }
                        return;
                    }

                    if (err.status === 403) {
                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                            dismissible: true
                        });
                        return;
                    }

                    notyf.error({
                        message: err.responseJSON.messages.error,
                        dismissible: true
                    });
                }
            })
        },
        errorElement: 'div',
        rules: {
            user_filter: {
                required: true
            }
        },
        messages: {
            user_filter: {
                required: 'Trường bộ lọc người dùng là bắt buộc'
            }
        },
        highlight: function (input) {
            $(input).addClass('is-invalid');
            $(input).parent('.input-group').addClass('is-invalid');
        },
        unhighlight: function (input) {
            $(input).removeClass('is-invalid');
            $(input).parent('.input-group').removeClass('is-invalid');
        },
        errorPlacement: function (error, input) {
            $(error).show()
            $(input).parents('.form-group').children('.invalid-feedback').append(error);
        }
    })
})
</script>
