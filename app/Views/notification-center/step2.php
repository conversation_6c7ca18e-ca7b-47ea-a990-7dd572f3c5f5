<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">

<main class="content">
    <div class="container-sm" style="width: 760px; max-width: 100%;">
        <?= form_open(base_url('notificationCenter/ajax_step2/' . $notification_details->id), 'id="form-add-noti"') ?>
            <div class="card mt-3 overflow-hidden">
                <div class="row mx-0 border-bottom">
                    <div class="col-12 col-sm-4 border-top p-3 border-4 text-primary border-primary">
                        <i class="bi bi-1-circle-fill fs-1"></i>
                        <p class="mb-0">Soạn thông báo</p>
                    </div>
                    <div class="col-12 col-sm-4 border-top p-3 border-4 text-primary border-primary">
                        <i class="bi bi-2-circle-fill fs-1"></i>
                        <p class="mb-0">Cấu hình kênh gửi thông báo</p>
                    </div>

                    <div class="col-12 col-sm-4 border-top p-3 border-4">
                        <i class="bi bi-3-circle-fill fs-1"></i>
                        <p class="mb-0">Chọn đối tượng nhận thông báo</p>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="p-3 border-bottom d-flex align-items-center">
                        <div class="form-check form-switch fs-4 me-auto">
                            <input class="form-check-input" type="checkbox" role="switch" id="send-mail" name="channels[]" value="Mail" <?= in_array('Mail', $notification_details->channels) ? 'checked' : '' ?>>
                            <label class="form-check-label fs-5 fw-bold" for="send-mail">Gửi qua email</label>
                        </div>

                        <div class="d-flex align-items-center ms-auto" style="gap: 0.25rem;">
                            <a href="<?= base_url('notificationCenter/previewMail/' . $notification_details->id) ?>" target="_blank" class="btn btn-light" id="preview-mail">Xem trước</a> 
                            <button type="button" class="btn btn-light text-primary" data-bs-toggle="modal" data-bs-target="#modal-test-mail"><i class="bi bi-send"></i> Gửi thử</a> 
                        </div>
                    </div>
                    <div class="p-3 border-bottom d-flex align-items-center">
                        <div class="form-check form-switch fs-4">
                            <input class="form-check-input" type="checkbox" role="switch" id="send-mobile" name="channels[]" value="Mobile" <?= in_array('Mobile', $notification_details->channels) ? 'checked' : '' ?>>
                            <label class="form-check-label fs-5 fw-bold" for="send-mobile">Gửi qua Mobile App</label>
                        </div>

                        <div class="d-flex align-items-center ms-auto" style="gap: 0.25rem;">
                            <button type="button" class="btn btn-light text-primary" data-bs-toggle="modal" data-bs-target="#modal-test-mobile"><i class="bi bi-send"></i> Gửi thử</a> 
                        </div> 
                    </div>
                    <div class="p-3 border-bottom d-flex align-items-center">
                        <div class="form-check form-switch fs-4 me-auto">
                            <input class="form-check-input" type="checkbox" role="switch" id="send-desktop" name="channels[]" value="Desktop" <?= in_array('Desktop', $notification_details->channels) ? 'checked' : '' ?>>
                            <label class="form-check-label fs-5 fw-bold" for="send-desktop">Gửi qua Desktop</label>
                        </div>

                        <div class="d-flex align-items-center ms-auto" style="gap: 0.25rem;">
                            <button type="button" class="btn btn-light text-primary" id="test-desktop"><i class="bi bi-send"></i> Gửi thử</a>
                        </div>
                    </div>
                   <div class="p-3">
                        <div class="form-check form-switch fs-4 me-auto">
                            <input class="form-check-input" type="checkbox" role="switch" id="send-popup" name="channels[]" value="Popup" <?= in_array('Popup', $notification_details->channels) ? 'checked' : '' ?>>
                            <label class="form-check-label fs-5 fw-bold" for="send-popup">Gửi qua Popup</label>
                        </div>
                        
                        <div class="row mt-4 <?= in_array('Popup', $notification_details->channels) ? '' : 'd-none' ?>" id="card-setup-popup">
                            <div class="col-12">
                                <div class="mb-3 thumbnail-url-input-group">
                                    <label class="form-label fw-bold d-block">URL ảnh mô tả</label>

                                    <div class="rounded-sm bg-light mb-2 d-flex align-items-center justify-content-center preview overflow-hidden" style="aspect-ratio: 16/9;">
                                        <?php if ($notification_details->thumbnail_url): ?>
                                            <img src="<?= $notification_details->thumbnail_url ?>" style="max-width: 100%; max-height: 100%; margin: auto;" />
                                        <?php endif ?>
                                    </div>

                                    <input type="url" class="form-control" name="popup_thumbnail_url" placeholder="Dán URL ảnh mô tả tại đây" value="<?= esc($notification_details->thumbnail_url) ?>">
                                    <div class="invalid-feedback"></div>
                                </div>

                                <div class="mb-3 form-group">
                                    <label class="form-label fw-bold d-block">URL nút hành động</label>
                                    <input type="url" class="form-control" name="popup_cta_url" value="<?= esc($notification_details->cta_url) ?>">
                                    <div class="invalid-feedback"></div>
                                </div>

                                <div class="mb-3 form-group">
                                    <label class="form-label fw-bold d-block">Nhãn nút hành động</label>
                                    <input type="text" class="form-control" name="popup_cta_text" value="<?= esc($notification_details->cta_text) ?>">
                                    <div class="invalid-feedback"></div>
                                </div>

                                <div class="mb-3 form-group">
                                    <label class="form-label fw-bold d-block mb-0">Hiển thị đến hết ngày</label>
                                    <p class="mb-2"><small>(để trống nếu muốn hiển thị vĩnh viễn)</small></p>
                                    <input type="date" class="form-control" name="popup_hidden_at" value="<?= $notification_details->hidden_at ? date('Y-m-d', strtotime($notification_details->hidden_at)) : '' ?>">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer border-top d-flex align-items-center justify-content-end" style="gap: 0.5rem;">
                    <a href="<?= base_url('notificationCenter/step1/' . $notification_details->id) ?>" class="btn btn-light">Quay lại</a>
                    <button type="submit" class="btn btn-primary btn-save d-flex align-items-center" style="gap: 0.25rem"><div class="spinner-border text-light loader" style="width: 16px; height: 16px; display: none;" role="status"></div> Tiếp tục</button>
                </div>
            </div>
        <?= form_close() ?>
    </div>
</main>

<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>

<div class="modal fade" id="modal-test-mobile" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="exampleModalLabel">Gửi thử qua Mobile App</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?php if ($email_in_testing_whitelist): ?>
                <?php if ($mobile_device_tokens): ?>
                    <?= form_open(base_url('notificationCenter/ajax_test_mobile/' . $notification_details->id), 'id="form-test-mobile" method="post"') ?>
                        <div class="modal-body">
                            Vui lòng chọn thiết bị di động thuộc tài khoản người dùng với địa chỉ email <b><?= $admin_details->email ?></b> tại trang <b>my.sepay.vn</b>

                            <ul class="list-group mt-2">
                                <?php foreach ($mobile_device_tokens as $device_token): ?>
                                    <li class="list-group-item d-flex align-items-center">
                                        <input class="form-check-input me-2 fs-4 mb-2" type="checkbox" value="<?= $device_token->id ?>" name="device_token_ids[]">
                                        <?= $device_token->platform == 'ios' 
                                            ? '<i class="bi bi-apple fs-4"></i>' : (
                                                $device_token->platform == 'android' ? '<i class="bi bi-android fs-4"></i>' : '<i class="bi bi-asterisk"></i>'
                                            ) ?> <span class="ms-1"><?= $device_token->device_name ? $device_token->device_name : '(không có tên)' ?></span>
                                    </li>
                                <?php endforeach ?>
                            </ul>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Hủy bỏ</button>
                            <button type="submit" class="btn btn-primary">Gửi thử</button>
                        </div>
                    <?= form_close() ?>
                <?php else: ?>
                    <div class="modal-body">
                        <div class="alert alert-warning mb-0">
                            <div class="alert-message">
                                Hiện không ghi nhận được thiết bị di động nào. Vui lòng tải ứng dụng SePay và đăng nhập với địa chỉ email <b><?= $admin_details->email ?></b>, sau đó tải lại trang này để hệ thống ghi nhận thiết bị mới.
                            </div>
                        </div>
                    </div>
                <?php endif ?>
            <?php else: ?>
                <div class="modal-body">
                    <div class="alert alert-warning mb-0">
                        <div class="alert-message">
                            Địa chỉ email <b><?= $admin_details->email ?></b> chưa được cấp quyền gửi thử, vui lòng liên hệ Super Admin.
                        </div>
                    </div>
                </div>
            <?php endif ?>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-test-mail" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="exampleModalLabel">Gửi thử qua email</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?php if ($email_in_testing_whitelist): ?>
                <?= form_open(base_url('notificationCenter/ajax_test_mail/' . $notification_details->id), 'id="form-test-mail" method="post"') ?>
                <div class="modal-body">
                    <div class="alert alert-info mb-0">
                        <div class="alert-message">
                            Hệ thống sẽ gửi mẫu thông báo đến địa chỉ email <b><?= $admin_details->email ?></b>.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Hủy bỏ</button>
                    <button type="submit" class="btn btn-primary">Gửi thử</button>
                </div>
                <?= form_close() ?>
            <?php else: ?>
                <div class="modal-body">
                    <div class="alert alert-warning mb-0">
                        <div class="alert-message">
                            Địa chỉ email <b><?= $admin_details->email ?></b> chưa được cấp quyền gửi thử, vui lòng liên hệ Super Admin.
                        </div>
                    </div>
                </div>
            <?php endif ?>
        </div>
    </div>
</div>

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url();?>/assets/tinymce/tinymce.min.js"></script>
<script src="<?= base_url(); ?>assets/js/jquery.validate.min.js"></script>
<script src="<?= base_url(); ?>assets/js/jquery.validate.additional-methods.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

<script>
var testMobileModal = new bootstrap.Modal(document.getElementById('modal-test-mobile'), { keyboard: false });
var testMailModal = new bootstrap.Modal(document.getElementById('modal-test-mail'), { keyboard: false });


function requestPermission() {
    if ('safari' in window && 'pushNotification' in window.safari) {
        alert('Chưa hỗ trợ gửi thông báo trên trình duyệt Safari')
        return;
    }

    return Notification.requestPermission()
}

$('#form-test-mobile').submit((e) => {
    e.preventDefault()

    $.ajax({
        method: 'post',
        url: $('#form-test-mobile').attr('action'),
        data: $('#form-test-mobile').serialize(),
        success: (res) => {
            testMobileModal.hide();

            if (res.status) {
                notyf.success({
                    message: 'Đã gửi thử mẫu thông báo, vui lòng kiểm tra thiết bị di động.',
                    dismissible: true
                });
            } else {
                notyf.error({
                    message: res.message,
                    dismissible: true
                });
            }
        },
        error: (err) => {
            testMobileModal.hide();

            notyf.error({
                message: res.message,
                dismissible: true
            });
        }
    })
})

$('#form-test-mail').submit((e) => {
    e.preventDefault()

    $.ajax({
        method: 'post',
        url: $('#form-test-mail').attr('action'),
        data: $('#form-test-mail').serialize(),
        success: (res) => {
            testMailModal.hide();

            if (res.status) {
                notyf.success({
                    message: 'Đã gửi thử mẫu thông báo, vui lòng kiểm tra mail.',
                    dismissible: true
                });
            } else {
                notyf.error({
                    message: res.message,
                    dismissible: true
                });
            }
        },
        error: (err) => {
            testMailModal.hide();

            notyf.error({
                message: res.message,
                dismissible: true
            });
        }
    })
})

$('#test-desktop').click(() => {
    requestPermission().then((permission) => {
        if (permission !== 'granted') {
            alert('Vui lòng bật cho phép gửi thông báo ở trình duyệt')
            return;
        }

        const notification = new Notification('<?= $notification_details->title ?>', {
            body: '<?= $notification_details->description ?>',
            icon: 'https://my.sepay.vn/assets/images/logo/sepay-icon.png',
        })

        notification.onclick = (event) => {
            event.preventDefault();
            window.open('https://my.sepay.vn', 'target=_blank');
        };
    })
})

$('#send-popup').change(() => {
    if ($('#send-popup').prop('checked')) {
        $('#card-setup-popup').removeClass('d-none');
    } else {
        $('#card-setup-popup').addClass('d-none');
    }
});

$(document).ready(() => {
    $('#form-add-noti').validate({
        submitHandler: function(form) {
            $(form).find('.btn-save').prop('disabled', true)
            $(form).find('.btn-save .loader').show();

            const formData = $(form).serializeArray();

            $.ajax({
                url: $(form).attr('action'),
                method: 'POST',
                data: formData,
                dataType: "JSON",
                success: (res, status, request) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (res.status) {
                        window.location = '<?= base_url('notificationCenter/step3/' . $notification_details->id) ?>'
                    } else {
                        notyf.error({
                            message: res.message,
                            dismissible: true
                        });
                    }
                },
                error: (err) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (err.status === 400) {
                        for (const field in err.responseJSON.messages) {
                            $(`*[name=${field}]`).addClass('is-invalid')
                            $(`*[name=${field}]`).parents('.form-group').addClass('is-invalid')
                                .children('.invalid-feedback').show().html(`
                                    <div id="${field}-error" class="error">${err.responseJSON.messages[field]}</div>
                                `)
                        }
                        return;
                    }

                    if (err.status === 403) {
                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                            dismissible: true
                        });
                        return;
                    }

                    notyf.error({
                        message: err.responseJSON.messages.error,
                        dismissible: true
                    });
                }
            })
        },
        errorElement: 'div',
        rules: {
        },
        messages: {
        },
        highlight: function (input) {
            $(input).addClass('is-invalid');
            $(input).parent('.input-group').addClass('is-invalid');
        },
        unhighlight: function (input) {
            $(input).removeClass('is-invalid');
            $(input).parent('.input-group').removeClass('is-invalid');
        },
        errorPlacement: function (error, input) {
            $(error).show()
            $(input).parents('.form-group').children('.invalid-feedback').append(error);
        }
    })
})

$('.thumbnail-url-input-group input[type=url]').on('input change paste', (e) => {
    let image = new Image
    image.src = e.target.value.trim()
    image.onload = () => {
        $(e.target).parent().find('.preview').html(`<img src="${image.src}" style="max-width: 100%; max-height: 100%; margin: auto;" />`)
    }
    image.onerror = () => {
        $(e.target).parent().find('.preview').html('')
    }
})
</script>
