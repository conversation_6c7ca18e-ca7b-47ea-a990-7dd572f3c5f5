<style>
    ul.pagination li a {
        position: relative;
        display: block;
        padding: var(--bs-pagination-padding-y) var(--bs-pagination-padding-x);
        font-size: var(--bs-pagination-font-size);
        color: var(--bs-pagination-color);
        text-decoration: none;
        background-color: var(--bs-pagination-bg);
        border: var(--bs-pagination-border-width) solid var(--bs-pagination-border-color);
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    ul.pagination li:first-child a {
        border-top-left-radius: var(--bs-pagination-border-radius);
        border-bottom-left-radius: var(--bs-pagination-border-radius);
    }

    ul.pagination li:last-child a {
        border-top-right-radius: var(--bs-pagination-border-radius);
        border-bottom-right-radius: var(--bs-pagination-border-radius);
    }

    ul.pagination li.active a {
        z-index: 1;
        color: var(--bs-pagination-active-color);
        background-color: var(--bs-pagination-active-bg);
        border-color: var(--bs-pagination-active-border-color);
    }
</style>

<main class="content">
    <div class="container-fluid">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url('invoice') ?>">Hóa đơn</a></li>
                <li class="breadcrumb-item active" aria-current="page">Hóa đơn trễ hạn</li>
            </ol>
        </nav>
        <div class="row row-cols-1 row-cols-sm-2 mt-3">
            <div class="col d-flex">
                <div class="card flex-fill">
                    <div class="card-body">
                        <h5 class="card-title">Tổng hóa đơn trễ hạn</h5>
                        <span class="h1 d-inline-block mt-1 mb-4" data-total-invoices></span>
                    </div>
                </div>
            </div>
            <div class="col d-flex">
                <div class="card flex-fill">
                    <div class="card-body">
                        <h5 class="card-title">Tổng số tiền trễ hạn</h5>
                        <span class="h1 d-inline-block mt-1 mb-4" data-total-amount></span>
                    </div>
                </div>
            </div>
        </div>
        <?php if ($admin_details->role == 'SuperAdmin' && !empty(array_filter($invoices, fn($invoice) => $invoice->company_status == 'Active'))) : ?>
            <div class="mb-3 text-end">
                <button type="button" class="btn btn-danger" id="suspendAllBtn">
                    <i class="align-middle" data-feather="alert-triangle"></i>
                    Khóa tất cả công ty trễ hạn
                </button>
            </div>
        <?php endif; ?>
        <div class="card">
            <div class="card-header pb-0">
                <h5 class="card-title mb-0">Danh sách hóa đơn trễ hạn</h5>
            </div>
            <div class="card-body">
                <?= form_open(base_url('invoice/overdue'), ['method' => 'get']) ?>
                <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-xl-4 row-cols-xxl-5 g-3 mb-sm-3">
                    <div class="col">
                        <div class="input-group">
                            <span class="input-group-text">ID công ty</span>
                            <input type="number" class="form-control" name="company_id" value="<?= $filters['company_id'] ?>" />
                        </div>
                    </div>
                    <div class="col">
                        <div class="input-group">
                            <span class="input-group-text">Gói dịch vụ</span>
                            <select class="form-select" name="product_id">
                                <option value="">Tất cả</option>
                                <?php foreach ($products as $product) : ?>
                                    <option value="<?= $product->id ?>" <?= $product->id == $filters['product_id'] ? 'selected' : '' ?>><?= $product->name ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="input-group">
                            <span class="input-group-text">Sắp xếp</span>
                            <select class="form-select" name="sort">
                                <option value="most_overdue" <?= $filters['sort'] == 'most_overdue' ? 'selected' : '' ?>>Trễ nhất</option>
                                <option value="least_overdue" <?= $filters['sort'] == 'least_overdue' ? 'selected' : '' ?>>Trễ ít nhất</option>
                                <option value="oldest" <?= $filters['sort'] == 'oldest' ? 'selected' : '' ?>>Cũ nhất</option>
                                <option value="newest" <?= $filters['sort'] == 'newest' ? 'selected' : '' ?>>Mới nhất</option>
                                <option value="amount_asc" <?= $filters['sort'] == 'amount_asc' ? 'selected' : '' ?>>Số tiền tăng dần</option>
                                <option value="amount_desc" <?= $filters['sort'] == 'amount_desc' ? 'selected' : '' ?>>Số tiền giảm dần</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                         <label class="form-check">
                            <input class="form-check-input" type="checkbox" name="with_inactivated" value="1" <?= $filters['with_inactivated'] ? 'checked' : '' ?>>
                            <span class="form-check-label">Hiển thị cả hóa đơn của công ty đã bị suspend</span>
                        </label>
                    </div>
                    <div class="col">
                        <button type="submit" class="btn btn-primary">Tìm</button>
                        <?php if ($filters['company_id'] || $filters['product_id'] || $filters['sort'] || $filters['with_inactivated']) : ?>
                            <a href="<?= base_url('invoice/overdue') ?>" class="btn btn-secondary">Xóa bộ lọc</a>
                        <?php endif; ?>
                    </div>
                </div>
                <?= form_close() ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Hóa đơn</th>
                                <th>Công ty</th>
                                <th>Gói dịch vụ</th>
                                <th>Số tiền</th>
                                <th>Tạo lúc</th>
                                <th>Thanh toán trước</th>
                                <th class="text-end">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($invoices as $key => $invoice) : ?>
                                <tr>
                                    <td><a href="<?= base_url('invoice/details/' . $invoice->id) ?>" target="_blank">#<?= $invoice->id ?></a></td>
                                    <td>
                                        <a href="<?= base_url('company/details/' . $invoice->company_id) ?>" target="_blank">#<?= $invoice->company_id ?> - <?= $invoice->company_name ?></a>
                                        <span class="badge bg-<?= $invoice->company_status == 'Active' ? 'success' : 'warning' ?>"><?= $invoice->company_status ?></span>
                                    </td>
                                    <td><a href="<?= base_url('subscription/edit/' . $invoice->subscription_id) ?>" target="_blank">#<?= $invoice->subscription_id ?> - <?= $invoice->product_name ?></a></td>
                                    <td><?= number_format($invoice->total) ?>đ</td>
                                    <td><?= $invoice->date ?></td>
                                    <td class="text-danger"><?= $invoice->paybefore ?> (Trễ <?= $invoice->days_overdue ?> ngày)</td>
                                    <td class="text-end">
                                        <?php if ($invoice->company_status == 'Active'): ?>
                                            <?= form_open(base_url('invoice/suspendcompany/' . $invoice->company_id), ['onsubmit' => 'return confirm("CẢNH BÁO: Bạn đang thực hiện khóa dịch vụ của công ty #' . $invoice->company_id . '.\n\nHành động này sẽ:\n- Tạm dừng dịch vụ\n- Gửi thông báo tới mail chủ sở hữu\n\nBạn có chắc chắn muốn tiếp tục?")']) ?>
                                                <button type="submit" class="btn btn-warning btn-sm">Khóa</button>
                                            <?= form_close() ?>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                            <?php if (empty($invoices)) : ?>
                                <tr>
                                    <td colspan="7" class="text-center">Không tìm thấy hóa đơn trễ hạn thanh toán nào</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                <?= $pager->links('default', 'default_full') ?>
            </div>
        </div>
    </div>
</main>

<?php if ($admin_details->role == 'SuperAdmin' && !empty(array_filter($invoices, fn($invoice) => $invoice->company_status == 'Active'))) : ?>
    <div class="modal fade" id="suspendAllModal" tabindex="-1" aria-labelledby="suspendAllModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="suspendAllModalLabel">
                        <i data-feather="alert-triangle" class="align-middle me-1"></i>
                        CẢNH BÁO: Khóa tất cả công ty trễ hạn
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <?= form_open(base_url('invoice/suspendalloverdue'), ['id' => 'suspendAllForm']) ?>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <div class="alert-message">
                            <h5>Bạn đang thực hiện khóa dịch vụ của <span class="fw-bold" id="companiesCount"><?= count($companies) ?></span> công ty có hóa đơn trễ hạn.</h5>

                            <p class="mb-0">Hành động này sẽ:</p>
                            <ul class="mb-0">
                                <li>Tạm dừng dịch vụ cho nhiều công ty</li>
                                <li>Gửi thông báo tới mail chủ sở hữu</li>
                            </ul>
                        </div>
                    </div>

                    <h5>Danh sách công ty sẽ bị khóa:</h5>
                    <div id="companiesToSuspendList"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy bỏ</button>
                    <button type="submit" class="btn btn-danger">Xác nhận khóa tất cả</button>
                </div>
                <?= form_close() ?>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php') ?>

<script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery-3.5.1.js') ?>"></script>
<script src="<?= base_url('assets/notyf/notyf.min.js') ?>"></script>
<script src="<?= base_url('assets/js/app.js?v=1') ?>"></script>

<script>
    $(() => {
        $.ajax({
            url: '<?= base_url('invoice/overdue-stats') ?>',
            type: 'GET',
            dataType: 'json',
            data: {
                company_id: '<?= $filters['company_id'] ?>',
                product_id: '<?= $filters['product_id'] ?>',
                with_inactivated: '<?= $filters['with_inactivated'] ?>',
            },
            beforeSend: function() {
                $('[data-total-invoices]').html('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>');
                $('[data-total-amount]').html('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>');
            },
            success: function(response) {
                $('[data-total-invoices]').text(response.data.total);
                $('[data-total-amount]').text(response.data.total_overdue);
            }
        });

        <?php if ($admin_details->role == 'SuperAdmin' && !empty(array_filter($invoices, fn($invoice) => $invoice->company_status == 'Active'))) : ?>
            $('#suspendAllBtn').click(function() {
                populateSuspendCompaniesModal();
                const modal = new bootstrap.Modal(document.getElementById('suspendAllModal'));
                modal.show();
            });

            function populateSuspendCompaniesModal() {
                const companiesList = $('#companiesToSuspendList');
                companiesList.empty();

                <?php
                $activeCompanies = [];
                $companyInvoiceCounts = [];
                $companiesCount = 0;

                foreach ($companies as $company) {
                    if ($company->company_status == 'Active') {
                        $activeCompanies[$company->company_id] = $company->company_name;
                        $companyInvoiceCounts[$company->company_id] = $company->invoices_count;
                        $companiesCount++;
                    }
                }
                ?>

                $('#companiesCount').text('<?= $companiesCount ?>');

                const activeCompanies = <?= json_encode($activeCompanies) ?>;
                const companyInvoiceCounts = <?= json_encode($companyInvoiceCounts) ?>;

                companiesList.append('<ol class="mb-0"></ol>');
                const orderList = companiesList.find('ol');

                for (const [companyId, companyName] of Object.entries(activeCompanies)) {
                    orderList.append(`
                        <li>
                            <strong><a href="<?= base_url('company/details/') ?>/${companyId}" target="_blank">#${companyId}</a></strong> - ${companyName}
                            <span class="badge bg-warning">${companyInvoiceCounts[companyId]} hóa đơn</span>
                        </li>
                    `);
                }
            }
        <?php endif; ?>
    });
</script>
