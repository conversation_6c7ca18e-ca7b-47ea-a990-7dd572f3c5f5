<div class="modal fade" id="previewInvoiceModal" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xem hóa đơn #<?= $invoice_details->tax_issued_id ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div class="text-center p-4 loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Đang tải...</span>
                    </div>
                    <div class="mt-2">Đang tải hóa đơn...</div>
                </div>
                <iframe id="invoicePreview" style="width: 100%; height: 80vh; display: none; border: none;"></iframe>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <a href="<?= base_url('invoice/ajax_vat_invoice/' . $invoice_details->id . '?action=download') ?>" class="btn btn-primary">
                    <i class="bi bi-download me-1"></i> Tải về
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    $(() => {
        $('[data-preview-invoice-button]').on('click', function() {
            $('#previewInvoiceModal').modal('show');
        });

        $('#previewInvoiceModal').on('show.bs.modal', function() {
            const modal = $(this);
            const iframe = modal.find('#invoicePreview');
            const loading = modal.find('.loading');

            iframe.hide();
            loading.show();

            $.ajax({
                url: '<?= base_url('invoice/ajax_vat_invoice/' . $invoice_details->id . '?action=preview') ?>',
                type: 'GET',
                success: function(response) {
                    if (response.errorCode !== 200 || !response.fileToBytes) {
                        loading.html(`
                            <div class="text-danger">
                                <i class="bi bi-exclamation-circle fs-1"></i>
                                <div class="mt-2">${response.description || 'Có lỗi xảy ra khi tải hóa đơn'}</div>
                            </div>
                        `);
                        return;
                    }

                    function base64ToUint8Array(base64) {
                        const binaryString = atob(base64);
                        const len = binaryString.length;
                        const bytes = new Uint8Array(len);
                        for (let i = 0; i < len; i++) {
                            bytes[i] = binaryString.charCodeAt(i);
                        }
                        return bytes;
                    }

                    try {
                        const pdfBytes = base64ToUint8Array(response.fileToBytes);
                        const blob = new Blob([pdfBytes], {
                            type: 'application/pdf'
                        });
                        const blobUrl = URL.createObjectURL(blob);

                        iframe.attr('src', blobUrl);

                        iframe.on('load', function() {
                            loading.hide();
                            iframe.show();
                        });
                    } catch (err) {
                        console.error(err);
                        loading.html(`
                            <div class="text-danger">
                                <i class="bi bi-exclamation-circle fs-1"></i>
                                <div class="mt-2">Lỗi xử lý file PDF</div>
                            </div>
                        `);
                    }
                },
                error: function(xhr) {
                    loading.html(`
                        <div class="text-danger">
                            <i class="bi bi-exclamation-circle fs-1"></i>
                            <div class="mt-2">Có lỗi xảy ra khi tải hóa đơn</div>
                        </div>
                    `);
                }
            });
        });

        $('#previewInvoiceModal').on('hidden.bs.modal', function() {
            const iframe = $(this).find('#invoicePreview');
            const src = iframe.attr('src');

            if (src) {
                URL.revokeObjectURL(src);
                iframe.attr('src', '');
            }

            $(this).find('.loading').html(`
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Đang tải...</span>
                </div>
                <div class="mt-2">Đang tải hóa đơn...</div>
            `);
        });
    });
</script>