<style>
    .copyjs {
        cursor: pointer;
    }
</style>
<main class="content">
    <div class="container-fluid">

        <div>
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-light mb-0">
                    <li class="breadcrumb-item"><a href="<?= base_url("company"); ?>">Công ty</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url("company/details/" . $company_details->id); ?>">#<?= esc($company_details->id . ' - ' . $company_details->short_name); ?></a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url("company/invoice/" . $company_details->id); ?>">Hoá đơn</a></li>

                    <li class="breadcrumb-item active" aria-current="page">Sửa HD #<?= esc($invoice_details->id); ?></li>
                </ol>
            </nav>
            <!-- End Breadcrumb -->
        </div>

        <div class="row mt-3">

            <?php include(APPPATH . 'Views/company/inc_left_menu.php'); ?>


            <div class="col-md-9 col-xl-10">


                <div class="">
                    <h1 class="h3 my-3 me-3">Hóa đơn #<?= esc($invoice_details->id); ?>
                        <?php if ($invoice_details->status == "Paid") { ?>
                            <span id="invoice_status" class="badge bg-success rounded-pill">Đã thanh toán</span>


                        <?php } else if ($invoice_details->status == "Unpaid") { ?>
                            <span id="invoice_status" class="badge bg-danger  rounded-pill">Chưa thanh toán</span>


                        <?php } else if ($invoice_details->status == "Cancelled") { ?>
                            <span id="invoice_status" class="badge bg-secondary  rounded-pill">Đã hủy</span>


                        <?php } else if ($invoice_details->status == "Refunded") { ?>
                            <span id="invoice_status" class="badge bg-secondary  rounded-pill">Đã hoàn tiền</span>


                        <?php } ?>

                        <?php if ($invoice_details->tax_issued == 1) { ?>
                            <span class="badge bg-success rounded-pill">Đã xuất HĐ VAT</span>

                        <?php } else if ($invoice_details->tax_issued == 0) { ?>
                            <span class="badge bg-danger  rounded-pill">Chưa xuất HĐ VAT</span>

                        <?php } ?>
                    </h1>
                    <div class="">
                        <div class="card">
                            <div class="card-body">
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Set status</button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" onclick="set_invoice_status('Paid')">Paid</a>
                                        <a class="dropdown-item" onclick="set_invoice_status('Unpaid')">Unpaid</a>
                                        <a class="dropdown-item" onclick="set_invoice_status('Cancelled')">Cancelled</a>
                                        <a class="dropdown-item" onclick="set_invoice_status('Refunded')">Refunded</a>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-outline-info" onclick="add_payment()"><i class="bi bi-plus-circle"></i> Add Payment</button>
                                <button class="btn btn-sm btn-outline-info" onclick="send_email()"><i class="bi bi-envelope"></i> Send Email (<?= esc($invoice_details->email_sent); ?>)</button>

                                <a target="_blank" href="<?= base_url('invoice/details/' . $invoice_details->id); ?>" class="btn btn-sm btn-outline-info"><i class="bi bi-eye"></i> View</a>

                                <?php if (has_permission('Invoice',  'can_delete')): ?>
                                    <button class="btn btn-sm btn-outline-danger ms-2 float-end" onclick="delete_invoice()"><i class="bi bi-trash"></i> Delete</button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>


                    <div class="">
                        <div class="card">

                            <div class="card-body">
                                <?php echo form_open('', "id='invoice_form'"); ?>
                                <input type="hidden" name="id" value="<?= $invoice_details->id; ?>">

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="inputEmail4">Invoice date</label>
                                            <input class="form-control" type="date" name="date" value="<?= esc($invoice_details->date); ?>" required />
                                            <span class="validity"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label" for="inputEmail4">Due date</label>
                                            <input class="form-control" type="date" name="duedate" value="<?= esc($invoice_details->duedate); ?>" required />
                                            <span class="validity"></span>
                                        </div>

                                    </div>
                                    <div class="col-md-6">

                                        <div class="mb-3">
                                            <label class="form-label" for="inputEmail4">Pay before</label>
                                            <input class="form-control" type="date" name="paybefore" value="<?= esc($invoice_details->paybefore); ?>" required />
                                            <span class="validity"></span>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label" for="inputEmail4">Invoice Type</label>
                                            <select id="inputState" class="form-control" name="type" required>
                                                <option value="NewOrder" <?php if ($invoice_details->type == "NewOrder") echo 'selected'; ?>>Đăng ký mới</option>
                                                <option value="Recurring" <?php if ($invoice_details->type == "Recurring") echo 'selected'; ?>>Gia hạn</option>
                                                <option value="Excess" <?php if ($invoice_details->type == "Excess") echo 'selected'; ?>>Vượt hạn mức</option>
                                                <option value="Credit" <?php if ($invoice_details->type == "Credit") echo 'selected'; ?>>Credit</option>
                                                <option value="SubscriptionChange" <?php if ($invoice_details->type == "SubscriptionChange") echo 'selected'; ?>>Đổi gói dịch vụ</option>

                                            </select>
                                        </div>

                                        <?php if ($invoice_details->status == "Paid"): ?>
                                            <div class="mb-3">
                                                <label class="form-label" for="inputEmail4">Date paid</label>
                                                <input class="form-control" type="datetime-local" name="datepaid" value="<?= esc($invoice_details->datepaid); ?>" required />
                                                <span class="validity"></span>
                                            </div>
                                        <?php endif; ?>

                                        <div class="mb-3">
                                            <label class="form-label" for="inputEmail4">% VAT</label>
                                            <input type="number" name="tax_rate" class="form-control" value="<?= intval($invoice_details->tax_rate); ?>">
                                        </div>
                                    </div>

                                </div>
                                <hr class="my-2">
                                <div class="fw-bold mb-2">Thông tin xuất hoá đơn VAT</div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="inputEmail4">Trạng thái xuất hoá đơn</label>
                                            <select name="tax_issued" id="tax_issued" class="form-select" aria-label="" required>
                                                <option value="0" <?php if ($invoice_details->tax_issued == 0) echo 'selected'; ?>>Chưa phát hành</option>
                                                <option value="1" <?php if ($invoice_details->tax_issued == 1) echo 'selected'; ?>>Đã phát hành</option>

                                            </select>
                                            <span class="validity"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label for="exampleFormControlInput2" class="form-label">Thông tin xuất</label>
                                            <select name="tax_info_id" id="tax_info_id" class="form-select" aria-label="" required>
                                                <option value="0">Chọn</option>

                                                <?php foreach ($tax_infos as $tax_info) { ?>
                                                    <option value="<?= esc($tax_info->id); ?>" <?php if ($invoice_details->tax_info_id == $tax_info->id) echo 'selected'; ?>><?= esc($tax_info->name); ?></option>
                                                <?php } ?>


                                            </select>
                                            <span class="validity"></span>
                                        </div>

                                    </div>

                                    <div class="col-md-6">
                                        <label class="form-label" for="inputEmail4">Ký hiệu hoá đơn</label>
                                        <input class="form-control" type="text" name="tax_issued_id" value="<?= esc($invoice_details->tax_issued_id); ?>" required />
                                        <span class="validity"></span>
                                    </div>
                                </div>
                                <?php if ($invoice_details->tax_issued == 1) { ?>
                                    <div class="border d-flex gap-3 align-items-center mt-3 p-3 bg-light rounded">
                                        <i class="bi bi-file-earmark-text" style="font-size: 2rem;"></i>
                                        <div class="flex-grow-1">
                                            <div class="fw-bold mb-2">Số hóa đơn: <?= esc($invoice_details->tax_issued_id); ?></div>
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-outline-primary" data-preview-invoice-button>
                                                    <i class="bi bi-eye me-1"></i> Xem
                                                </button>
                                                <a href="<?= base_url('invoice/ajax_vat_invoice/' . $invoice_details->id . '?action=download'); ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-download me-1"></i> Tải về
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <hr class="my-4">
                                <div class="mb-2"><a class="btn btn-primary btn-sm" href="javascript:;" onclick="add_item()">Thêm Item</a></div>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead class="bg-light">
                                            <th>Description</th>
                                            <th>Type</th>
                                            <th>Tax</th>
                                            <th>Start date</th>
                                            <th>End date</th>
                                            <th>Amount</th>
                                            <th></th>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($invoice_items as $item): ?>
                                                <tr>
                                                    <td><?= esc($item->description); ?></td>
                                                    <td><?= esc($item->type); ?></td>
                                                    <td><?php if ($item->taxed == 1) echo 'Yes';
                                                        else echo 'No'; ?></td>

                                                    <td><?= esc(date("d/m/Y", strtotime($item->start_date))); ?></td>
                                                    <td><?= esc(date("d/m/Y", strtotime($item->end_date))); ?></td>
                                                    <td><?= number_format($item->amount); ?> đ</td>
                                                    <td><a href="javascript:;" onclick="edit_item(<?= esc($item->id); ?>)">Sửa</a></td>

                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                        <tbody>
                                            <tr>
                                                <td colspan="4">
                                                    <div class="my-3">
                                                        <label class="form-label">Public notes</label>
                                                        <textarea name="public_note" class="form-control" placeholder="" rows="3"><?= esc($invoice_details->public_note); ?></textarea>
                                                    </div>
                                                </td>
                                                <td colspan="2">
                                                    <div>
                                                        <table width="100%" class="table">
                                                            <tbody class="fw-bold">
                                                                <tr>
                                                                    <td class="text-end">Subtotal</td>
                                                                    <td><?= number_format($invoice_details->subtotal); ?> đ</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-end">Credit</td>
                                                                    <td>0 đ</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-end">Tax (<?= number_format($invoice_details->tax_rate); ?>%)</td>
                                                                    <td><?= number_format($invoice_details->tax); ?> đ</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-end">Total</td>
                                                                    <td><?= number_format($invoice_details->total); ?> đ</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>


                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-info btn-save" onclick="invoice_update();"> Lưu thay đổi</button>
                                </div>
                                <?php echo form_close(); ?>
                                <hr class="my-4">

                                <?php if (count($stransactions) > 0): ?>
                                    <h5>Related Transactions</h5>

                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead class="bg-light">
                                                <th>Transaction ID</th>
                                                <th>Date</th>
                                                <th>Amount</th>
                                                <th></th>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($stransactions as $strans): ?>
                                                    <tr>
                                                        <td><?= esc($strans->id); ?></td>
                                                        <td><?= esc(date("d/m/Y H:i:s", strtotime($strans->date))); ?></td>
                                                        <td><?= number_format($strans->in); ?> đ</td>
                                                        <td class="text-end">
                                                            <?php if (has_permission('Stransaction', 'can_edit')): ?>
                                                                <a href="javascript:;" class="me-2" onclick="edit_payment(<?= esc($strans->id); ?>)">Sửa</a>
                                                            <?php endif; ?>
                                                            <?php if (has_permission('Stransaction',  'can_delete')): ?>
                                                                <a href="javascript:;" class="text-danger" onclick="delete_payment(<?= esc($strans->id); ?>)">Xóa</a>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>

                                <?php endif; ?>
                            </div>
                        </div>
                    </div>



                </div>




            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php'); ?>

<!-- Payment Modal -->
<div class="modal fade" id="paymentAddModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="paymentAddModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title payment-modal-title" id="paymentAddModalLabel">Thêm giao dịch</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?php echo form_open('', "id='payment_add_form' class='needs-validation form-add-payment' novalidate"); ?>
            <input type="hidden" name="invoice_id" value="<?= $invoice_details->id; ?>">
            <div class="modal-body m-lg-3">


                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label" for="inputEmail4">Date</label>
                            <input class="form-control" type="datetime-local" name="date" value="<?= date("Y-m-d H:i:s"); ?>" required />
                            <span class="validity"></span>
                        </div>



                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label" for="inputEmail4">Amount</label>
                            <input class="form-control" type="number" name="in" value="<?= esc(intval($invoice_details->total)); ?>" required />
                            <span class="validity"></span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="mb-3">
                            <label class="form-label" for="inputEmail4">Description</label>
                            <textarea name="description" class="form-control" placeholder="" rows="3"></textarea>
                            <span class="validity"></span>
                        </div>
                    </div>
                </div>


            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light me-3" data-bs-dismiss="modal">Đóng</button>
                <a class="btn btn-primary btn-account-add btn-payment-add" onclick="payment_save()">Thêm</a>
            </div>
            </form>
        </div>
    </div>
</div>
<!-- Payment Modal -->

<!-- Invoice Item Modal -->
<div class="modal fade" id="invoiceItemModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="invoiceItemModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title invoice-item-modal-title" id="invoiceItemModalLabel">Edit invoice item</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?php echo form_open('', "id='invoice_item_form' class='needs-validation form-add-payment' novalidate"); ?>
            <input type="hidden" name="id">
            <input type="hidden" name="invoice_id" value="<?= esc($invoice_details->id); ?>">
            <div class="modal-body m-lg-3">


                <div class="row">


                    <div class="col-md-6" id="product_select">
                        <div class="mb-3">
                            <label class="form-label" for="inputEmail4">Loại</label>
                            <select name="type" class="form-control" required>
                                <option value="Other">Other</option>
                                <option value="Product">Product</option>
                                <option value="Upgrade">Upgrade</option>
                                <option value="Discount">Discount</option>
                                <option value="Addon">Addon</option>

                            </select>
                            <span class="validity"></span>
                        </div>
                    </div>

                    <div class="col-md-6" id="product_select">
                        <div class="mb-3">
                            <label class="form-label" for="inputEmail4">Product</label>
                            <select name="item_id" class="form-control" required>
                                <option value="">Chọn...</option>

                                <?php foreach ($products as $product) { ?>
                                    <option value="<?= esc($product->id); ?>"><?= esc($product->name); ?></option>
                                <?php } ?>

                            </select>
                            <span class="validity"></span>
                        </div>
                    </div>


                    <div class="col-12">
                        <div class="mb-3">
                            <label class="form-label" for="inputEmail4">Item Description</label>
                            <textarea name="description" class="form-control" placeholder="" rows="3"></textarea>
                            <span class="validity"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label" for="inputEmail4">Item start date</label>
                            <input class="form-control" type="date" name="start_date" required />
                            <span class="validity"></span>
                        </div>



                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label" for="inputEmail4">Item end date</label>
                            <input class="form-control" type="date" name="end_date" required />
                            <span class="validity"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label" for="inputEmail4">Taxed?</label>
                            <select name="taxed" class="form-control">
                                <option value="0">No</option>
                                <option value="1">Yes</option>

                            </select>
                            <span class="validity"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label" for="inputEmail4">Tax %</label>
                            <input class="form-control" type="number" name="tax_rate" required />
                            <span class="validity"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label" for="inputEmail4">Amount</label>
                            <input class="form-control" type="number" name="amount" required />
                            <span class="validity"></span>
                        </div>
                    </div>


                </div>


            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light me-3" data-bs-dismiss="modal">Đóng</button>
                <a class="btn btn-primary btn-invoice-item-add" onclick="item_save()">Lưu thay đổi</a>
            </div>
            </form>
        </div>
    </div>
</div>
<!-- Payment Modal -->




<script src="<?php echo base_url(); ?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/jquery-3.6.0.min.js"></script>

<script type="text/javascript" src="<?php echo base_url(); ?>/assets/DataTables/datatables.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url(); ?>/assets/js/app.js"></script>


<script src="<?php echo base_url(); ?>/assets/clipboardjs/clipboard.min.js"></script>


<script>
    function invoice_update() {


        url = "<?php echo base_url('invoice/ajax_invoice_update'); ?>";
        post_data = $('#invoice_form').serialize();


        $(".btn-save").attr("disabled", true);
        $('#btn_loading').html('');
        $(".btn-save").html('<div class="spinner-border text-light" role="status" id="btn_loading"></div>');

        $.ajax({
            url: url,
            type: "POST",
            data: post_data,
            dataType: "JSON",
            success: function(data) {

                $(".btn-save").attr("disabled", false);
                $('#btn_loading').remove();
                $(".btn-save").html('Cập nhật');

                //if success close modal and reload ajax table
                if (data.status == true) {

                    notyf.success({
                        message: 'Đã cập nhật',
                        dismissible: true
                    });


                } else {
                    alert('Lỗi: ' + data.message);
                }

            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                $(".btn-save").attr("disabled", false);
                $('#btn_loading').remove();
                $(".btn-save").html('Cập nhật');
            }

        });
    }


    function set_invoice_status(new_status) {

        url = "<?php echo base_url('invoice/ajax_status_update'); ?>";

        $.ajax({
            url: url,
            type: "POST",
            data: {
                id: <?= $invoice_details->id; ?>,
                status: new_status,
                "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"
            },
            dataType: "JSON",
            success: function(data) {


                //if success close modal and reload ajax table
                if (data.status == true) {

                    notyf.success({
                        message: 'Đã cập nhật',
                        dismissible: true
                    });
                    if (new_status == 'Unpaid')
                        $("#invoice_status").text('Chưa thanh toán').removeClass('bg-secondary bg-danger bg-success').addClass('bg-danger');
                    else if (new_status == 'Paid')
                        $("#invoice_status").text('Đã thanh toán').removeClass('bg-secondary bg-danger bg-success').addClass('bg-success');
                    else if (new_status == 'Cancelled')
                        $("#invoice_status").text('Đã huỷ').removeClass('bg-secondary bg-danger bg-success').addClass('bg-secondary');
                    else if (new_status == 'Refunded')
                        $("#invoice_status").text('Đã hoàn tiền').removeClass('bg-secondary bg-danger bg-success').addClass('bg-secondary');

                } else {
                    alert('Lỗi: ' + data.message);
                }

            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');

            }

        });
    }

    var addPaymentModal = new bootstrap.Modal(document.getElementById('paymentAddModal'), {
        keyboard: false
    });

    var invoiceItemModal = new bootstrap.Modal(document.getElementById('invoiceItemModal'), {
        keyboard: false
    });

    function add_payment() {
        $('#payment_add_form')[0].reset();
        save_method = 'add';
        $('.payment-modal-title').text('Thêm giao dịch');
        $(".btn-payment-add").html('Thêm');
        addPaymentModal.show();
    }

    function payment_save() {
        var url;
        if (save_method == 'add') {
            url = "<?php echo base_url('stransaction/ajax_add_by_invoice'); ?>";

        } else if (save_method == 'update') {
            url = "<?php echo base_url('stransaction/ajax_update_by_invoice'); ?>";
        } else {
            url = "<?php echo base_url('stransaction'); ?>";
        }

        $(".btn-payment-add").attr("disabled", true);
        $('#btn_payment_loading').html('');
        $(".btn-payment-add").html('<div class="spinner-border text-light" role="status" id="btn_payment_loading"></div>');

        $.ajax({
            url: url,
            type: "POST",
            data: $('#payment_add_form').serialize(),
            dataType: "JSON",
            success: function(data) {
                //if success close modal and reload ajax table
                if (data.status == true) {
                    addPaymentModal.hide();


                    if (save_method == 'add') {
                        notyf.success({
                            message: 'Thêm thành công',
                            dismissible: true
                        });
                        location.reload();

                    } else if (save_method == 'update') {
                        notyf.success({
                            message: 'Sửa thành công',
                            dismissible: true
                        });
                    }
                } else {

                    $(".btn-payment-add").attr("disabled", false);
                    $('#btn_payment_loading').remove();
                    $(".btn-payment-add").html('Thêm');
                    alert('Lỗi: ' + data.message);

                }

            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                $(".btn-payment-add").attr("disabled", false);
                $('#btn_payment_loading').remove();
                $(".btn-payment-add").html('Thêm');
            }

        });
    }


    function add_item() {


        save_method = 'add';
        $('#invoice_item_form')[0].reset();
        $('.invoice-item-modal-title').text('Add invoice item');
        $(".btn-invoice-item-add").html('Thêm');
        invoiceItemModal.show();

    }


    function edit_item(id) {


        save_method = 'update';
        $('#invoice_item_form')[0].reset();

        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo base_url('invoice/ajax_get_item'); ?>/" + id,
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                if (data.status == true) {

                    $('#invoice_item_form').find('input[name=id]').val(data.data.id);
                    $('#invoice_item_form').find('input[name=start_date]').val(data.data.start_date);
                    $('#invoice_item_form').find('input[name=end_date]').val(data.data.end_date);
                    $('#invoice_item_form').find('input[name=amount]').val(parseInt(data.data.amount));
                    $('#invoice_item_form').find('textarea[name=description]').val(data.data.description);
                    $('#invoice_item_form').find('input[name=tax_rate]').val(parseInt(data.data.tax_rate));
                    $('#invoice_item_form').find('select[name=taxed]').val(parseInt(data.data.taxed));

                    $('#invoice_item_form').find('select[name=type]').val(data.data.type);
                    $('#invoice_item_form').find('select[name=item_id]').val(data.data.item_id);

                    $('.invoice-item-modal-title').text('Edit invoice item');
                    $(".btn-invoice-item-add").html('Lưu thay đổi');
                    invoiceItemModal.show();

                } else {
                    alert('Lỗi: ' + data.message);
                }


            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Lỗi không thể lấy được thông tin invoice item này');
            }
        });
    }


    function item_save() {
        var url;
        if (save_method == 'add') {
            url = "<?php echo base_url('invoice/ajax_item_add'); ?>";

        } else if (save_method == 'update') {
            url = "<?php echo base_url('invoice/ajax_item_update'); ?>";
        } else {
            url = "<?php echo base_url('invoice'); ?>";
        }

        $(".btn-invoice-item-add").attr("disabled", true);
        $('#btn_invoice_item_loading').html('');
        $(".btn-invoice-item-add").html('<div class="spinner-border text-light" role="status" id="btn_invoice_item_loading"></div>');

        $.ajax({
            url: url,
            type: "POST",
            data: $('#invoice_item_form').serialize(),
            dataType: "JSON",
            success: function(data) {
                //if success close modal and reload ajax table
                if (data.status == true) {
                    invoiceItemModal.hide();


                    if (save_method == 'add') {
                        notyf.success({
                            message: 'Thêm thành công',
                            dismissible: true
                        });
                        location.reload();

                    } else if (save_method == 'update') {
                        notyf.success({
                            message: 'Sửa thành công',
                            dismissible: true
                        });
                        location.reload();

                    }
                } else {

                    $(".btn-invoice-item-add").attr("disabled", false);
                    $('#btn_invoice_item_loading').remove();
                    $(".btn-invoice-item-add").html('Thêm');
                    alert('Lỗi: ' + data.message);

                }

            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                $(".btn-invoice-item-add").attr("disabled", false);
                $('#btn_invoice_item_loading').remove();
                $(".btn-invoice-item-add").html('Thêm');
            }

        });
    }


    function delete_item(id) {
        if (confirm("Bạn có chắc chắn muốn xóa item này?")) {
            $.ajax({
                url: "<?php echo base_url('invoice/ajax_item_delete'); ?>",
                type: "POST",
                data: {
                    id: id,
                    "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"
                },
                dataType: "JSON",
                success: function(data) {
                    //if success close modal and reload ajax table
                    if (data.status == true) {
                        location.reload();
                    } else {

                        alert('Lỗi: ' + data.message);

                    }

                },
                error: function(jqXHR, textStatus, errorThrown) {
                    alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');

                }

            });
        }

    }

    function send_email() {

        if (confirm("Bạn có chắc chắn muốn gửi email đến khách hàng thông tin hoá đơn này?") == true) {

            url = "<?php echo base_url('invoice/ajax_send_mail_invoice'); ?>";

            $.ajax({
                url: url,
                type: "POST",
                data: {
                    id: <?= $invoice_details->id; ?>,
                    "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"
                },
                dataType: "JSON",
                success: function(data) {
                    //if success close modal and reload ajax table
                    if (data.status == true) {

                        notyf.success({
                            message: 'Đã gửi email đến khách hàng',
                            dismissible: true
                        });

                    } else {
                        alert('Lỗi: ' + data.message);
                    }

                },
                error: function(jqXHR, textStatus, errorThrown) {
                    alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');

                }

            });
        }
    }

    <?php if (has_permission('Invoice',  'can_delete')): ?>

        function delete_invoice() {
            if (!confirm("CẢNH BÁO: Bạn có chắc chắn muốn xóa hoá đơn này?\n\nThao tác này không thể hoàn tác và sẽ xóa hoá đơn khỏi hệ thống.")) {
                return;
            }

            const deleteBtn = $(".btn-outline-danger");
            const originalHtml = deleteBtn.html();
            deleteBtn.attr("disabled", true);
            deleteBtn.html('<div class="spinner-border spinner-border-sm text-light" role="status"></div> Đang xóa...');

            $.ajax({
                url: "<?php echo base_url('invoice/ajax_delete'); ?>/" + <?= $invoice_details->id ?>,
                type: "POST",
                data: {
                    "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"
                },
                dataType: "JSON",
                success: function(data) {
                    if (data.status === true) {
                        window.location.href = "<?= base_url('company/invoice/' . $company_details->id) ?>";
                    } else {
                        deleteBtn.attr("disabled", false);
                        deleteBtn.html(originalHtml);
                        alert('Lỗi: ' + (data.message || 'Không thể xóa hoá đơn'));
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    deleteBtn.attr("disabled", false);
                    deleteBtn.html(originalHtml);

                    alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                }
            });
        }
    <?php endif ?>

    <?php if (has_permission('Stransaction',  'can_edit')): ?>

        function edit_payment(id) {
            save_method = 'update';
            $('#payment_add_form')[0].reset();

            if ($('#payment_add_form').find('input[name=id]').length === 0) {
                $('#payment_add_form').append('<input type="hidden" name="id">');
            }

            $.ajax({
                url: "<?php echo base_url('stransaction/ajax_get'); ?>/" + id,
                type: "GET",
                dataType: "JSON",
                success: function(data) {
                    if (data.status == true) {
                        $('#payment_add_form').find('input[name=id]').val(data.data.id);
                        $('#payment_add_form').find('input[name=date]').val(formatDateTimeForInput(data.data.date));
                        $('#payment_add_form').find('input[name=in]').val(parseInt(data.data.in));
                        $('#payment_add_form').find('textarea[name=description]').val(data.data.description);

                        $('#paymentAddModalLabel').text('Sửa giao dịch');
                        $(".btn-account-add").html('Cập nhật');

                        addPaymentModal.show();
                    } else {
                        alert('Lỗi: ' + data.message);
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    alert('Lỗi không thể lấy được thông tin giao dịch này');
                }
            });
        }

        function formatDateTimeForInput(dateString) {
            const date = new Date(dateString);

            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');

            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }
    <?php endif ?>

    <?php if (has_permission('Stransaction',  'can_delete')): ?>

        function delete_payment(id) {
            if (!confirm("Bạn có chắc chắn muốn xóa giao dịch này?")) {
                return;
            }

            $.ajax({
                url: "<?php echo base_url('stransaction/ajax_delete'); ?>/" + id,
                type: "POST",
                data: {
                    "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"
                },
                dataType: "JSON",
                success: function(data) {
                    if (data.status === true) {
                        location.reload();
                    } else {
                        alert('Lỗi: ' + (data.message || 'Không thể xóa giao dịch'));
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
                }
            });
        }
    <?php endif ?>    
</script>

<?php include(APPPATH . 'Views/invoice/includes/preview-vat-invoice.php'); ?>
