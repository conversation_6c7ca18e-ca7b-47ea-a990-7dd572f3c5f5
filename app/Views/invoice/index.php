 
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
        <main class="content">
            <div class="container-fluid">
              
                <div class="card mt-3" style="width:100%">
                    <div class="card-body">
                    <form action="<?= base_url('invoice');?>">
                    <div class="row g-3 mb-3">
                        <div class="col-md-2">
                            <label for="formGroupExampleInput" class="form-label">Invoice ID</label>
                            <input type="number" name="id" class="form-control" placeholder="Nhập ID hoá đơn" value="<?php if(isset($filter_args['id'])) echo esc($filter_args['id']);?>">
                        </div>
                        <div class="col-md-2">
                            <label for="formGroupExampleInput" class="form-label">Company ID</label>
                            <input type="number" name="company_id" class="form-control" placeholder="Nhập ID khách hàng" value="<?php if(isset($filter_args['company_id'])) echo esc($filter_args['company_id']);?>">
                        </div>
                        <div class="col-md-2">
                            <label for="formGroupExampleInput" class="form-label">Trạng thái</label>
                            <select name="status" id="inputState" class="form-select">
                                <option value="" selected>Tất cả</option>
                                <option value="Unpaid" <?php if(isset($filter_args['status']) && $filter_args['status'] == 'Unpaid') echo 'selected';?>>Chưa thanh toán</option>
                                <option value="Paid" <?php if(isset($filter_args['status']) && $filter_args['status'] == 'Paid') echo 'selected';?>>Đã thanh toán</option>
                                <option value="Cancelled" <?php if(isset($filter_args['status']) && $filter_args['status'] == 'Cancelled') echo 'selected';?>>Đã huỷ</option>
                                <option value="Refunded" <?php if(isset($filter_args['status']) && $filter_args['status'] == 'Refunded') echo 'selected';?>>Đã hoàn tiền</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="formGroupExampleInput" class="form-label">Loại</label>
                            <select name="type" id="inputState" class="form-select">
                                <option value="" selected>Tất cả</option>
                                <option value="NewOrder" <?php if(isset($filter_args['type']) && $filter_args['type'] == 'NewOrder') echo 'selected';?>>Đăng ký mới</option>
                                <option value="Recurring" <?php if(isset($filter_args['type']) && $filter_args['type'] == 'Recurring') echo 'selected';?>>Gia hạn</option>
                                <option value="Excess" <?php if(isset($filter_args['type']) && $filter_args['type'] == 'Excess') echo 'selected';?>>Vượt mức</option>
                                <option value="Credit" <?php if(isset($filter_args['type']) && $filter_args['type'] == 'Credit') echo 'selected';?>>Credit</option>
                                <option value="SubscriptionChange" <?php if(isset($filter_args['type']) && $filter_args['type'] == 'SubscriptionChange') echo 'selected';?>>Đổi gói dịch vụ</option>
                            </select>
                        </div> 
                        <div class="col-md-2">
                            <label for="formGroupExampleInput" class="form-label">Xuất hoá đơn</label>
                            <select name="tax_issued" id="inputState" class="form-select">
                                <option value="" selected>Tất cả</option>
                                <option value="0" <?php if(isset($filter_args['tax_issued']) && $filter_args['tax_issued'] == 0) echo 'selected';?>>Chưa phát hành</option>
                                <option value="1" <?php if(isset($filter_args['tax_issued']) && $filter_args['tax_issued'] == 1) echo 'selected';?>>Đã phát hành</option>
                            </select>
                        </div>

                        <div class="col my-auto">
                        <label for="formGroupExampleInput" class="form-label"></label>

                        <button type="submit" class="btn btn-primary btn-sm">Lọc</button>
                        <a href="<?= base_url('invoice');?>" class="btn btn-secondary btn-sm">Bỏ lọc</a>

                        </div>

                        </div>
                    </form>
                         
                        <div class="row">
    
                           
                                <div class="">
                                <table class="table table-hover table-striped table-bordered text-muted align-middle display nowrap" id="invoice_table" style="width:100%">
                                    <thead class="table-light text-muted">
                                        <tr class="align-middle">
                                            <th></th>
                                            <th>Invoice ID</th>
                                            <th>Company</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Total</th>
                                            <th>Xuất HĐ</th>
                                            <th>Date</th>
                                            <th></th>

                                         </tr>
                                    </thead>
                                    <tbody>
                
                                       
                                    </tbody>
                                </table>
                                </div>
                             
                            
                        </div>
                    </div>
                </div>
                
            </div>
        </main>

        <?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>


        </div>
    </div>


  

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>

<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
    
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

<script>



$(document).ready(function() {

var  table = $('#invoice_table').DataTable({ 
   rowReorder: {
       selector: 'td:nth-child(0)'
   },
   responsive: true,


    "processing": true,
    "serverSide": true,
    "order": [],
    "pageLength": 20,

    <?php 

    $url = base_url('invoice/all_ajax_list') . '/?' . http_build_query($filter_args);
    
    ?>

    "ajax": {
        "url": "<?= $url; ?>",
        "data": {"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
        "type": "POST"
    },


    "language": {
        "sProcessing":   "Đang xử lý...",
        "sLengthMenu":   "Xem _MENU_ mục",
        "sZeroRecords":  "Không tìm thấy dòng nào phù hợp",
        "sInfo":         "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
        "sInfoEmpty":    "Đang xem 0 đến 0 trong tổng số 0 mục",
        "sInfoFiltered": "(được lọc từ _MAX_ mục)",
        "sInfoPostFix":  "",
        "sSearch":       "Tìm:",
        "sUrl":          "",
        "oPaginate": {
            "sFirst":    "Đầu",
            "sPrevious": "Trước",
            "sNext":     "Tiếp",
            "sLast":     "Cuối"
        }
    },

    "columnDefs": [
       { responsivePriority: 1, targets: 1 },
       { responsivePriority: 2, targets: 2 },
       { responsivePriority: 3, targets: 3 },
       { responsivePriority: 4, targets: 7 },

        { 
            "visible": false,
            "targets": [ 0 ],
            "orderable": false,
        },

        { 
            "targets": [7],
            "orderable": false,
        } 
        
    ],

    
});

table.on ('init', function () {
   $('*[type="search"][class="form-control form-control-sm"]').attr('style','max-width:120px');
   $('div.dataTables_filter').parent().attr('class','col-6');
   $('div.dataTables_length').parent().attr('class','col-6');
});


});

<?php if (has_permission('Invoice', 'can_delete')): ?>
function delete_invoice(id) {
    if (!confirm("CẢNH BÁO: Bạn có chắc chắn muốn xóa hoá đơn #" + id + "?\n\nThao tác này không thể hoàn tác và sẽ xóa hoàn toàn hoá đơn khỏi hệ thống.")) {
        return;
    }
    
    $.ajax({
        url: "<?php echo base_url('invoice/ajax_delete'); ?>/" + id,
        type: "POST",
        data: {
            "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"
        },
        dataType: "JSON",
        success: function(data) {
            if (data.status === true) {
                notyf.success({
                    message: 'Đã xóa hoá đơn thành công',
                    dismissible: true
                });
                
                $('#invoice_table').DataTable().ajax.reload();
            } else {
                alert('Lỗi: ' + (data.message || 'Không thể xóa hoá đơn'));
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
        }
    });
}
<?php endif; ?>
</script>