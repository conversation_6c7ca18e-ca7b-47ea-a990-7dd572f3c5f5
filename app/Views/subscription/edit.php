<main class="content">
    <div class="container-fluid">
        <div>
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-light mb-0">
                    <li class="breadcrumb-item"><a href="<?= base_url("company"); ?>">Công ty</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url("company/details/" . $company_details->id); ?>">#<?= esc($company_details->id . ' - ' . $company_details->short_name); ?></a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url("company/subscription/" . $company_details->id); ?>">Subscription</a></li>
                    <li class="breadcrumb-item active" aria-current="page">#<?= esc($subscription_details->id); ?></li>
                </ol>
            </nav>
            <!-- End Breadcrumb -->
        </div>
        <div class="row mt-3">
            <?php include(APPPATH . 'Views/company/inc_left_menu.php'); ?>
            <div class="col-md-9 col-xl-10">
                <div class="">
                    <h1 class="h3 my-3 me-3">Subscription #<?= esc($subscription_details->id); ?>
                        <?php if ($subscription_details->status == "Active") { ?>
                            <span id="subscription_status" class="badge bg-success rounded-pill">Active</span>
                        <?php } else if ($subscription_details->status == "Pending") { ?>
                            <span id="subscription_status" class="badge bg-danger  rounded-pill">Pending</span>
                        <?php } else if ($subscription_details->status == "Suspended") { ?>
                            <span id="subscription_status" class="badge bg-warning  rounded-pill">Suspended</span>
                        <?php } else if ($subscription_details->status == "Terminated") { ?>
                            <span id="subscription_status" class="badge bg-secondary  rounded-pill">Terminated</span>
                        <?php } else if ($subscription_details->status == "Cancelled") { ?>
                            <span id="subscription_status" class="badge bg-secondary  rounded-pill">Cancelled</span>
                        <?php } ?>
                    </h1>
                    <div class="">
                        <div class="card">
                            <div class="card-body">
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Set status</button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" onclick="set_subscription_status('Pending')">Pending</a>
                                        <a class="dropdown-item" onclick="set_subscription_status('Active')">Active</a>
                                        <a class="dropdown-item" onclick="set_subscription_status('Suspended')">Suspended</a>
                                        <a class="dropdown-item" onclick="set_subscription_status('Terminated')">Terminated</a>
                                        <a class="dropdown-item" onclick="set_subscription_status('Cancelled')">Cancelled</a>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-outline-info" onclick="generate_invoice()"><i class="bi bi-plus-circle"></i> Generate Invoice</button>
                                <button class="btn btn-sm btn-outline-danger ms-2 float-end"><i class="bi bi-trash"></i> Delete</button>
                            </div>
                        </div>
                    </div>

                    <div class="">
                        <div class="card">
                            <div class="card-body">
                                <?php echo form_open('', "id='subscription_form'"); ?>
                                <input type="hidden" name="id" value="<?= $subscription_details->id; ?>">

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="inputEmail4">Begin date</label>
                                            <input class="form-control" type="date" name="begin_date" value="<?= esc($subscription_details->begin_date); ?>" required />
                                            <span class="validity"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label" for="inputEmail4">End date</label>
                                            <input class="form-control" type="date" name="end_date" value="<?= esc($subscription_details->end_date); ?>" required />
                                            <span class="validity"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label" for="inputEmail4">Plan</label>
                                            <select id="inputState" class="form-select" name="plan_id" required>
                                                <?php foreach ($products as $product): ?>
                                                    <option value="<?= esc($product->id); ?>" <?php if ($product->id == $subscription_details->plan_id) echo 'selected'; ?>><?= esc($product->name); ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label" for="inputEmail4">Monthly Transactions Limit</label>
                                            <input type="number" value="<?= esc(intval($subscription_details->monthly_transaction_limit)); ?>" class="form-control" name="monthly_transaction_limit">
                                        </div>
                                    </div>
                                    <div class="col-md-6">

                                        <div class="mb-3">
                                            <label class="form-label" for="inputEmail4">First payment</label>
                                            <input type="number" value="<?= esc(intval($subscription_details->first_payment)); ?>" class="form-control" name="first_payment">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label" for="inputEmail4">Recurring payment</label>
                                            <input type="number" value="<?= esc(intval($subscription_details->recurring_payment)); ?>" class="form-control" name="recurring_payment">
                                        </div>


                                        <div class="mb-3">
                                            <label class="form-label" for="inputEmail4">Billing Cycle</label>
                                            <select id="inputState" class="form-select" name="billing_cycle" required>
                                                <option value="free" <?php if ($subscription_details->billing_cycle == 'free') echo 'selected'; ?>>Free</option>
                                                <option value="monthly" <?php if ($subscription_details->billing_cycle == 'monthly') echo 'selected'; ?>>Monthly</option>
                                                <option value="quarterly" <?php if ($subscription_details->billing_cycle == 'quarterly') echo 'selected'; ?>>Quarterly</option>
                                                <option value="semi-annually" <?php if ($subscription_details->billing_cycle == 'semi-annually') echo 'selected'; ?>>Semi-Annually</option>
                                                <option value="annually" <?php if ($subscription_details->billing_cycle == 'annually') echo 'selected'; ?>>Annually</option>
                                                <option value="biennially" <?php if ($subscription_details->billing_cycle == 'biennially') echo 'selected'; ?>>Biennially</option>
                                                <option value="triennially" <?php if ($subscription_details->billing_cycle == 'triennially') echo 'selected'; ?>>Triennially</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" value="1" id="allow_exceed_limit" name="allow_exceed_limit" <?= $subscription_details->allow_exceed_limit ? 'checked' : ''; ?>>
                                            <span class="form-check-label" for="allow_exceed_limit">
                                                Cho phép vượt hạn mức giao dịch
                                            </span>
                                        </label>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" value="1" id="disable_auto_suspend" name="disable_auto_suspend" <?= $subscription_details->disable_auto_suspend == 1 ? 'checked' : ''; ?>>
                                            <span class="form-check-label" for="disable_auto_suspend">
                                                Không tự động khóa khi trễ hạn thanh toán
                                            </span>
                                        </label>
                                    </div>

                                    <div id="disable_suspension_until_div" style="display: <?= $subscription_details->disable_auto_suspend == 1 ? 'block' : 'none'; ?>">
                                        <div class="row row-cols-2">
                                            <div class="col mb-3">
                                                <label for="disable_suspension_days" class="form-label">Sẽ không khóa cho đến</label>
                                                <select class="form-select" name="disable_suspension_days" id="disable_suspension_days">
                                                    <?php
                                                    $options = [
                                                        '+1 day' => '1 ngày sau',
                                                        '+2 day' => '2 ngày sau',
                                                        '+3 day' => '3 ngày sau',
                                                        '+5 day' => '5 ngày sau',
                                                        '+1 week' => '1 tuần sau',
                                                        '+2 week' => '2 tuần sau',
                                                    ];

                                                    $dates = [];

                                                    foreach ($options as $interval => $label) {
                                                        $date = date('Y-m-d', strtotime($interval));
                                                        $dates[] = $date;
                                                        $selected = $subscription_details->disable_suspension_until == $date || (empty($subscription_details->disable_suspension_until) && $interval === '+1 week') ? 'selected' : '';
                                                        echo "<option value=\"$date\" $selected>$label</option>";
                                                    }
                                                    ?>
                                                    <option value="custom" <?= ! empty($subscription_details->disable_suspension_until) && !in_array($subscription_details->disable_suspension_until, $dates) ? 'selected' : ''; ?>>Tùy chỉnh</option>
                                                </select>
                                            </div>

                                            <div class="col">
                                                <div class="mb-3" id="custom-suspension-date" style="display: <?= ! empty($subscription_details->disable_suspension_until) && !in_array($subscription_details->disable_suspension_until, $dates) ? 'block' : 'none'; ?>">
                                                    <label for="disable_suspension_until" class="form-label">Ngày tùy chỉnh</label>
                                                    <input class="form-control" type="date" value="<?= esc($subscription_details->disable_suspension_until); ?>" id="disable_suspension_until" name="disable_suspension_until">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-info btn-save" onclick="subscription_update();"> Lưu thay đổi</button>
                                </div>
                                <?php echo form_close(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php'); ?>

<script src="<?php echo base_url(); ?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="<?php echo base_url(); ?>/assets/DataTables/datatables.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/app.js"></script>
<script src="<?php echo base_url(); ?>/assets/clipboardjs/clipboard.min.js"></script>

<script>
    $(document).on('change', '#disable_auto_suspend', function() {
            if ($(this).is(':checked')) {
                $('#disable_suspension_until_div').show();
            } else {
                $('#disable_suspension_until_div').hide();
            }
        })
        .on('change', '#disable_suspension_days', function() {
            if ($(this).val() == 'custom') {
                $('#custom-suspension-date').show();
            } else {
                $('#custom-suspension-date').hide();
            }
        });

    function subscription_update() {
        url = "<?php echo base_url('subscription/ajax_subscription_update'); ?>";
        post_data = $('#subscription_form').serialize();

        $.ajax({
            url: url,
            type: "POST",
            data: post_data,
            dataType: "JSON",
            beforeSend: function() {
                $('.btn-save').prepend('<div class="spinner-border spinner-border-sm align-middle me-2" role="status"></div>');
            },
            success: function(data) {
                if (data.status == true) {
                    notyf.success('Đã cập nhật');
                } else {
                    alert('Lỗi: ' + data.message);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
            },
            complete: function() {
                $('.btn-save').find('.spinner-border').remove();
            }
        });
    }


    function set_subscription_status(new_status) {
        url = "<?php echo base_url('subscription/ajax_status_update'); ?>";

        $.ajax({
            url: url,
            type: "POST",
            data: {
                id: <?= $subscription_details->id; ?>,
                status: new_status,
                "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"
            },
            dataType: "JSON",
            success: function(data) {
                //if success close modal and reload ajax table
                if (data.status == true) {

                    notyf.success({
                        message: 'Đã cập nhật',
                        dismissible: true
                    });
                    if (new_status == 'Pending')
                        $("#subscription_status").text('Pending').removeClass('bg-secondary bg-danger bg-success').addClass('bg-danger');
                    else if (new_status == 'Active')
                        $("#subscription_status").text('Active').removeClass('bg-secondary bg-danger bg-success').addClass('bg-success');
                    else if (new_status == 'Cancelled')
                        $("#subscription_status").text('Cancelled').removeClass('bg-secondary bg-danger bg-success').addClass('bg-secondary');
                    else if (new_status == 'Suspended')
                        $("#subscription_status").text('Suspended').removeClass('bg-secondary bg-danger bg-success').addClass('bg-warning');
                    else if (new_status == 'Terminated')
                        $("#subscription_status").text('Terminated').removeClass('bg-secondary bg-danger bg-success').addClass('bg-dark');

                } else {
                    alert('Lỗi: ' + data.message);
                }

            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');

            }

        });
    }

    function generate_invoice() {
        if (confirm("Bạn có chắc chắn muốn tạo hoá đơn gia hạn mới cho Subscription này?") == true) {

            url = "<?php echo base_url('subscription/ajax_generate_invoice'); ?>";

            $.ajax({
                url: url,
                type: "POST",
                data: {
                    id: <?= $subscription_details->id; ?>,
                    "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"
                },
                dataType: "JSON",
                success: function(data) {


                    //if success close modal and reload ajax table
                    if (data.status == true) {

                        //notyf.success({message:'Đã tạo hoá đơn mới. ID ' + date.invoice_id, dismissible: true});
                        location.reload();
                    } else {
                        alert('Lỗi: ' + data.message);
                    }

                },
                error: function(jqXHR, textStatus, errorThrown) {
                    alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');

                }
            });
        }
    }
</script>
