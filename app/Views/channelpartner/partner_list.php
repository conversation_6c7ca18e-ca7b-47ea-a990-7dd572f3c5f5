<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
<main class="content">
   <div class="container-fluid">
   <div class="row">
    <div class="col-md-4 col-sm-6 col-12 d-flex">
        <div class="card flex-fill">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center">
                    Tổng hoa hồng 
                    <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Tổng hoa hồng của tất cả đối tác" title=""></i>
                </div>
                <span class="h3 d-inline-block mt-1 mb-3 text-info"><?= number_format($total_commission); ?> đ</span>
            </div>
        </div>
    </div>

    <div class="col-md-4 col-sm-6 col-12 d-flex">
        <div class="card flex-fill">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center">
                    Hoa hồng đang xét 
                    <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Tổng hoa hồng đối tác đang chờ duyệt" title=""></i>
                </div>
                <span class="h3 d-inline-block mt-1 mb-3 text-warning"><?= number_format($total_commission_pending); ?> đ</span>
            </div>
        </div>
    </div>

    <div class="col-md-4 col-sm-6 col-12 d-flex">
        <div class="card flex-fill">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center">
                    Hoa hồng đã duyệt 
                    <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Tổng hoa hồng đối tác đã phê duyệt" title=""></i>
                </div>
                <span class="h3 d-inline-block mt-1 mb-3 text-success"><?= number_format($total_commission_real); ?> đ</span>
            </div>
        </div>
    </div>

    <div class="col-md-4 col-sm-6 col-12 d-flex">
        <div class="card flex-fill">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center">
                    Tổng số dư của đối tác
                    <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Số dư còn lại của các đối tác" title=""></i>
                </div>
                <span class="h3 d-inline-block mt-1 text-info mb-3"><?= number_format($data_total_amount_officer ?? 0); ?> đ</span>
            </div>
        </div>
    </div>

    <div class="col-md-4 col-sm-6 col-12 d-flex">
        <div class="card flex-fill">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center">
                    Số tiền đang chờ & xem xét (rút hoa hồng)
                    <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Tổng tiền rút chờ duyệt" title=""></i>
                </div>
                <span class="h3 d-inline-block mt-1 text-warning mb-3"><?= number_format($total_withdraw_pending ?? 0); ?> đ</span>
            </div>
        </div>
    </div>

    <div class="col-md-4 col-sm-6 col-12 d-flex">
        <div class="card flex-fill">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center">
                    Số tiền đã duyệt (rút hoa hồng)
                    <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Tổng số tiền đã rút" title=""></i>
                </div>
                <span class="h3 d-inline-block mt-1 text-success mb-3"><?= number_format($total_withdraw_real ?? 0); ?> đ</span>
            </div>
        </div>
    </div>
</div>

<!-- Initialize Bootstrap tooltips -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        var tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(function (tooltip) {
            new bootstrap.Tooltip(tooltip);
        });
    });
</script>

      <div class="card mt-3" style="width:100%">
         <div class="card-body">
            <div class="row">
               <div class="col-auto d-none d-sm-block">
                  <h3>Danh sách đối tác</h3>
               </div>
            </div>
            <div class="row">
               
               <div class="">
                  <table class="table table-hover table-striped table-bordered text-muted align-middle display nowrap" id="partner_table" style="width:100%">
                     <thead class="table-light text-muted">
                        <tr class="align-middle">
                        </tr>
                     </thead>
                     <tbody>
                     </tbody>
                  </table>
               </div>
            </div>
         </div>
      </div>
   </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>
</div>
</div>
<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>
<script>
   let datadb = <?= json_encode($data_partner_list);?>    
   
   var table;
   $(document).ready(function() {
       table = $('#partner_table').DataTable({
           paging: true,
           scrollX: true,
           autoWidth: false,
           language: {
               processing: "Message khi đang tải dữ liệu",
               search: "Tìm kiếm",
               lengthMenu: "Hiển thị _MENU_",
               info: "Hiển thị trang _PAGE_ / _PAGES_",
               infoEmpty: "Hiển thị trang _PAGE_ / _PAGES_",
               loadingRecords: "",
               infoFiltered: "(Được lọc trong  _MAX_ record dữ liệu )",
               zeroRecords: "Không tìm kiếm được giá trị",
               emptyTable: "Không có dữ liệu",
               paginate: {
                   first: " Đầu",
                   previous: " Trước",
                   next: " Sau",
                   last: " Cuối",
               },
           },
           data: datadb, // Sử dụng dữ liệu từ biến `data`
           columns: [{
                   data: "channel_partner_officer_id",
                   title: "short",
                   className: "d-none"
               },
               {
                   data: "",
                   title: "Thông tin tài khoản",
                   render: function(data, type, row, meta) {
                       
                    return `
                    <div class="d-flex flex-column">
                        <span class="fw-bold">Tên: <span class="text-primary">${row.name}</span></span>
                        <span class="fw-bold">Email: <span class="text-primary">${row.email}</span></span>
                    </div>`;
                   }
               },
               {
                   data: "",
                   title: "% Hoa hồng",
                   render: function(data, type, row, meta) {
                       return `<span class="fs-6">${row.rate}</span>`;
                   }
               },

               {
                   data: "role",
                   title: "Chức vụ",
                   className: "",
                   render: function(data, type, row, meta) {
                       return `<span class="${row.color}">${data}</span>`;
                   }
               },
               
               {
                   data: "",
                   title: "Trạng thái",
                   className: "",
                   render: function(data, type, row, meta) {
                       return `<span class="${row.color_status}">${row.color_text}</span>`;
                   }
               },
               {
                    data: null, // Không lấy trực tiếp từ một key duy nhất
                    title: "Thông tin hoa hồng",
                    className: "",
                    render: function(data, type, row, meta) {
                        return `
                            <div>
                                <div><strong>HH đã nhận:</strong> ${row.totalCommission || 0}</div>
                                <div><strong>HH đã rút:</strong> ${row.totalWithdraw || 0}</div>
                                <div><strong>Số dư:</strong> ${row.balance_amount || 0}</div>
                            </div>
                        `;
                    }
                },
               {
                   data: "created_at",
                   title: "Ngày tạo"
               },
               {
                   data: null,
                   title: "Tác vụ",
                   className: "",
                   render: function(data, type, row, meta) {
                       return `
                           <a href="javascript:;" onclick="loginPartner(${row.channel_partner_officer_id})" class="btn btn-sm btn-outline-primary ms-2 me-1 mt-2"><i class="bi bi-eye me-2"></i>Đăng nhập</a>
                           
                       `;
                   }
               }
           ],
           order: [
               [0, 'desc']
           ] // Sắp xếp theo cột ID từ lớn đến bé
           
       });
   });

   // reload table 
   $(".sidebar-toggle").on("click",()=>{
        setTimeout(()=>{
            table.draw();
        },500)
    })
    
    function loginPartner(officer_id) {
        $.get(`/channelpartner/<?=$partner?>/login_as_channel_partner/${officer_id}`, function(response) {
        console.log(response);
        
        if (response) {
            window.open(response, "_blank");
        } else {
            notyf.error({
                message:"Lỗi hệ thống xác thực hoặc tài khoản chưa kích hoạt!"
            })
        }
    }).fail(function() {
        notyf.error({
                message:"Lỗi hệ thống hãy liên hệ kỹ thuật!"
            })
    });
}

   
   
</script>