<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
<main class="content">
   <div class="container-fluid">
   <div class="row g-3">
    <div class="col-md-4 col-sm-6 col-12 d-flex">
        <div class="card flex-fill">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center">
                    Tổng hoa hồng 
                    <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Tổng hoa hồng của tất cả đối tác" title=""></i>
                </div>
                <span class="h3 d-inline-block mt-1 mb-3 text-info"><?= number_format($total_commission); ?> đ</span>
            </div>
        </div>
    </div>

    <div class="col-md-4 col-sm-6 col-12 d-flex">
        <div class="card flex-fill">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center">
                    Hoa hồng đang xét 
                    <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Tổng hoa hồng đối tác đang chờ duyệt" title=""></i>
                </div>
                <span class="h3 d-inline-block mt-1 mb-3 text-warning"><?= number_format($total_commission_pending); ?> đ</span>
            </div>
        </div>
    </div>

    <div class="col-md-4 col-sm-6 col-12 d-flex">
        <div class="card flex-fill">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center">
                    Hoa hồng đã duyệt 
                    <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-title="Tổng hoa hồng đối tác đã phê duyệt" title=""></i>
                </div>
                <span class="h3 d-inline-block mt-1 mb-3 text-success"><?= number_format($total_commission_real); ?> đ</span>
            </div>
        </div>
    </div>
</div>

<!-- Initialize Bootstrap tooltips -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        var tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(function (tooltip) {
            new bootstrap.Tooltip(tooltip);
        });
    });
</script>

      <div class="card mt-3" style="width:100%">
         <div class="card-body">
            <div class="row">
               <div class="col-auto d-none d-sm-block">
                  <h3>Danh sách hoa hồng</h3>
               </div>
            </div>
            <div class="row">
               <div class="filter-item mb-3">
                  <button class="btn btn-filter btn-outline-primary"  onclick="filterData('All')">
                  Tất cả
                  <span class="badge bg-danger" id="count-all" >0</span>
                  </button>
                  <button class="btn btn-filter  btn-outline-primary" onclick="filterData('Approved')">
                  Đã duyệt
                  <span class="badge bg-danger" id="count-approved">0</span>
                  </button>
                  <button class="btn btn-filter  btn-outline-primary"  onclick="filterData('Pending')">
                  Đang chờ
                  <span class="badge bg-danger" id="count-pending" >0</span>
                  </button>
                  <button class="btn btn-filter mt-1 mt-sm-0 btn-outline-primary"  onclick="filterData('Rejected')">
                  Từ chối
                  <span class="badge bg-danger" id="count-rejected" >0</span>
                  </button>
               </div>
               <div class="">
                  <table class="table table-hover table-striped table-bordered text-muted align-middle display nowrap" id="partner_table" style="width:100%">
                     <thead class="table-light text-muted">
                        <tr class="align-middle">
                        </tr>
                     </thead>
                     <tbody>
                     </tbody>
                  </table>
               </div>
            </div>
         </div>
      </div>
   </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>
</div>
</div>
<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>
<script>
   let datadb = <?= json_encode($data_commission);?>    
   
   var table;
   $(document).ready(function() {
       table = $('#partner_table').DataTable({
           paging: true,
           scrollX: true,
           autoWidth: false,
           language: {
               processing: "Message khi đang tải dữ liệu",
               search: "Tìm kiếm",
               lengthMenu: "Hiển thị _MENU_",
               info: "Hiển thị trang _PAGE_ / _PAGES_",
               infoEmpty: "Hiển thị trang _PAGE_ / _PAGES_",
               loadingRecords: "",
               infoFiltered: "(Được lọc trong  _MAX_ record dữ liệu )",
               zeroRecords: "Không tìm kiếm được giá trị",
               emptyTable: "Không có dữ liệu",
               paginate: {
                   first: " Đầu",
                   previous: " Trước",
                   next: " Sau",
                   last: " Cuối",
               },
           },
           data: datadb, // Sử dụng dữ liệu từ biến `data`
           columns: [{
                   data: "id",
                   title: "short",
                   className: "d-none"
               },
               {
                   data: "",
                   title: "Thông tin tài khoản",
                   render: function(data, type, row, meta) {
                       
                    return `
                    <div class="d-flex flex-column">
                        <span class="fw-bold">Tên: <span class="text-primary">${row.channel_partner_officer_name}</span></span>
                        <span class="fw-bold">Email: <span class="text-primary">${row.channel_partner_officer_email}</span></span>
                    </div>`;
                   }
               },
               {
                   data: "",
                   title: "Hoa hồng",
                   render: function(data, type, row, meta) {
                       return `<span class="fs-6">${row.commission_amount} (${row.rate})</span>`;
                   }
               },
               
               {
                   data: "status",
                   title: "Trạng thái",
                   className: "",
                   render: function(data, type, row, meta) {
                       return `<span class="${row.color}">${data}</span>`;
                   }
               },
               
   
               {
                   data: "created_at",
                   title: "Ngày tạo"
               },
               {
                   data: null,
                   title: "Tác vụ",
                   className: "",
                   render: function(data, type, row, meta) {
                       return `
                           <a href="/channelpartner/<?=$partner?>/commission/detail/${row.id}" class="btn btn-sm btn-outline-primary ms-2 me-1 mt-2"><i class="bi bi-eye me-2"></i>Chi tiết</a>
                           
                       `;
                   }
               }
           ],
           order: [
               [0, 'desc']
           ] // Sắp xếp theo cột ID từ lớn đến bé
           
       });
   
       
       let data = table.data().toArray();
           // Lọc ra các bản ghi có trạng thái "Pending"
       let pendingCount = data.filter(function(record) {
           return record.status === 'Pending';
       }).length;
   
       // Lọc ra các bản ghi có trạng thái "Approved"
       let approvedCount = data.filter(function(record) {
           return record.status === 'Approved';
       }).length;
   
       // Lọc ra các bản ghi có trạng thái "prejected"
       let rejectedCount = data.filter(function(record) {
           return record.status === 'Rejected';
       }).length;
   
       // Tổng số bản ghi
       let totalCount = data.length;
       $("#count-all").text(totalCount); // Số lượng Pending
       $("#count-approved").text(approvedCount);   // Số lượng Active
       $("#count-rejected").text(rejectedCount);   // Số lượng Prejected
       $("#count-pending").text(pendingCount);     // Tổng số lượng
       
   });

   // reload table 
   $(".sidebar-toggle").on("click",()=>{
        setTimeout(()=>{
            table.draw();
        },500)
    })
   function filterData(str){
       // Loại bỏ class active-btn từ tất cả các nút
       $('.btn-filter').removeClass('active-btn');
   
       // Thêm class active-btn vào nút hiện tại
       $(event.target).addClass("active-btn");
   
       if(str == "All"){
           table.search('').columns().search('').draw();
           return;
       }
       if(str == "Approved"){
           table.column(3).search('Approved').draw();
           return;
       }
       if(str == "Rejected"){
           table.column(3).search('Rejected').draw();
           return;
       }
       if(str == "Pending"){
           table.column(3).search('Pending').draw();
           return;
       }
       table.search('').columns().search('').draw();
   }
   
   
</script>