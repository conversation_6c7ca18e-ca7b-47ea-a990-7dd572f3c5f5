<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
<meta name="csrf-token" content="<?= csrf_hash() ?>">
<!-- Thêm SweetAlert2 từ CDN -->
<link rel="stylesheet" href="/assets/sweealert2/sweealert2.min.css">
<script src="/assets/sweealert2/sweealert2.min.js"></script>
<div class="container-fluid">
   <div>
      <!-- Breadcrumb -->
      <nav aria-label="breadcrumb">
         <ol class="breadcrumb breadcrumb-light mb-0 ms-2 mt-2">
            <li class="breadcrumb-item active"><a href="/channelpartner/<?=$partner?>/withdraw"><< Yêu cầu rút tiền</a></li>
         </ol>
      </nav>
      <!-- End Breadcrumb -->
   </div>
   <div class="container mt-4">
      <div class="row mt-3">
         <div class="col-12">
            <div class="row">
               <div class="mx-auto col-md-6">
                  <div class="card">
                     <div class="card-header">
                        <h5 class="card-title mb-0 text-uppercase">Thông tin tài khoản Officer</h5>
                     </div>
                     <div class="card-body">
                        <div class="row">
                           <div class="col-md-4 col-6">
                              <span class="text-muted">Tên</span>
                           </div>
                           <div class="col-md-8 col-6">
                              <span class='fw-bold'><?= $data_detail['channel_partner_officer']['name']??"NA/N" ?></span>
                           </div>
                        </div>
                        <div class="row mt-3">
                           <div class="col-md-4 col-6">
                              <span class="text-muted">Email</span>
                           </div>
                           <div class="col-md-8 col-6">
                              <span class='fw-bold'><?= $data_detail['channel_partner_officer']['email']??"NA/N" ?></span>
                           </div>
                        </div>
                        <div class="row mt-3">
                           <div class="col-md-4 col-6">
                              <span class="text-muted">Vị trí</span>
                           </div>
                           <div class="col-md-8 col-6">
                              <span class='fw-bold text-info'><?= $data_detail['channel_partner_officer']['role']??"NA/N" ?></span>
                           </div>
                        </div>
                        <div class="row mt-3">
                           <div class="col-md-4 col-6">
                              <span class="text-muted">Trạng thái</span>
                           </div>
                           <div class="col-md-8 col-6">
                              <span class='fw-bold text-success'><?php $status = array(0=>"Đã khóa",1=>"Hoạt động"); echo $status[$data_detail['channel_partner_officer']['active']??"Không xác định"] ?></span>
                           </div>
                        </div>
                        <div class="row mt-3">
                           <div class="col-md-4 col-6">
                              <span class="text-muted">Số dư</span>
                           </div>
                           <div class="col-md-8 col-6">
                              <span class='fw-bold fs-3'><?= number_format($data_detail['channel_partner_officer']['balance_amount']?? 0)." đ"??"NA/N" ?></span>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
      <div class="mt-3">
         <div class="card">
            <div class="card-header">
               <h5 class="card-title mb-0">Thông tin yêu cầu</h5>
            </div>
            <div class="card-body">
               <div class="table-responsive">
                  <table class="table dt-responsive table-bordered table-striped align-middle" id="tableBase" style="width:100%">
                     <?php if(!empty($value)){ ?>
                     <thead>
                        <tr>
                           <th class="text-nowrap">Yêu cầu rút</th>
                           <th class="text-nowrap">Trạng thái</th>
                           <?php if(!empty($value["rejection_reasons"]) && $value["status"] == "Rejected"){ ?>
                           <th class="text-nowrap">Lý do</th>
                           <?php } ?>
                           
                           <th class="text-nowrap">Thông tin ngân hàng</th>
                           <th class="text-nowrap">Ngày tạo</th>
                           <?php if($value["status"]=="Pending"|| $value["status"]=="InProgress"){ ?>
                           <th class="text-nowrap block-change-status">Tác vụ</th>
                           <?php }?>
                        </tr>
                     </thead>
                     <tbody>
                        <tr>
                           <td><?= $value["withdraw_amount"] ?? 0 ?></td>
                           <td>
                              <span class="fw-bold">
                              <span class="<?= array('Pending'=>'text-warning', 'Rejected'=>'text-danger','Closed'=>'text-dark','InProgress'=>'text-info', 'Approved'=>'text-success')[$data_detail["withdraw"]["status"]] ?? "text-info" ?> text-status-res">
                              <?= $data_detail["withdraw"]["status"] ?? "NA/N" ?>
                              </span>
                              </span>
                           </td>
                           <?php if(!empty($value["rejection_reasons"]) && $data_detail["withdraw"]["status"] == "Rejected"): ?>
                           <td><?= $data_detail["withdraw"]["rejection_reasons"] ?></td>
                           <?php endif; ?>
                           <td>
                           <div class="d-flex flex-column">
                              <span class="fw-bold">Ngân hàng: <span class="text-primary"><?=$value['bank']??""?></span></span>
                              <span class="fw-bold">Tài khoản: <span class="text-primary"><?=$value['num_bank']??""?></span></span>
                              <span class="fw-bold">Sở hữu: <span class="text-primary"><?=$value['name_bank']??""?></span></span>
                           </div>
                           </td>
                           
                           <td><?= $value["created_at"] ?? "NA/N" ?></td>
                           <?php if($value["status"]=="Pending" || $value["status"]=="InProgress"){ ?>
                           <td class="text-center block-change-status">
                              <a href="javascript:;" onclick="updatewithdraw(<?= $data_detail['withdraw']['id'];?>, 'InProgress')" class="btn btn-sm btn-outline-info ms-2 me-2 mt-2">
                              <i class="bi bi-check2-circle"></i></i> Xem xét
                              </a>
                              <a href="javascript:;" onclick="updatewithdraw(<?= $data_detail['withdraw']['id'];?>, 'Approved')" class="btn btn-sm btn-outline-success ms-2 me-2 mt-2">
                              <i class="bi bi-check-all"></i> Chấp nhận
                              </a>
                              <a href="javascript:;" onclick="updatewithdraw(<?= $data_detail['withdraw']['id'];?>, 'Rejected')" class="btn btn-sm btn-outline-danger ms-2 me-2 mt-2">
                              <i class="bi bi-x-circle"></i> Từ chối
                              </a>
                           </td>
                           <?php }?>
                        </tr>
                     </tbody>
                     <?php }else{ ?>
                     <tbody>
                        <tr>
                           <td colspan="8" class="text-center">Không có dữ liệu yêu cầu</td>
                        </tr>
                     </tbody>
                     <?php } ?>
                  </table>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>
<script src="/assets/js/jquery-3.5.1.js"></script>
<script src="/assets/js/jquery.dataTables.min.js"></script>
<script src="/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="/assets/js/dataTables.rowReorder.min.js"></script>
<script src="/assets/js/dataTables.responsive.min.js"></script>
<script>
   $(document).ready(function() {
       // Lấy CSRF token từ meta tag
       let csrfToken = $('meta[name="csrf-token"]').attr('content');
           // Cấu hình jQuery để thêm CSRF token vào tất cả các yêu cầu AJAX
               $.ajaxSetup({
               headers: {
                   'X-CSRF-TOKEN': csrfToken
               }
           });
       $('#tableBase').DataTable({
           paging: false,
           searching: false,
           ordering: true,
           language: {
               processing: "Message khi đang tải dữ liệu",
               search: "Tìm kiếm",
               lengthMenu: "Hiển thị _MENU_",
               info: "",
               infoEmpty: "Hiển thị trang _PAGE_ / _PAGES_",
               loadingRecords: "",
               infoFiltered: "(Được lọc trong _MAX_ record dữ liệu)",
               zeroRecords: "Không tìm kiếm được giá trị",
               emptyTable: "Không có dữ liệu",
               paginate: {
                   first: "Đầu",
                   previous: "Trước",
                   next: "Sau",
                   last: "Cuối",
               },
           },
       });
   });
</script>
<script>
   function updateCsrfToken(csrfToken) {
       $('meta[name="csrf-token"]').attr('content', csrfToken);
       $.ajaxSetup({
           headers: {
               'X-CSRF-TOKEN': csrfToken
           }
       });
   }
   function updatewithdraw(id, status) {
       if (status === "Approved") {
           // Xử lý khi trạng thái là Approved
           Swal.fire({
               title: 'Xác nhận',
               text: 'Bạn có chắc muốn chấp nhận hoa hồng này?',
               icon: 'warning',
               showCancelButton: true,
               confirmButtonText: 'Chấp nhận',
               cancelButtonText: 'Hủy',
               confirmButtonColor: '#3085d6',
               cancelButtonColor: '#d33',
           }).then((result) => {
               if (result.isConfirmed) {
                   // Xử lý logic khi hoa hồng được chấp nhận
                   let data_status = {
                        channel_partner_withdraw_id:id,
                       status: status
                   }
                  updateStatus(data_status);
               }
           });
       }else if (status === "InProgress") {
           // Xử lý khi trạng thái là Approved
           Swal.fire({
               title: 'Xác nhận',
               text: 'Bạn có chắc muốn xem xét hoa hồng này?',
               icon: 'warning',
               showCancelButton: true,
               confirmButtonText: 'Chấp nhận',
               cancelButtonText: 'Hủy',
               confirmButtonColor: '#3085d6',
               cancelButtonColor: '#d33',
           }).then((result) => {
               if (result.isConfirmed) {
                   // Xử lý logic khi hoa hồng được chấp nhận
                   let data_status = {
                        channel_partner_withdraw_id:id,
                       status: status
                   }
                  updateStatus(data_status);
               }
           });
       }
        else if (status === "Rejected") {
           Swal.fire({
               title: 'Nhập lý do',
               input: 'textarea',
               inputPlaceholder: 'Nhập lý do của bạn ở đây...',
               inputAttributes: {
                   'aria-label': 'Nhập lý do của bạn'
               },
               showCancelButton: true,
               cancelButtonText: 'Hủy',
               confirmButtonText: 'Xác nhận',
               confirmButtonColor: '#3085d6',
               cancelButtonColor: '#d33',
               preConfirm: (text) => {
                   if (!text) {
                       Swal.showValidationMessage('Vui lòng nhập lý do');
                   }
                   return text;
               }
           }).then((result) => {
               if (result.isConfirmed) {
                   const textareaValue = result.value;
                   let data_status = {
                    channel_partner_withdraw_id:id,
                       status: status,
                       rejection_reasons:textareaValue,
                   }
                  updateStatus(data_status);
               }
           });
       }
   }
   function updateStatus(data) {
       $.ajax({
           url: "/channelpartner/<?=$partner?>/withdraw/update",
           type: "POST",
           data: data,
           success: function(response, textStatus, jqXHR) {
               if (response.code == 200) {
                   // Thông báo thành công
                   notyf.success({
                       message: response.message
                   });
                if(data.status=="Rejected" || data.status=="Approved"){
                    $(".block-change-status").addClass('d-none');
                }
   
                $(".text-status-res").text(data.status)
                .removeClass('text-warning text-success text-info text-danger')
                .addClass({
                "Approved": 'text-success',
                "InProgress": 'text-info',
                "Rejected": 'text-danger'
                }[data.status] || '');
   
                }
            },
   
           error: function(err, textStatus, errorThrown) {
               if (err.status == 422) {
                   notyf.error({
                       message: "Lỗi xác thực dữ liệu, hãy liên hệ kỹ thuật để thử lại!"
                   });
               } else if (err.status == 403) {
                   // Thông báo lỗi không có quyền
                   notyf.error({
                       message: "Hãy nhấn F5 tải lại trang để thử lại!"
                   });
               } else if (err.status == 500) {
                   // Thông báo lỗi server
                   notyf.error({
                       message: "Lỗi hệ thống hãy liên hệ kỹ thuật hoặc tải lại trang!"
                   });
               } else {
                   // Xử lý lỗi không xác định
                   notyf.error({
                       message: "Đã xảy ra lỗi. Vui lòng thử lại sau!"
                   });
               }
           },
   
           complete: function(jqXHR, textStatus) {
               // Lấy CSRF token mới từ tiêu đề phản hồi
               let newCsrfToken = jqXHR.getResponseHeader('X-CSRF-TOKEN');
               if (newCsrfToken) {
                   updateCsrfToken(newCsrfToken);
               }
   
             
           }
       });
   }
   
   
</script>
<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>