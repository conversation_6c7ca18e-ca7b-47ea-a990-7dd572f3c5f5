<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
<meta name="csrf-token" content="<?= csrf_hash() ?>">
<!-- Thêm SweetAlert2 từ CDN -->
<link rel="stylesheet" href="/assets/sweealert2/sweealert2.min.css">
<script src="/assets/sweealert2/sweealert2.min.js"></script>
<div class="container-fluid">
   <div class="my-3">
      <!-- Breadcrumb -->
      <nav aria-label="breadcrumb">
         <ol class="breadcrumb breadcrumb-light mb-0 ms-2 ">
            <li class="breadcrumb-item active"><a href="/channelpartner/<?= $partner?>/commission"><< Hoa hồng</a></li>
         </ol>
      </nav>
      <!-- End Breadcrumb -->
   </div>
   
   <div class="row mt-2">
      <div class="mt-3">
         <div class="card">
            <div class="card-header">
               <h5 class="card-title mb-0">Thông tin Hoa hồng</h5>
            </div>
            <div class="card-body">
               <div class="table-responsive">
                  <table class="table dt-responsive table-bordered table-striped align-middle" id="tableBase" style="width:100%">
                     <?php if(!empty($data_detail["commission"])){ ?>
                     <thead>
                        <tr>
                           <th class="text-nowrap">Hoa hồng</th>
                           <th class="text-nowrap">Phân loại</th>
                           <th class="text-nowrap">Trạng thái</th>
                           <?php if(!empty($data_detail["commission"]["rejection_reasons"]) && $data_detail["commission"]["status"] == "Rejected"){ ?>
                           <th class="text-nowrap">Lý do</th>
                           <?php } ?>
                           <th class="text-nowrap">Ngày tạo</th>
                           <?php if($data_detail["commission"]["status"]=="Pending"){ ?>
                           <th class="text-nowrap block-change-status">Tác vụ</th>
                           <?php }?>
                        </tr>
                     </thead>
                     <tbody>
                        <tr>
                           
                           <td class="fs-4 fw-bold"><?= number_format($data_detail["commission"]["amount"] ?? 0) . " đ" ?> (<?= number_format($data_detail["commission"]["rate"] ?? 0) . "%" ?>)</td>
                           <td><?= $data_detail["commission"]["type"] ?? "NA/N" ?></td>
                           <td>
                              <span class="fw-bold">
                              <span class="<?= array('Pending'=>'text-warning', 'Rejected'=>'text-danger', 'Approved'=>'text-success')[$data_detail["commission"]["status"]] ?? "text-info" ?> text-status-res">
                              <?= $data_detail["commission"]["status"] ?? "NA/N" ?>
                              </span>
                              </span>
                           </td>
                           <?php if(!empty($data_detail["commission"]["rejection_reasons"]) && $data_detail["commission"]["status"] == "Rejected"): ?>
                           <td><?= $data_detail["commission"]["rejection_reasons"] ?></td>
                           <?php endif; ?>
                           <td><?= $data_detail["commission"]["created_at"] ?? "NA/N" ?></td>
                           <?php if($data_detail["commission"]["status"]=="Pending"){ ?>
                           <td class="text-center block-change-status">
                              <a href="javascript:;" onclick="updateCommission(<?= $data_detail['commission']['id'];?>, 'Approved')" class="btn btn-sm btn-outline-success ms-2 me-2 mt-2">
                              <i class="bi bi-check-all"></i> Chấp nhận
                              </a>
                              <a href="javascript:;" onclick="updateCommission(<?= $data_detail['commission']['id'];?>, 'Rejected')" class="btn btn-sm btn-outline-danger ms-2 me-2 mt-2">
                              <i class="bi bi-x-circle"></i> Từ chối
                              </a>
                           </td>
                           <?php }?>
                        </tr>
                     </tbody>
                     <?php }else{ ?>
                     <tbody>
                        <tr>
                           <td colspan="8" class="text-center">Không có dữ liệu hoa hồng</td>
                        </tr>
                     </tbody>
                     <?php } ?>
                  </table>
               </div>
            </div>
         </div>
      </div>
   </div>
   <div class="row mt-2">
      <div class="col-12">
         <div class="row">
        
            <div class="col-md-4">
               <div class="card">
                  <div class="card-header">
                     <h5 class="card-title mb-0 text-uppercase">Thông tin hóa đơn</h5>
                  </div>
                  <div class="card-body">
                     <div class="row">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Mã hóa đơn</span>
                        </div>
                        <div class="col-md-8 col-6">
                                <span class='fw-bold'>#<?= $data_detail['invoice']['id']?? "NA/N" ?></span><span><a href="javascript:;" onclick="showDetailInvoice(<?= $data_detail['invoice']['id']?? 'NA/N' ?>,'<?= $partner ?? 'NA/N' ?>',<?= $data_detail['channel_partner_officer']['id']?? 'NA/N' ?>,'<?= number_format($data_detail['invoice']['total']?? 0).' đ'??'NA/N' ?>')">  xem chi tiết</a></span>
                        </div>
                     </div>
                     <div class="row mt-3">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Tên gói</span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold'><?= $data_detail['invoice']['product_name']??"NA/N" ?></span>
                        </div>
                     </div>
                     <div class="row mt-3">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Giá gói</span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold'><?= number_format($data_detail['invoice']['product_amount']??0)." đ"??"NA/N" ?></span>
                        </div>
                     </div>
                     <div class="row mt-3">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Mô tả</span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold'><?= $data_detail['invoice']['description']??"NA/N" ?></span>
                        </div>
                     </div>
                     <div class="row mt-3">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Phân loại</span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold text-info'><?= $data_detail['invoice']['type']??"NA/N" ?></span>
                        </div>
                     </div>
                     <div class="row mt-3">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Trạng thái </span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold <?= $color_text?>'><?= $data_detail['invoice']['status']??"NA/N" ?></span>
                        </div>
                     </div>
                    
                     
                     <div class="row mt-3">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Tổng hóa đơn</span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold fs-5'><?= number_format($data_detail['invoice']['total']?? 0)." đ"??"NA/N" ?></span>
                        </div>
                     </div>

                     <div class="row mt-3">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Ngày tạo </span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold'><?= $data_detail['invoice']['created_at']??"NA/N" ?></span>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            
            <div class="col-md-4">
               <div class="card">
                  <div class="card-header d-flex">
                     <h5 class="card-title  me-2 mb-0 text-uppercase">Thông tin tài khoản officer </h5>
                  </div>
                  <div class="card-body mx-2">
                     <div class="row">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Tên</span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold'><?= $data_detail['channel_partner_officer']['name']??"NA/N" ?></span>
                        </div>
                     </div>
                     <div class="row mt-3">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Email</span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold'><?= $data_detail['channel_partner_officer']['email']??"NA/N" ?></span>
                        </div>
                     </div>
                     <div class="row mt-3">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Vị trí</span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold text-info'><?= $data_detail['channel_partner_officer']['role']??"NA/N" ?></span>
                        </div>
                     </div>
                     <div class="row mt-3">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Trạng thái</span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold text-success'><?php $status = array(0=>"Đã khóa",1=>"Hoạt động"); echo $status[$data_detail['channel_partner_officer']['active']??"Không xác định"] ?></span>
                        </div>
                     </div>
                     <div class="row mt-3">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Số dư</span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold fs-4'><?= number_format($data_detail['channel_partner_officer']['balance_amount']?? 0)." đ"??"NA/N" ?></span>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         

            <div class="col-md-4">
               <div class="card">
                  <div class="card-header">
                     <h5 class="card-title mb-0 text-uppercase">Hồ sơ công ty</h5>
                  </div>
                  <div class="card-body">
                     <div class="row">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Tên công ty</span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold'><?= $data_detail['company']['fullname']??"NA/N" ?></span>
                        </div>
                     </div>
                     <div class="row mt-3">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Thương hiệu</span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold'><?= $data_detail['company']['shortname']??"NA/N" ?></span>
                        </div>
                     </div>
                     <div class="row mt-3">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Trạng thái</span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold'>
                           <span class="<?= $color_text ?? "" ?>"><?= $data_detail['company']['status']??"NA/N" ?></span>
                           </span>
                        </div>
                     </div>
                     <div class="row mt-3">
                        <div class="col-md-4 col-7">
                           <span class="text-muted">Ngày tạo</span>
                        </div>
                        <div class="col-md-8 col-5">
                           <span class='fw-bold'><?= $data_detail['company']['created_at']??"NA/N" ?></span>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>

<?= $this->include("channelpartner/mddetail") ?>

<script src="/assets/js/jquery-3.5.1.js"></script>
<script src="/assets/js/jquery.dataTables.min.js"></script>
<script src="/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="/assets/js/dataTables.rowReorder.min.js"></script>
<script src="/assets/js/dataTables.responsive.min.js"></script>
<script>

   $(document).ready(function() {
       // Lấy CSRF token từ meta tag
       let csrfToken = $('meta[name="csrf-token"]').attr('content');
           // Cấu hình jQuery để thêm CSRF token vào tất cả các yêu cầu AJAX
               $.ajaxSetup({
               headers: {
                   'X-CSRF-TOKEN': csrfToken
               }
           });
       $('#tableBase').DataTable({
           paging: false,
           searching: false,
           ordering: true,
           language: {
               processing: "Message khi đang tải dữ liệu",
               search: "Tìm kiếm",
               lengthMenu: "Hiển thị _MENU_",
               info: "",
               infoEmpty: "Hiển thị trang _PAGE_ / _PAGES_",
               loadingRecords: "",
               infoFiltered: "(Được lọc trong _MAX_ record dữ liệu)",
               zeroRecords: "Không tìm kiếm được giá trị",
               emptyTable: "Không có dữ liệu",
               paginate: {
                   first: "Đầu",
                   previous: "Trước",
                   next: "Sau",
                   last: "Cuối",
               },
           },
       });
   });

   let isFetching = false;
   function showDetailInvoice(id,partner,officer_id,total) {
    if (isFetching) return; 
    isFetching = true; 

    const url = "/channelpartner/"+partner+"/commission/detail/invoice-detail?invoice_id=" + id + "&channel_partner_officer_id=" + officer_id;
    
    // Gọi API lấy thông tin hóa đơn
    $.get(url)
    .done((res) => {
        // Kiểm tra mã phản hồi từ server
        if (res.code == 200) {
            
            if (res.data.length < 1) {
                notyf.error({
                    message: "Lỗi thông tin chi tiết, Hãy liên hệ kỹ thuật"
                });
            } else {
                // Cập nhật nội dung vào modal
                updateModal(res.data,total);
            }
        } else {
            console.log("Error response:", res); // Log lỗi nếu có
            notyf.error({
                message: res.message || 'Đã xảy ra lỗi'
            });
        }
    })
    .fail((jqXHR, textStatus, errorThrown) => {
        notyf.error({
            message: jqXHR.responseJSON.message || 'Đã xảy ra lỗi khi kết nối với server'
        });
    })
    .always(() => {
            isFetching = false;
        });
   }
   
   // Hàm cập nhật nội dung vào modal
   function updateModal(item,total) {
    // Hiển thị modal
    // Xóa nội dung cũ của modal
    var invoiceDetails = $('#invoiceDetails');
    invoiceDetails.empty();
    
    // Lặp qua dữ liệu và thêm các mục chi tiết vào modal
    invoiceDetails.append(`
   <div class="d-flex mt-2  flex-column align-items-center">
    <div class="p-3 col-12 mb-3 shadow-sm">
      
      <div class="">
        <!-- Mã hóa đơn -->
        <div class="row mb-3 border-bottom">
          <div class="col-6">
            <strong class="text-muted">Mã hóa đơn: </strong>#${item.Product_invoice_id}
          </div>
        </div>

        <!-- Thông tin sản phẩm -->
        <strong class="pt-3 mb-2">Thông tin dịch vụ</strong>
        <div class="row mb-2">
          <div class="col-4">
            <strong class="text-muted">Tên:</strong>
          </div>
          <div class="col-8">
            ${item.Product_name||""}
          </div>
        </div>

        <div class="row mb-2">
          <div class="col-4">
            <strong class="text-muted">Mô tả:</strong>
          </div>
          <div class="col-8">
            ${item.Product_description||""}
          </div>
        </div>

        <div class="row mb-2">
          <div class="col-4">
            <strong class="text-muted">Ngày bắt đầu:</strong>
          </div>
          <div class="col-8">
            ${item.Product_start_date||""}
          </div>
        </div>
        <div class="row mb-2">
          <div class="col-4">
            <strong class="text-muted">Ngày kết thúc:</strong>
          </div>
          <div class="col-8">
            ${item.Product_end_date||""}
          </div>
        </div>

        <div class="row mb-2">
          <div class="col-4">
            <strong class="text-muted">Thuế:</strong>
          </div>
          <div class="col-8">
            ${item.Product_tax_invoice_item || ""} (${parseInt(item.Product_tax_rate || 0)}%)

          </div>
        </div>

        <div class="row mb-2 border-bottom pb-3">
          <div class="col-4">
            <strong class="text-muted">Giá trị:</strong>
          </div>
          <div class="col-8">
            ${item.Product_amount_invoice_item||""}
          </div>
        </div>

        

        <!-- Thông tin giảm giá -->
        <strong class="pt-3 mb-2">Thông tin đính kèm</strong>
        <div class="row">
          <div class="col-4">
            <strong class="text-muted">Mô tả:</strong>
          </div>
          <div class="col-8">
            ${item.Adddes_description||""}
          </div>
        </div>

         <div class="row mb-2">
          <div class="col-4">
            <strong class="text-muted">Thuế:</strong>
          </div>
          <div class="col-8">
            ${parseInt(item.Adddes_tax_rate|| 0) +" %" ||"0 %"}
          </div>
        </div>

        <div class="row mb-2">
          <div class="col-4">
            <strong class="text-muted">Giảm giá:</strong>
          </div>
          <div class="col-8">
            ${item.Adddes_amount_invoice_item ||" 0 đ"}
          </div>
        </div>
   
        <!-- Tổng tiền -->
        <div class="text-end pt-3 border-top">
          <strong class="text-muted mt-2">Tổng hóa đơn:</strong> ${total||""}
        </div>
      </div>
    </div>
   </div>
`);

    $("#siteDetailModal").modal('show');
   
   }
</script>
<script>
   function updateCsrfToken(csrfToken) {
       $('meta[name="csrf-token"]').attr('content', csrfToken);
       $.ajaxSetup({
           headers: {
               'X-CSRF-TOKEN': csrfToken
           }
       });
   }
   function updateCommission(id, status) {
       if (status === "Approved") {
           // Xử lý khi trạng thái là Approved
           Swal.fire({
               title: 'Xác nhận',
               text: 'Bạn có chắc muốn chấp nhận hoa hồng này?',
               icon: 'warning',
               showCancelButton: true,
               confirmButtonText: 'Chấp nhận',
               cancelButtonText: 'Hủy',
               confirmButtonColor: '#3085d6',
               cancelButtonColor: '#d33',
           }).then((result) => {
               if (result.isConfirmed) {
                   // Xử lý logic khi hoa hồng được chấp nhận
                   let data_status = {
                       commission_id:id,
                       status: status
                   }
                  updateStatus(data_status);
               }
           });
       } else if (status === "Rejected") {
           Swal.fire({
               title: 'Nhập lý do',
               input: 'textarea',
               inputPlaceholder: 'Nhập lý do của bạn ở đây...',
               inputAttributes: {
                   'aria-label': 'Nhập lý do của bạn'
               },
               showCancelButton: true,
               cancelButtonText: 'Hủy',
               confirmButtonText: 'Xác nhận',
               confirmButtonColor: '#3085d6',
               cancelButtonColor: '#d33',
               preConfirm: (text) => {
                   if (!text) {
                       Swal.showValidationMessage('Vui lòng nhập lý do');
                   }
                   return text;
               }
           }).then((result) => {
               if (result.isConfirmed) {
                   const textareaValue = result.value;
                   let data_status = {
                       commission_id:id,
                       status: status,
                       rejection_reasons:textareaValue,
                   }
                  updateStatus(data_status);
               }
           });
       }
   }
   function updateStatus(data) {
       $.ajax({
           url: "/channelpartner/<?= $partner?>/commission/update",
           type: "POST",
           data: data,
           success: function(response, textStatus, jqXHR) {
               if (response.code == 200) {
                   // Thông báo thành công
                   notyf.success({
                       message: response.message
                   });
                   $(".block-change-status").addClass('d-none');
                   $(".text-status-res")
                   .text(data.status)
                   .removeClass('text-warning text-success text-danger')
                   .addClass(data.status == "Approved" ? 'text-success' : 'text-danger');
               }
           },
   
           error: function(err, textStatus, errorThrown) {
               if (err.status == 422) {
                   notyf.error({
                       message: "Lỗi xác thực dữ liệu, hãy liên hệ kỹ thuật để thử lại!"
                   });
               } else if (err.status == 403) {
                   // Thông báo lỗi không có quyền
                   notyf.error({
                       message: "Hãy nhấn F5 tải lại trang để thử lại!"
                   });
               } else if (err.status == 500) {
                   // Thông báo lỗi server
                   notyf.error({
                       message: "Lỗi hệ thống hãy liên hệ kỹ thuật!"
                   });
               } else {
                   // Xử lý lỗi không xác định
                   notyf.error({
                       message: "Đã xảy ra lỗi. Vui lòng thử lại sau!"
                   });
               }
           },
   
           complete: function(jqXHR, textStatus) {
               // Lấy CSRF token mới từ tiêu đề phản hồi
               let newCsrfToken = jqXHR.getResponseHeader('X-CSRF-TOKEN');
               if (newCsrfToken) {
                   updateCsrfToken(newCsrfToken);
               }
   
             
           }
       });
   }
   
   
</script>
<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>