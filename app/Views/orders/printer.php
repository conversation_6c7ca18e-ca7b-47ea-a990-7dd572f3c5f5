<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title><PERSON><PERSON> in đơn vận chuyển...</title>
    <style>
        body, html { margin: 0; padding: 0; }
    </style>
</head>

<body>
    <p><PERSON><PERSON> in đơn vận chuyển...</p>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script type="module">
        const imgData = `data:image/png;base64, <?= $base64 ?>`;
          
        const imgWidth = 10.52 * 10 * 0.7;
        const imgHeight = 17 * 10 * 0.7;
          
        const { jsPDF } = window.jspdf;
        const pdfDoc = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: [imgWidth, imgHeight]
        });
        pdfDoc.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
        const pdfBlob = pdfDoc.output('blob');
        const blobUrl = URL.createObjectURL(pdfBlob);
        window.location.href = blobUrl;
    </script>
</body>
</html>