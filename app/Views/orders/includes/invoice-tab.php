<div class="tab-pane fade" id="invoice-content" role="tabpanel" aria-labelledby="invoice-tab">
    <div class="d-flex justify-content-between align-items-start mb-3">
        <div>
            <h5 class="mb-1">H<PERSON>a đơn #<?= $invoice->id ?></h5>
            <?php if ($invoice->status === 'pending'): ?>
                <span class="badge bg-warning">Chờ xử lý</span>
            <?php elseif ($invoice->status === 'draft'): ?>
                <span class="badge bg-secondary">Bản nháp</span>
            <?php elseif ($invoice->status === 'issued'): ?>
                <span class="badge bg-success">Đ<PERSON> phát hành</span>
            <?php endif; ?>
        </div>
        <div>
            <?php if ($invoice->status === 'pending'): ?>
                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editInvoiceModal">
                    <i class="bi bi-pencil me-1"></i> Chỉnh sửa
                </button>
            <?php elseif ($invoice->status === 'issued'): ?>
                <button type="button" class="btn btn-sm btn-primary pe-1" data-bs-toggle="tooltip" title="Xem hóa đơn" data-preview-invoice-button>
                    <i class="bi bi-eye me-1"></i>
                </button>
                <a href="<?= base_url('orders/invoice/' . $order->id . '?action=download') ?>" class="btn btn-sm btn-success pe-1" data-bs-toggle="tooltip" title="Tải hóa đơn">
                    <i class="bi bi-download me-1"></i>
                </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="row g-3">
        <div class="col-md-6">
            <div class="card shadow-none border h-100">
                <div class="card-header pb-0">
                    <h6 class="card-title mb-0">Thông tin khách hàng</h6>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-sm-4">Họ tên:</dt>
                        <dd class="col-sm-8"><?= esc($invoice->customer_name) ?></dd>
                        <?php if ($invoice->company_name): ?>
                            <dt class="col-sm-4">Tên công ty:</dt>
                            <dd class="col-sm-8"><?= esc($invoice->company_name) ?></dd>
                        <?php endif; ?>
                        <?php if ($invoice->tax_code): ?>
                            <dt class="col-sm-4">Mã số thuế:</dt>
                            <dd class="col-sm-8"><?= esc($invoice->tax_code) ?></dd>
                        <?php endif; ?>
                        <dt class="col-sm-4">Số điện thoại:</dt>
                        <dd class="col-sm-8"><?= esc($invoice->customer_phone) ?></dd>
                        <?php if ($invoice->customer_email): ?>
                            <dt class="col-sm-4">Email:</dt>
                            <dd class="col-sm-8"><?= esc(implode(', ', json_decode($invoice->customer_email))) ?></dd>
                        <?php endif; ?>
                        <dt class="col-sm-4">Địa chỉ:</dt>
                        <dd class="col-sm-8"><?= nl2br(esc($invoice->customer_address)) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-none border h-100">
                <div class="card-header pb-0">
                    <h6 class="card-title mb-0">Chi tiết hóa đơn</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th style="width: 50px">STT</th>
                                    <th>Sản phẩm</th>
                                    <th style="width: 90px" class="text-center">Số lượng</th>
                                    <th style="width: 120px" class="text-end">Đơn giá</th>
                                    <th style="width: 120px" class="text-end">Thành tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $i = 1;
                                foreach ($items as $item): ?>
                                    <tr>
                                        <td><?= $i++ ?></td>
                                        <td>
                                            <?php if (! empty($item->image)): ?>
                                                <img src="<?= $item->image ?>" alt="<?= esc($item->name) ?>" class="ms-2" width="50" height="50">
                                            <?php endif; ?>
                                            <?= esc($item->name) ?>
                                        </td>
                                        <td class="text-center"><?= $item->quantity ?></td>
                                        <td class="text-end"><?= format_currency($item->price / (1 + $invoice->tax_rate / 100)) ?></td>
                                        <td class="text-end"><?= format_currency($item->total_price / (1 + $invoice->tax_rate / 100)) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4" class="text-end"><strong>Tổng tiền sản phẩm:</strong></td>
                                    <td class="text-end"><?= format_currency($invoice->total) ?></td>
                                </tr>
                                <?php if ($invoice->tax > 0): ?>
                                    <tr>
                                        <td colspan="4" class="text-end">
                                            <strong>Thuế VAT (<?= $invoice->tax_rate ?>%):</strong>
                                        </td>
                                        <td class="text-end"><?= format_currency($invoice->tax) ?></td>
                                    </tr>
                                <?php endif; ?>
                                <tr class="table-primary">
                                    <td colspan="4" class="text-end"><strong>Tổng thanh toán:</strong></td>
                                    <td class="text-end fs-5 fw-bold"><?= format_currency($invoice->subtotal) ?></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <?php if ($invoice->public_note): ?>
                        <div class="mt-3">
                            <strong>Ghi chú:</strong>
                            <p class="mb-0"><?= nl2br(esc($invoice->public_note)) ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="previewInvoiceModal" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xem hóa đơn #<?= $invoice->invoice_id ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div class="text-center p-4 loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Đang tải...</span>
                    </div>
                    <div class="mt-2">Đang tải hóa đơn...</div>
                </div>
                <iframe id="invoicePreview" style="width: 100%; height: 80vh; display: none; border: none;"></iframe>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <a href="<?= base_url('orders/invoice/' . $order->id . '?action=download') ?>" class="btn btn-primary">
                    <i class="bi bi-download me-1"></i> Tải về
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    $(() => {
        $('[data-preview-invoice-button]').on('click', function() {
            $('#previewInvoiceModal').modal('show');
        });

        $('#previewInvoiceModal').on('show.bs.modal', function() {
            const modal = $(this);
            const iframe = modal.find('#invoicePreview');
            const loading = modal.find('.loading');

            iframe.hide();
            loading.show();

            $.ajax({
                url: '<?= base_url('orders/invoice/' . $order->id . '?action=preview') ?>',
                type: 'GET',
                success: function(response) {
                    if (response.errorCode !== 200 || !response.fileToBytes) {
                        loading.html(`
                            <div class="text-danger">
                                <i class="bi bi-exclamation-circle fs-1"></i>
                                <div class="mt-2">${response.description || 'Có lỗi xảy ra khi tải hóa đơn'}</div>
                            </div>
                        `);
                        return;
                    }

                    function base64ToUint8Array(base64) {
                        const binaryString = atob(base64);
                        const len = binaryString.length;
                        const bytes = new Uint8Array(len);
                        for (let i = 0; i < len; i++) {
                            bytes[i] = binaryString.charCodeAt(i);
                        }
                        return bytes;
                    }

                    try {
                        const pdfBytes = base64ToUint8Array(response.fileToBytes);
                        const blob = new Blob([pdfBytes], {
                            type: 'application/pdf'
                        });
                        const blobUrl = URL.createObjectURL(blob);

                        iframe.attr('src', blobUrl);

                        iframe.on('load', function() {
                            loading.hide();
                            iframe.show();
                        });
                    } catch (err) {
                        console.error(err);
                        loading.html(`
                            <div class="text-danger">
                                <i class="bi bi-exclamation-circle fs-1"></i>
                                <div class="mt-2">Lỗi xử lý file PDF</div>
                            </div>
                        `);
                    }
                },
                error: function(xhr) {
                    loading.html(`
                        <div class="text-danger">
                            <i class="bi bi-exclamation-circle fs-1"></i>
                            <div class="mt-2">Có lỗi xảy ra khi tải hóa đơn</div>
                        </div>
                    `);
                }
            });
        });

        $('#previewInvoiceModal').on('hidden.bs.modal', function() {
            const iframe = $(this).find('#invoicePreview');
            const src = iframe.attr('src');

            if (src) {
                URL.revokeObjectURL(src);
                iframe.attr('src', '');
            }

            $(this).find('.loading').html(`
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Đang tải...</span>
                </div>
                <div class="mt-2">Đang tải hóa đơn...</div>
            `);
        });
    });
</script>