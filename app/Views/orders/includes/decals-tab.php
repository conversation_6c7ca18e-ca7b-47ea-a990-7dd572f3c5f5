<div class="tab-pane fade" id="decals-content" role="tabpanel" aria-labelledby="decals-tab">
    <?php if (! empty($decals)): ?>
        <div class="table-responsive">
            <table class="table table-hover table-fixed" style="min-width: 1000px;">
                <thead>
                    <tr>
                        <th>STT</th>
                        <th>Mục</th>
                        <th>Tài khoản ngân hàng</th>
                        <th>Loa</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($decals as $index => $decal): ?>
                        <tr>
                            <td><?= $index + 1 ?></td>
                            <td><?= $decal->item_name ?></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="https://my.sepay.vn/assets/images/banklogo/<?= $decal->bank_icon ?>" alt="<?= esc($decal->bank_name) ?>" height="20" class="me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="<?= esc($decal->bank_name) ?>">
                                    <?= esc($decal->account_number) ?: '<span class="fw-light">(trống)</span>' ?>
                                </div>
                            </td>
                            <td>
                                <div class="d-inline-flex flex-column">
                                    <?php if ($decal->output_device_id): ?>
                                        <code class="fs-5"><?= esc($decal->output_device_serial_number) ?></code>
                                        <span class="text-sm"><?= esc($decal->output_device_vendor) ?></span>
                                    <?php else: ?>
                                        <span class="text-danger fw-bold"><i class="bi bi-exclamation-triangle-fill"></i> Chưa gán loa</span>
                                        <small class="text-muted">Cần gán loa để có thể giao hàng</small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex gap-1">
                                    <?php if (!$decal->output_device_company_id && $order->status != 'Shipping'): ?>
                                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editDecalModal" data-decal-id="<?= $decal->id ?>" data-decal-name="<?= esc($decal->item_name) ?>">
                                            <i class="bi bi-pencil"></i> Sửa
                                        </button>
                                    <?php endif ?>

                                    <?php if (!in_array($decal->bank_id, [10, 17])): ?>
                                        <?php if ($decal->account_number && ! $decal->virtual_account_number && $decal->output_device_id): ?>
                                            <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#createVaConfirmationModal" data-decal-id="<?= $decal->id ?>">
                                                <i class="bi bi-qr-code"></i> Tạo mã QR
                                            </button>
                                        <?php endif; ?>

                                        <?php if ($decal->virtual_account_number): ?>
                                            <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#printQrModal" data-decal-id="<?= $decal->id ?>">
                                                <i class="bi bi-printer"></i> In mã QR
                                            </button>
                                        <?php endif ?>

                                        <?php if ($decal->bank_id == 8 && $decal->virtual_account_number && !$decal->output_device_company_id && $order->status != 'Shipping'): ?>
                                            <button type="button" class="btn btn-sm btn-outline-light text-danger bg-white" data-bs-toggle="modal" data-bs-target="#deleteQrModal" data-decal-id="<?= $decal->id ?>">
                                                <i class="bi bi-trash"></i> Xóa mã QR
                                            </button>
                                        <?php endif; ?>
                                    <?php endif ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="text-center p-4">
            <button type="button" class="btn btn-primary" data-generate-decals-button>
                Tạo thủ công toàn bộ
            </button>
        </div>
    <?php endif; ?>
</div>


<div class="modal fade" id="editDecalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chỉnh sửa thông tin <span data-decal-name></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open(base_url('orders/update_decal/' . $order->id), ['class' => 'edit-decal-form']) ?>
            <input type="hidden" name="decal_id">
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Chọn loa</label>
                    <div class="d-flex">
                        <select name="device_id" class="form-select device-select" placeholder="Tìm kiếm loa..."></select>
                        <button type="button" class="btn btn-success d-flex align-items-center ms-2" id="openScanner" style="width:120px;">
                            <div class="spinner-border text-light loader me-1" style="width: 14px; height: 14px; display: none;" role="status"></div>
                            <i class="bi bi-qr-code-scan me-1"></i> Quét QR
                        </button>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Số tài khoản</label>
                    <input type="text" name="account_number" class="form-control">
                    <div class="alert alert-warning text-sm mt-2 mb-0 d-none">
                        <div class="alert-message">
                            Chỉ có thể đổi số tài khoản ngân hàng khi và chỉ khi gỡ mã QR đã tạo trước đó.
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Hủy</button>
                <button type="submit" class="btn btn-primary">Lưu thay đổi</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<div class="modal fade" id="createVaConfirmationModal" tabindex="-1" aria-labelledby="createVaConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <?= form_open(base_url('orders/create_va/' . $order->id), ['class' => 'createVaForm']) ?>
            <input type="hidden" name="decal_id">
            <div class="modal-body text-center py-4">
                <i class="bi bi-qr-code text-success" style="font-size: 3rem;"></i>
                <h3 class="fs-4">Xác nhận tạo mã QR</h3>
                <div class="text-secondary">
                    Việc này sẽ tạo trước số VA và giữ lại cho loa này, bạn có chắc chắn muốn tạo không?
                </div>
            </div>
            <div class="modal-footer">
                <div class="row w-100 g-2">
                    <div class="col mt-0">
                        <button type="button" class="btn w-100" data-bs-dismiss="modal">
                            Hủy
                        </button>
                    </div>
                    <div class="col mt-0">
                        <button type="submit" class="btn btn-success w-100">
                            Tạo mã QR
                        </button>
                    </div>
                </div>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<div class="modal fade" id="deleteQrModal" tabindex="-1" aria-labelledby="deleteQrModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <?= form_open(base_url('orders/delete_va/' . $order->id), ['class' => 'deleteVaForm']) ?>
            <input type="hidden" name="decal_id">
            <div class="modal-body text-center py-4">
                <i class="bi bi-trash text-danger" style="font-size: 3rem;"></i>
                <h3 class="fs-4">Xác nhận xóa mã QR</h3>
                <div class="text-secondary">
                    Việc này sẽ xóa mã QR của loa này, bạn có chắc chắn muốn xóa không?
                </div>
            </div>
            <div class="modal-footer">
                <div class="row w-100 g-2">
                    <div class="col mt-0">
                        <button type="button" class="btn w-100" data-bs-dismiss="modal">
                            Hủy
                        </button>
                    </div>
                    <div class="col mt-0">
                        <button type="submit" class="btn btn-danger w-100">
                            Xóa mã QR
                        </button>
                    </div>
                </div>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<div class="modal fade" id="printQrModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">In mã QR</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label class="form-label">Mã QR</label>
                            <div class="text-center">
                                <img src="" alt="QR Code" class="img-fluid qr-code" style="max-width: 150px;">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label d-block">Chọn loại mã QR</label>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault2" checked>
                                <label class="form-check-label" for="flexRadioDefault2">
                                    In mã QR theo số tài khoản ảo
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault1">
                            <label class="form-check-label" for="flexRadioDefault1">
                                In mã QR theo số tài khoản chính
                            </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Logo ngân hàng</label>
                            <div>
                                <img src="" alt="Bank Logo" class="img-fluid bank-logo" style="max-height: 50px;">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Số tài khoản</label>
                            <input type="text" class="form-control account-number" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Số tài khoản ảo</label>
                            <input type="text" class="form-control virtual-account" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Ngân hàng</label>
                            <input type="text" class="form-control bank-name" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Nội dung thanh toán</label>
                            <input type="text" class="form-control description" readonly>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Serial number</label>
                            <input type="text" class="form-control serial-number" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Mã vận đơn</label>
                            <input type="text" class="form-control tracking-number" value="<?= esc($order->tracking_code) ?>" readonly>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="printQrBtn">
                    <i class="bi bi-printer"></i> In mã QR
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="qrModal" tabindex="-1" aria-labelledby="qrModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body p-0">
                <div id="loadingMessage" class="text-center p-4">🎥 Đang khởi tạo camera...</div>
                <video id="scanner-container" class="w-100 h-100" autoplay playsinline style="display: none;"></video>
                <canvas id="canvas" style="display: none;"></canvas>
            </div>
        </div>
    </div>
</div><div class="modal fade" id="createVaConfirmationModal" tabindex="-1" aria-labelledby="createVaConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <?= form_open(base_url('orders/create_va/' . $order->id), ['class' => 'createVaForm']) ?>
            <input type="hidden" name="decal_id">
            <div class="modal-body text-center py-4">
                <i class="bi bi-qr-code text-success" style="font-size: 3rem;"></i>
                <h3 class="fs-4">Xác nhận tạo mã QR</h3>
                <div class="text-secondary">
                    Việc này sẽ tạo trước số VA và giữ lại cho loa này, bạn có chắc chắn muốn tạo không?
                </div>
            </div>
            <div class="modal-footer">
                <div class="row w-100 g-2">
                    <div class="col mt-0">
                        <button type="button" class="btn w-100" data-bs-dismiss="modal">
                            Hủy
                        </button>
                    </div>
                    <div class="col mt-0">
                        <button type="submit" class="btn btn-success w-100">
                            Tạo mã QR
                        </button>
                    </div>
                </div>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<div class="modal fade" id="deleteQrModal" tabindex="-1" aria-labelledby="deleteQrModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <?= form_open(base_url('orders/delete_va/' . $order->id), ['class' => 'deleteVaForm']) ?>
            <input type="hidden" name="decal_id">
            <div class="modal-body text-center py-4">
                <i class="bi bi-trash text-danger" style="font-size: 3rem;"></i>
                <h3 class="fs-4">Xác nhận xóa mã QR</h3>
                <div class="text-secondary">
                    Việc này sẽ xóa mã QR của loa này, bạn có chắc chắn muốn xóa không?
                </div>
            </div>
            <div class="modal-footer">
                <div class="row w-100 g-2">
                    <div class="col mt-0">
                        <button type="button" class="btn w-100" data-bs-dismiss="modal">
                            Hủy
                        </button>
                    </div>
                    <div class="col mt-0">
                        <button type="submit" class="btn btn-danger w-100">
                            Xóa mã QR
                        </button>
                    </div>
                </div>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<div class="modal fade" id="printQrModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">In mã QR</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label class="form-label">Mã QR</label>
                            <div class="text-center">
                                <img src="" alt="QR Code" class="img-fluid qr-code" style="max-width: 300px;">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Logo ngân hàng</label>
                            <div>
                                <img src="" alt="Bank Logo" class="img-fluid bank-logo" style="max-height: 50px;">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Số tài khoản</label>
                            <input type="text" class="form-control account-number" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Số tài khoản ảo</label>
                            <input type="text" class="form-control virtual-account" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Ngân hàng</label>
                            <input type="text" class="form-control bank-name" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Nội dung thanh toán</label>
                            <input type="text" class="form-control description" readonly>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Serial number</label>
                            <input type="text" class="form-control serial-number" readonly>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="printQrBtn">
                    <i class="bi bi-printer"></i> In mã QR
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="qrModal" tabindex="-1" aria-labelledby="qrModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body p-0">
                <div id="loadingMessage" class="text-center p-4">🎥 Đang khởi tạo camera...</div>
                <video id="scanner-container" class="w-100 h-100" autoplay playsinline style="display: none;"></video>
                <canvas id="canvas" style="display: none;"></canvas>
            </div>
        </div>
    </div>
</div>

<script>
    $(() => {
        const deviceSelect = new TomSelect('.device-select', {
            valueField: 'id',
            labelField: 'name',
            searchField: ['name', 'serial_number'],
            placeholder: 'Tìm kiếm loa...',
            create: false,
            plugins: ['clear_button'],
            render: {
                option: function(item, escape) {
                    return `<div class="py-1 px-2">
                        <div class="fw-medium">${escape(item.serial_number)}</div>
                        <div class="text-muted small">${escape(item.vendor)}</div>
                    </div>`;
                },
                item: function(item, escape) {
                    return `<div>
                        <div class="fw-medium">${escape(item.serial_number)}</div>
                        <div class="text-muted small">${escape(item.vendor)}</div>
                    </div>`;
                }
            }
        });

        let video = document.createElement("video");
        let canvasElement = document.getElementById("canvas");
        let canvas = canvasElement.getContext("2d");
        let loadingMessage = document.getElementById("loadingMessage");
        let previousQRCode = '';

        function drawLine(begin, end, color) {
            canvas.beginPath();
            canvas.moveTo(begin.x, begin.y);
            canvas.lineTo(end.x, end.y);
            canvas.lineWidth = 4;
            canvas.strokeStyle = color;
            canvas.stroke();
        }

        const startScanner = () => {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                notyf.error("Trình duyệt của bạn không hỗ trợ camera hoặc chưa cấp quyền truy cập camera");
                return;
            }

            navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: "environment",
                        width: {
                            ideal: 1280
                        },
                        height: {
                            ideal: 720
                        }
                    }
                })
                .then(function(stream) {
                    video.srcObject = stream;
                    video.setAttribute("playsinline", true);
                    video.play();
                    loadingMessage.style.display = "none";
                    video.style.display = "block";
                    canvasElement.style.display = "block";
                    requestAnimationFrame(tick);
                })
                .catch((err) => {
                    if (err.name === 'NotAllowedError') {
                        notyf.error("Vui lòng cấp quyền truy cập camera để quét mã QR");
                    } else if (err.name === 'NotFoundError') {
                        notyf.error("Không tìm thấy camera trên thiết bị");
                    } else {
                        notyf.error("Có lỗi xảy ra khi khởi tạo camera: " + err.message);
                    }
                    console.error("Lỗi camera:", err);
                });
        }

        function tick() {
            if (video.readyState === video.HAVE_ENOUGH_DATA) {
                canvasElement.height = video.videoHeight;
                canvasElement.width = video.videoWidth;
                canvas.drawImage(video, 0, 0, canvasElement.width, canvasElement.height);
                var imageData = canvas.getImageData(0, 0, canvasElement.width, canvasElement.height);
                var code = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });
                if (code) {
                    drawLine(code.location.topLeftCorner, code.location.topRightCorner, "#FF3B58");
                    drawLine(code.location.topRightCorner, code.location.bottomRightCorner, "#FF3B58");
                    drawLine(code.location.bottomRightCorner, code.location.bottomLeftCorner, "#FF3B58");
                    drawLine(code.location.bottomLeftCorner, code.location.topLeftCorner, "#FF3B58");

                    if (code.data !== previousQRCode) {
                        previousQRCode = code.data;
                        deviceSelect.setValue(code.data);
                        $("#qrModal").modal("hide");
                    }
                }
            }
            requestAnimationFrame(tick);
        }

        $("#openScanner").click(function() {
            $("#qrModal").modal("show");
            startScanner();
        });

        $("#qrModal").on("hidden.bs.modal", function() {
            if (video.srcObject) {
                video.srcObject.getTracks().forEach(track => track.stop());
                video.srcObject = null;
            }
            previousQRCode = "";
            loadingMessage.style.display = "block";
            video.style.display = "none";
            canvasElement.style.display = "none";
        });

        $('#editDecalModal').on('show.bs.modal', function(e) {
            const decalId = $(e.relatedTarget).data('decal-id');
            const decalName = $(e.relatedTarget).data('decal-name');
            const form = $(this).find('form');

            form.find('input[name="decal_id"]').val(decalId);
            form.closest('.modal').find('[data-decal-name]').text(decalName);

            $.ajax({
                url: `<?= base_url('orders/get_decal/') ?>/${decalId}`,
                type: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                },
                beforeSend: function() {
                    deviceSelect.clear();
                    deviceSelect.clearOptions();
                },
                success: function(response) {
                    if (response.status) {
                        const decal = response.data;
                        deviceSelect.addOptions(response.devices);
                        if (decal.output_device_id) {
                            deviceSelect.setValue(decal.output_device_id);
                        }
                        form.find('input[name="account_number"]').val(decal.account_number).prop('disabled', decal.bank_id == 8 && decal.virtual_account_number);

                        if (decal.bank_id == 8 && decal.virtual_account_number) {
                            form.find('input[name="account_number"]').next('.alert').removeClass('d-none');
                        } else {
                            form.find('input[name="account_number"]').next('.alert').addClass('d-none');
                        }
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra khi tải thông tin');
                }
            });
        });

        $('.edit-decal-form').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.prop('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-2" role="status"></span>Đang xử lý...');
                },
                success: function(response) {
                    if (response.status) {
                        notyf.success('Đã cập nhật thông tin thành công');
                        $('#editDecalModal').modal('hide');
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                complete: function() {
                    button.prop('disabled', false).text('Lưu thay đổi');
                },
                error: function(error) {
                    notyf.error('Có lỗi xảy ra khi cập nhật thông tin');
                },
            });
        });

        $('[data-generate-decals-button]').on('click', function() {
            const button = $(this);

            $.ajax({
                url: `<?= base_url('orders/generate_decals/' . $order->id) ?>`,
                type: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                },
                beforeSend: function() {
                    button.prop('disabled', true).prepend('<span class="spinner-border spinner-border-sm align-middle me-2" role="status"></span>');
                },
                success: function(response) {
                    if (response.status) {
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                complete: function() {
                    button.prop('disabled', false).html('Tạo thủ công toàn bộ');
                },
            });
        });

        $('#createVaConfirmationModal').on('show.bs.modal', function(e) {
            const decalId = $(e.relatedTarget).data('decal-id');
            $(this).find('input[name="decal_id"]').val(decalId);
        });

        $('#deleteQrModal').on('show.bs.modal', function(e) {
            const decalId = $(e.relatedTarget).data('decal-id');
            $(this).find('input[name="decal_id"]').val(decalId);
        });

        $('.createVaForm').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.prop('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-2" role="status"></span>Đang xử lý...');
                },
                success: function(response) {
                    if (response.status) {
                        $('#createVaConfirmationModal').modal('hide');
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                complete: function() {
                    button.prop('disabled', false).html('Tạo mã QR');
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra khi tạo mã QR');
                },
            });
        });

        $('.deleteVaForm').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.prop('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-2" role="status"></span>Đang xử lý...');
                },
                success: function(response) {
                    if (response.status) {
                        $('#deleteVaConfirmationModal').modal('hide');
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                complete: function() {
                    button.prop('disabled', false).html('Xóa mã QR');
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra khi xóa mã QR');
                },
            });
        });

        $('#printQrModal').on('show.bs.modal', function(e) {
            const decalId = $(e.relatedTarget).data('decal-id');
            const modal = $(this);

            $.ajax({
                url: `<?= base_url('orders/get_qr') ?>/${decalId}`,
                type: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                },
                beforeSend: function() {
                    modal.find('.qr-code').attr('src', '');
                    modal.find('.bank-logo').attr('src', '');
                    modal.find('.virtual-account').val('');
                    modal.find('.bank-name').val('');
                },
                success: function(response) {
                    if (response.status) {
                        modal.find('.qr-code').attr('src', response.data.qr_code);
                        modal.find('.bank-logo').attr('src', response.data.bank_logo);
                        modal.find('.virtual-account').val(response.data.virtual_account_number);
                        modal.find('.bank-name').val(response.data.bank_name);
                        modal.find('.description').val(response.data.description);
                        modal.find('.serial-number').val(response.data.serial_number);
                        modal.find('.account-number').val(response.data.account_number);
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra khi tải mã QR');
                }
            });
        });

        $('#printQrBtn').on('click', function() {
            const modal = $('#printQrModal');
            const qrCode = modal.find('.qr-code');
            const bankName = modal.find('.bank-name').val();
            const accountNumber = modal.find('.account-number').val();
            let virtualAccount = modal.find('.virtual-account').val();
            let description = modal.find('.description').val();
            const serialNumber = modal.find('.serial-number').val();
            const isAbbank = bankName.toLowerCase().includes('abbank');
            const trackingNumber = modal.find('.tracking-number').val();
            
            let qrCodeUrl = qrCode.attr('src');
            if ($('#flexRadioDefault1').is(':checked')) {
                qrCodeUrl = qrCodeUrl.replace(/&des=.*$/, '&des=');
                virtualAccount = '';
                description = '';
            }

            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>In mã QR</title>
                        <style>
                            @page {
                                size: A4;
                                margin: 0;
                            }
                            body {
                                font-family: Arial, sans-serif;
                                margin: 0;
                                padding: 0;
                                width: 210mm;
                                height: 297mm;
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                                position: relative;
                            }
                            .qr-container {
                                text-align: center;
                                width: 80mm;
                                height: 134mm;
                                position: absolute;
                                border: 1mm dashed #000;
                                left: 50%;
                                transform: translateX(-50%);
                                box-sizing: border-box;
                                margin-top: 10mm;
                            }
                            .qr-info {
                                position: absolute;
                                top: 5mm;
                                left: 0;
                                width: 100%;
                                text-align: center;
                                font-size: 8pt;
                                line-height: 1.4;
                            }
                            .qr-info p {
                                margin: 2mm 0;
                                padding: 0 3mm;
                            }
                            .qr-code {
                                width: 55mm;
                                height: 55mm;
                                position: absolute;
                                left: 50%;
                                bottom: ${isAbbank ? '17mm' : '27mm'};
                                transform: translateX(-50%);
                                object-fit: contain;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="qr-container">
                            <div class="qr-info">
                                ${bankName ? `<p>Ngân hàng: ${bankName}</p>` : ''}
                                ${accountNumber ? `<p>Số tài khoản: ${accountNumber}</p>` : ''}
                                ${virtualAccount ? `<p>Số tài khoản ảo: ${virtualAccount}</p>` : ''}
                                ${description ? `<p>Nội dung: ${description}</p>` : ''}
                                ${serialNumber ? `<p>Serial number: ${serialNumber}</p>` : ''}
                                ${trackingNumber ? `<p>Mã vận đơn: ${trackingNumber}</p>` : ''}
                            </div>
                            <img src="${qrCodeUrl}" alt="QR Code" class="qr-code">
                        </div>
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.focus();
            setTimeout(() => {
                printWindow.print();
            }, 500);
        });
    })
</script>
