<div class="card mb-3">
    <div class="card-header pb-0">
        <h5 class="card-title mb-0">Cập nhật trạng thái</h5>
    </div>
    <div class="card-body">
        <?= form_open(base_url('orders/update_status/' . $order->id), ['id' => 'updateStatusForm']) ?>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="status" class="form-label">Trạng thái đơn hàng</label>
                <select class="form-select" id="status" name="status">
                    <?php foreach (\App\Enums\OrderStatus::toArray() as $status): ?>
                        <option value="<?= $status ?>" <?= $order->status == $status ? 'selected' : '' ?>><?= \App\Enums\OrderStatus::getLabel($status) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-6 mb-3">
                <label for="payment_status" class="form-label">Trạng thái thanh toán</label>
                <select class="form-select" id="payment_status" name="payment_status">
                    <option value="Unpaid" <?= $order->payment_status == 'Unpaid' ? 'selected' : '' ?>>Chưa thanh toán</option>
                    <option value="Paid" <?= $order->payment_status == 'Paid' ? 'selected' : '' ?>>Đã thanh toán</option>
                    <option value="Refunded" <?= $order->payment_status == 'Refunded' ? 'selected' : '' ?>>Đã hoàn tiền</option>
                </select>
            </div>
        </div>

        <div id="shippingFields" class="row" style="display: none;">
            <div class="col-md-6 mb-3">
                <label for="shipping_company" class="form-label">Đơn vị vận chuyển</label>
                <select class="form-select" id="shipping_company" name="shipping_company">
                    <option value="">-- Chọn đơn vị vận chuyển --</option>
                    <?php foreach ($shippingCompanies as $key => $value): ?>
                        <option value="<?= $key ?>" <?= $order->shipping_company == $key ? 'selected' : '' ?>><?= esc($value) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-6 mb-3">
                <label for="tracking_code" class="form-label">Mã vận đơn</label>
                <input type="text" class="form-control" id="tracking_code" name="tracking_code" value="<?= esc($order->tracking_code ?? '') ?>">
            </div>
        </div>

        <div class="mb-3">
            <label for="status_note" class="form-label">Ghi chú cập nhật</label>
            <textarea class="form-control" id="status_note" name="status_note" rows="3" placeholder="Ghi chú thêm về việc cập nhật trạng thái (không bắt buộc)"></textarea>
        </div>

        <div class="d-flex justify-content-end">
            <button type="submit" class="btn btn-primary" disabled>
                Cập nhật trạng thái
            </button>
        </div>
        <?= form_close() ?>
    </div>
</div>

<script>
    $(() => {
        function toggleShippingFields() {
            if ($('#status').val() === 'Shipping') {
                $('#shippingFields').show();
            } else {
                $('#shippingFields').hide();
            }
        }

        toggleShippingFields();

        $('#status').change(toggleShippingFields);

        $('#updateStatusForm').validate({
            rules: {
                status: {
                    required: true,
                },
                payment_status: {
                    required: true,
                },
            },
            messages: {
                status: {
                    required: 'Vui lòng chọn trạng thái đơn hàng',
                },
                payment_status: {
                    required: 'Vui lòng chọn trạng thái thanh toán',
                },
            },
            submitHandler: function(form, event) {
                event.preventDefault();

                const button = $(form).find('button[type="submit"]');

                $.ajax({
                    url: form.action,
                    type: 'POST',
                    data: $(form).serialize(),
                    beforeSend: function() {
                        button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-1" role="status" aria-hidden="true"></span> Đang xử lý...');
                        $('.invalid-feedback').remove();
                        $('input, select').removeClass('is-invalid');
                    },
                    success: function(response) {
                        if (response.status) {
                            window.location.reload();
                        } else {
                            notyf.error(response.message);
                        }
                    },
                    error: function(error) {
                        button.prop('disabled', false);

                        if (error.status === 422) {
                            const errors = error.responseJSON.messages;

                            $.each(errors, function(key, value) {
                                const input = $(`#${key}`);
                                input.addClass('is-invalid');
                                input.next('.invalid-feedback').remove();
                                input.after(`<div class="invalid-feedback">${value}</div>`);
                            });
                        } else {
                            notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                        }
                    },
                    complete: function() {
                        button.html('Cập nhật trạng thái');
                    },
                });
            }
        });

        $('#status, #payment_status, #shipping_company, #tracking_code, #status_note').on('change keyup', function() {
            const status = $('#status').val();
            const paymentStatus = $('#payment_status').val();
            const status_note = $('#status_note').val().trim();

            if ((status && paymentStatus) || status_note) {
                $('#updateStatusForm button[type="submit"]').prop('disabled', false);
            } else {
                $('#updateStatusForm button[type="submit"]').prop('disabled', true);
            }
        });
    });
</script>
