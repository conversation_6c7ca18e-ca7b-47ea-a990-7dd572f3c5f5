<div class="tab-pane fade show active" id="payments-content" role="tabpanel" aria-labelledby="payments-tab">
    <div class="d-flex justify-content-end mb-3">
        <?php if ($order->payment_method == 'bank_transfer' && $order->payment_status == 'Unpaid'): ?>
            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
                <i class="bi bi-plus-circle me-1"></i> Thêm thanh toán
            </button>
        <?php endif; ?>
    </div>

    <?php if (! empty($payments)): ?>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Ngày GD</th>
                        <th>Phương thức</th>
                        <th>Mã GD</th>
                        <th class="text-end">Số tiền</th>
                        <th>Trạng thái</th>
                        <th width="150">Ghi chú</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($payments as $payment): ?>
                        <tr>
                            <td>
                                <?= ! empty($payment->transaction_date)
                                    ? date('d/m/Y H:i:s', strtotime($payment->transaction_date))
                                    : date('d/m/Y H:i:s', strtotime($payment->created_at)) ?>
                            </td>
                            <td>
                                <?php
                                switch ($payment->payment_method) {
                                    case 'bank_transfer':
                                        echo '<span class="badge bg-info">Chuyển khoản</span>';
                                        break;
                                    case 'cod':
                                        echo '<span class="badge bg-warning">Tiền mặt (COD)</span>';
                                        break;
                                    default:
                                        echo '<span class="badge bg-secondary">' . esc($payment->payment_method) . '</span>';
                                }
                                ?>
                            </td>
                            <td>
                                <?= !empty($payment->transaction_id)
                                    ? esc($payment->transaction_id)
                                    : '<span class="text-muted fst-italic">Không có</span>' ?>
                            </td>
                            <td class="text-end fw-bold">
                                <?= format_currency($payment->amount) ?>
                            </td>
                            <td>
                                <?php
                                switch ($payment->status) {
                                    case 'Completed':
                                        echo '<span class="badge bg-success">Hoàn thành</span>';
                                        break;
                                    case 'Pending':
                                        echo '<span class="badge bg-warning">Chờ xác nhận</span>';
                                        break;
                                    case 'Failed':
                                        echo '<span class="badge bg-danger">Thất bại</span>';
                                        break;
                                    case 'Refunded':
                                        echo '<span class="badge bg-secondary">Hoàn tiền</span>';
                                        break;
                                    default:
                                        echo '<span class="badge bg-secondary">' . esc($payment->status) . '</span>';
                                }
                                ?>
                            </td>
                            <td class="text-truncate" style="max-width: 150px;" data-bs-toggle="tooltip" title="<?= esc($payment->note) ?>">
                                <?= $payment->note ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php elseif ($order->payment_status == 'Unpaid'): ?>
        <div class="text-center p-3">
            <div class="text-muted mb-3">Chưa có thông tin thanh toán nào được ghi nhận</div>

            <?php if ($order->payment_status == 'Unpaid'): ?>
                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
                    <i class="bi bi-plus-circle me-1"></i> Thêm thanh toán
                </button>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <?php if ($order->payment_status == 'Paid'): ?>
        <div class="alert alert-success mt-3 mb-0">
            <div class="alert-message d-flex align-items-center">
                <i class="bi bi-check-circle-fill me-2 fs-5"></i>
                <div>
                    <strong>Đơn hàng đã được thanh toán đầy đủ</strong>
                    <div class="small">Tổng thanh toán: <?= format_currency($order->total_amount) ?></div>
                </div>
            </div>
        </div>
    <?php elseif ($order->payment_status == 'Unpaid'): ?>
        <div class="alert alert-warning mt-3 mb-0">
            <div class="alert-message d-flex align-items-center">
                <i class="bi bi-exclamation-triangle-fill me-2 fs-5"></i>
                <div>
                    <strong>Đơn hàng chưa được thanh toán</strong>
                    <div class="small">Cần thanh toán: <?= format_currency($order->total_amount) ?></div>
                </div>
            </div>
        </div>
    <?php elseif ($order->payment_status == 'Refunded'): ?>
        <div class="alert alert-info mt-3 mb-0">
            <div class="alert-message d-flex align-items-center">
                <i class="bi bi-arrow-return-left me-2 fs-5"></i>
                <div>
                    <strong>Đơn hàng đã được hoàn tiền</strong>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<div class="modal fade" id="addPaymentModal" tabindex="-1" aria-labelledby="addPaymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPaymentModalLabel">Thêm thông tin thanh toán</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('orders/add_payment/' . $order->id), ['id' => 'addPaymentForm']) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="payment_method" class="form-label">Phương thức thanh toán</label>
                    <select class="form-select" id="payment_method" name="payment_method" required>
                        <option value="bank_transfer" selected>Chuyển khoản ngân hàng</option>
                        <option value="cod">Tiền mặt (COD)</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="payment_amount" class="form-label">Số tiền</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="payment_amount" name="amount" min="1000" required>
                        <span class="input-group-text">đ</span>
                    </div>
                </div>

                <div id="bankTransferFields">
                    <div class="mb-3">
                        <label for="transaction_date" class="form-label">Ngày chuyển khoản</label>
                        <input type="datetime" class="form-control" id="transaction_date" name="transaction_date" required>
                    </div>

                    <div class="mb-3">
                        <label for="transaction_id" class="form-label">Mã giao dịch</label>
                        <input type="text" class="form-control" id="transaction_id" name="transaction_id" placeholder="Mã giao dịch/Số tham chiếu">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="payment_note" class="form-label">Ghi chú</label>
                    <textarea class="form-control" id="payment_note" name="note" rows="2" placeholder="Ghi chú về giao dịch thanh toán này"></textarea>
                </div>

                <div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="update_order_status" name="update_order_status" value="1" checked>
                        <label class="form-check-label" for="update_order_status">
                            Cập nhật trạng thái thanh toán của đơn hàng
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary">Thêm thanh toán</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script>
    $(() => {
        $('#payment_method').on('change', function() {
            if ($(this).val() === 'bank_transfer') {
                $('#bankTransferFields').show();
                $('#transaction_date').prop('required', true);
            } else {
                $('#bankTransferFields').hide();
                $('#transaction_date').prop('required', false);
            }
        });

        $('#addPaymentModal').on('show.bs.modal', function() {
            $('#payment_amount').val(<?= $order->total_amount ?>);
        });

        $('#addPaymentForm').validate({
            rules: {
                payment_method: {
                    required: true
                },
                amount: {
                    required: true,
                    number: true,
                    min: 1000
                },
                transaction_date: {
                    required: function() {
                        return $('#payment_method').val() === 'bank_transfer';
                    },
                    date: true
                }
            },
            messages: {
                payment_method: {
                    required: "Vui lòng chọn phương thức thanh toán"
                },
                amount: {
                    required: "Vui lòng nhập số tiền",
                    number: "Vui lòng nhập số hợp lệ",
                    min: "Số tiền phải từ 1.000đ trở lên"
                },
                transaction_date: {
                    required: "Vui lòng nhập ngày chuyển khoản",
                    date: "Vui lòng nhập ngày hợp lệ"
                }
            },
            errorElement: 'div',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                error.insertAfter(element.closest('.input-group') || element);
            },
            highlight: function(element) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function(element) {
                $(element).removeClass('is-invalid');
            },
            submitHandler: function(form, event) {
                event.preventDefault();

                const formData = $(form).serialize();
                const submitButton = $(form).find('button[type="submit"]');

                $.ajax({
                    url: $(form).attr('action'),
                    type: 'POST',
                    data: formData,
                    beforeSend: function() {
                        submitButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1" role="status"></span>Đang xử lý...');
                    },
                    success: function(response) {
                        if (response.status) {
                            notyf.success('Đã thêm thanh toán thành công');
                            $('#addPaymentModal').modal('hide');
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            notyf.error(response.message || 'Có lỗi xảy ra khi thêm thanh toán');
                            submitButton.prop('disabled', false).text('Thêm thanh toán');
                        }
                    },
                    error: function(xhr) {
                        submitButton.prop('disabled', false).text('Thêm thanh toán');

                        if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.messages) {
                            const errors = xhr.responseJSON.messages;
                            $.each(errors, function(key, value) {
                                const element = $('#' + key);
                                element.addClass('is-invalid');

                                if (element.next('.invalid-feedback').length === 0) {
                                    $('<div class="invalid-feedback">' + value + '</div>').insertAfter(element);
                                } else {
                                    element.next('.invalid-feedback').text(value);
                                }
                            });
                        } else {
                            notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                        }
                    }
                });
            }
        });
    });
</script>