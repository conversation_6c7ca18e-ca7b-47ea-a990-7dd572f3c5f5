<?php if ($tracking || $partner): ?>
    <div class="card mb-0">
        <div class="card-header d-flex justify-content-between align-items-center" data-bs-toggle="collapse" href="#trackingCollapse" role="button" aria-expanded="false" aria-controls="trackingCollapse">
            <h5 class="card-title mb-0">Thông tin nguồn đơn hàng</h5>
            <i class="bi bi-chevron-down"></i>
        </div>
        <div class="collapse" id="trackingCollapse">
            <div class="card-body pt-0">
                <?php if ($tracking): ?>
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row mb-0">
                                <dt class="col-sm-4">IP:</dt>
                                <dd class="col-sm-8"><?= esc($tracking->ip_address) ?></dd>
                                <dt class="col-sm-4">T<PERSON><PERSON><PERSON> du<PERSON>ệt:</dt>
                                <dd class="col-sm-8"><?= esc($tracking->user_agent) ?></dd>
                                <dt class="col-sm-4">Trang giới thiệu:</dt>
                                <dd class="col-sm-8"><?= esc($tracking->referrer) ?: 'Truy cập trực tiếp' ?></dd>
                                <dt class="col-sm-4">Thời gian:</dt>
                                <dd class="col-sm-8"><?= date('d/m/Y H:i:s', strtotime($tracking->created_at)) ?></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row mb-0">
                                <dt class="col-sm-4">Source:</dt>
                                <dd class="col-sm-8"><?= esc($tracking->utm_source) ?: 'Không có' ?></dd>
                                <dt class="col-sm-4">Medium:</dt>
                                <dd class="col-sm-8"><?= esc($tracking->utm_medium) ?: 'Không có' ?></dd>
                                <dt class="col-sm-4">Campaign:</dt>
                                <dd class="col-sm-8"><?= esc($tracking->utm_campaign) ?: 'Không có' ?></dd>
                                <dt class="col-sm-4">Term:</dt>
                                <dd class="col-sm-8"><?= esc($tracking->utm_term) ?: 'Không có' ?></dd>
                                <dt class="col-sm-4">Content:</dt>
                                <dd class="col-sm-8"><?= esc($tracking->utm_content) ?: 'Không có' ?></dd>
                            </dl>
                        </div>
                    </div>
                <?php endif; ?>
                <div <?php if ($tracking) : ?> class="mt-3 border-top pt-3" <?php endif; ?>>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="fw-bold mb-0">Người giới thiệu</h6>
                        <button type="button" class="btn btn-link btn-sm p-0" data-bs-toggle="modal" data-bs-target="#editPartnerModal" title="Chỉnh sửa người giới thiệu">
                            <i class="bi bi-pencil"></i>
                        </button>
                    </div>
                    <?php if ($partner): ?>
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <div class="fw-medium"><?= esc($partner->name) ?></div>
                                <div class="text-muted small">
                                    #<?= $partner->id ?> - <?= esc($partner->email) ?>
                                </div>
                            </div>
                            <div>
                                <a href="<?= base_url('partner/login_as_partner/' . $partner->id) ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                    <i class="bi bi-box-arrow-in-right me-1"></i>Đăng nhập
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-muted fst-italic">Chưa có thông tin người giới thiệu</div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<div class="modal fade" id="editPartnerModal" tabindex="-1" aria-labelledby="editPartnerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPartnerModalLabel">Chỉnh sửa người giới thiệu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('orders/update_partner/' . $order->id), ['id' => 'updatePartnerForm']) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="partner_id" class="form-label">Chọn người giới thiệu</label>
                    <select id="partner_id" name="partner_id" class="form-select" placeholder="Tìm kiếm người giới thiệu..."></select>
                    <div class="form-text">Tìm kiếm theo tên hoặc email của người giới thiệu</div>
                </div>
                <?php if (isset($partner) && $partner): ?>
                    <div class="text-end mt-3">
                        <button type="button" class="btn btn-sm btn-outline-danger" id="removePartner">
                            <i class="bi bi-trash me-1"></i> Xóa người giới thiệu
                        </button>
                    </div>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary">Lưu thay đổi</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script>
    $(() => {
        let partnerSelect = new TomSelect('#partner_id', {
            valueField: 'id',
            labelField: 'name',
            searchField: ['name', 'email'],
            placeholder: 'Tìm kiếm người giới thiệu...',
            create: false,
            load: function(query, callback) {
                if (!query.length) return callback();

                $.ajax({
                    url: '<?= base_url('orders/search_partners') ?>',
                    type: 'POST',
                    data: {
                        '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                        search: query
                    },
                    dataType: 'json',
                    error: function() {
                        callback();
                    },
                    success: function(res) {
                        callback(res.data);
                    }
                });
            },
            render: {
                option: function(item, escape) {
                    return `<div class="py-1 px-2 flex-column align-items-start">
                        <div class="mb-1">${escape(item.name)}</div>
                        <div class="text-muted small">#${escape(item.id)} - ${escape(item.email)}</div>
                    </div>`;
                },
                item: function(item, escape) {
                    return `<div class="py-1 px-2 flex-column align-items-start">
                        <div class="mb-1">${escape(item.name)}</div>
                        <div class="text-muted small">#${escape(item.id)} - ${escape(item.email)}</div>
                    </div>`;
                }
            }
        });

        <?php if (isset($partner) && $partner): ?>
            partnerSelect.addOption({
                id: '<?= $partner->id ?>',
                name: '<?= $partner->name ?>',
                email: '<?= $partner->email ?>'
            });
            partnerSelect.setValue('<?= $partner->id ?>');
        <?php endif; ?>

        $('#removePartner').on('click', function() {
            partnerSelect.clear();
        });

        $('#updatePartnerForm').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-1" role="status"></span>Đang lưu...');
                    $('.invalid-feedback').remove();
                    $('.is-invalid').removeClass('is-invalid');
                },
                success: function(response) {
                    if (response.status) {
                        $('#editPartnerModal').modal('hide');
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function(error) {
                    button.prop('disabled', false).text('Lưu thay đổi');

                    if (error.status === 422) {
                        const errors = error.responseJSON.messages;
                        $.each(errors, function(key, value) {
                            const input = $(`#${key}`);
                            input.addClass('is-invalid');
                            input.after(`<div class="invalid-feedback">${value}</div>`);
                        });
                    } else {
                        notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                    }
                },
                complete: function() {
                    button.prop('disabled', false).text('Lưu thay đổi');
                }
            });
        });
    });
</script>