<div class="card mb-3">
    <div class="card-header pb-0">
        <h5 class="card-title mb-0">Thông tin đơn hàng</h5>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <strong>Mã đơn hàng:</strong> <?= $order->order_code ?>
        </div>
        <div class="mb-3">
            <strong>Ngày đặt:</strong> <?= date('d/m/Y H:i:s', strtotime($order->created_at)) ?>
        </div>
        <div class="mb-3">
            <strong>Trạng thái:</strong>
            <?= \App\Enums\OrderStatus::toHtml($order->status) ?>
        </div>
        <div class="mb-3">
            <strong>Trạng thái thanh toán:</strong>
            <?= get_order_payment_status_badge($order->payment_status) ?>
        </div>
        <div>
            <strong>Phương thức thanh toán:</strong>
            <?php
            switch ($order->payment_method) {
                case 'cod':
                    echo '<PERSON>h toán khi nhận hàng (COD)';
                    break;
                case 'bank_transfer':
                    echo 'Chuyển khoản ngân hàng';
                    break;
                default:
                    echo 'Không xác định';
                    break;
            }
            ?>
        </div>
        <?php if ($order->notes): ?>
            <div class="mt-3">
                <strong>Ghi chú:</strong>
                <span class="text-muted"><?= nl2br(esc($order->notes)) ?></span>
            </div>
        <?php endif; ?>
        <?php if (!empty($order->shipping_company) || !empty($order->tracking_code)): ?>
            <div class="position-relative mt-3 border-top pt-3">
                <h5 class="fw-bold mb-3">Thông tin vận chuyển</h5>
                <button type="button" class="btn btn-link position-absolute" data-bs-toggle="modal" data-bs-target="#editShippingModal" title="Chỉnh sửa thông tin vận chuyển" style="top: 0.5rem; right: -0.75em;">
                    <i class="bi bi-pencil"></i>
                </button>

                <?php if (!empty($order->shipping_company)): ?>
                    <div class="mb-2">
                        <strong>Đơn vị vận chuyển:</strong> <?= esc($order->shipping_company) ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($order->tracking_code)): ?>
                    <div>
                        <strong>Mã vận đơn:</strong> <?= esc($order->tracking_code) ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<div class="modal fade" id="editShippingFeeModal" tabindex="-1" aria-labelledby="editShippingFeeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editShippingFeeModalLabel">Chỉnh sửa phí vận chuyển</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('orders/update_shipping_fee/' . $order->id), ['id' => 'updateShippingFeeForm']) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="shipping_fee" class="form-label">Phí vận chuyển</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="shipping_fee" name="shipping_fee" value="<?= number_format($order->shipping_fee, 0, '', '') ?>" min="0" required>
                        <span class="input-group-text">đ</span>
                    </div>
                    <div class="form-text">Nhập 0 nếu miễn phí vận chuyển</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary">Cập nhật</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<div class="modal fade" id="editShippingModal" tabindex="-1" aria-labelledby="editShippingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editShippingModalLabel">Chỉnh sửa thông tin vận chuyển</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('orders/update_shipping/' . $order->id), ['id' => 'updateShippingForm']) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="modal_shipping_company" class="form-label">Đơn vị vận chuyển</label>
                    <select class="form-select" id="modal_shipping_company" name="shipping_company">
                        <option value="">-- Chọn đơn vị vận chuyển --</option>
                        <?php foreach ($shippingCompanies as $key => $value): ?>
                            <option value="<?= $key ?>" <?= $order->shipping_company == $key ? 'selected' : '' ?>><?= esc($value) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="modal_tracking_code" class="form-label">Mã vận đơn</label>
                    <input type="text" class="form-control" id="modal_tracking_code" name="tracking_code" value="<?= esc($order->tracking_code ?? '') ?>">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary" id="saveShippingButton">Lưu thay đổi</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script>
    $(() => {
        $('#updateShippingFeeForm').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-1" role="status"></span>Đang cập nhật...');
                    $('.invalid-feedback').remove();
                    $('input').removeClass('is-invalid');
                },
                success: function(response) {
                    if (response.status) {
                        $('#editShippingFeeModal').modal('hide');
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function(error) {
                    button.prop('disabled', false).text('Cập nhật');

                    if (error.status === 422) {
                        const errors = error.responseJSON.messages;
                        $.each(errors, function(key, value) {
                            const input = $(`#${key}`);
                            input.addClass('is-invalid');
                            input.after(`<div class="invalid-feedback">${value}</div>`);
                        });
                    } else {
                        notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                    }
                },
                complete: function() {
                    button.prop('disabled', false).text('Cập nhật');
                }
            });
        });

        $('#updateShippingForm').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1" role="status"></span>Đang lưu...');
                    $('.invalid-feedback').remove();
                    $('input, select').removeClass('is-invalid');
                },
                success: function(response) {
                    if (response.status) {
                        $('#editShippingModal').modal('hide');
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function(error) {
                    button.prop('disabled', false).text('Lưu thay đổi');

                    if (error.status === 422) {
                        const errors = error.responseJSON.messages;
                        $.each(errors, function(key, value) {
                            const input = $(`#modal_${key}`);
                            input.addClass('is-invalid');
                            input.after(`<div class="invalid-feedback">${value}</div>`);
                        });
                    } else {
                        notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                    }
                },
                complete: function() {
                    button.prop('disabled', false).text('Lưu thay đổi');
                }
            });
        });
    });
</script>