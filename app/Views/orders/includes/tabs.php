<div class="tab">
    <ul class="nav nav-tabs" id="orderDetailsTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="payments-tab" data-bs-toggle="tab" data-bs-target="#payments-content" type="button" role="tab" aria-controls="payments-content" aria-selected="true">
                Thông tin thanh toán
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history-content" type="button" role="tab" aria-controls="history-content" aria-selected="false">
                Lịch sử đơn hàng
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="decals-tab" data-bs-toggle="tab" data-bs-target="#decals-content" type="button" role="tab" aria-controls="decals-content" aria-selected="false">
                Danh sách loa
            </button>
        </li>
    </ul>
    <div class="tab-content" id="orderDetailsTabsContent">
        <?php include(APPPATH . 'Views/orders/includes/payments-tab.php') ?>

        <?php include(APPPATH . 'Views/orders/includes/histories-tab.php') ?>

        <?php include(APPPATH . 'Views/orders/includes/decals-tab.php') ?>
    </div>
</div>

<script>
    $(() => {
        const hash = window.location.hash;

        if (hash) {
            const tabId = hash.replace('#', '');
            const tab = $(`#${tabId}-tab`);
            if (tab.length) {
                tab.tab('show');
            }
        }

        $('button[data-bs-toggle="tab"]').on('shown.bs.tab', function(e) {
            const id = $(e.currentTarget).prop('id').replace('-tab', '');
            window.location.hash = id;
        });
    });
</script>
