<div class="card mb-3">
    <div class="card-header pb-0">
        <h5 class="card-title mb-0">Địa chỉ giao hàng</h5>
        <button type="button" class="btn btn-link position-absolute" data-bs-toggle="modal" data-bs-target="#editAddressModal" title="Chỉnh sửa địa chỉ" style="top: 0.5rem; right: 0.5rem;">
            <i class="bi bi-pencil"></i>
        </button>
    </div>
    <div class="card-body">
        <address>
            <?= esc($order->address) ?><br>
            <?= esc($order->ward) ?>, <?= esc($order->district) ?><br>
            <?= esc($order->province) ?>
        </address>
        <div class="mt-2">
            <a href="<?= $mapUrl ?>" class="btn btn-sm btn-outline-secondary" target="_blank">
                <i class="bi bi-geo-alt me-1"></i> Xem trên Google Map
            </a>
        </div>
    </div>
</div>

<div class="modal fade" id="editAddressModal" tabindex="-1" aria-labelledby="editAddressModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAddressModalLabel">Chỉnh sửa địa chỉ giao hàng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('orders/update_address/' . $order->id), ['id' => 'updateAddressForm']) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="address" class="form-label">Địa chỉ</label>
                    <input type="text" class="form-control" id="address" name="address" value="<?= esc($order->address) ?>">
                </div>
                <div class="mb-3">
                    <label for="ward" class="form-label">Phường/Xã</label>
                    <input type="text" class="form-control" id="ward" name="ward" value="<?= esc($order->ward) ?>">
                </div>
                <div class="mb-3">
                    <label for="district" class="form-label">Quận/Huyện</label>
                    <input type="text" class="form-control" id="district" name="district" value="<?= esc($order->district) ?>">
                </div>
                <div class="mb-3">
                    <label for="province" class="form-label">Tỉnh/Thành phố</label>
                    <input type="text" class="form-control" id="province" name="province" value="<?= esc($order->province) ?>">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary">Lưu địa chỉ</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script>
    $(() => {
        $('#updateAddressForm').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-1" role="status"></span>Đang lưu...');
                    $('.invalid-feedback').remove();
                    $('input').removeClass('is-invalid');
                },
                success: function(response) {
                    if (response.status) {
                        $('#editAddressModal').modal('hide');
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function(error) {
                    button.prop('disabled', false).text('Lưu địa chỉ');

                    if (error.status === 422) {
                        const errors = error.responseJSON.messages;
                        $.each(errors, function(key, value) {
                            const input = $(`#${key}`);
                            input.addClass('is-invalid');
                            input.after(`<div class="invalid-feedback">${value}</div>`);
                        });
                    } else {
                        notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                    }
                },
                complete: function() {
                    button.prop('disabled', false).text('Lưu địa chỉ');
                }
            });
        });
    });
</script>