<div class="card mb-3">
    <div class="card-header pb-0">
        <h5 class="card-title mb-0">Thông tin khách hàng</h5>
        <button type="button" class="btn btn-link position-absolute" data-bs-toggle="modal" data-bs-target="#editCustomerModal" title="Chỉnh sửa thông tin khách hàng" style="top: 0.5rem; right: 0.5rem;">
            <i class="bi bi-pencil"></i>
        </button>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <strong>Loại khách hàng:</strong>
            <?= $order->customer_type == 'wholesale' ?
                '<span class="badge bg-primary">Khách sỉ</span>' :
                '<span class="badge bg-secondary">Khách lẻ</span>' ?>
        </div>
        <div class="mb-3">
            <strong>Họ tên:</strong> <?= esc($order->customer_name) ?>
        </div>
        <div>
            <strong>Số điện thoại:</strong>
            <a href="tel:<?= esc($order->customer_phone) ?>"><?= esc($order->customer_phone) ?></a>
        </div>
        <?php if ($order->customer_email): ?>
            <div class="mt-3">
                <strong>Email:</strong>
                <a href="mailto:<?= esc($order->customer_email) ?>"><?= esc($order->customer_email) ?></a>
            </div>
        <?php endif; ?>
    </div>
</div>

<div class="modal fade" id="editCustomerModal" tabindex="-1" aria-labelledby="editCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCustomerModalLabel">Chỉnh sửa thông tin khách hàng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('orders/update_customer/' . $order->id), ['id' => 'updateCustomerForm']) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="customer_type" class="form-label">Loại khách hàng</label>
                    <select class="form-select" id="customer_type" name="customer_type">
                        <option value="retail" <?= $order->customer_type == 'retail' ? 'selected' : '' ?>>Khách lẻ</option>
                        <option value="wholesale" <?= $order->customer_type == 'wholesale' ? 'selected' : '' ?>>Khách sỉ</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="customer_name" class="form-label">Họ tên</label>
                    <input type="text" class="form-control" id="customer_name" name="customer_name" value="<?= esc($order->customer_name) ?>">
                </div>
                <div class="mb-3">
                    <label for="customer_phone" class="form-label">Số điện thoại</label>
                    <input type="text" class="form-control" id="customer_phone" name="customer_phone" value="<?= esc($order->customer_phone) ?>">
                </div>
                <div class="mb-3">
                    <label for="customer_email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="customer_email" name="customer_email" value="<?= esc($order->customer_email) ?>">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary">Lưu thông tin</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script>
    $(() => {
        $('#updateCustomerForm').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-1" role="status"></span>Đang lưu...');
                    $('.invalid-feedback').remove();
                    $('input, select').removeClass('is-invalid');
                },
                success: function(response) {
                    if (response.status) {
                        $('#editCustomerModal').modal('hide');
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function(error) {
                    button.prop('disabled', false).text('Lưu thông tin');

                    if (error.status === 422) {
                        const errors = error.responseJSON.messages;
                        $.each(errors, function(key, value) {
                            const input = $(`#${key}`);
                            input.addClass('is-invalid');
                            input.after(`<div class="invalid-feedback">${value}</div>`);
                        });
                    } else {
                        notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                    }
                },
                complete: function() {
                    button.prop('disabled', false).text('Lưu thông tin');
                }
            });
        }); 
    });
</script>