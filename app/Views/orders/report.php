<style>
    .dataTables_info {
        padding-top: 0 !important;
    }

    .stats-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        border-radius: 0.5rem;
    }

    .stats-icon i {
        font-size: 1.25rem;
    }
</style>

<main class="content">
    <div class="container-fluid my-3">
        <div class="d-flex justify-content-between mb-3">
            <h1 class="h3">Báo cáo đơn hàng</h1>
        </div>

        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-3 mb-3">
            <div class="col d-flex">
                <div class="card flex-fill mb-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col mt-0">
                                <h5 class="card-title">Tổng đơn hàng <i class="bi bi-question-circle ms-2" data-bs-toggle="tooltip" title="Tổng số đơn hàng trong hệ thống"></i></h5>
                            </div>
                            <div class="col-auto">
                                <div class="stats-icon bg-primary text-white">
                                    <i class="bi bi-cart"></i>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mt-1">
                            <h3 class="mb-0 me-3" id="stat-total-orders"><span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span></h3>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col d-flex">
                <div class="card flex-fill mb-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col mt-0">
                                <h5 class="card-title">Chờ xác nhận <i class="bi bi-question-circle ms-2" data-bs-toggle="tooltip" title="Đơn hàng chờ xác nhận"></i></h5>
                            </div>
                            <div class="col-auto">
                                <div class="stats-icon bg-warning text-white">
                                    <i class="bi bi-clock"></i>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-end mt-1">
                            <h3 class="mb-0 me-2" id="stat-pending-confirmation-orders"><span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span></h3>
                            <div class="text-muted" id="stat-pending-confirmation-quantity"><span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col d-flex">
                <div class="card flex-fill mb-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col mt-0">
                                <h5 class="card-title">Hoàn thành <i class="bi bi-question-circle ms-2" data-bs-toggle="tooltip" title="Tổng số đơn hàng đã hoàn thành"></i></h5>
                            </div>
                            <div class="col-auto">
                                <div class="stats-icon bg-success text-white">
                                    <i class="bi bi-check-circle"></i>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mt-1">
                            <h3 class="mb-0 me-3" id="stat-completed-orders"><span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span></h3>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col d-flex">
                <div class="card flex-fill mb-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col mt-0">
                                <h5 class="card-title">Đang xử lý <i class="bi bi-question-circle ms-2" data-bs-toggle="tooltip" title="Tổng số đơn hàng đang xử lý"></i></h5>
                            </div>
                            <div class="col-auto">
                                <div class="stats-icon bg-warning text-white">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mt-1">
                            <h3 class="mb-0 me-3" id="stat-pending-orders"><span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span></h3>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col d-flex">
                <div class="card flex-fill mb-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col mt-0">
                                <h5 class="card-title">Doanh thu <i class="bi bi-question-circle ms-2" data-bs-toggle="tooltip" title="Tổng doanh thu từ các đơn hàng"></i></h5>
                            </div>
                            <div class="col-auto">
                                <div class="stats-icon bg-danger text-white">
                                    <i class="bi bi-currency-dollar"></i>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mt-1">
                            <h3 class="mb-0 me-3" id="stat-revenue"><span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span></h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-3">
            <div class="col-lg-8">
                <div class="card mb-0 h-100">
                    <div class="card-header d-flex justify-content-between">
                        <h5 class="card-title mb-0">Đơn hàng theo thời gian</h5>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-secondary chart-type active" data-type="count">Số đơn</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary chart-type" data-type="amount">Doanh thu</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary chart-type" data-type="quantity">Số lượng loa</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="position: relative; height:350px;">
                            <canvas id="ordersChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card mb-0 h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Đơn hàng theo nguồn</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="position: relative; height:350px;">
                            <canvas id="sourceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card mb-0 h-100">
                    <div class="card-header pb-0">
                        <h5 class="card-title mb-0">Top sản phẩm bán chạy</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table mb-0">
                                <thead>
                                    <tr>
                                        <th>Sản phẩm</th>
                                        <th class="text-end">Số lượng</th>
                                        <th class="text-end">Doanh thu</th>
                                    </tr>
                                </thead>
                                <tbody id="top-products">
                                    <tr>
                                        <td colspan="3" class="text-center"><span class="spinner-border spinner-border-sm"></span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card mb-0 h-100">
                    <div class="card-header pb-0">
                        <h5 class="card-title mb-0">Phân bố đơn hàng theo khu vực</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="position: relative; height:350px;">
                            <canvas id="regionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php') ?>

<script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery-3.5.1.js') ?>"></script>
<script src="<?= base_url('assets/notyf/notyf.min.js') ?>"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
<script src="<?= base_url('assets/js/app.js?v=1') ?>"></script>

<script>
    $(() => {
        $.ajax({
            url: '<?= base_url('orders/ajax_stats') ?>',
            type: 'POST',
            data: {
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
            },
            dataType: 'JSON',
            success: function({
                status,
                data
            }) {
                if (status) {
                    $('#stat-total-orders').text(data.total_orders.toLocaleString());
                    $('#stat-completed-orders').text(data.completed_orders.toLocaleString());
                    $('#stat-pending-orders').text(data.pending_orders.toLocaleString());
                    $('#stat-revenue').text(data.revenue.toLocaleString());
                    $('#stat-pending-confirmation-orders').text(`${data.pending_orders_count.toLocaleString()} đơn`);
                    $('#stat-pending-confirmation-quantity').text(`${data.pending_orders_quantity.toLocaleString()} loa`);
                }
            },
            error: function() {
                notyf.error('Có lỗi xảy ra trong quá trình lấy dữ liệu');
            }
        });

        const ordersChartCtx = $('#ordersChart').get(0).getContext('2d');
        const sourceChartCtx = $('#sourceChart').get(0).getContext('2d');
        const regionChartCtx = $('#regionChart').get(0).getContext('2d');

        const ordersChart = new Chart(ordersChartCtx, {
            type: 'line',
            data: {
                datasets: [{
                    label: 'Số đơn hàng',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    tension: 0.2,
                    pointRadius: 3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'day',
                            displayFormats: {
                                day: 'dd/MM'
                            }
                        },
                        title: {
                            display: true,
                            text: 'Ngày'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Số đơn hàng'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            title: function(tooltipItems) {
                                const date = new Date(tooltipItems[0].parsed.x);
                                return date.toLocaleDateString('vi-VN', {
                                    day: '2-digit',
                                    month: '2-digit',
                                    year: 'numeric',
                                });
                            }
                        }
                    }
                }
            }
        });

        const sourceChart = new Chart(sourceChartCtx, {
            type: 'doughnut',
            data: {
                labels: [],
                datasets: [{
                    data: [],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(199, 199, 199, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.formattedValue;
                                const dataset = context.dataset;
                                const total = dataset.data.reduce((acc, data) => acc + data, 0);
                                const percentage = Math.round((context.raw / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        const regionChart = new Chart(regionChartCtx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: 'Số đơn hàng',
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    data: []
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        $('.chart-type').click(function() {
            $('.chart-type').removeClass('active');
            $(this).addClass('active');
            updateOrdersChart();
        });

        function updateOrdersChart() {
            const chartType = $('.chart-type.active').data('type');
            $.ajax({
                url: '<?= base_url('orders/ajax_chart') ?>',
                type: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                    type: chartType
                },
                dataType: 'JSON',
                success: function({
                    status,
                    data
                }) {
                    if (status) {
                        let label = 'Số đơn hàng';
                        let yAxisTitle = 'Số đơn hàng';

                        if (chartType === 'amount') {
                            label = 'Doanh thu';
                            yAxisTitle = 'Doanh thu (đ)';
                        } else if (chartType === 'quantity') {
                            label = 'Số lượng loa';
                            yAxisTitle = 'Số lượng loa';
                        }

                        ordersChart.data.datasets[0].label = label;
                        ordersChart.options.scales.y.title.text = yAxisTitle;

                        ordersChart.data.labels = data.labels;
                        ordersChart.data.datasets[0].data = data.values;
                        ordersChart.update();

                        sourceChart.data.labels = data.sources.map(source => source.name);
                        sourceChart.data.datasets[0].data = data.sources.map(source => source.count);
                        sourceChart.update();
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra trong quá trình lấy dữ liệu');
                }
            });
        }

        updateOrdersChart();

        function formatCurreny(value) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(value);
        }

        $.ajax({
            url: '<?= base_url('orders/ajax_extended_stats') ?>',
            type: 'POST',
            data: {
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
            },
            dataType: 'JSON',
            success: function({
                status,
                data
            }) {
                if (status) {
                    let productsHtml = '';

                    data.top_products.forEach(product => {
                        productsHtml += `<tr>
                            <td>${product.name}</td>
                            <td class="text-end">${product.total_quantity}</td>
                            <td class="text-end">${formatCurreny(product.revenue)}</td>
                        </tr>`;
                    });
                    $('#top-products').html(productsHtml);

                    regionChart.data.labels = data.regions.map(region => region.name);
                    regionChart.data.datasets[0].data = data.regions.map(region => region.count);
                    regionChart.update();
                }
            }
        });
    });
</script>
