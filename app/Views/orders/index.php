<link href="<?= base_url('assets/tom-select/tom-select.bootstrap5.css') ?>" rel="stylesheet">

<style>
    .dataTables_info {
        padding-top: 0 !important;
    }
</style>

<main class="content">
    <div class="container-fluid my-3">
        <div class="row mb-3 align-items-center">
            <div class="col">
                <h3>Quản lý đơn hàng</h3>
            </div>
        </div>

        <div class="card mb-3">
            <div class="card-header pb-0">
                <h5 class="card-title mb-0">Bộ lọc</h5>
            </div>
            <div class="card-body">
                <form id="filter-form">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Khoảng thời gian</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-calendar3"></i></span>
                                <input type="text" class="form-control" id="date_filter" name="date_filter" placeholder="Chọn khoảng thời gian">
                                <button type="button" id="clear-date" class="btn btn-outline-secondary">
                                    <i class="bi bi-x"></i>
                                </button>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">Trạng thái</label>
                            <select class="form-select tomselect" name="status[]" id="status" multiple>
                                <?php foreach (\App\Enums\OrderStatus::toArray() as $status): ?>
                                    <option value="<?= $status ?>"><?= \App\Enums\OrderStatus::getLabel($status) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">Thanh toán</label>
                            <select class="form-select" name="payment_status" id="payment_status">
                                <option value="">Tất cả (<span class="payment-status-count-all">0</span>)</option>
                                <option value="Unpaid">Chưa thanh toán (<span class="payment-status-count" data-status="Unpaid">0</span>)</option>
                                <option value="Paid">Đã thanh toán (<span class="payment-status-count" data-status="Paid">0</span>)</option>
                                <option value="Refunded">Đã hoàn tiền (<span class="payment-status-count" data-status="Refunded">0</span>)</option>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">Loại khách hàng</label>
                            <select class="form-select" name="customer_type" id="customer_type">
                                <option value="">Tất cả (<span class="customer-type-count-all">0</span>)</option>
                                <option value="retail">Khách lẻ (<span class="customer-type-count" data-type="retail">0</span>)</option>
                                <option value="wholesale">Khách sỉ (<span class="customer-type-count" data-type="wholesale">0</span>)</option>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">Nguồn</label>
                            <select class="form-select" name="source" id="source">
                                <option value="">Tất cả</option>
                                <?php foreach ($sources as $source): ?>
                                    <option value="<?= esc($source['source']) ?>"><?= esc($source['source']) ?> (<?= $source['count'] ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-12">
                            <button type="button" id="reset-filter" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i> Xóa bộ lọc
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <table class="table" id="dataTables">
                    <thead>
                        <tr>
                            <th>Mã đơn hàng</th>
                            <th>Khách hàng</th>
                            <th>Nguồn</th>
                            <th>Số lượng</th>
                            <th>Tổng tiền</th>
                            <th>Trạng thái</th>
                            <th>Thanh toán</th>
                            <th>Ngày tạo</th>
                            <?php if (has_permission('Order', 'can_view_all')): ?>
                                <th></th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
</main>

<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php') ?>

<script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery-3.5.1.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery.dataTables.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.bootstrap5.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.rowReorder.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.responsive.min.js') ?>"></script>
<script src="<?= base_url('assets/daterangepicker/moment.min.js') ?>"></script>
<script src="<?= base_url('assets/daterangepicker/daterangepicker.js') ?>"></script>
<script src="<?= base_url('assets/notyf/notyf.min.js') ?>"></script>
<script src="<?= base_url('assets/js/app.js?v=1') ?>"></script>
<script src="<?= base_url('assets/tom-select/tom-select.complete.min.js') ?>"></script>

<script>
    $(() => {
        const statusSelect = new TomSelect('#status', {
            plugins: ['remove_button'],
            placeholder: 'Chọn trạng thái',
            allowEmptyOption: true,
            render: {
                item: function(data, escape) {
                    return `<div>${escape(data.text)}</div>`;
                }
            }
        });

        const dataTable = $('#dataTables').DataTable({
            processing: true,
            serverSide: true,
            pageLength: 25,
            responsive: true,
            ajax: {
                url: '<?= base_url('orders/ajax_list') ?>',
                type: 'POST',
                data: function(d) {
                    d['<?= csrf_token() ?>'] = '<?= csrf_hash() ?>';

                    d.date_from = $('#date_filter').data('start-date');
                    d.date_to = $('#date_filter').data('end-date');

                    d.status = $('#status').val();
                    d.payment_status = $('#payment_status').val();
                    d.customer_type = $('#customer_type').val();
                    d.source = $('#source').val();

                    return d;
                }
            },
            language: {
                sProcessing: 'Đang xử lý...',
                sLengthMenu: 'Xem _MENU_ mục',
                sZeroRecords: 'Không tìm thấy dòng nào phù hợp',
                sInfo: 'Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục',
                sInfoEmpty: 'Đang xem 0 đến 0 trong tổng số 0 mục',
                sInfoFiltered: '(được lọc từ _MAX_ mục)',
                sInfoPostFix: '',
                sSearch: 'Tìm:',
                oPaginate: {
                    sFirst: 'Đầu',
                    sLast: 'Cuối',
                    sNext: 'Tiếp',
                    sPrevious: 'Trước',
                },
            },
            order: [
                [8, 'desc']
            ],
            columnDefs: [
                {
                    orderable: false,
                    targets: [2, 4],
                },
                <?php if (has_permission('Order', 'can_view_all')) : ?> {
                        orderable: false,
                        targets: [8]
                    },
                <?php endif; ?>
            ],
            dom: `<'d-flex flex-wrap gap-2 justify-content-between'fB>tr<'d-flex flex-wrap gap-2 justify-content-center justify-content-md-between mt-3'<'d-flex flex-wrap justify-content-center align-items-center gap-3'li>p>`,
        });

        $('#date_filter').daterangepicker({
            autoUpdateInput: false,
            locale: {
                cancelLabel: 'Xóa',
                applyLabel: 'Áp dụng',
                fromLabel: 'Từ',
                toLabel: 'Đến',
                customRangeLabel: 'Tùy chọn',
                daysOfWeek: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
                monthNames: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
                firstDay: 1
            },
            ranges: {
                'Hôm nay': [moment(), moment()],
                'Hôm qua': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                '7 ngày qua': [moment().subtract(6, 'days'), moment()],
                '30 ngày qua': [moment().subtract(29, 'days'), moment()],
                'Tháng này': [moment().startOf('month'), moment().endOf('month')],
                'Tháng trước': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            alwaysShowCalendars: true,
            opens: 'right'
        });

        function applyFilters() {
            saveFilterState();
            updateFilterCounts();
            dataTable.ajax.reload();
        }

        $('#status, #payment_status, #customer_type, #source').on('change', function() {
            applyFilters();
        });

        $('#date_filter').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('DD/MM/YYYY') + ' - ' + picker.endDate.format('DD/MM/YYYY'));
            $(this).data('start-date', picker.startDate.format('YYYY-MM-DD'));
            $(this).data('end-date', picker.endDate.format('YYYY-MM-DD'));
            applyFilters();
        });

        $('#date_filter').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
            $(this).data('start-date', '');
            $(this).data('end-date', '');
            applyFilters();
        });

        $('#clear-date').click(function() {
            $('#date_filter').val('');
            $('#date_filter').data('start-date', '');
            $('#date_filter').data('end-date', '');
            applyFilters();
        });

        $('#filter-form').on('submit', function(e) {
            e.preventDefault();
            applyFilters();
        });

        $('#reset-filter').click(function() {
            $('#filter-form')[0].reset();
            $('#date_filter').val('');
            $('#date_filter').data('start-date', '');
            $('#date_filter').data('end-date', '');
            statusSelect.setValue([]);
            sessionStorage.removeItem('orderFilters');
            applyFilters();
        });

        function updateFilterCounts(excludeFilter = null) {
            const filters = {
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
            };

            if ($('#date_filter').data('start-date')) {
                filters.date_from = $('#date_filter').data('start-date');
            }
            if ($('#date_filter').data('end-date')) {
                filters.date_to = $('#date_filter').data('end-date');
            }
            if (excludeFilter !== 'status') {
                filters.status = statusSelect.getValue();
            }
            if (excludeFilter !== 'payment_status' && $('#payment_status').val()) {
                filters.payment_status = $('#payment_status').val();
            }
            if (excludeFilter !== 'customer_type' && $('#customer_type').val()) {
                filters.customer_type = $('#customer_type').val();
            }
            if (excludeFilter !== 'source' && $('#source').val()) {
                filters.source = $('#source').val();
            }

            $.ajax({
                url: '<?= base_url('orders/ajax_filter_counts') ?>',
                type: 'POST',
                data: filters,
                dataType: 'json',
                success: function(response) {
                    if (response.status) {
                        let paymentStatusTotal = 0;
                        Object.entries(response.data.paymentStatusCounts).forEach(([status, count]) => {
                            $(`.payment-status-count[data-status="${status}"]`).text(count);
                            paymentStatusTotal += count;
                        });
                        $('.payment-status-count-all').text(paymentStatusTotal);

                        let customerTypeTotal = 0;
                        Object.entries(response.data.customerTypeCounts).forEach(([type, count]) => {
                            $(`.customer-type-count[data-type="${type}"]`).text(count);
                            customerTypeTotal += count;
                        });
                        $('.customer-type-count-all').text(customerTypeTotal);
                    }
                }
            });
        }

        const loadFilterState = () => {
            const savedState = sessionStorage.getItem('orderFilters');
            if (savedState) {
                const filterState = JSON.parse(savedState);

                if (filterState.date_filter) {
                    $('#date_filter').val(filterState.date_filter);
                    $('#date_filter').data('start-date', filterState.date_from);
                    $('#date_filter').data('end-date', filterState.date_to);
                }

                if (filterState.status && filterState.status.length) {
                    statusSelect.setValue(filterState.status);
                } else {
                    statusSelect.setValue([]);
                }
                $('#payment_status').val(filterState.payment_status);
                $('#customer_type').val(filterState.customer_type);
                $('#source').val(filterState.source);

                updateFilterCounts();
                dataTable.ajax.reload();
            } else {
                statusSelect.setValue([]);
                updateFilterCounts();
            }
        };

        const saveFilterState = () => {
            const filterState = {
                date_filter: $('#date_filter').val(),
                date_from: $('#date_filter').data('start-date'),
                date_to: $('#date_filter').data('end-date'),
                status: statusSelect.getValue(),
                payment_status: $('#payment_status').val(),
                customer_type: $('#customer_type').val(),
                source: $('#source').val()
            };
            sessionStorage.setItem('orderFilters', JSON.stringify(filterState));
        };

        loadFilterState();
    });
</script>
