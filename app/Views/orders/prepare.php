<link rel="stylesheet" href="<?= base_url('assets/tom-select/tom-select.bootstrap5.css') ?>">
<script src="<?= base_url('assets/js/jquery-3.5.1.js') ?>"></script>

<style>
    .preparation-progress {
        background: linear-gradient(90deg, #28a745 0%, #28a745 var(--progress, 0%), #e9ecef var(--progress, 0%), #e9ecef 100%);
        height: 8px;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .item-status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .action-buttons .btn {
        margin-right: 0.25rem;
        margin-bottom: 0.25rem;
    }

    .qr-preview {
        max-width: 100px;
        max-height: 100px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
    }

    .keyboard-shortcut {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 1000;
        display: flex;
        flex-direction: column;
        gap: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .keyboard-shortcut kbd {
        background: #fff;
        color: #000;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: monospace;
        font-size: 11px;
        border: 1px solid #ccc;
    }
</style>

<main class="content">
    <div class="container-fluid my-3">
        <div class="mb-3 d-flex justify-content-between flex-wrap align-items-center">
            <h1 class="h3">Chuẩn bị đơn hàng #<?= $order->order_code ?></h1>
            <div class="d-flex gap-2">
                <a href="<?= base_url("orders/{$order->id}") ?>" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-1"></i> Quay lại chi tiết
                </a>
            </div>
        </div>

        <?php if (session()->getFlashdata('message')): ?>
            <div class="alert alert-<?= session()->getFlashdata('alert-type') ?> alert-dismissible fade show" role="alert">
                <?= session()->getFlashdata('message') ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="card mb-3">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-2">Tiến độ chuẩn bị</h5>
                        <div class="preparation-progress mb-2" style="--progress: <?= $progress['progress_percentage'] ?>%"></div>
                        <div class="d-flex justify-content-between text-sm">
                            <span><?= $progress['completed'] ?>/<?= $progress['total'] ?> sản phẩm đã hoàn tất</span>
                            <span><?= $progress['progress_percentage'] ?>%</span>
                        </div>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <?php if ($progress['is_ready']): ?>
                            <?php if (empty($order->tracking_code)): ?>
                                <button type="button" class="btn btn-success btn-lg" id="createShippingBtn">
                                    <i class="bi bi-truck me-2"></i>Tạo Đơn Giao Hàng
                                </button>
                            <?php else: ?>
                                <button type="button" class="btn btn-success btn-lg" id="printTrackingBtn">
                                    <i class="bi bi-truck me-2"></i>In Vận Đơn
                                </button>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="text-muted">
                                <small>Hoàn tất tất cả sản phẩm để tạo đơn giao hàng</small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mb-3">
            <div class="card-header">
                <h5 class="mb-0">Danh sách sản phẩm cần chuẩn bị</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 50px">STT</th>
                                <th>Sản phẩm</th>
                                <th style="width: 150px">Số tài khoản</th>
                                <th style="width: 150px">Loa được gán</th>
                                <th style="width: 200px" class="text-center">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $i = 1;
                            foreach ($items as $item): ?>
                                <tr data-item-id="<?= $item->id ?>">
                                    <td><?= $i++ ?></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="<?= $item->image ?>" alt="" class="me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                            <div>
                                                <div class="fw-medium"><?= esc($item->product_name) ?></div>
                                                <?php if ($item->notes): ?>
                                                    <small class="text-muted"><?= esc($item->notes) ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <img src="https://my.sepay.vn/assets/images/banklogo/<?= $item->icon_path ?>" alt="" class="me-2" style="width: 20px; height: 20px; object-fit: cover;">
                                        <?php if ($item->account_number): ?>
                                            <code><?= esc($item->account_number) ?></code>
                                        <?php else: ?>
                                            <code class="text-muted">N/A</code>
                                        <?php endif; ?>
                                        <a href="javascript:void(0)" class="set-account-btn" data-item-id="<?= $item->id ?>" data-account-number="<?= $item->account_number ?>">
                                            <i class="bi bi-pencil ms-1"></i>
                                        </a>
                                    </td>
                                    <td>
                                        <?php if ($item->serial_number): ?>
                                            <code><?= esc($item->serial_number) ?></code>
                                        <?php else: ?>
                                            <i class="text-danger small">Chưa gán</small>
                                            <?php endif; ?>
                                            <a href="javascript:void(0)" class="assign-speaker-btn" data-item-id="<?= $item->id ?>">
                                                <i class="bi bi-pencil ms-1"></i>
                                            </a>
                                    </td>
                                    <td class="text-center">
                                        <div class="action-buttons">
                                            <?php if ($item->output_device_id && $item->account_number && empty($item->virtual_account_number)): ?>
                                                <button type="button" class="btn btn-sm btn-primary generate-qr-btn" data-item-id="<?= $item->id ?>">
                                                    <i class="bi bi-qr-code me-1"></i> Tạo và in QR
                                                </button>
                                            <?php endif; ?>

                                            <?php if (! empty($item->virtual_account_number)): ?>
                                                <button type="button" class="btn btn-sm btn-success print-qr-btn"
                                                    data-item-id="<?= $item->id ?>"
                                                    data-printed="<?= $item->qrcode_printed ?>">
                                                    <i class="bi bi-printer me-1"></i>
                                                    <?= $item->qrcode_printed ? 'Đã in QR' : 'In QR' ?>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="row g-3">
            <div class="col-md-6">
                <div class="card mb-0">
                    <div class="card-header">
                        <h6 class="mb-0">Thông tin đơn hàng</h6>
                    </div>
                    <div class="card-body">
                        <dl class="row mb-0">
                            <dt class="col-sm-4">Mã đơn:</dt>
                            <dd class="col-sm-8"><?= $order->order_code ?></dd>
                            <dt class="col-sm-4">Khách hàng:</dt>
                            <dd class="col-sm-8"><?= esc($order->customer_name) ?></dd>
                            <dt class="col-sm-4">Điện thoại:</dt>
                            <dd class="col-sm-8"><?= esc($order->customer_phone) ?></dd>
                            <dt class="col-sm-4">Ngày tạo:</dt>
                            <dd class="col-sm-8"><?= date('d/m/Y H:i', strtotime($order->created_at)) ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100 mb-0">
                    <div class="card-header">
                        <h6 class="mb-0">Thống kê chuẩn bị</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-3">
                                <div class="border-end">
                                    <h4 class="text-primary mb-0"><?= $progress['total'] ?></h4>
                                    <small class="text-muted">Tổng SP</small>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="border-end">
                                    <h4 class="text-success mb-0"><?= $progress['completed'] ?></h4>
                                    <small class="text-muted">Hoàn tất</small>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="border-end">
                                    <h4 class="text-warning mb-0"><?= $progress['needs_qr'] ?></h4>
                                    <small class="text-muted">Cần QR</small>
                                </div>
                            </div>
                            <div class="col-3">
                                <h4 class="text-info mb-0"><?= $progress['needs_speaker'] ?></h4>
                                <small class="text-muted">Cần Loa</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<div class="modal fade" id="setAccountModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm số tài khoản</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="setAccountForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Số tài khoản</label>
                        <input type="text" class="form-control" name="account_number" placeholder="Nhập số tài khoản để tạo QR thanh toán">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">Lưu</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View QR Modal -->
<div class="modal fade" id="viewQRModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xem mã QR</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="qrImage" src="" alt="QR Code" class="img-fluid">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="downloadQR">Tải xuống</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="assignSpeakerModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Gán loa</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="assignSpeakerForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Quét mã QR hoặc nhập mã loa <span class="text-danger">*</span></label>
                        <input type="text"
                            class="form-control form-control-lg text-center"
                            name="speaker_id"
                            id="speakerIdInput"
                            autocomplete="off"
                            placeholder="Đặt con trỏ vào đây và quét mã QR">
                        <div class="form-text text-center mt-2">
                            <i class="bi bi-upc-scan"></i> Quét mã QR trên loa để tự động điền
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-check me-1"></i> Gán
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="keyboard-shortcut">
    <div>
        <span>Gán loa nhanh:</span>
        <kbd>S</kbd>
    </div>
    <div>
        <span>Tạo QR nhanh:</span>
        <kbd>T</kbd>
    </div>
    <div>
        <span>In QR nhanh:</span>
        <kbd>I</kbd>
    </div>
</div>

<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php') ?>

<script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
<script src="<?= base_url('assets/notyf/notyf.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery.validate.min.js') ?>"></script>
<script src="<?= base_url('assets/tom-select/tom-select.complete.min.js') ?>"></script>
<script src="<?= base_url('assets/js/app.js?v=1') ?>"></script>

<script>
    $(document).ready(function() {
        let currentItemId = null;

        const setAccountModal = new bootstrap.Modal(document.getElementById('setAccountModal'));
        const viewQRModal = new bootstrap.Modal(document.getElementById('viewQRModal'));
        const assignSpeakerModal = new bootstrap.Modal(document.getElementById('assignSpeakerModal'));

        const speakerIdInput = document.getElementById('speakerIdInput');
        const assignSpeakerForm = document.getElementById('assignSpeakerForm');

        function convertToAsciiString(numberString) {
            let result = '';
            let i = 0;

            while (i < numberString.length) {
                if (i + 2 < numberString.length) {
                    let threeDigit = parseInt(numberString.substring(i, i + 3));
                    if (threeDigit >= 65 && threeDigit <= 122) { // A-Z (65-90) và a-z (97-122)
                        result += String.fromCharCode(threeDigit);
                        i += 3;
                        continue;
                    }
                }

                if (i + 1 < numberString.length) {
                    let twoDigit = parseInt(numberString.substring(i, i + 2));
                    if (twoDigit >= 65 && twoDigit <= 99) {
                        result += String.fromCharCode(twoDigit);
                        i += 2;
                        continue;
                    }
                }

                result += numberString[i];
                i++;
            }

            return result;
        }

        document.getElementById('assignSpeakerModal').addEventListener('shown.bs.modal', function() {
            speakerIdInput.focus();
        });

        document.getElementById('assignSpeakerModal').addEventListener('hidden.bs.modal', function() {
            assignSpeakerForm.reset();
            currentItemId = null;
        });

        $('.assign-speaker-btn').click(function() {
            currentItemId = $(this).data('item-id');
            assignSpeakerModal.show();
        });

        $('#assignSpeakerForm').submit(function(e) {
            e.preventDefault();
            const speakerId = speakerIdInput.value.trim();

            const convertedSpeakerId = /^\d+$/.test(speakerId) ? convertToAsciiString(speakerId) : speakerId;

            speakerIdInput.value = convertedSpeakerId;

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="spinner-border spinner-border-sm me-1 align-middle"></i> Đang xử lý...';

            $.ajax({
                url: `<?= base_url("orders/assignSpeaker/{$order->id}") ?>/${currentItemId}`,
                method: 'POST',
                data: {
                    serial_number: convertedSpeakerId,
                    <?= csrf_token() ?>: '<?= csrf_hash() ?>'
                },
                success: function(response) {
                    if (response.status) {
                        assignSpeakerModal.hide();
                        location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra khi gán loa');;
                },
                complete: function() {
                    speakerIdInput.select();
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalBtnText;
                    speakerIdInput.focus()
                }
            });
        });

        $('.set-account-btn').click(function() {
            currentItemId = $(this).data('item-id');
            const accountNumber = $(this).data('account-number');
            $('input[name="account_number"]').val(accountNumber);
            setAccountModal.show();
        });

        $('#setAccountForm').submit(function(e) {
            e.preventDefault();

            const accountNumber = $(this).find('input[name="account_number"]').val();

            $.ajax({
                url: `<?= base_url("orders/setAccountNumber/{$order->id}") ?>/${currentItemId}`,
                method: 'POST',
                data: {
                    account_number: accountNumber,
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                },
                success: function(response) {
                    if (response.status) {
                        notyf.success(response.message);
                        setAccountModal.hide();
                        location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra khi cập nhật số tài khoản');
                }
            });
        });

        function openPrintQRWindow(data) {
            const bankName = data.bank_name;
            const accountNumber = data.account_number;
            const virtualAccount = data.virtual_account_number;
            const description = data.description;
            const serialNumber = data.serial_number;
            const isAbbank = bankName.toLowerCase().includes('abbank');
            const trackingNumber = '<?= esc($order->tracking_code) ?>';

            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>In mã QR</title>
                        <style>
                            @page {
                                size: A4;
                                margin: 0;
                            }
                            body {
                                font-family: Arial, sans-serif;
                                margin: 0;
                                padding: 0;
                                width: 210mm;
                                height: 297mm;
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                                position: relative;
                            }
                            .qr-container {
                                text-align: center;
                                width: 80mm;
                                height: 134mm;
                                position: absolute;
                                border: 1mm dashed #000;
                                left: 50%;
                                transform: translateX(-50%);
                                box-sizing: border-box;
                                margin-top: 10mm;
                            }
                            .qr-info {
                                position: absolute;
                                top: 5mm;
                                left: 0;
                                width: 100%;
                                text-align: center;
                                font-size: 8pt;
                                line-height: 1.4;
                            }
                            .qr-info p {
                                margin: 2mm 0;
                                padding: 0 3mm;
                            }
                            .qr-code {
                                width: 55mm;
                                height: 55mm;
                                position: absolute;
                                left: 50%;
                                bottom: ${isAbbank ? '17mm' : '27mm'};
                                transform: translateX(-50%);
                                object-fit: contain;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="qr-container">
                            <div class="qr-info">
                                ${bankName ? `<p>Ngân hàng: ${bankName}</p>` : ''}
                                ${accountNumber ? `<p>Số tài khoản: ${accountNumber}</p>` : ''}
                                ${virtualAccount ? `<p>Số tài khoản ảo: ${virtualAccount}</p>` : ''}
                                ${description ? `<p>Nội dung: ${description}</p>` : ''}
                                ${serialNumber ? `<p>Mã loa: ${serialNumber}</p>` : ''}
                                ${trackingNumber ? `<p>Mã vận đơn: ${trackingNumber}</p>` : ''}
                            </div>
                            <img src="${data.qr_code}" alt="QR Code" class="qr-code">
                        </div>
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.focus();
            setTimeout(() => {
                printWindow.print();
            }, 500);
        }

        $('.generate-qr-btn').click(function() {
            const itemId = $(this).data('item-id');
            const btn = $(this);

            btn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm align-middle me-1"></i> Đang tạo...');

            $.ajax({
                url: `<?= base_url("orders/generateQR/{$order->id}") ?>/${itemId}`,
                method: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                },
                success: function(response) {
                    if (response.status) {
                        openPrintQRWindow(response.data);
                    } else {
                        notyf.error(response.message);
                    }
                    btn.prop('disabled', false).html('<i class="bi bi-qr-code me-1"></i> Tạo và in QR');
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra khi tạo mã QR');
                    btn.prop('disabled', false).html('<i class="bi bi-qr-code me-1"></i> Tạo và in QR');
                }
            });
        });

        $('.print-qr-btn').on('click', function() {
            const btn = $(this);
            if (btn.prop('disabled')) return;

            const itemId = btn.data('item-id');
            btn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm align-middle me-1"></i> Đang tải...');

            $.ajax({
                url: `<?= base_url("orders/get_qr") ?>/${itemId}`,
                method: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                },
                success: function(response) {
                    if (response.status) {
                        openPrintQRWindow(response.data);
                        btn.html('<i class="bi bi-printer"></i> Đã in QR');
                    } else {
                        notyf.error(response.message);
                        btn.prop('disabled', false).html('<i class="bi bi-printer"></i> In QR');
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra khi tải mã QR');
                    btn.prop('disabled', false).html('<i class="bi bi-printer"></i> In QR');
                }
            });
        });

        $('#printTrackingBtn').click(async () => {
            const res = await fetch("<?= base_url(sprintf('orders/print_tracking_order/%s', $order->id)) ?>", {
                method: 'GET',
            });

            const blob = await res.blob();
            const url = URL.createObjectURL(blob);
            window.open(url);
        })

        $('#createShippingBtn').click(function() {
            if (!confirm('Bạn có chắc chắn muốn tạo đơn giao hàng? Hành động này không thể hoàn tác.')) {
                return;
            }

            const btn = $(this);
            btn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-2"></i>Đang tạo đơn giao hàng...');

            $.ajax({
                url: `<?= base_url("orders/create_shipping_order/{$order->id}") ?>`,
                method: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                },
                success: function(response) {
                    if (response.status) {
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                        btn.prop('disabled', false).html('<i class="bi bi-truck me-2"></i>Tạo Đơn Giao Hàng');
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra khi tạo đơn giao hàng');
                    btn.prop('disabled', false).html('<i class="bi bi-truck me-2"></i>Tạo Đơn Giao Hàng');
                }
            });
        });

        $(document).on('keydown', function(e) {
            if (e.key.toLowerCase() === 's') {
                e.preventDefault();

                const unassignedRow = $('tr[data-item-id]').filter(function() {
                    return !$(this).find('td:eq(3) code').length;
                }).first();

                if (unassignedRow.length) {
                    const itemId = unassignedRow.data('item-id');
                    currentItemId = itemId;
                    assignSpeakerModal.show();

                    unassignedRow.addClass('table-warning');
                    setTimeout(() => {
                        unassignedRow.removeClass('table-warning');
                    }, 2000);
                } else {
                    notyf.error('Không còn sản phẩm nào cần gán loa');
                }
            }

            if (e.key.toLowerCase() === 't') {
                e.preventDefault();

                const generateBtn = $('.generate-qr-btn').filter(function() {
                    return !$(this).data('clicked');
                }).first();

                if (generateBtn.length) {
                    generateBtn.data('clicked', true);

                    const row = generateBtn.closest('tr');
                    row.addClass('table-warning');

                    setTimeout(() => {
                        generateBtn.trigger('click');
                        generateBtn.hide();
                        setTimeout(() => {
                            row.removeClass('table-warning');
                        }, 2000);
                    }, 500);
                } else {
                    notyf.error('Không còn sản phẩm nào cần tạo QR');
                    $('.generate-qr-btn').removeData('clicked');
                }
            }

            if (e.key.toLowerCase() === 'i') {
                e.preventDefault();

                const printBtn = $('.print-qr-btn').filter(function() {
                    return !$(this).data('clicked');
                }).first();

                if (printBtn.length) {
                    printBtn.data('clicked', true);

                    const row = printBtn.closest('tr');
                    row.addClass('table-warning');

                    setTimeout(() => {
                        printBtn.trigger('click');
                        setTimeout(() => {
                            row.removeClass('table-warning');
                        }, 2000);
                    }, 500);
                } else {
                    notyf.error('Không còn mã QR nào cần in');
                    $('.print-qr-btn').removeData('clicked');
                }
            }
        });

        setTimeout(() => {
            $('.keyboard-shortcut').fadeOut();
        }, 10000);
    });
</script>