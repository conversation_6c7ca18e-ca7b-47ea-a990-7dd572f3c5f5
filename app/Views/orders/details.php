<link rel="stylesheet" href="<?= base_url('assets/tom-select/tom-select.bootstrap5.css') ?>">
<script src="<?= base_url('assets/js/jquery-3.5.1.js') ?>"></script>

<style>
    .ts-control .item {
        flex-direction: column;
        align-items: flex-start;
    }
</style>

<main class="content">
    <div class="container-fluid my-3">
        <div class="mb-3 d-flex justify-content-between flex-wrap align-items-center">
            <h1 class="h3">Chi tiết đơn hàng #<?= $order->order_code ?></h1>
            <div class="d-flex gap-2">
                <a href="<?= base_url("orders/prepare/{$order->id}") ?>" class="btn btn-warning">
                    <i class="bi bi-gear me-1"></i>
                    Chuẩn bị đơn hàng
                </a>

                <a href="<?= base_url('orders') ?>" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-1"></i> Quay lại
                </a>
            </div>
        </div>

        <?php if (session()->getFlashdata('message')): ?>
            <div class="alert alert-<?= session()->getFlashdata('alert-type') ?> alert-dismissible fade show" role="alert">
                <?= session()->getFlashdata('message') ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>


        <?php
        $unassignedCount = 0;
        foreach ($decals as $decal) {
            if (! $decal->output_device_id) {
                $unassignedCount++;
            }
        }
        ?>

        <?php if ($unassignedCount > 0): ?>
            <div class="alert alert-warning">
                <div class="alert-message d-flex align-items-start">
                    <i class="bi bi-exclamation-triangle-fill me-2 mt-1"></i>
                    <div>
                        <strong>Cảnh báo: Có <?= $unassignedCount ?> loa chưa được gán</strong>
                        <br>
                        <small>Cần gán loa cho tất cả sản phẩm trước khi có thể chuyển trạng thái đơn hàng sang "Đang giao hàng".</small>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <div class="row g-3">
            <div class="col-12 col-xl-4 order-md-1">
                <div class="card mb-3">
                    <div class="card-header pb-0">
                        <h5 class="card-title mb-0">Thông tin đơn hàng</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Mã đơn hàng:</strong> <?= $order->order_code ?>
                            <a href="#" class="ms-1" data-copy-btn data-copy-text="<?= $order->order_code ?>" title="Sao chép">
                                <i class="bi bi-clipboard"></i>
                            </a>
                        </div>
                        <div class="mb-3">
                            <strong>Ngày đặt:</strong> <?= date('d/m/Y H:i:s', strtotime($order->created_at)) ?>
                        </div>
                        <div class="mb-3">
                            <strong>Trạng thái:</strong>
                            <?= \App\Enums\OrderStatus::toHtml($order->status) ?>
                        </div>
                        <div class="mb-3">
                            <strong>Trạng thái thanh toán:</strong>
                            <?= get_order_payment_status_badge($order->payment_status) ?>
                        </div>
                        <div>
                            <strong>Phương thức thanh toán:</strong>
                            <?php
                            switch ($order->payment_method) {
                                case 'cod':
                                    echo 'Thanh toán khi nhận hàng (COD)';
                                    break;
                                case 'bank_transfer':
                                    echo 'Chuyển khoản ngân hàng';
                                    break;
                                default:
                                    echo 'Không xác định';
                                    break;
                            }
                            ?>
                        </div>
                        <?php if ($order->notes): ?>
                            <div class="mt-3">
                                <strong>Ghi chú:</strong>
                                <span class="text-muted"><?= nl2br(esc($order->notes)) ?></span>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($order->shipping_company) || !empty($order->tracking_code)): ?>
                            <div class="position-relative mt-3 border-top pt-3">
                                <h5 class="fw-bold mb-3">Thông tin vận chuyển</h5>
                                <button type="button" class="btn btn-link position-absolute" data-bs-toggle="modal" data-bs-target="#editShippingModal" title="Chỉnh sửa thông tin vận chuyển" style="top: 0.5rem; right: -0.75em;">
                                    <i class="bi bi-pencil"></i>
                                </button>

                                <?php if (!empty($order->shipping_company)): ?>
                                    <div class="mb-2">
                                        <strong>Đơn vị vận chuyển:</strong> <?= esc($order->shipping_company) ?>
                                    </div>
                                <?php endif; ?>

                                <?php if (!empty($order->tracking_code)): ?>
                                    <div>
                                        <strong>Mã vận đơn:</strong> <?= esc($order->tracking_code) ?>
                                    </div>
                                    
                                    <a id="print-tracking-order" class="btn btn-primary mt-2 d-inline-flex rounded align-items-center gap-2">
                                        <div class="spinner-border spinner-border-sm me-1 align-middle" role="status" style="display: none;"></div>
                                        <i class="bi bi-printer me-1"></i> In đơn vận chuyển <span class="badge bg-light text-dark">Nhấn P</span>
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <div class="position-relative mt-3 border-top pt-3">
                                <h5 class="fw-bold mb-3">Thông tin vận chuyển</h5>
                                <button type="button" class="btn btn-link position-absolute" data-bs-toggle="modal" data-bs-target="#editShippingModal" title="Chỉnh sửa thông tin vận chuyển" style="top: 0.5rem; right: -0.75em;">
                                    <i class="bi bi-pencil"></i>
                                </button>

                                <?php if ($order->status !== 'Shipping' && $order->status !== 'Completed' && $order->status !== 'Cancelled'): ?>
                                    <?php if ($unassignedCount === 0): ?>
                                        <button type="button" class="btn btn-sm btn-primary create-ghtk-order-btn" data-order-id="<?= $order->id ?>">
                                            <i class="bi bi-truck me-1"></i> Tạo đơn GHTK
                                        </button>
                                    <?php else: ?>
                                        <div class="text-muted fst-italic">Cần gán loa cho tất cả sản phẩm trước khi có thể tạo đơn hàng GHTK</div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card mb-3">
                    <div class="card-header pb-0">
                        <h5 class="card-title mb-0">Thông tin khách hàng</h5>
                        <button type="button" class="btn btn-link position-absolute" data-bs-toggle="modal" data-bs-target="#editCustomerModal" title="Chỉnh sửa thông tin khách hàng" style="top: 0.5rem; right: 0.5rem;">
                            <i class="bi bi-pencil"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Loại khách hàng:</strong>
                            <?= $order->customer_type == 'wholesale' ?
                                '<span class="badge bg-primary">Khách sỉ</span>' :
                                '<span class="badge bg-secondary">Khách lẻ</span>' ?>
                        </div>
                        <div class="mb-3">
                            <strong>Họ tên:</strong> <?= esc($order->customer_name) ?> <a href="#" class="ms-1" data-copy-btn data-copy-text="<?= esc($order->customer_name) ?>" title="Sao chép">
                                <i class="bi bi-clipboard"></i>
                            </a>
                        </div>
                        <div>
                            <strong>Số điện thoại:</strong>
                            <a href="tel:<?= esc($order->customer_phone) ?>"><?= esc($order->customer_phone) ?></a>
                            <a href="#" class="ms-1" data-copy-btn data-copy-text="<?= esc($order->customer_phone) ?>" title="Sao chép">
                                <i class="bi bi-clipboard"></i>
                            </a>
                        </div>
                        <?php
                        $fullAddress = $order->address . ', ' . $order->ward . ', ' . $order->district . ', ' . $order->province;
                        ?>
                        <div class="mt-3">
                            <strong>Địa chỉ:</strong>
                            <a href="<?= $mapUrl ?>" target="_blank"><?= esc($fullAddress) ?></a>
                            <a href="#" class="ms-1" data-copy-btn data-copy-text="<?= esc($fullAddress) ?>" title="Sao chép">
                                <i class="bi bi-clipboard"></i>
                            </a>
                        </div>
                        <?php if ($order->customer_email): ?>
                            <div class="mt-3">
                                <strong>Email:</strong>
                                <a href="mailto:<?= esc($order->customer_email) ?>"><?= esc($order->customer_email) ?></a>
                                <a href="#" class="ms-1" data-copy-btn data-copy-text="<?= esc($order->customer_email) ?>" title="Sao chép">
                                    <i class="bi bi-clipboard"></i>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card mb-0">
                    <div class="card-header pb-0">
                        <h5 class="card-title mb-0">Thông tin nguồn đơn hàng</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($tracking): ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <dl class="row mb-0">
                                        <dt class="col-sm-4">IP:</dt>
                                        <dd class="col-sm-8"><?= esc($tracking->ip_address) ?></dd>
                                        <dt class="col-sm-4">Trình duyệt:</dt>
                                        <dd class="col-sm-8"><?= esc($tracking->user_agent) ?></dd>
                                        <dt class="col-sm-4">Trang giới thiệu:</dt>
                                        <dd class="col-sm-8"><?= esc($tracking->referrer) ?: 'Truy cập trực tiếp' ?></dd>
                                        <dt class="col-sm-4">Thời gian:</dt>
                                        <dd class="col-sm-8"><?= date('d/m/Y H:i:s', strtotime($tracking->created_at)) ?></dd>
                                    </dl>
                                </div>
                                <div class="col-md-6">
                                    <dl class="row mb-0">
                                        <dt class="col-sm-4">Source:</dt>
                                        <dd class="col-sm-8"><?= esc($tracking->utm_source) ?: 'Không có' ?></dd>
                                        <dt class="col-sm-4">Medium:</dt>
                                        <dd class="col-sm-8"><?= esc($tracking->utm_medium) ?: 'Không có' ?></dd>
                                        <dt class="col-sm-4">Campaign:</dt>
                                        <dd class="col-sm-8"><?= esc($tracking->utm_campaign) ?: 'Không có' ?></dd>
                                        <dt class="col-sm-4">Term:</dt>
                                        <dd class="col-sm-8"><?= esc($tracking->utm_term) ?: 'Không có' ?></dd>
                                        <dt class="col-sm-4">Content:</dt>
                                        <dd class="col-sm-8"><?= esc($tracking->utm_content) ?: 'Không có' ?></dd>
                                    </dl>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div <?php if ($tracking) : ?> class="mt-3 border-top pt-3" <?php endif; ?>>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="fw-bold mb-0">Người giới thiệu</h6>
                                <button type="button" class="btn btn-link btn-sm p-0" data-bs-toggle="modal" data-bs-target="#editPartnerModal" title="Chỉnh sửa người giới thiệu">
                                    <i class="bi bi-pencil"></i>
                                </button>
                            </div>
                            <?php if ($partner): ?>
                                <div class="d-flex align-items-center justify-content-between">
                                    <div>
                                        <div class="fw-medium"><?= esc($partner->name) ?></div>
                                        <div class="text-muted small">
                                            #<?= $partner->id ?> - <?= esc($partner->email) ?>
                                        </div>
                                    </div>
                                    <div>
                                        <a href="<?= base_url('partner/login_as_partner/' . $partner->id) ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                            <i class="bi bi-box-arrow-in-right me-1"></i>Đăng nhập
                                        </a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="text-muted fst-italic">Chưa có thông tin người giới thiệu</div>
                            <?php endif; ?>
                        </div>
                        <div class="mt-3">
                            <strong>Mã cán bộ bán</strong>
                            <?php if ($order->bank_referral_code): ?>
                                <div>
                                    <?= esc($order->bank_referral_code) ?>
                                </div>
                            <?php else: ?>
                                <div class="text-muted fst-italic">Không có</div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12 col-xl-8 order-md-0">
                <div class="card mb-3">
                    <div class="card-header pb-0">
                        <h5 class="card-title mb-0">Chi tiết đơn hàng</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table mb-0">
                                <thead>
                                    <tr>
                                        <th style="width: 50px">STT</th>
                                        <th>Sản phẩm</th>
                                        <th style="width: 90px" class="text-center">Số lượng</th>
                                        <th style="width: 120px" class="text-end">Đơn giá</th>
                                        <th style="width: 120px" class="text-end">Thành tiền</th>
                                        <th style="width: 80px" class="text-center">Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $i = 1;
                                    foreach ($items as $item): ?>
                                        <tr>
                                            <td><?= $i++ ?></td>
                                            <td>
                                                <?php if (! empty($item->image)): ?>
                                                    <img src="<?= $item->image ?>" alt="<?= esc($item->name) ?>" class="ms-2" width="50" height="50">
                                                <?php endif; ?>
                                                <?= esc($item->name) ?>
                                            </td>
                                            <td class="text-center"><?= $item->quantity ?></td>
                                            <td class="text-end"><?= format_currency($item->price) ?></td>
                                            <td class="text-end"><?= format_currency($item->total_price) ?></td>
                                            <td class="text-center">
                                                <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editQuantityModal" data-item-id="<?= $item->id ?>" data-item-name="<?= esc($item->name) ?>" data-item-quantity="<?= $item->quantity ?>" data-item-price="<?= $item->price ?>" title="Sửa số lượng">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="5" class="text-end"><strong>Tổng tiền sản phẩm:</strong></td>
                                        <td class="text-end"><?= format_currency($order->subtotal) ?></td>
                                    </tr>
                                    <tr>
                                        <td colspan="5" class="text-end">
                                            <strong>Giảm giá:</strong>
                                            <a class="ms-1" data-bs-toggle="modal" data-bs-target="#editDiscountModal" title="Chỉnh sửa giảm giá">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                        </td>
                                        <td class="text-end">
                                            <?= $order->discount_amount > 0 ? format_currency($order->discount_amount) : '0đ' ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="5" class="text-end">
                                            <strong>Phí vận chuyển:</strong>
                                            <a class="ms-1" data-bs-toggle="modal" data-bs-target="#editShippingFeeModal" title="Chỉnh sửa phí vận chuyển">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                        </td>
                                        <td class="text-end">
                                            <?= $order->shipping_fee > 0 ? format_currency($order->shipping_fee) : 'Miễn phí' ?>
                                        </td>
                                    </tr>
                                    <tr class="table-primary">
                                        <td colspan="5" class="text-end"><strong>Tổng thanh toán:</strong></td>
                                        <td class="text-end fs-5 fw-bold"><?= format_currency($order->total_amount) ?></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="card mb-3">
                    <div class="card-header pb-0">
                        <h5 class="card-title mb-0">Cập nhật trạng thái</h5>
                    </div>
                    <div class="card-body">
                        <?= form_open(base_url('orders/update_status/' . $order->id), ['id' => 'updateStatusForm']) ?>
                        <div class="row row-cols-1 row-cols-md-2">
                            <div class="col mb-3">
                                <label for="status" class="form-label">Trạng thái đơn hàng</label>
                                <select class="form-select" id="status" name="status">
                                    <?php foreach (\App\Enums\OrderStatus::getLabels() as $key => $value): ?>
                                        <option value="<?= $key ?>" <?= $order->status == $key ? 'selected' : '' ?>><?= \App\Enums\OrderStatus::getLabel($key) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php if (in_array($admin_details->role, ['Admin', 'SuperAdmin'])): ?>
                                <div class="col mb-3">
                                    <label for="payment_status" class="form-label">Trạng thái thanh toán</label>
                                    <select class="form-select" id="payment_status" name="payment_status">
                                        <option value="Unpaid" <?= $order->payment_status == 'Unpaid' ? 'selected' : '' ?>>Chưa thanh toán</option>
                                        <option value="Paid" <?= $order->payment_status == 'Paid' ? 'selected' : '' ?>>Đã thanh toán</option>
                                        <option value="Refunded" <?= $order->payment_status == 'Refunded' ? 'selected' : '' ?>>Đã hoàn tiền</option>
                                    </select>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div id="shippingFields" class="row" style="display: none;">
                            <div class="col-md-6 mb-3">
                                <label for="shipping_company" class="form-label">Đơn vị vận chuyển</label>
                                <select class="form-select" id="shipping_company" name="shipping_company">
                                    <option value="">-- Chọn đơn vị vận chuyển --</option>
                                    <?php foreach ($shippingCompanies as $key => $value): ?>
                                        <option value="<?= $key ?>" <?= $order->shipping_company == $key ? 'selected' : '' ?>><?= esc($value) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="tracking_code" class="form-label">Mã vận đơn</label>
                                <input type="text" class="form-control" id="tracking_code" name="tracking_code" value="<?= esc($order->tracking_code ?? '') ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="status_note" class="form-label">Ghi chú cập nhật</label>
                            <textarea class="form-control" id="status_note" name="status_note" rows="3" placeholder="Ghi chú thêm về việc cập nhật trạng thái (không bắt buộc)"></textarea>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary" disabled>
                                Cập nhật trạng thái
                            </button>
                        </div>
                        <?= form_close() ?>
                    </div>
                </div>

                <div class="tab">
                    <ul class="nav nav-tabs" id="orderDetailsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="payments-tab" data-bs-toggle="tab" data-bs-target="#payments-content" type="button" role="tab" aria-controls="payments-content" aria-selected="true">
                                Thông tin thanh toán
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history-content" type="button" role="tab" aria-controls="history-content" aria-selected="false">
                                Lịch sử đơn hàng
                            </button>
                        </li>
                        <?php if ($invoice): ?>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="invoice-tab" data-bs-toggle="tab" data-bs-target="#invoice-content" type="button" role="tab" aria-controls="invoice-content" aria-selected="false">
                                    Hóa đơn
                                </button>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="decals-tab" data-bs-toggle="tab" data-bs-target="#decals-content" type="button" role="tab" aria-controls="decals-content" aria-selected="false">
                                Danh sách loa
                                <?php
                                if (! empty($decals)) {
                                    $unassignedCount = 0;
                                    foreach ($decals as $decal) {
                                        if (!$decal->output_device_id) {
                                            $unassignedCount++;
                                        }
                                    }
                                    if ($unassignedCount > 0) {
                                        echo '<span class="badge bg-danger ms-2">' . $unassignedCount . ' chưa gán</span>';
                                    }
                                }
                                ?>
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content" id="orderDetailsTabsContent">
                        <div class="tab-pane fade show active" id="payments-content" role="tabpanel" aria-labelledby="payments-tab">
                            <div class="d-flex justify-content-end mb-3">
                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
                                    <i class="bi bi-plus-circle me-1"></i> Thêm thanh toán
                                </button>
                            </div>

                            <?php if (! empty($transactions)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Ngày GD</th>
                                                <th>Phương thức</th>
                                                <th>Mã GD</th>
                                                <th class="text-end">Số tiền</th>
                                                <th width="150">Ghi chú</th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($transactions as $transaction): ?>
                                                <tr>
                                                    <td>
                                                        <?= ! empty($transaction->transaction_date)
                                                            ? date('d/m/Y H:i:s', strtotime($transaction->transaction_date))
                                                            : date('d/m/Y H:i:s', strtotime($transaction->created_at)) ?>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        switch ($transaction->payment_method) {
                                                            case 'bank_transfer':
                                                                echo '<span class="badge bg-info">Chuyển khoản</span>';
                                                                break;
                                                            case 'cod':
                                                                echo '<span class="badge bg-warning">Tiền mặt (COD)</span>';
                                                                break;
                                                            default:
                                                                echo '<span class="badge bg-secondary">' . esc($transaction->payment_method) . '</span>';
                                                        }
                                                        ?>
                                                    </td>
                                                    <td>
                                                        <?= !empty($transaction->transaction_id)
                                                            ? esc($transaction->transaction_id)
                                                            : '<span class="text-muted fst-italic">Không có</span>' ?>
                                                    </td>
                                                    <td class="text-end fw-bold">
                                                        <?= format_currency($transaction->amount) ?>
                                                    </td>
                                                    <td class="text-truncate" style="max-width: 150px;" data-bs-toggle="tooltip" title="<?= esc($transaction->description) ?>">
                                                        <?= $transaction->description ?>
                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editPaymentModal" data-id="<?= $transaction->id ?>">
                                                            <i class="bi bi-pencil"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-danger" onclick="deletePayment(<?= $transaction->id ?>)">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php elseif ($order->payment_status == 'Unpaid'): ?>
                                <div class="text-center p-3">
                                    <div class="text-muted mb-3">Chưa có thông tin thanh toán nào được ghi nhận</div>

                                    <?php if ($order->payment_status == 'Unpaid'): ?>
                                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
                                            <i class="bi bi-plus-circle me-1"></i> Thêm thanh toán
                                        </button>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <?php if ($order->payment_status == 'Paid'): ?>
                                <div class="alert alert-success mt-3 mb-0">
                                    <div class="alert-message d-flex align-items-center">
                                        <i class="bi bi-check-circle-fill me-2 fs-5"></i>
                                        <div>
                                            <strong>Đơn hàng đã được thanh toán đầy đủ</strong>
                                            <div class="small">Tổng thanh toán: <?= format_currency($order->total_amount) ?></div>
                                        </div>
                                    </div>
                                </div>
                            <?php elseif ($order->payment_status == 'Unpaid'): ?>
                                <div class="alert alert-warning mt-3 mb-0">
                                    <div class="alert-message d-flex align-items-center">
                                        <i class="bi bi-exclamation-triangle-fill me-2 fs-5"></i>
                                        <div>
                                            <strong>Đơn hàng chưa được thanh toán</strong>
                                            <div class="small">Cần thanh toán: <?= format_currency($order->total_amount) ?></div>
                                        </div>
                                    </div>
                                </div>
                            <?php elseif ($order->payment_status == 'Refunded'): ?>
                                <div class="alert alert-info mt-3 mb-0">
                                    <div class="alert-message d-flex align-items-center">
                                        <i class="bi bi-arrow-return-left me-2 fs-5"></i>
                                        <div>
                                            <strong>Đơn hàng đã được hoàn tiền</strong>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="tab-pane fade" id="history-content" role="tabpanel" aria-labelledby="history-tab">
                            <?php if (!empty($histories)): ?>
                                <ul class="timeline">
                                    <?php foreach ($histories as $history): ?>
                                        <li class="timeline-item">
                                            <div class="timeline-marker"></div>
                                            <div class="timeline-content">
                                                <div class="d-flex justify-content-between align-items-start mb-1">
                                                    <div>
                                                        <?= \App\Enums\OrderStatus::toHtml($history->status) ?>
                                                        <?= get_order_payment_status_badge($history->payment_status) ?>
                                                    </div>
                                                    <span class="text-muted text-sm text-end"><?= date('d/m/Y H:i:s', strtotime($history->created_at)) ?></span>
                                                </div>

                                                <?php if (!empty($history->note)): ?>
                                                    <p class="timeline-text mb-0"><?= nl2br(esc($history->note)) ?></p>
                                                <?php else: ?>
                                                    <p class="timeline-text text-muted fst-italic mb-0">Không có ghi chú</p>
                                                <?php endif; ?>

                                                <div class="text-muted small mb-3 mt-2">
                                                    <?php if ($history->admin_name): ?>
                                                        <i class="bi bi-person me-1"></i><?= esc($history->admin_name) ?>
                                                    <?php else: ?>
                                                        <i class="bi bi-robot me-1"></i> Hệ thống
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php else: ?>
                                <div class="text-center p-4">
                                    <div class="text-muted">Chưa có lịch sử đơn hàng nào được ghi nhận</div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <?php if ($invoice): ?>
                            <?php include(APPPATH . 'Views/orders/includes/invoice-tab.php') ?>
                        <?php endif; ?>

                        <?php include(APPPATH . 'Views/orders/includes/decals-tab.php') ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php') ?>

<div class="modal fade" id="editShippingModal" tabindex="-1" aria-labelledby="editShippingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editShippingModalLabel">Chỉnh sửa thông tin vận chuyển</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('orders/update_shipping/' . $order->id), ['id' => 'updateShippingForm']) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="modal_shipping_company" class="form-label">Đơn vị vận chuyển</label>
                    <select class="form-select" id="modal_shipping_company" name="shipping_company">
                        <option value="">-- Chọn đơn vị vận chuyển --</option>
                        <?php foreach ($shippingCompanies as $key => $value): ?>
                            <option value="<?= $key ?>" <?= $order->shipping_company == $key ? 'selected' : '' ?>><?= esc($value) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="modal_tracking_code" class="form-label">Mã vận đơn</label>
                    <input type="text" class="form-control" id="modal_tracking_code" name="tracking_code" value="<?= esc($order->tracking_code ?? '') ?>">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary" id="saveShippingButton">Lưu thay đổi</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<div class="modal fade" id="editCustomerModal" tabindex="-1" aria-labelledby="editCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCustomerModalLabel">Chỉnh sửa thông tin khách hàng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('orders/update_customer/' . $order->id), ['id' => 'updateCustomerForm']) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="customer_type" class="form-label">Loại khách hàng</label>
                    <select class="form-select" id="customer_type" name="customer_type">
                        <option value="retail" <?= $order->customer_type == 'retail' ? 'selected' : '' ?>>Khách lẻ</option>
                        <option value="wholesale" <?= $order->customer_type == 'wholesale' ? 'selected' : '' ?>>Khách sỉ</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="customer_name" class="form-label">Họ tên</label>
                    <input type="text" class="form-control" id="customer_name" name="customer_name" value="<?= esc($order->customer_name) ?>">
                </div>
                <div class="mb-3">
                    <label for="customer_email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="customer_email" name="customer_email" value="<?= esc($order->customer_email) ?>">
                </div>
                <div class="mb-3">
                    <label for="customer_phone" class="form-label">Số điện thoại</label>
                    <input type="text" class="form-control" id="customer_phone" name="customer_phone" value="<?= esc($order->customer_phone) ?>">
                </div>
                <div class="mb-3">
                    <label for="address" class="form-label">Địa chỉ</label>
                    <input type="text" class="form-control" id="address" name="address" value="<?= esc($order->address) ?>">
                </div>
                <div class="mb-3">
                    <label for="ward" class="form-label">Phường/Xã</label>
                    <input type="text" class="form-control" id="ward" name="ward" value="<?= esc($order->ward) ?>">
                </div>
                <div class="mb-3">
                    <label for="district" class="form-label">Quận/Huyện</label>
                    <input type="text" class="form-control" id="district" name="district" value="<?= esc($order->district) ?>">
                </div>
                <div class="mb-3">
                    <label for="province" class="form-label">Tỉnh/Thành phố</label>
                    <input type="text" class="form-control" id="province" name="province" value="<?= esc($order->province) ?>">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary">Lưu thông tin</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<div class="modal fade" id="editShippingFeeModal" tabindex="-1" aria-labelledby="editShippingFeeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editShippingFeeModalLabel">Chỉnh sửa phí vận chuyển</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('orders/update_shipping_fee/' . $order->id), ['id' => 'updateShippingFeeForm']) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="shipping_fee" class="form-label">Phí vận chuyển</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="shipping_fee" name="shipping_fee" value="<?= number_format($order->shipping_fee, 0, '', '') ?>" min="0" required>
                        <span class="input-group-text">đ</span>
                    </div>
                    <div class="form-text">Nhập 0 nếu miễn phí vận chuyển</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary">Cập nhật</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<div class="modal fade" id="editPartnerModal" tabindex="-1" aria-labelledby="editPartnerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPartnerModalLabel">Chỉnh sửa người giới thiệu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('orders/update_partner/' . $order->id), ['id' => 'updatePartnerForm']) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="partner_id" class="form-label">Chọn người giới thiệu</label>
                    <select id="partner_id" name="partner_id" class="form-select" placeholder="Tìm kiếm người giới thiệu..."></select>
                    <div class="form-text">Tìm kiếm theo tên hoặc email của người giới thiệu</div>
                </div>
                <?php if (isset($partner) && $partner): ?>
                    <div class="text-end mt-3">
                        <button type="button" class="btn btn-sm btn-outline-danger" id="removePartner">
                            <i class="bi bi-trash me-1"></i> Xóa người giới thiệu
                        </button>
                    </div>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary">Lưu thay đổi</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<div class="modal fade" id="addPaymentModal" tabindex="-1" aria-labelledby="addPaymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPaymentModalLabel">Thêm thông tin thanh toán</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('orders/add_payment/' . $order->id), ['id' => 'addPaymentForm']) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="payment_method" class="form-label">Phương thức thanh toán</label>
                    <select class="form-select" id="payment_method" name="payment_method" required>
                        <?php if ($order->payment_method === 'bank_transfer'): ?>
                            <option value="bank_transfer" selected>Chuyển khoản ngân hàng</option>
                        <?php else: ?>
                            <option value="cod">Tiền mặt (COD)</option>
                        <?php endif; ?>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="payment_amount" class="form-label">Số tiền</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="payment_amount" name="amount" min="1000" required>
                        <span class="input-group-text">đ</span>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="transaction_date" class="form-label">Ngày thanh toán</label>
                    <input type="datetime-local" class="form-control" id="transaction_date" name="transaction_date" required value="<?= date('Y-m-d\TH:i:s') ?>">
                </div>

                <div class="mb-3">
                    <label for="transaction_id" class="form-label">Mã giao dịch</label>
                    <input type="text" class="form-control" id="transaction_id" name="transaction_id" placeholder="Mã giao dịch/Số tham chiếu">
                </div>

                <div class="mb-3">
                    <label for="payment_note" class="form-label">Description</label>
                    <textarea class="form-control" id="payment_note" name="description" rows="2"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary">Thêm thanh toán</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<?php if ($invoice && $invoice->status === 'pending'): ?>
    <div class="modal fade" id="editInvoiceModal" tabindex="-1" aria-labelledby="editInvoiceModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editInvoiceModalLabel">Chỉnh sửa thông tin hóa đơn</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <?= form_open(base_url('orders/update_invoice/' . $invoice->id), ['id' => 'updateInvoiceForm']) ?>
                <div class="modal-body">
                    <h6 class="mb-3">Thông tin khách hàng</h6>
                    <div class="mb-3">
                        <label for="name" class="form-label">Họ tên</label>
                        <input type="text" class="form-control" id="name" name="name" value="<?= esc($invoice->customer_name) ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="company_name" class="form-label">Tên công ty</label>
                        <input type="text" class="form-control" id="company_name" name="company_name" value="<?= esc($invoice->company_name) ?>">
                    </div>
                    <div class="mb-3">
                        <label for="tax_code" class="form-label">Mã số thuế</label>
                        <input type="text" class="form-control" id="tax_code" name="tax_code" value="<?= esc($invoice->tax_code) ?>">
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <select id="invoice_customer_email" name="email[]" multiple>
                            <?php
                            $emails = json_decode($invoice->customer_email ?: '[]');
                            foreach ($emails as $email) :
                            ?>
                                <option value="<?= esc($email) ?>" selected><?= esc($email) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">Địa chỉ</label>
                        <input type="text" class="form-control" id="address" name="address" value="<?= esc($invoice->customer_address) ?>">
                        <div class="form-text">Nhập địa chỉ chi tiết của khách hàng</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    <button type="submit" class="btn btn-primary">Lưu thay đổi</button>
                </div>
                <?= form_close() ?>
            </div>
        </div>
    </div>
<?php endif; ?>

<div class="modal fade" id="editPaymentModal" tabindex="-1" aria-labelledby="editPaymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPaymentModalLabel">Chỉnh sửa thông tin thanh toán</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('orders/update_payment'), ['id' => 'updatePaymentForm']) ?>
            <input type="hidden" name="id" id="payment_id">
            <div class="modal-body">
                <div class="mb-3">
                    <label for="payment_method" class="form-label">Phương thức thanh toán</label>
                    <select class="form-select" id="payment_method" name="payment_method" required>
                        <option value="bank_transfer" selected>Chuyển khoản ngân hàng</option>
                        <option value="cod">Tiền mặt (COD)</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="payment_amount" class="form-label">Số tiền</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="payment_amount" name="amount" min="1000" required>
                        <span class="input-group-text">đ</span>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="transaction_date" class="form-label">Ngày thanh toán</label>
                    <input type="datetime-local" class="form-control" id="transaction_date" name="transaction_date" required value="<?= date('Y-m-d\TH:i:s') ?>">
                </div>

                <div class="mb-3">
                    <label for="transaction_id" class="form-label">Mã giao dịch</label>
                    <input type="text" class="form-control" id="transaction_id" name="transaction_id" placeholder="Mã giao dịch/Số tham chiếu">
                </div>

                <div class="mb-3">
                    <label for="payment_note" class="form-label">Description</label>
                    <textarea class="form-control" id="payment_note" name="description" rows="2"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary">Cập nhật</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<div class="modal fade" id="editDiscountModal" tabindex="-1" aria-labelledby="editDiscountModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editDiscountModalLabel">Chỉnh sửa giảm giá</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('orders/update_discount/' . $order->id), ['id' => 'updateDiscountForm']) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="discount_amount" class="form-label">Số tiền giảm giá</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="discount_amount" name="discount_amount" value="<?= number_format($order->discount_amount, 0, '', '') ?>" min="0" required>
                        <span class="input-group-text">đ</span>
                    </div>
                    <div class="form-text">Nhập 0 nếu không có giảm giá</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary">Cập nhật</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<div class="modal fade" id="editQuantityModal" tabindex="-1" aria-labelledby="editQuantityModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editQuantityModalLabel">Sửa số lượng sản phẩm</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('orders/update_quantity/' . $order->id), ['id' => 'updateQuantityForm']) ?>
            <div class="modal-body">
                <input type="hidden" id="item_id" name="item_id" value="">

                <div class="mb-3">
                    <label for="item_name_display" class="form-label">Sản phẩm</label>
                    <input type="text" class="form-control" id="item_name_display" readonly>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <label for="current_quantity" class="form-label">Số lượng hiện tại</label>
                        <input type="number" class="form-control" id="current_quantity" readonly>
                    </div>
                    <div class="col-md-6">
                        <label for="new_quantity" class="form-label">Số lượng mới <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="new_quantity" name="new_quantity" min="1" required>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <label for="unit_price" class="form-label">Đơn giá</label>
                        <input type="text" class="form-control" id="unit_price" readonly>
                    </div>
                    <div class="col-md-6">
                        <label for="total_price_display" class="form-label">Thành tiền mới</label>
                        <input type="text" class="form-control" id="total_price_display" readonly>
                    </div>
                </div>

                <div class="mt-3">
                    <label for="quantity_note" class="form-label">Ghi chú thay đổi</label>
                    <textarea class="form-control" id="quantity_note" name="quantity_note" rows="2" placeholder="Ghi chú lý do thay đổi số lượng (không bắt buộc)"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="submit" class="btn btn-primary">Cập nhật số lượng</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
<script src="<?= base_url('assets/notyf/notyf.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery.validate.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery.validate.additional-methods.min.js') ?>"></script>
<script src="<?= base_url('assets/tom-select/tom-select.complete.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jsQR.js') ?>"></script>
<script src="<?= base_url('assets/js/app.js?v=1') ?>"></script>

<script>
    $(() => {
        function toggleShippingFields() {
            if ($('#status').val() === 'Shipping') {
                $('#shippingFields').show();
            } else {
                $('#shippingFields').hide();
            }
        }

        toggleShippingFields();

        $('#status').change(toggleShippingFields);

        $('#updateStatusForm').validate({
            rules: {
                status: {
                    required: true,
                },
                payment_status: {
                    required: true,
                },
            },
            messages: {
                status: {
                    required: 'Vui lòng chọn trạng thái đơn hàng',
                },
                payment_status: {
                    required: 'Vui lòng chọn trạng thái thanh toán',
                },
            },
            submitHandler: function(form, event) {
                event.preventDefault();

                const button = $(form).find('button[type="submit"]');

                $.ajax({
                    url: form.action,
                    type: 'POST',
                    data: $(form).serialize(),
                    beforeSend: function() {
                        button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-1" role="status" aria-hidden="true"></span> Đang xử lý...');
                        $('.invalid-feedback').remove();
                        $('input, select').removeClass('is-invalid');
                    },
                    success: function(response) {
                        if (response.status) {
                            window.location.reload();
                        } else {
                            notyf.error(response.message);
                        }
                    },
                    error: function(error) {
                        button.prop('disabled', false);

                        if (error.status === 422) {
                            const errors = error.responseJSON.messages;

                            $.each(errors, function(key, value) {
                                const input = $(`#${key}`);
                                input.addClass('is-invalid');
                                input.next('.invalid-feedback').remove();
                                input.after(`<div class="invalid-feedback">${value}</div>`);
                            });
                        } else {
                            notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                        }
                    },
                    complete: function() {
                        button.html('Cập nhật trạng thái');
                    },
                });
            }
        });

        $('#status, #payment_status, #shipping_company, #tracking_code, #status_note').on('change keyup', function() {
            const status = $('#status').val();
            const paymentStatus = $('#payment_status').val();
            const status_note = $('#status_note').val().trim();
            const isAdmin = <?= in_array($admin_details->role, ['Admin', 'SuperAdmin']) ? 'true' : 'false'; ?>;

            if (isAdmin) {
                if ((status && paymentStatus) || status_note) {
                    $('#updateStatusForm button[type="submit"]').prop('disabled', false);
                } else {
                    $('#updateStatusForm button[type="submit"]').prop('disabled', true);
                }
            } else {
                if (status || status_note) {
                    $('#updateStatusForm button[type="submit"]').prop('disabled', false);
                } else {
                    $('#updateStatusForm button[type="submit"]').prop('disabled', true);
                }
            }
        });

        $('#updateShippingForm').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1" role="status"></span>Đang lưu...');
                    $('.invalid-feedback').remove();
                    $('input, select').removeClass('is-invalid');
                },
                success: function(response) {
                    if (response.status) {
                        $('#editShippingModal').modal('hide');
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function(error) {
                    button.prop('disabled', false).text('Lưu thay đổi');

                    if (error.status === 422) {
                        const errors = error.responseJSON.messages;
                        $.each(errors, function(key, value) {
                            const input = $(`#modal_${key}`);
                            input.addClass('is-invalid');
                            input.after(`<div class="invalid-feedback">${value}</div>`);
                        });
                    } else {
                        notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                    }
                },
                complete: function() {
                    button.prop('disabled', false).text('Lưu thay đổi');
                }
            });
        });

        $('#updateCustomerForm').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-1" role="status"></span>Đang lưu...');
                    $('.invalid-feedback').remove();
                    $('input, select').removeClass('is-invalid');
                },
                success: function(response) {
                    if (response.status) {
                        $('#editCustomerModal').modal('hide');
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function(error) {
                    button.prop('disabled', false).text('Lưu thông tin');

                    if (error.status === 422) {
                        const errors = error.responseJSON.messages;
                        $.each(errors, function(key, value) {
                            const input = $(`#${key}`);
                            input.addClass('is-invalid');
                            input.after(`<div class="invalid-feedback">${value}</div>`);
                        });
                    } else {
                        notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                    }
                },
                complete: function() {
                    button.prop('disabled', false).text('Lưu thông tin');
                }
            });
        });

        $('#updateShippingFeeForm').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-1" role="status"></span>Đang cập nhật...');
                    $('.invalid-feedback').remove();
                    $('input').removeClass('is-invalid');
                },
                success: function(response) {
                    if (response.status) {
                        $('#editShippingFeeModal').modal('hide');
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function(error) {
                    button.prop('disabled', false).text('Cập nhật');

                    if (error.status === 422) {
                        const errors = error.responseJSON.messages;
                        $.each(errors, function(key, value) {
                            const input = $(`#${key}`);
                            input.addClass('is-invalid');
                            input.after(`<div class="invalid-feedback">${value}</div>`);
                        });
                    } else {
                        notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                    }
                },
                complete: function() {
                    button.prop('disabled', false).text('Cập nhật');
                }
            });
        });

        let partnerSelect = new TomSelect('#partner_id', {
            valueField: 'id',
            labelField: 'name',
            searchField: ['name', 'email'],
            placeholder: 'Tìm kiếm người giới thiệu...',
            create: false,
            load: function(query, callback) {
                if (!query.length) return callback();

                $.ajax({
                    url: '<?= base_url('orders/search_partners') ?>',
                    type: 'POST',
                    data: {
                        '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                        search: query
                    },
                    dataType: 'json',
                    error: function() {
                        callback();
                    },
                    success: function(res) {
                        callback(res.data);
                    }
                });
            },
            render: {
                option: function(item, escape) {
                    return `<div class="py-1 px-2 flex-column align-items-start">
                        <div class="mb-1">${escape(item.name)}</div>
                        <div class="text-muted small">#${escape(item.id)} - ${escape(item.email)}</div>
                    </div>`;
                },
                item: function(item, escape) {
                    return `<div class="py-1 px-2 flex-column align-items-start">
                        <div class="mb-1">${escape(item.name)}</div>
                        <div class="text-muted small">#${escape(item.id)} - ${escape(item.email)}</div>
                    </div>`;
                }
            }
        });

        <?php if (isset($partner) && $partner): ?>
            partnerSelect.addOption({
                id: '<?= $partner->id ?>',
                name: '<?= $partner->name ?>',
                email: '<?= $partner->email ?>'
            });
            partnerSelect.setValue('<?= $partner->id ?>');
        <?php endif; ?>

        $('#removePartner').on('click', function() {
            partnerSelect.clear();
        });

        $('#updatePartnerForm').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-1" role="status"></span>Đang lưu...');
                    $('.invalid-feedback').remove();
                    $('.is-invalid').removeClass('is-invalid');
                },
                success: function(response) {
                    if (response.status) {
                        $('#editPartnerModal').modal('hide');
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function(error) {
                    button.prop('disabled', false).text('Lưu thay đổi');

                    if (error.status === 422) {
                        const errors = error.responseJSON.messages;
                        $.each(errors, function(key, value) {
                            const input = $(`#${key}`);
                            input.addClass('is-invalid');
                            input.after(`<div class="invalid-feedback">${value}</div>`);
                        });
                    } else {
                        notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                    }
                },
                complete: function() {
                    button.prop('disabled', false).text('Lưu thay đổi');
                }
            });
        });

        $('#addPaymentModal').on('show.bs.modal', function() {
            $('#payment_amount').val(<?= $order->total_amount ?>);
        });

        $('#addPaymentForm').validate({
            rules: {
                payment_method: {
                    required: true
                },
                amount: {
                    required: true,
                    number: true,
                    min: 1000
                },
                transaction_date: {
                    required: function() {
                        return $('#payment_method').val() === 'bank_transfer';
                    },
                    date: true
                }
            },
            messages: {
                payment_method: {
                    required: "Vui lòng chọn phương thức thanh toán"
                },
                amount: {
                    required: "Vui lòng nhập số tiền",
                    number: "Vui lòng nhập số hợp lệ",
                    min: "Số tiền phải từ 1.000đ trở lên"
                },
                transaction_date: {
                    required: "Vui lòng nhập ngày chuyển khoản",
                    date: "Vui lòng nhập ngày hợp lệ"
                }
            },
            errorElement: 'div',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                error.insertAfter(element.closest('.input-group') || element);
            },
            highlight: function(element) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function(element) {
                $(element).removeClass('is-invalid');
            },
            submitHandler: function(form, event) {
                event.preventDefault();

                const formData = $(form).serialize();
                const submitButton = $(form).find('button[type="submit"]');

                $.ajax({
                    url: $(form).attr('action'),
                    type: 'POST',
                    data: formData,
                    beforeSend: function() {
                        submitButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1 align-middle" role="status"></span>Đang xử lý...');
                    },
                    success: function(response) {
                        if (response.status) {
                            notyf.success('Đã thêm thanh toán thành công');
                            $('#addPaymentModal').modal('hide');
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            notyf.error(response.message || 'Có lỗi xảy ra khi thêm thanh toán');
                            submitButton.prop('disabled', false).text('Thêm thanh toán');
                        }
                    },
                    error: function(xhr) {
                        submitButton.prop('disabled', false).text('Thêm thanh toán');

                        if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.messages) {
                            const errors = xhr.responseJSON.messages;
                            $.each(errors, function(key, value) {
                                const element = $('#' + key);
                                element.addClass('is-invalid');

                                if (element.next('.invalid-feedback').length === 0) {
                                    $('<div class="invalid-feedback">' + value + '</div>').insertAfter(element);
                                } else {
                                    element.next('.invalid-feedback').text(value);
                                }
                            });
                        } else {
                            notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                        }
                    }
                });
            }
        });

        $('#editPaymentModal').on('show.bs.modal', function(e) {
            const paymentId = $(e.relatedTarget).data('id');
            const modal = $(this);
            modal.find('#payment_id').val(paymentId);

            $.ajax({
                url: '<?= base_url('orders/get_payment') ?>/' + paymentId,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.status) {
                        const payment = response.data;
                        modal.find('#payment_method').val(payment.payment_method);
                        modal.find('#payment_amount').val(parseInt(payment.amount));
                        modal.find('#transaction_date').val(payment.transaction_date);
                        modal.find('#transaction_id').val(payment.transaction_id);
                        modal.find('#payment_note').val(payment.description);
                    } else {
                        notyf.error(response.message || 'Có lỗi xảy ra khi lấy thông tin thanh toán');
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                }
            });
        });

        $('#updatePaymentForm').validate({
            rules: {
                payment_method: {
                    required: true
                }
            },
            submitHandler: function(form, event) {
                event.preventDefault();

                const formData = $(form).serialize();
                const submitButton = $(form).find('button[type="submit"]');

                $.ajax({
                    url: $(form).attr('action') + '/' + $(form).find('#payment_id').val(),
                    type: 'POST',
                    data: formData,
                    beforeSend: function() {
                        submitButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1 align-middle" role="status"></span>Đang xử lý...');
                    },
                    success: function(response) {
                        if (response.status) {
                            $('#editPaymentModal').modal('hide');
                            window.location.reload();
                        } else {
                            notyf.error(response.message || 'Có lỗi xảy ra khi cập nhật thanh toán');
                            submitButton.prop('disabled', false).text('Cập nhật');
                        }
                    },
                    error: function(xhr) {
                        submitButton.prop('disabled', false).text('Cập nhật');

                        if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.messages) {
                            const errors = xhr.responseJSON.messages;
                            $.each(errors, function(key, value) {
                                const element = $('#' + key);
                                element.addClass('is-invalid');

                                if (element.next('.invalid-feedback').length === 0) {
                                    $('<div class="invalid-feedback">' + value + '</div>').insertAfter(element);
                                } else {
                                    element.next('.invalid-feedback').text(value);
                                }
                            });
                        } else {
                            notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                        }
                    }
                });
            }
        });

        <?php if ($invoice && $invoice->status === 'pending'): ?>
            $('#updateInvoiceForm').on('submit', function(e) {
                e.preventDefault();

                const form = $(this);
                const button = form.find('button[type="submit"]');

                const formData = new FormData(form[0]);

                $.ajax({
                    url: form.attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    beforeSend: function() {
                        button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-1" role="status"></span>Đang lưu...');
                        $('.invalid-feedback').remove();
                        $('input, textarea').removeClass('is-invalid');
                    },
                    success: function(response) {
                        if (response.status) {
                            $('#editInvoiceModal').modal('hide');
                            window.location.reload();
                        } else {
                            notyf.error(response.message);
                        }
                    },
                    error: function(error) {
                        button.prop('disabled', false).text('Lưu thay đổi');

                        if (error.status === 422) {
                            const errors = error.responseJSON.messages;
                            $.each(errors, function(key, value) {
                                const input = $(`#${key}`);
                                input.addClass('is-invalid');
                                input.after(`<div class="invalid-feedback">${value}</div>`);
                            });
                        } else {
                            notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                        }
                    },
                    complete: function() {
                        button.prop('disabled', false).text('Lưu thay đổi');
                    }
                });
            });

            new TomSelect('#invoice_customer_email', {
                plugins: ['remove_button'],
                valueField: 'value',
                labelField: 'value',
                searchField: 'value',
                create: true,
                createOnBlur: true,
                maxItems: 5,
                placeholder: 'Nhập email nhận hóa đơn',
                render: {
                    option: function(item, escape) {
                        return `<div class="py-1 px-2">
                            <div class="mb-1">${escape(item.value)}</div>
                        </div>`;
                    }
                },
            });
        <?php endif; ?>

        $('#updateDiscountForm').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-1" role="status"></span>Đang cập nhật...');
                    $('.invalid-feedback').remove();
                    $('input').removeClass('is-invalid');
                },
                success: function(response) {
                    if (response.status) {
                        $('#editDiscountModal').modal('hide');
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function(error) {
                    button.prop('disabled', false).text('Cập nhật');

                    if (error.status === 422) {
                        const errors = error.responseJSON.messages;
                        $.each(errors, function(key, value) {
                            const input = $(`#${key}`);
                            input.addClass('is-invalid');
                            input.after(`<div class="invalid-feedback">${value}</div>`);
                        });
                    } else {
                        notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                    }
                },
                complete: function() {
                    button.prop('disabled', false).text('Cập nhật');
                }
            });
        });
    });

    function deletePayment(id) {
        if (confirm('Bạn có chắc chắn muốn xóa thanh toán này?')) {
            $.ajax({
                url: '<?= base_url('orders/delete_payment') ?>/' + id,
                type: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                },
                success: function(response) {
                    if (response.status) {
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                },
            });
        }
    }
</script>

<script>
    $(() => {
        $('#editQuantityModal').on('show.bs.modal', function(e) {
            const button = $(e.relatedTarget);
            const modal = $(this);

            const itemId = button.data('item-id');
            const itemName = button.data('item-name');
            const itemQuantity = button.data('item-quantity');
            const itemPrice = button.data('item-price');

            modal.find('#item_id').val(itemId);
            modal.find('#item_name_display').val(itemName);
            modal.find('#current_quantity').val(itemQuantity);
            modal.find('#new_quantity').val(itemQuantity);
            modal.find('#unit_price').val(new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(itemPrice));
            modal.find('#total_price_display').val(new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(itemPrice * itemQuantity));
            modal.find('#quantity_note').val('');
        });

        $('#new_quantity').on('input', function() {
            const quantity = parseFloat($(this).val()) || 0;
            const unitPrice = parseFloat($('#unit_price').val().replace(/[^\d]/g, '')) || 0;
            const totalPrice = quantity * unitPrice;

            $('#total_price_display').val(new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(totalPrice));
        });

        $('#updateQuantityForm').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const button = form.find('button[type="submit"]');
            const currentQuantity = parseInt($('#current_quantity').val());
            const newQuantity = parseInt($('#new_quantity').val());

            if (newQuantity === currentQuantity) {
                notyf.error('Số lượng mới phải khác số lượng hiện tại');
                return;
            }

            if (newQuantity < 1) {
                notyf.error('Số lượng phải lớn hơn 0');
                return;
            }

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                beforeSend: function() {
                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-1" role="status"></span>Đang cập nhật...');
                    $('.invalid-feedback').remove();
                    $('input, textarea').removeClass('is-invalid');
                },
                success: function(response) {
                    if (response.status) {
                        $('#editQuantityModal').modal('hide');
                        window.location.reload();
                    } else {
                        notyf.error(response.message || 'Có lỗi xảy ra khi cập nhật số lượng');
                    }
                },
                error: function(error) {
                    if (error.status === 422) {
                        const errors = error.responseJSON.messages;
                        $.each(errors, function(key, value) {
                            const input = $(`#${key}`);
                            input.addClass('is-invalid');
                            input.after(`<div class="invalid-feedback">${value}</div>`);
                        });
                    } else {
                        notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                    }
                },
                complete: function() {
                    button.prop('disabled', false).text('Cập nhật số lượng');
                }
            });
        });

        const hash = window.location.hash;

        if (hash) {
            const tabId = hash.replace('#', '');
            const tab = $(`#${tabId}-tab`);
            if (tab.length) {
                tab.tab('show');
            }
        }

        $('button[data-bs-toggle="tab"]').on('shown.bs.tab', function(e) {
            const id = $(e.currentTarget).prop('id').replace('-tab', '');
            window.location.hash = id;
        });

        $('[data-copy-btn]').on('click', function(e) {
            e.preventDefault();

            const button = $(this);
            const text = button.data('copy-text');
            navigator.clipboard.writeText(text);
            button.find('i').removeClass('bi-clipboard').addClass('bi-check');
            setTimeout(() => {
                button.find('i').removeClass('bi-check').addClass('bi-clipboard');
            }, 1000);
        });

        $('.create-ghtk-order-btn').on('click', function() {
            const orderId = $(this).data('order-id');
            const button = $(this);

            if (!confirm('Bạn có chắc chắn muốn tạo đơn hàng GHTK cho đơn hàng này?')) {
                return;
            }

            button.prop('disabled', true).html('<i class="bi bi-hourglass-split me-1"></i> Đang xử lý...');

            $.ajax({
                url: '<?= base_url('orders/create_shipping_order/') ?>/' + orderId,
                type: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status) {
                        window.location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function(xhr) {
                    notyf.error(xhr.responseJSON.message);
                },
                complete: function() {
                    button.prop('disabled', false).html('<i class="bi bi-truck me-1"></i> Tạo đơn GHTK');
                }
            });
        });
    });
    
    $('#print-tracking-order').click(() => {
        window.open(` <?= base_url(sprintf('orders/print_tracking_order/%s', $order->id)) ?>`, '_blank');
    })
    
    $(document).on('keydown', function(e) {
        if (e.key === 'p') {
            e.preventDefault();
            $('#print-tracking-order').click();
        }
    });
</script>