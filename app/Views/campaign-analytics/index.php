<link rel="stylesheet" href="<?= base_url('assets/tom-select/tom-select.bootstrap5.css') ?>">

<style>
    .dataTables_info {
        padding-top: 0 !important;
    }
</style>

<main class="content">
    <div class="container-fluid p-0 p-md-3">
        <div class="row row-cols-1 row-cols-md-2 mt-2 mb-3 mt-md-0">
            <div class="col">
                <h3>Thống kê Chiến dịch quảng cáo</h3>
            </div>
            <div class="col d-flex justify-content-start justify-content-md-end">
                <div class="btn-group gap-2">
                    <button type="button" class="btn btn-light bg-white shadow-sm" id="date">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="calendar" class="align-middle mt-n1 me-1">
                            <path d="M8 2v4"></path>
                            <path d="M16 2v4"></path>
                            <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                            <path d="M3 10h18"></path>
                        </svg>
                        <span></span>
                    </button>
                    <button type="button" class="btn btn-light bg-white shadow-sm btn-sm" onclick="$('#filter').slideToggle();">
                        <i class="fas fa-filter"></i>
                        Bộ lọc
                    </button>
                </div>
            </div>
        </div>

        <div class="card mb-3" id="filter" style="display: none;">
            <div class="card-header pb-0">
                <h5 class="card-title mb-0">
                    Bộ lọc
                </h5>
                <button type="button" class="position-absolute btn-close" style="top: 1rem; right: 1rem;" aria-label="Đóng" onclick="$(this).closest('.card').slideUp();"></button>
            </div>
            <div class="card-body pt-3">
                <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-6 g-2 g-md-3">
                    <div class="col">
                        <label for="source" class="form-label">Source</label>
                        <select name="source" id="source" class="form-select" multiple>
                            <option value="">Tất cả</option>
                            <?php foreach ($sources as $source) : ?>
                                <?php if ($source['utm_source'] === '') continue; ?>
                                <option value="<?= $source['utm_source'] ?>"><?= $source['utm_source'] ?> (<?= $source['count'] ?>)</option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col">
                        <label for="medium" class="form-label">Medium</label>
                        <select name="medium" id="medium" class="form-select" multiple>
                            <option value="">Tất cả</option>
                            <?php foreach ($mediums as $medium) : ?>
                                <?php if ($medium['utm_medium'] === '') continue; ?>
                                <option value="<?= $medium['utm_medium'] ?>"><?= $medium['utm_medium'] ?> (<?= $source['count'] ?>)</option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col">
                        <label for="campaign" class="form-label">Campaign</label>
                        <select name="campaign" id="campaign" class="form-select" multiple>
                            <option value="">Tất cả</option>
                            <?php foreach ($campaigns as $campaign) : ?>
                                <?php if ($campaign['utm_campaign'] === '') continue; ?>
                                <option value="<?= $campaign['utm_campaign'] ?>"><?= $campaign['utm_campaign'] ?> (<?= $source['count'] ?>)</option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col">
                        <label for="content" class="form-label">Content</label>
                        <select name="content" id="content" class="form-select" multiple>
                            <option value="">Tất cả</option>
                            <?php foreach ($contents as $content) : ?>
                                <?php if ($content['utm_content'] === '') continue; ?>
                                <option value="<?= $content['utm_content'] ?>"><?= $content['utm_content'] ?> (<?= $source['count'] ?>)</option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col">
                        <label for="term" class="form-label">Term</label>
                        <select name="term" id="term" class="form-select" multiple>
                            <option value="">Tất cả</option>
                            <?php foreach ($terms as $term) : ?>
                                <?php if ($term['utm_term'] === '') continue; ?>
                                <option value="<?= $term['utm_term'] ?>"><?= $term['utm_term'] ?> (<?= $source['count'] ?>)</option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="d-flex gap-2 mt-3">
                    <button type="button" class="btn btn-sm btn-primary" id="btnFilter">
                        <i class="fas fa-filter"></i>
                        Lọc
                    </button>
                    <button type="button" class="btn btn-sm btn-secondary" id="btnReset">
                        <i class="fas fa-undo"></i>
                        Đặt lại
                    </button>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header pb-0">
                <h5 class="card-title">Danh sách tracking</h5>
            </div>
            <div class="card-body">
                <table class="table" id="dataTables">
                    <thead>
                        <tr>
                            <th></th>
                            <th style="max-width: 200px;">Công ty</th>
                            <th>Source</th>
                            <th>Medium</th>
                            <th>Campaign</th>
                            <th>Content</th>
                            <th>Term</th>
                            <th>Referer</th>
                            <th>Ngày tạo</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <div class="card">
            <div class="card-header pb-0">
                <h5 class="card-title mb-0">
                    Thống kê nguồn chiến dịch
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="source-stats-table">
                        <thead>
                            <tr>
                                <th>Source</th>
                                <th>Công ty</th>
                                <th>Công ty có trả phí</th>
                                <th>Doanh thu</th>
                                <th>Chiếm tỷ lệ doanh thu</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</main>

<script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery-3.5.1.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery.dataTables.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.bootstrap5.min.js') ?>"></script>
<script src="<?= base_url('assets/DataTables/Buttons-2.0.1/js/dataTables.buttons.min.js') ?>"></script>
<script src="<?= base_url('assets/DataTables/Buttons-2.0.1/js/buttons.bootstrap5.min.js') ?>"></script>
<script src="<?= base_url('assets/DataTables/Buttons-2.0.1/js/buttons.html5.min.js') ?>"></script>
<script src="<?= base_url('assets/jszip/jszip.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.responsive.min.js') ?>"></script>
<script src="<?= base_url('assets/notyf/notyf.min.js') ?>"></script>
<script src="<?= base_url('assets/tom-select/tom-select.complete.min.js') ?>"></script>
<script src="<?= base_url('assets/daterangepicker/moment.min.js') ?>"></script>
<script src="<?= base_url('assets/daterangepicker/daterangepicker.js') ?>"></script>
<script src="<?= base_url('assets/js/app.js?v=1') ?>"></script>

<script>
    $(function() {
        const urlParams = new URLSearchParams(window.location.search);
        let startDate = urlParams.get('start_date');
        let endDate = urlParams.get('end_date');

        const start = startDate ? moment(startDate, 'YYYY-MM-DD') : moment().startOf('year');
        const end = endDate ? moment(endDate, 'YYYY-MM-DD') : moment().endOf('year');

        startDate = start.format('YYYY-MM-DD');
        endDate = end.format('YYYY-MM-DD');

        function cb(start, end) {
            $('#date').find('span').text(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
        }

        $('#date').daterangepicker({
            startDate: start,
            endDate: end,
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Chọn',
                cancelLabel: 'Đóng',
                customRangeLabel: 'Tùy chỉnh',
            },
            ranges: {
                'Hôm nay': [moment(), moment()],
                'Hôm qua': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                '7 ngày qua': [moment().subtract(6, 'days'), moment()],
                '30 ngày qua': [moment().subtract(29, 'days'), moment()],
                'Tháng này': [moment().startOf('month'), moment().endOf('month')],
                'Tháng trước': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                'Năm nay': [moment().startOf('year'), moment().endOf('year')],
                'Năm trước': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')],
                'Tất cả': [moment('2020-01-01'), moment()],
            }
        }, cb);

        $('#date').on('apply.daterangepicker', function(ev, picker) {
            startDate = picker.startDate.format('YYYY-MM-DD');
            endDate = picker.endDate.format('YYYY-MM-DD');

            const url = new URL(window.location.href);
            url.searchParams.set('start_date', startDate);
            url.searchParams.set('end_date', endDate);
            window.history.pushState({}, '', url);

            table.ajax.reload();

            getSourceStats();
        });

        cb(start, end);

        const table = $('#dataTables').DataTable({
            processing: true,
            serverSide: true,
            responsive: true,
            order: [],
            pageLength: 10,
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, 'Tất cả'],
            ],
            ajax: {
                url: '<?= base_url('campaign-analytics/ajax_list') ?>',
                data: function(d) {
                    d['<?= csrf_token() ?>'] = '<?= csrf_hash() ?>';
                    d['source'] = $(document).find('select#source').val();
                    d['medium'] = $(document).find('select#medium').val();
                    d['campaign'] = $(document).find('select#campaign').val();
                    d['content'] = $(document).find('select#content').val();
                    d['term'] = $(document).find('select#term').val();
                    d['start_date'] = startDate;
                    d['end_date'] = endDate;
                },
                type: 'POST',
            },
            language: {
                sProcessing: 'Đang xử lý...',
                sLengthMenu: 'Xem _MENU_',
                sZeroRecords: 'Không tìm thấy dòng nào phù hợp',
                sInfo: 'Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục',
                sInfoEmpty: 'Đang xem 0 đến 0 trong tổng số 0 mục',
                sInfoFiltered: '(được lọc từ tổng _MAX_ mục)',
                sInfoPostFix: '',
                sSearch: 'Tìm:',
                sUrl: '',
                oPaginate: {
                    sFirst: 'Đầu',
                    sPrevious: 'Trước',
                    sNext: 'Tiếp',
                    sLast: 'Cuối'
                }
            },
            columnDefs: [
                {
                    targets: [3, 4, 5, 6, 7],
                    className: 'min-tablet-p',
                    responsivePriority: 2
                },
                {
                    targets: [1, 2, 8],
                    className: 'all',
                    responsivePriority: 1
                }
            ],
            buttons: {
                buttons: [{
                        extend: 'csv',
                        className: 'btn-sm',
                        text: '<i class="fas fa-file-csv"></i> Xuất CSV',
                    },
                    {
                        extend: 'excel',
                        className: 'btn-sm',
                        text: '<i class="fas fa-file-excel"></i> Xuất Excel',
                    },
                ],
            },
            dom: `<'d-flex flex-wrap gap-2 justify-content-between'fB>tr<'d-flex flex-wrap gap-2 justify-content-center justify-content-md-between mt-3'<'d-flex flex-wrap justify-content-center align-items-center gap-3'li>p>`,
        });

        $('#btnFilter').on('click', function() {
            table.ajax.reload();
        });

        function initTomSelect(element) {
            return new TomSelect(element, {
                plugins: ['remove_button'],
                render: {
                    item: function(data, escape) {
                        return '<div>' + escape(data.text) + '</div>';
                    },
                    option: function(data, escape) {
                        return '<div>' + escape(data.text) + '</div>';
                    }
                }
            });
        }

        const source = initTomSelect('select#source');
        const medium = initTomSelect('select#medium');
        const campaign = initTomSelect('select#campaign');
        const content = initTomSelect('select#content');
        const term = initTomSelect('select#term');

        moment.defineLocale('vi', {
            months: 'tháng 1_tháng 2_tháng 3_tháng 4_tháng 5_tháng 6_tháng 7_tháng 8_tháng 9_tháng 10_tháng 11_tháng 12'.split(
                '_'
            ),
            monthsShort: 'Thg 01_Thg 02_Thg 03_Thg 04_Thg 05_Thg 06_Thg 07_Thg 08_Thg 09_Thg 10_Thg 11_Thg 12'.split(
                '_'
            ),
            monthsParseExact: true,
            weekdays: 'chủ nhật_thứ hai_thứ ba_thứ tư_thứ năm_thứ sáu_thứ bảy'.split(
                '_'
            ),
            weekdaysShort: 'CN_T2_T3_T4_T5_T6_T7'.split('_'),
            weekdaysMin: 'CN_T2_T3_T4_T5_T6_T7'.split('_'),
            weekdaysParseExact: true,
            meridiemParse: /sa|ch/i,
            isPM: function(input) {
                return /^ch$/i.test(input);
            },
            meridiem: function(hours, minutes, isLower) {
                if (hours < 12) {
                    return isLower ? 'sa' : 'SA';
                } else {
                    return isLower ? 'ch' : 'CH';
                }
            },
            longDateFormat: {
                LT: 'HH:mm',
                LTS: 'HH:mm:ss',
                L: 'DD/MM/YYYY',
                LL: 'D MMMM [năm] YYYY',
                LLL: 'D MMMM [năm] YYYY HH:mm',
                LLLL: 'dddd, D MMMM [năm] YYYY HH:mm',
                l: 'DD/M/YYYY',
                ll: 'D MMM YYYY',
                lll: 'D MMM YYYY HH:mm',
                llll: 'ddd, D MMM YYYY HH:mm',
            },
            calendar: {
                sameDay: '[Hôm nay lúc] LT',
                nextDay: '[Ngày mai lúc] LT',
                nextWeek: 'dddd [tuần tới lúc] LT',
                lastDay: '[Hôm qua lúc] LT',
                lastWeek: 'dddd [tuần trước lúc] LT',
                sameElse: 'L',
            },
            relativeTime: {
                future: '%s tới',
                past: '%s trước',
                s: 'vài giây',
                ss: '%d giây',
                m: 'một phút',
                mm: '%d phút',
                h: 'một giờ',
                hh: '%d giờ',
                d: 'một ngày',
                dd: '%d ngày',
                w: 'một tuần',
                ww: '%d tuần',
                M: 'một tháng',
                MM: '%d tháng',
                y: 'một năm',
                yy: '%d năm',
            },
            dayOfMonthOrdinalParse: /\d{1,2}/,
            ordinal: function(number) {
                return number;
            },
            week: {
                dow: 1,
                doy: 4,
            },
        });

        $('#btnReset').on('click', function() {
            source.clear();
            medium.clear();
            campaign.clear();
            content.clear();
            term.clear();
            $('#date').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
            table.ajax.reload();
        });

        function getSourceStats() {
            $.ajax({
                url: '<?= base_url('campaign-analytics/ajax_source_stats') ?>',
                method: 'GET',
                data: {
                    start_date: startDate,
                    end_date: endDate,
                },
                dataType: 'json',
                success: function(response) {
                    let tbody = $('#source-stats-table tbody');
                    tbody.empty();

                    if (response.length === 0) {
                        tbody.append(`<tr><td colspan="5" class="text-center">Không có dữ liệu</td></tr>`);
                    }

                    response.forEach(function(stat) {
                        tbody.append(`
                            <tr>
                                <td class="fw-bold">${stat.utm_source}</td>
                                <td>${stat.total_companies}</td>
                                <td>${stat.total_paid_companies}</td>
                                <td>${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(stat.total_paid)}</td>
                                <td>${stat.total_paid > 0 ? stat.percentage_share : 0}%</td>
                            </tr>
                        `);
                    });
                },
                error: function(xhr) {
                    console.error('Error fetching source stats:', xhr.responseText);
                }
            });
        }

        getSourceStats();
    });
</script>