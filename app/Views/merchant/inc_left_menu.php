<?php 
$uri = service('uri');
$segment1 = $uri->getSegment(1);
$segment2 = $uri->getSegment(2);
 

?>
<div class="col-md-3 col-xl-2">

    <div class="card">

        <div class="list-group list-group-flush">
            
            <a class="list-group-item list-group-item-action <?php if( $segment1 == 'merchant' && $segment2 == 'details') echo 'active';?>" href="<?php echo base_url('merchant/details/' . $merchant_details->merchant_id);?>"><i class="bi bi-buildings me-2"></i> <PERSON><PERSON> sơ</a>

            <?php if(has_permission('BankAccount', 'can_view_all')) { ?>
            <a class="list-group-item list-group-item-action <?php if($segment1 == 'merchant' && $segment2 == 'bankaccount') echo 'active';?>" href="<?php echo base_url('merchant/bankaccount/' . $merchant_details->merchant_id);?>"><i class="bi bi-bank me-2"></i> Ngân hàng</a>
            <?php } ?>

            <?php if(has_permission('TaxInfo', 'can_view_all')) { ?>
            <a class="list-group-item list-group-item-action <?php if($segment2 == 'tax_info' || ($segment1 == 'taxinfo')) echo 'active';?>" href="<?php echo base_url('merchant/tax_info/' . $merchant_details->merchant_id);?>"><i class="bi bi-card-list me-2"></i> Tax Info</a>
            <?php } ?>

            <?php if(has_permission('Invoice', 'can_view_all')) { ?>
            <a class="list-group-item list-group-item-action <?php if($segment2 == 'invoice' || $segment1 == 'invoice') echo 'active';?>" href="<?php echo base_url('merchant/invoice/' . $merchant_details->merchant_id);?>"><i class="bi bi-receipt me-2"></i> Hóa đơn</a>
            <?php } ?>

            <?php if(has_permission('Stransaction', 'can_view_all')) { ?>
            <a class="list-group-item list-group-item-action <?php if($segment2 == 'stransaction') echo 'active';?>" href="<?php echo base_url('merchant/stransaction/' . $merchant_details->merchant_id);?>"><i class="bi bi-cash me-2"></i> Giao dịch</a>
            <?php } ?>

        </div>
    </div>
</div>