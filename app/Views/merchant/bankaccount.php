<main class="content">
    <div class="container-fluid">

        <div>
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("merchant");?>">Merchant</a></li> 
                <li class="breadcrumb-item"><a href="<?= base_url("merchant/details/" . $merchant_details->merchant_id);?>">#<?= esc($merchant_details->merchant_id . ' - ' . $merchant_details->brand_name);?></a></li> 

                <li class="breadcrumb-item active" aria-current="page">Bank Account</li> 
                </ol>
            </nav>
            <!-- End Breadcrumb -->
        </div>
        
        <div class="row mt-3">

            <?php include(APPPATH . 'Views/merchant/inc_left_menu.php');?>


            <div class="col-md-9 col-xl-10">
                 
                

                <div class="mt-3">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"></h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped table-bordered text-muted align-middle display " id="bank_account_table">

                                    <thead class="table-light text-muted">
                                        <tr class="align-middle">
                                            <th></th>
                                            <th>ID</th>
                                            <th>Company</th>
                                            <th>Merchant</th>
                                            <th>Số TK</th>
                                            <th>Chủ TK</th>
                                            <th>Kết nối</th>
                                            <th>Giao dịch</th>
                                            <th>GD gần nhất</th>
                                            <th>Tạo lúc</th>


                                         </tr>
                                    </thead>
                                    <tbody>
                
                                       
                                    </tbody>
                                </table>
                            </div>
                             
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>



 
<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>

 
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>


<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/app.js"></script>


<script>
 

 $(document).ready(function() {

      var  table = $('#bank_account_table').DataTable({ 
         rowReorder: {
             selector: 'td:nth-child(0)'
         },
         responsive: false,

      
          "processing": true,
          "serverSide": true,
          "order": [],
          "pageLength": 10,

          "ajax": {
              "url": "<?php echo base_url('merchant/bankaccount_ajax_list'); ?>?merchant_id=<?= esc($merchant_details->merchant_id);?>",
              "data": {"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
              "type": "POST"
          },


          "language": {
              "sProcessing":   "Đang xử lý...",
              "sLengthMenu":   "Xem _MENU_ mục",
              "sZeroRecords":  "Không tìm thấy dòng nào phù hợp",
              "sInfo":         "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
              "sInfoEmpty":    "Đang xem 0 đến 0 trong tổng số 0 mục",
              "sInfoFiltered": "(được lọc từ _MAX_ mục)",
              "sInfoPostFix":  "",
              "sSearch":       "Tìm:",
              "sUrl":          "",
              "oPaginate": {
                  "sFirst":    "Đầu",
                  "sPrevious": "Trước",
                  "sNext":     "Tiếp",
                  "sLast":     "Cuối"
              }
          },

          "columnDefs": [
             { responsivePriority: 1, targets: 1 },
             { responsivePriority: 2, targets: 3 },
              
              { 
                  "visible": false,
                  "targets": [ 0 ],
                  "orderable": false,
              },

              { 
                  "visible": false,
                  "targets": [ 2,3 ],
                  "orderable": false,
              },


              
          ],

          "drawCallback": function( settings ) {
             var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
             var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                 return new bootstrap.Tooltip(tooltipTriggerEl, {trigger : 'hover'})
             });
         },

          
      });

      table.on( 'responsive-display', function ( e, datatable, row, showHide, update ) {
         var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
         var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
             return new bootstrap.Tooltip(tooltipTriggerEl, {trigger : 'hover'})
         });

     } );

    

     table.on ('init', function () {
         $('*[type="search"][class="form-control form-control-sm"]').attr('style','max-width:120px');
         $('div.dataTables_filter').parent().attr('class','col-6');
         $('div.dataTables_length').parent().attr('class','col-6');
     })

    
 });
 

</script>