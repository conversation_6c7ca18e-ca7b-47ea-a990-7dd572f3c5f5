 
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
        <main class="content">
            <div class="container-fluid">
              
                <div class="card mt-3" style="width:100%">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-auto d-none d-sm-block">
                                <h3>Danh sách Merchant</h3>
                            </div>

                            <div class="col-auto ms-auto text-end row">
                                <div class="mb-2">
                                    <a href="javascript:;" onclick="merchant_add()" class="btn btn-primary btn-sm float-end"><i class="bi bi-plus"></i> Thêm Merchant</a>

                                </div>

                            
                            </div>
                        </div>
                         
                        <div class="row">
                           
                           
                                <div class="">
                                <table class="table table-hover table-striped table-bordered text-muted align-middle display nowrap" id="merchant_table" style="width:100%">
                                    <thead class="table-light text-muted">
                                        <tr class="align-middle">
                                            <th></th>
                                            <th>ID</th>
                                            <th>Brand</th>
                                            <th>Company</th>
                                            <th>Loại</th>
                                            <th>Credit</th>
                                            <th>Companies</th>
                                            <th>Bank Accounts</th>
                                            <th>Transactions</th>
                                            <th>Income</th>
                                            <th>Ngày tạo</th>

                                         </tr>
                                    </thead>
                                    <tbody>
                
                                       
                                    </tbody>
                                </table>
                                </div>
                             
                            
                        </div>
                    </div>
                </div>
                
            </div>
        </main>

        <?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>


        </div>
    </div>



<!-- Modal -->
<div class="modal fade" id="merchantAddModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="merchantAddModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="merchantAddModalLabel">Thêm Merchant</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <?php echo form_open('',"id='merchant_add_form' class='needs-validation form-add-merchant' novalidate");?>
        <input type="hidden"name="id" value="">
        <div class="modal-body m-lg-3">

                  

                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Tên thương hiệu <span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" name="brand_name" minlength="1" maxlength="100" placeholder="" required>
                    </div>

                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Tên công ty <span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" name="company_name" placeholder="" required>
                    </div>

                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Số điện thoại</label>
                        <input type="number" class="form-control" name="phonenumber" placeholder="">
                    </div>
                
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Email</label>
                        <input type="email" class="form-control" name="email" placeholder="">
                    </div>
                
                    
                    <div class="mb-3">
                        <label class="form-label">Đồng bộ giao dịch <span class="text-danger">(*)</span></label>
                        <select class="form-select" name="trans_type" aria-label="trans_type" required>
                            <option value="">Chọn...</option>
                            <option value="C">Tiền vào</option>
                            <option value="D">Tiền ra</option>
                            <option value="DC">Cả tiền vào và tiền ra</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Loại <span class="text-danger">(*)</span></label>
                        <select class="form-select" name="type" aria-label="type" required>
                            <option value="">Chọn...</option>
                            <option value="Development">Development</option>
                            <option value="Production">Production</option>
                            <option value="Staging">Staging</option>
                        </select>
                    </div>


                    <div class="mb-3">
                        <label for="exampleFormControlTextarea1" class="form-label">Ghi chú</label>
                        <textarea class="form-control" name="note" id="exampleFormControlTextarea1" rows="3"></textarea>
                    </div>
                
 
        
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary me-3" data-bs-dismiss="modal">Đóng</button>
                <a class="btn btn-primary btn-merchant-add" onclick="save()">Thêm</a>
            </div>
        </form>
        </div>
    </div>
</div>
<!-- Modal -->

  

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>

<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
    
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

<script>



$(document).ready(function() {

var  table = $('#merchant_table').DataTable({ 
   rowReorder: {
       selector: 'td:nth-child(0)'
   },
   responsive: true,


    "processing": true,
    "serverSide": true,
    "order": [],
    "pageLength": 20,

    "ajax": {
        "url": "<?php echo base_url('merchant/ajax_list'); ?>",
        "data": {"<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
        "type": "POST"
    },


    "language": {
        "sProcessing":   "Đang xử lý...",
        "sLengthMenu":   "Xem _MENU_ mục",
        "sZeroRecords":  "Không tìm thấy dòng nào phù hợp",
        "sInfo":         "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
        "sInfoEmpty":    "Đang xem 0 đến 0 trong tổng số 0 mục",
        "sInfoFiltered": "(được lọc từ _MAX_ mục)",
        "sInfoPostFix":  "",
        "sSearch":       "Tìm:",
        "sUrl":          "",
        "oPaginate": {
            "sFirst":    "Đầu",
            "sPrevious": "Trước",
            "sNext":     "Tiếp",
            "sLast":     "Cuối"
        }
    },

    "columnDefs": [
       { responsivePriority: 1, targets: 1 },
       { responsivePriority: 2, targets: 2 },
       

        { 
            "visible": false,
            "targets": [ 0 ],
            "orderable": false,
        },

        { 
            "targets": [ 6,7,8],
            "orderable": false,
        } 
        
    ],

    
});

table.on ('init', function () {
   $('*[type="search"][class="form-control form-control-sm"]').attr('style','max-width:120px');
   $('div.dataTables_filter').parent().attr('class','col-6');
   $('div.dataTables_length').parent().attr('class','col-6');
});


});



var merchantAddModal = new bootstrap.Modal(document.getElementById('merchantAddModal'), {
    keyboard: false
});
 
function reset_merchant_form() {
        $('#merchant_add_form')[0].reset();

        $('#btn_loading').html('');
        $(".btn-merchant-add").html('Thêm');
        $(".btn-merchant-add").attr("disabled", false);
 
    }

function merchant_add() {
    reset_merchant_form();
    save_method = 'add';
    $('.modal-title').text('Thêm Merchant'); // Set title to Bootstrap modal title
    $(".btn-merchant-add").html('Thêm');
    merchantAddModal.show();
}
 

function save()
{

    var url;
    
    url = "<?php echo base_url('merchant/ajax_merchant_add');?>";

        
    $(".btn-merchant-add").attr("disabled", true);
    $('#btn_loading').html('');
    $(".btn-merchant-add").html('<div class="spinner-border text-light" role="status" id="btn_loading"></div>');

    $.ajax({
        url : url,
        type: "POST",
        data: $('#merchant_add_form').serialize(),
        dataType: "JSON",
        success: function(data)
        {
            //if success close modal and reload ajax table
            if(data.status == true) {
                merchantAddModal.hide();

                location.reload();

            } else {
                
                $(".btn-merchant-add").attr("disabled", false);
                $('#btn_loading').remove();
                $(".btn-merchant-add").html('Thêm');
                alert('Lỗi: ' + data.message);

            }
        
        },
        error: function (jqXHR, textStatus, errorThrown)
        {
            alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
            $(".btn-merchant-add").attr("disabled", false);
            $('#btn_loading').remove();
            $(".btn-merchant-add").html('Thêm');
        }
        
    });
}


</script>