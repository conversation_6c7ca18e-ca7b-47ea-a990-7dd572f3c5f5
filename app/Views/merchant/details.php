<main class="content">
    <div class="container-fluid">

         <div>
             <!-- Breadcrumb -->
             <nav aria-label="breadcrumb">
              <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("merchant");?>">Merchant</a></li> 
                <li class="breadcrumb-item active" aria-current="page">#<?= esc($merchant_details->merchant_id . ' - ' . $merchant_details->brand_name);?></li> 
              </ol>
            </nav>
            <!-- End Breadcrumb -->
         </div>

        <div class="row mt-3">

            <?php include(APPPATH . 'Views/merchant/inc_left_menu.php');?>


            <div class="col-md-9 col-xl-10">

                <h4>#<?= esc($merchant_details->merchant_id . ' - ' . $merchant_details->brand_name);?></h4>
               

                <div class="row">
                    <div class="col-md-7">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0"><PERSON><PERSON> sơ Merchant</h5>
                            </div>
                            <div class="card-body">

                                <div class="row">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Tên công ty</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php echo esc($merchant_details->company_name);?></span>

                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Thương hiệu</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php echo esc($merchant_details->brand_name);?></span>

                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Loại</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php echo esc($merchant_details->type);?></span>

                                    </div>
                                </div>
                                

                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Ngày đăng ký</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php echo esc($merchant_details->created_at);?></span>

                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Số dư Credit</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold <?php if($merchant_details->credit > 0) echo "text-success";?>'><?php echo number_format($merchant_details->credit);?> đ</span>

                                    </div>
                                </div>

                                <?php if(has_permission('Merchant', 'can_edit')) { ?>

                                <div class="mt-3">
                                <span class='fw-bold'><a href="#"  data-bs-toggle="modal" data-bs-target="#editMerchantModal" class="ms-3"><i class="bi bi-pencil"></i> Sửa</a></span>
                                </div>

                                <?php } ?>

                               


                            </div>
                        </div>


                    </div>
                    <div class="col-md-5">

                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Thông tin liên hệ</h5>
                            </div>
                            <div class="card-body">

                                <div class="row">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Họ và tên</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span
                                            class='fw-bold'><?php echo esc($merchant_details->customer_name);?></span>

                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Email</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php echo esc($merchant_details->email);?></span>

                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Số điện thoại</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php echo esc($merchant_details->phonenumber);?></span>

                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-4  col-6">
                                        <span class="text-muted">Ghi chú</span>
                                    </div>
                                    <div class="col-md-8  col-6">

                                        <span class='fw-bold'><?php echo nl2br(esc($merchant_details->note));?></span>

                                    </div>
                                </div>
                               

                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-2">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6 col-md-3 text-center">
                                    <div><b class="text-<?php if($paid_invoice_count>0) echo 'primary'; else echo 'muted'; ?> fs-3"><?= number_format($paid_invoice_count);?></b> Invoices Paid</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($paid_invoice_count>0) echo 'success'; else echo 'muted'; ?>"> <?= number_format($paid_invoice_sum->total_amount);?> đ</span>
                                </div>
                           
                                <div class="col-6 col-md-3 text-center">
                                    <div><b class="text-<?php if($stransaction_count>0) echo 'primary'; else echo 'muted'; ?> fs-3"><?= number_format($stransaction_count);?></b> Transactions</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($stransaction_count>0) echo 'success'; else echo 'muted'; ?>"> <?= number_format($stransaction_sum->total_in);?> đ</span>
                                </div>
                                     
                                <div class="col-6 col-md-3 text-center">
                                    <div><b class="text-danger fs-3"><?= number_format($unpaid_invoice_count);?></b> Invoices Unpaid</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-danger"> <?= number_format($unpaid_invoice_sum->total_amount);?> đ</span>
                                </div>
                                <div class="col-6 col-md-3 text-center">
                                    <div>Users</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($bank_account_count>0) echo 'primary'; else echo 'muted'; ?>"> <?= number_format($user_count);?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-2">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6 col-md-3 text-center">
                                    <div>Bank Accounts</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($bank_account_count>0) echo 'primary'; else echo 'muted'; ?>"> <?= number_format($bank_account_count);?></span>
                                </div>
                           
                                <div class="col-6 col-md-3 text-center">
                                    <div>Bank Transactions</div>
                                    <span class="h3 d-inline-block mt-1 mb-3 text-<?php if($transaction_count>0) echo 'primary'; else echo 'muted'; ?>"> <?= number_format($transaction_count);?></span>
                                </div>

                           

                                <div class="col-6 col-md-3 text-center">
                                    <div>Last Transaction</div>
                                     <span class="h5 d-inline-block mt-1 mb-3 text-primary"> <?php if(is_object($last_transaction)) echo timespan($last_transaction->last_transaction,2);?></span> 
 
                                </div>
                                     
                              
                            </div>
                        </div>
                    </div>
                </div>
 

               
            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>


 
<?php if(has_permission('Merchant', 'can_edit')) { ?>

<!-- Edit Merchant Modal -->
<div class="modal fade" id="editMerchantModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="editMerchantModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="editMerchantModalLabel">Sửa thông tin Merchant</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <?php echo form_open('',"id='merchant_form' class='needs-validation form-edit-merchant' novalidate");?>
        <input type="hidden" name="id" value="<?=esc($merchant_details->merchant_id);?>">

        <div class="modal-body m-lg-3">

                    
            <div class="row">

            <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Tên thương hiệu <span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" name="brand_name" minlength="1" maxlength="100" placeholder="" value="<?= esc($merchant_details->brand_name);?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Tên công ty <span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" name="company_name" placeholder=""  value="<?= esc($merchant_details->company_name);?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Tên người đại diện làm việc <span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" name="customer_name" placeholder=""  value="<?= esc($merchant_details->customer_name);?>">
                    </div>

                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Số điện thoại</label>
                        <input type="number" class="form-control" name="phonenumber" placeholder=""  value="<?= esc($merchant_details->phonenumber);?>">
                    </div>
                
                    <div class="mb-3">
                        <label for="exampleFormControlInput3" class="form-label">Email</label>
                        <input type="email" class="form-control" name="email" placeholder=""  value="<?= esc($merchant_details->email);?>">
                    </div>
                
                     


                    <div class="mb-3">
                        <label for="exampleFormControlTextarea1" class="form-label">Ghi chú</label>
                        <textarea class="form-control" name="note" id="exampleFormControlTextarea1" rows="3"><?= nl2br(esc($merchant_details->note));?></textarea>
                    </div>
                
                
          
               
            </div>
        
        
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light me-3" data-bs-dismiss="modal">Đóng</button>
                <a class="btn btn-primary btn-user-update" onclick="merchant_save()">Lưu thay đổi</a>
            </div>
        </form>
        </div>
    </div>
</div>
<!-- Edit Merchant Modal -->
 <?php } ?>

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>

<script type="text/javascript" src="<?php echo base_url();?>/assets/DataTables/datatables.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/app.js"></script>

<script>

 
<?php if(has_permission('Merchant', 'can_edit')) { ?>

function merchant_save()
{
    
     url = "<?php echo base_url('merchant/ajax_merchant_update');?>";

    

    $(".btn-merchant-update").attr("disabled", true);
    $('#btn_merchant-update_loading').html('');
    $(".btn-merchant-update").html('<div class="spinner-border text-light" role="status" id="btn_merchant-update_loading"></div>');

    $.ajax({
        url : url,
        type: "POST",
        data: $('#merchant_form').serialize(),
        dataType: "JSON",
        success: function(data)
        {
            //if success close modal and reload ajax table
            if(data.status == true) {

                 location.reload();

              
            } else {
                
                $(".btn-merchant-update-add").attr("disabled", false);
                $('#btn_merchant-update_loading').remove();
                alert('Lỗi: ' + data.message);

            }
        
        },
        error: function (jqXHR, textStatus, errorThrown)
        {
            alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
            $(".btn-merchant-update-add").attr("disabled", false);
             $('#btn_merchant-update_loading').remove();
        }
        
    });
}

<?php  } ?>

</script>