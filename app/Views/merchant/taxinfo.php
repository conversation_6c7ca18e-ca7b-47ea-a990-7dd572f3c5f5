<main class="content">
    <div class="container-fluid">
        <div>
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url("merchant");?>">Merchant</a></li> 
                <li class="breadcrumb-item"><a href="<?= base_url("merchant/details/" . $merchant_details->merchant_id);?>">#<?= esc($merchant_details->merchant_id . ' - ' . $merchant_details->short_name);?></a></li> 

                <li class="breadcrumb-item active" aria-current="page">Tax Info</li> 
                </ol>
            </nav>
            <!-- End Breadcrumb -->
        </div>
        <div class="row mt-3">

            <?php include(APPPATH . 'Views/merchant/inc_left_menu.php');?>


            <div class="col-md-9 col-xl-10">
                
                <div class="mt-3">
                    <div class="card">
                        <div class="card-header">
                        <a href="<?= base_url('taxinfo/create_for_merchant/' . $merchant_details->merchant_id);?>" class="btn btn-primary btn-sm float-end"><i class="bi bi-plus"></i> Thêm mới</a>   

                            <h5 class="card-title mb-0">Địa chỉ xuất VAT</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <th>ID</th>
                                        <th>Loại</th>
                                        <th>Tên</th>
                                        <th>MST</th>
                                        <th>Địa chỉ</th>
                                        <th>Email</th>
                                        <th>Tạo lúc</th>
                                    </thead>
                                    <tbody>
                                        <?php foreach($tax_infos as $tax_info): ?>
                                        <tr>
                                            <td><a href="<?= base_url("taxinfo/edit/" . $tax_info->id);?>"><?= esc($tax_info->id);?></a></td>
                                            <td><a href="<?= base_url("taxinfo/edit/" . $tax_info->id);?>"><?= esc($tax_info->type);?></a></td>
                                            <td><a href="<?= base_url("taxinfo/edit/" . $tax_info->id);?>"><?= esc($tax_info->name);?></a></td>

                                            <td><?= esc($tax_info->tax_code);?></td>
                                            <td><?= esc(get_full_tax_info_address($tax_info->id));?>, Việt Nam</td>
                                            <td><?= esc($tax_info->email);?></td>
                                            <td><?= esc($tax_info->created_at);?></td>

                                            <td><button onclick="delete_tax_info(<?= esc($tax_info->id);?>)" class="btn btn-sm btn-danger  mt-2"><i class="bi bi-trash"> Xóa</i></button></td>

                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>

                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>

</div>
</div>




<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.6.0.min.js"></script>

<script type="text/javascript" src="<?php echo base_url();?>/assets/DataTables/datatables.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/app.js"></script>

<script>

   function delete_tax_info(id) {
        if (confirm("Bạn có chắc chắn muốn xóa thông tin này?")) {
            $.ajax({
            url : "<?php echo base_url('taxinfo/ajax_delete');?>",
            type: "POST",
            data: {id: id, "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
            dataType: "JSON",
            success: function(data)
            {
                //if success close modal and reload ajax table
                if(data.status == true) {
                    location.reload();

                } else {
                    
                    alert('Lỗi: ' + data.message);
 
                }
            
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
               
            }
            
        });
        }
        
    }
</script>