 
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
        <main class="content">
            <div class="container-fluid">
              
            <div>
             <!-- Breadcrumb -->
             <nav aria-label="breadcrumb">
              <ol class="breadcrumb breadcrumb-light mb-0">
                <li class="breadcrumb-item"><a href="<?php echo base_url('ticket');?>">Hỗ trợ</a></li>
                <li class="breadcrumb-item active" aria-current="page">Tạo yêu cầu</li>

              </ol>
            </nav>
            <!-- End Breadcrumb -->
         </div>
            <div class="card mt-3" style="width:100%">
                    <div class="card-body">
                        <h2 class="h3">Tạo yêu cầu hỗ trợ</h2>
                        <hr>
                        <div class="row">
                            <div class="col-md-7">

                            

                                <?php echo form_open('/ticket/create',"id='ticket_create_form' class='needs-validation ticket_create_form' novalidate");?>

                                <input type="hidden" name="company_id" value="<?= esc($company_details->id);?>">
                                <div class="mt-3 mx-auto" style="max-width:580px">

                                 <div class="mb-3">
                                        <label for="exampleFormControlInput1" class="form-label">Gửi đến khách hàng: <a href="<?= base_url('company/details/' . $company_details->id);?>">#<?= esc($company_details->id) . ' - ' . esc($company_details->short_name);?></a>
                                    </div>
                                 
                                    <div class="mb-3">
                                        <label for="exampleFormControlInput1" class="form-label">Tiêu đề <span class="text-danger">*</span></label>
                                        <input type="text" name="subject" class="form-control" id="exampleFormControlInput1" placeholder="" value="" required="">
                                        <div class="invalid-feedback">
                                            Vui lòng điền tiêu đề.
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="exampleFormControlInput2" class="form-label">Nội dung <span class="text-danger">*</span></label>
                                        <textarea name="body" class="form-control" id="exampleFormControlInput2" placeholder="" value="" required="" rows="5"></textarea>
                                        <div class="invalid-feedback">
                                            Vui lòng điền nội dung.
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3 text-center mt-3">

                                    <button type="submit" name="btn_submit" class="btn btn-primary btn-save">Tạo mới</button>
                                    </div>

                                <?php echo form_close();?>
                            </div>
                            <div class="col-md-5">
                                <div class="p-2 border rounded border-info">
                                    <h4 class="mt-3 ms-2">Thông tin</h4>
                                    <ul class="lh-lg">
                                         
                                    </ul>

                                </div>
                            </div>
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </main>

        <?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>


        </div>
    </div>


   
<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>

<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
    
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>


<script>

(() => {
  'use strict'

  // Fetch all the forms we want to apply custom Bootstrap validation styles to
  const forms = document.querySelectorAll('.needs-validation')

  // Loop over them and prevent submission
  Array.from(forms).forEach(form => {
    form.addEventListener('submit', event => {
        event.preventDefault()
        event.stopPropagation()
      if (form.checkValidity()) {
       create_ticket();
      }  

      form.classList.add('was-validated')
    }, false)
  })
})()




function create_ticket() {
    
    url = "<?php echo base_url('ticket/ajax_create');?>";
    post_data = $('#ticket_create_form').serialize();


    $(".btn-save").attr("disabled", true);
    $('#btn_loading').html('');
    $(".btn-save").html('<div class="spinner-border text-light" role="status" id="btn_loading"></div>');

    $.ajax({
        url : url,
        type: "POST",
        data: post_data,
        dataType: "JSON",
        success: function(data)
        {

            $(".btn-save").attr("disabled", false);
            $('#btn_loading').remove();
            $(".btn-save").html('Tạo mới');

            if(data.status == true) {
                location.href = "<?= base_url('ticket/details');?>/" + data.id;
            } else  {
                alert('Lỗi: ' + data.message);
            }
        
        },
        error: function (jqXHR, textStatus, errorThrown)
        {
            alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');
            $(".btn-save").attr("disabled", false);
            $('#btn_loading').remove();
            $(".btn-save").html('Tạo mới');
        }
        
    });
    }
</script>