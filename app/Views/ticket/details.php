 <link rel="stylesheet" href="<?php echo base_url(); ?>/assets/css/dataTables.bootstrap5.min.css">
 <link rel="stylesheet" href="<?php echo base_url(); ?>/assets/css/rowReorder.bootstrap5.min.css">
 <link rel="stylesheet" href="<?php echo base_url(); ?>/assets/css/responsive.dataTables.min.css">
 <link rel="stylesheet" href="<?php echo base_url(); ?>/assets/fancyapps/fancybox.css" />
 <style>
     .upload-area {
         border: 2px dashed #dee2e6 !important;
         transition: all 0.3s ease;
         min-height: 120px;
     }

     .upload-area:hover {
         border-color: #0d6efd !important;
     }

     /* Image preview styles */
     .image-preview-item {
         position: relative;
         width: 100px;
         height: 100px;
         border-radius: 8px;
         overflow: hidden;
         box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
         margin-bottom: 10px;
         display: inline-block;
         text-decoration: none;
         color: inherit;
         transition: all 0.2s ease;
     }

     .image-preview-item:hover {
         transform: translateY(-2px);
         box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
     }

     .image-preview-item img {
         width: 100%;
         height: 100%;
         object-fit: cover;
     }

     .image-preview-item .remove-image {
         position: absolute;
         top: 4px;
         right: 4px;
         background: rgba(0, 0, 0, 0.5);
         color: white;
         border-radius: 50%;
         width: 20px;
         height: 20px;
         display: flex;
         align-items: center;
         justify-content: center;
         cursor: pointer;
         font-size: 12px;
         transition: all 0.2s ease;
     }

     .image-preview-item .remove-image:hover {
         background: rgba(0, 0, 0, 0.7);
         transform: scale(1.1);
     }

     .image-preview-item .file-preview {
         display: flex;
         justify-content: center;
         align-items: center;
         background-color: #f8f9fa;
         font-size: 2rem;
         color: #6c757d;
         width: 100%;
         height: 100%;
     }

     /* Attachment info styles */
     .attachment-info {
         position: absolute;
         bottom: 0;
         left: 0;
         right: 0;
         padding: 4px;
         background-color: rgba(255, 255, 255, 0.8);
         font-size: 10px;
         text-align: center;
     }

     .attachment-name {
         display: block;
         white-space: nowrap;
         overflow: hidden;
         text-overflow: ellipsis;
     }

     /* Button styles */
     #reply-upload-button {
         transition: all 0.2s ease;
     }

     #reply-upload-button:hover {
         transform: translateY(-1px);
     }

     /* Ticket attachments container */
     .ticket-attachments {
         margin-top: 15px;
         padding: 10px;
         background-color: rgba(248, 249, 250, 0.5);
         border-radius: 8px;
     }

     .ticket-attachments h6 {
         font-size: 0.9rem;
         color: #495057;
         margin-bottom: 10px;
     }
 </style>
<?php
// Function to render attachments
function renderAttachments($attachments) {
    if (empty($attachments)) {
        return '';
    }
    
    $s3Service = new \App\Libraries\AwsS3Service();
    $html = '<div class="ticket-attachments my-3">';
    $html .= '<h6>Tệp đính kèm:</h6>';
    $html .= '<div class="d-flex flex-wrap gap-2">';
    
    foreach ($attachments as $attachment) {
        $presignedUrl = $s3Service->getPresignedUrl($attachment->s3_key);
        $html .= '<a href="' . esc($presignedUrl) . '" data-fancybox="gallery" data-caption="' . esc($attachment->file_name) . '" class="image-preview-item">';
        if (strpos($attachment->file_type, 'image/') === 0) {
            $html .= '<img src="' . esc($presignedUrl) . '" alt="' . esc($attachment->file_name) . '" class="img-fluid">';
        } else {
            $html .= '<div class="file-preview"><i class="bi bi-file-earmark"></i></div>';
        }
        $html .= '<div class="attachment-info">';
        $html .= '<span class="attachment-name">' . esc($attachment->file_name) . '</span>';
        $html .= '</div>';
        $html .= '</a>';
    }
    
    $html .= '</div></div>';
    
    return $html;
}

$ticketAttachmentModel = model(\App\Models\TicketAttachmentModel::class);
$ticketAttachments = $ticketAttachmentModel->getTicketAttachments($ticket_details->id);

if (!empty($ticket_replies)) {
    foreach ($ticket_replies as $reply) {
        $reply->attachments = $ticketAttachmentModel->getReplyAttachments($reply->id);
    }
}
?>
 <main class="content">
     <div class="container-fluid">

         <div>
             <!-- Breadcrumb -->
             <nav aria-label="breadcrumb">
                 <ol class="breadcrumb breadcrumb-light mb-0">
                     <li class="breadcrumb-item"><a href="<?php echo base_url('ticket'); ?>">Hỗ trợ</a></li>
                     <li class="breadcrumb-item active" aria-current="page">Yêu cầu số # <?= esc($ticket_details->id); ?>
                     </li>

                 </ol>
             </nav>
             <!-- End Breadcrumb -->
         </div>

         <div class="row mt-2">

             <?php include(APPPATH . 'Views/ticket/inc_left_menu.php'); ?>


             <div class="col-md-9 col-xl-10">


                 <div class="col-12">
                     <div class="card">
                         <div class="card-body">


                             <div class="btn-group btn-group-sm">
                                 <button type="button" class="btn btn-outline-info dropdown-toggle"
                                     data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Set Ticket
                                     Status</button>
                                 <div class="dropdown-menu">
                                     <a class="dropdown-item" onclick="set_ticket_status('Open')">Open</a>
                                     <a class="dropdown-item" onclick="set_ticket_status('Answered')">Answered</a>
                                     <a class="dropdown-item" onclick="set_ticket_status('InProgress')">In-Progress</a>
                                     <a class="dropdown-item" onclick="set_ticket_status('OnHold')">On-Hold</a>
                                     <a class="dropdown-item" onclick="set_ticket_status('Closed')">Closed</a>
                                 </div>
                             </div>


                         </div>
                     </div>
                 </div>


                 <div class="row">



                     <div class="col-md-8">
                         <div class="card">
                             <div class="card-body">
                                 <h5 class="card-title"><i class="bi bi-envelope me-2"></i> <?= esc($ticket_details->subject); ?>
                                 </h5>
                                 <?= get_ticket_status_badge($ticket_details->status); ?> <small class="text-muted ms-2">Tạo lúc:
                                     <?= esc(date("d/m/Y H:i:s", strtotime($ticket_details->created_at))); ?></small>
                                 <hr>

                                 <?php if ($ticket_details->status != "Closed") { ?>

                                     <?php echo form_open('/ticket/reply', "id='ticket_reply_form' class='needs-validation ticket_reply_form' novalidate"); ?>
                                     <?php echo csrf_field(); ?>
                                     <input type="hidden" name="ticket_id" value="<?= esc($ticket_details->id); ?>">
                                     <input type="hidden" name="company_id" value="<?= esc($ticket_details->company_id); ?>">

                                     <div class="mt-3 mx-auto">


                                         <div class="mb-3">
                                             <label for="exampleFormControlInput2" class="form-label">Trả lời:</label>
                                             <textarea name="body" class="form-control" id="exampleFormControlInput2" placeholder=""
                                                 value="" required="" rows="5"></textarea>
                                         </div>
                                         <div class="mb-3">
                                             <label class="form-label d-flex justify-content-between align-items-center">
                                                 <span>Hình ảnh đính kèm</span>
                                                 <small class="text-muted">
                                                    Hỗ trợ: JPG, JPEG, PNG, GIF, WEBP (Tối đa <?= (new \Config\AwsS3())->maxFileSize / (1024 * 1024) ?>MB/file)
                                                </small>
                                             </label>
                                             <div class="upload-area border rounded p-3">
                                                 <div class="d-flex flex-wrap gap-2 mb-2" id="reply-image-preview"></div>
                                                 <div class="d-flex align-items-center justify-content-center">
                                                     <button type="button" class="btn btn-outline-primary btn-sm" id="reply-upload-button">
                                                         <i class="bi bi-image me-1"></i>Thêm hình ảnh
                                                     </button>
                                                 </div>
                                                 <input type="file" id="reply-file-input" multiple accept="image/jpg, image/jpeg, image/png, image/gif, image/webp" class="d-none">
                                             </div>
                                         </div>
                                     </div>

                                     <div class="mb-3 text-end mt-3">

                                         <button type="submit" name="btn_submit" class="btn btn-primary btn-save">Gửi đi</button>
                                     </div>

                                     <?php echo form_close(); ?>
                                     <hr>

                                 <?php } ?>



                                 <?php foreach ($ticket_replies as $reply) { ?>

                                     <div class="d-flex align-items-start">
                                         <img src="<?php echo esc(get_gravatar(esc($reply->email), 32)); ?>" width="56" height="56"
                                             class="rounded-circle me-3">
                                         <div class="flex-grow-1 text-break">
                                             <small class="float-end"><?php echo timespan($reply->created_at, 1); ?></small>
                                             <p class="mb-2"><strong><?= esc($reply->name); ?>
                                                     <?php if ($reply->owner_type == "Admin") { ?> <span class="badge bg-danger">
                                                             Admin</span> <?php } else {  ?> <span class="badge bg-info">
                                                             Customer</span> <?php } ?></strong></p>
                                             <p>
                                                 <?= nl2br(esc($reply->body)); ?>
                                             </p>
                                             <small
                                                 class="text-muted"><?= esc(date("d/m/Y H:i:s", strtotime($reply->created_at))); ?></small><br>
                                         </div>
                                     </div>
                                     <!-- Add the reply attachments -->
                                     <?= renderAttachments($reply->attachments) ?>

                                     <hr>

                                 <?php } ?>
                                 <div class="d-flex align-items-start">
                                     <img src="<?php echo esc(get_gravatar(esc($ticket_details->email), 32)); ?>" width="56"
                                         height="56" class="rounded-circle me-3">
                                     <div class="flex-grow-1 text-break">
                                         <small class="float-end"><?php echo timespan($ticket_details->created_at, 1); ?></small>
                                         <p class="mb-2"><strong><?= esc($ticket_details->name); ?></strong>
                                             <?php if ($ticket_details->owner_type == "Admin") { ?> <span
                                                     class="badge bg-danger"> Admin</span> <?php } else {  ?> <span
                                                     class="badge bg-info"> Customer</span> <?php } ?></p>
                                         <p>
                                             <?= nl2br(esc($ticket_details->body)); ?>
                                         </p>
                                         <small
                                             class="text-muted"><?= esc(date("d/m/Y H:i:s", strtotime($ticket_details->created_at))); ?></small><br>
                                     </div>
                                 </div>

                                 <!-- Add the ticket attachments -->
                                 <?= renderAttachments($ticketAttachments) ?>
                                 <hr>



                             </div>

                         </div>
                     </div>
                     <div class="col-md-4">
                         <div class="card">
                             <div class="card-body">

                                 <h5 class="card-title">Thông tin khách hàng</h5>

                                 <table class="table">
                                     <tbody>
                                         <tr>
                                             <td>Tên công ty</td>
                                             <td class="fw-bold"><a
                                                     href="<?= base_url('company/details/' . esc($company_details->id)); ?>">#<?= esc($company_details->id . ' - ' . $company_details->full_name); ?></a>
                                             </td>
                                         </tr>
                                         <?php if (is_object($user_details)) { ?>

                                             <tr>
                                                 <td>Tên khách hàng</td>
                                                 <td class="fw-bold">
                                                     <?= esc($user_details->lastname . ' ' . $user_details->firstname); ?></td>
                                             </tr>
                                             <tr>
                                                 <td>Điện thoại</td>
                                                 <td class="fw-bold"><?= esc($user_details->phonenumber); ?></td>
                                             </tr>
                                             <tr>
                                                 <td>Email</td>
                                                 <td class="fw-bold"> <?= esc($user_details->email); ?></td>
                                             </tr>

                                         <?php } ?>
                                         <tr>
                                             <td>Trả lời cuối</td>
                                             <td class="fw-bold"> <?= esc($ticket_details->lastreply); ?></td>
                                         </tr>

                                     </tbody>
                                 </table>


                             </div>
                         </div>
                     </div>
                 </div>

             </div>
         </div>
     </div>
 </main>

 <?php include(APPPATH . 'Views/templates/sepay/inc_footer.php'); ?>


 </div>
 </div>



 <script src="<?php echo base_url(); ?>/assets/js/bootstrap.bundle.min.js"></script>

 <script src="<?php echo base_url(); ?>/assets/js/jquery-3.5.1.js"></script>
 <script src="<?php echo base_url(); ?>/assets/js/jquery.validate.min.js"></script>
 <script src="<?php echo base_url(); ?>/assets/js/additional-methods.min.js"></script>
 <script src="<?php echo base_url(); ?>/assets/js/jquery.dataTables.min.js"></script>
 <script src="<?php echo base_url(); ?>/assets/js/dataTables.bootstrap5.min.js"></script>
 <script src="<?php echo base_url(); ?>/assets/js/dataTables.rowReorder.min.js"></script>
 <script src="<?php echo base_url(); ?>/assets/js/dataTables.responsive.min.js"></script>
 <script src="<?php echo base_url(); ?>/assets/fancyapps/fancybox.umd.js"></script>

 <script src="<?php echo base_url(); ?>/assets/notyf/notyf.min.js"></script>

 <script src="<?php echo base_url(); ?>/assets/js/app.js?v=1"></script>


 <script>
     function set_ticket_status(status) {

         url = "<?php echo base_url('ticket/ajax_set_status'); ?>";
         post_data = {
             ticket_id: <?= esc($ticket_details->id); ?>,
             status: status,
             <?php echo csrf_token() ?>: "<?php echo csrf_hash() ?>"
         };

         $.ajax({
             url: url,
             type: "POST",
             data: post_data,
             dataType: "JSON",
             success: function(data) {


                 if (data.status == true) {

                     location.reload();
                 } else {
                     alert('Lỗi: ' + data.message);
                 }

             },
             error: function(jqXHR, textStatus, errorThrown) {
                 alert('Có lỗi xảy ra! Vui lòng ấn F5 và thử lại');

             }

         });
     }
 </script>

 <script>
     $(document).ready(function() {
        // Initialize Fancybox
        Fancybox.bind("[data-fancybox]", {
            // Options can be added here
        });

         const uploadButton = $('#reply-upload-button');
         const fileInput = $('#reply-file-input');
         const imagePreview = $('#reply-image-preview');
         const maxFileSize = <?= (new \Config\AwsS3())->maxFileSize ?>;
         const maxFiles = <?= (new \Config\AwsS3())->maxFiles ?>;
         let uploadedFiles = [];

         uploadButton.click(() => fileInput.click());

         fileInput.on('change', function(e) {
             const files = Array.from(e.target.files);

             if (uploadedFiles.length + files.length > maxFiles) {
                 notyf.error(`Bạn chỉ có thể tải lên tối đa ${maxFiles} ảnh`);
                 return;
             }

             files.forEach(file => {
                if (!file.type.match(/image\/(jpg|jpeg|png|gif|webp)/)) {
                    notyf.error('Chỉ chấp nhận file hình ảnh (jpg, jpeg, png, gif, webp)');
                    return;
                }

                 if (file.size > maxFileSize) {
                     notyf.error('File không được vượt quá 2MB');
                     return;
                 }

                 const reader = new FileReader();
                 reader.onload = function(e) {
                     const preview = `
                    <div class="image-preview-item">
                        <img src="${e.target.result}" alt="preview">
                        <div class="remove-image">
                            <i class="bi bi-x"></i>
                        </div>
                    </div>
                `;
                     imagePreview.append(preview);
                     uploadedFiles.push(file);
                 }
                 reader.readAsDataURL(file);
             });
         });

         $(document).on('click', '.remove-image', function() {
             const index = $(this).parent().index();
             uploadedFiles.splice(index, 1);
             $(this).parent().remove();
         });

         // Form validation rules
         $('#ticket_reply_form').validate({
             ignore: [],
             rules: {
                 body: {
                     required: true,
                     minlength: 5,
                     maxlength: 5000
                 },
             },
             messages: {
                 body: {
                     required: "Vui lòng nhập nội dung phản hồi",
                     minlength: "Nội dung phản hồi phải có ít nhất {0} ký tự",
                     maxlength: "Nội dung phản hồi không được vượt quá {0} ký tự"
                 },
             },
             errorElement: 'span',
             errorPlacement: function(error, element) {
                 error.addClass('invalid-feedback');
                 if (element.prop('type') === 'file') {
                     error.insertAfter(element.parent().parent());
                 } else {
                     error.insertAfter(element);
                 }
             },
             highlight: function(element, errorClass, validClass) {
                 $(element).addClass('is-invalid').removeClass('is-valid');
             },
             unhighlight: function(element, errorClass, validClass) {
                 $(element).removeClass('is-invalid').addClass('is-valid');
             },
             submitHandler: function(form) {
                 const formData = new FormData(form);
                 uploadedFiles.forEach((file, index) => {
                     formData.append('images[]', file, file.name);
                 });
                 const replyBtn = $(form).find('button[type="submit"]');

                 replyBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang gửi...');

                 $.ajax({
                     url: "<?php echo base_url('ticket/ajax_reply'); ?>",
                     type: 'POST',
                     data: formData,
                     processData: false,
                     contentType: false,
                     dataType: 'JSON',
                     success: function(response) {
                         if (response.status) {
                             notyf.success('Đã gửi phản hồi thành công');
                             setTimeout(function() {
                                 location.reload();
                             }, 1000);
                         } else {
                             notyf.error(response.message);
                             replyBtn.prop('disabled', false).text('Gửi phản hồi');
                         }
                     },
                     error: function() {
                         notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                         replyBtn.prop('disabled', false).text('Gửi phản hồi');
                     }
                 });
             }
         });

         $('#reply-file-input').on('change', function() {
             $(this).valid();
         });

     });
 </script>