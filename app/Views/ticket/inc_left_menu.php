<?php 
$uri = service('uri');
 

?>
<div class="col-md-3 col-xl-2">

    <div class="card">

        <div class="list-group list-group-flush">
            <a class="list-group-item list-group-item-action <?php if($status == NULL) echo 'active';?>" href="<?php echo base_url('ticket/');?>"> T<PERSON>t cả <?php if($count_all >0) { ?><span class="float-end">(<?= number_format($count_all); ?>)</span><?php } ?></a>
            <a class="list-group-item list-group-item-action <?php if($status == "Open") echo 'active';?>" href="<?php echo base_url('ticket?status=Open');?>"> Đang mở <?php if($count_open >0) { ?><span class="float-end">(<?= number_format($count_open); ?>)</span><?php } ?></a>

            <a class="list-group-item list-group-item-action <?php if($status == "InProgress") echo 'active';?>" href="<?php echo base_url('ticket?status=InProgress');?>"> Đang xử lý <?php if($count_inprogress >0) { ?><span class="float-end">(<?= number_format($count_inprogress); ?>)</span><?php } ?></a>

            <a class="list-group-item list-group-item-action <?php if($status == "ClientReply") echo 'active';?>" href="<?php echo base_url('ticket?status=ClientReply');?>"> Khách phản hồi <?php if($count_clientreply >0) { ?><span class="float-end">(<?= number_format($count_clientreply); ?>)</span><?php } ?></a>

            <a class="list-group-item list-group-item-action <?php if($status == "Answered") echo 'active';?>" href="<?php echo base_url('ticket?status=Answered');?>"> Đã trả lời <?php if($count_answered >0) { ?><span class="float-end">(<?= number_format($count_answered); ?>)</span><?php } ?></a>

            <a class="list-group-item list-group-item-action <?php if($status == "OnHold") echo 'active';?>" href="<?php echo base_url('ticket?status=OnHold');?>"> On-Hold <?php if($count_onhold >0) { ?><span class="float-end">(<?= number_format($count_onhold); ?>)</span><?php } ?></a>

            <a class="list-group-item list-group-item-action <?php if($status == "Closed") echo 'active';?>" href="<?php echo base_url('ticket?status=Closed');?>"> Đã đóng <?php if($count_closed >0) { ?><span class="float-end">(<?= number_format($count_closed); ?>)</span><?php } ?></a>


        </div>
    </div>
</div>