 <link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
 <link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
 <link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">
 <main class="content">
     <div class="container-fluid">

     <div>
             <!-- Breadcrumb -->
             <nav aria-label="breadcrumb">
                 <ol class="breadcrumb breadcrumb-light mb-0">
                     <li class="breadcrumb-item"><a href="<?php echo base_url('ticket');?>">Hỗ trợ</a></li>
                     <li class="breadcrumb-item active" aria-current="page">Xem da<PERSON> s<PERSON>ch
                     </li>

                 </ol>
             </nav>
             <!-- End Breadcrumb -->
         </div>

                 <div class="row mt-3">
                     <?php include(APPPATH . 'Views/ticket/inc_left_menu.php');?>


                     <div class="col-md-9 col-xl-10">
                     <div class="card mt-3" style="width:100%">
             <div class="card-body">
           
                         <div class="table-responsive">
                             <table
                                 class="table table-hover table-striped table-bordered text-muted align-middle display nowrap"
                                 id="data_table" style="width:100%">
                                 <thead class="table-light text-muted">
                                     <tr class="align-middle">
                                         <th></th>
                                         <th>Tiêu đề</th>
                                         <th>Trạng thái</th>
                                         <th>Người gửi</th>
                                         <th>Tạo lúc</th>

                                     </tr>
                                 </thead>
                                 <tbody>


                                 </tbody>
                             </table>
                             </div>
                     </div>
                         </div>
                     </div>

                 </div>
             

     </div>
 </main>

 <?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>


 </div>
 </div>



 <script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

 <script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
 <script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
 <script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
 <script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
 <script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>

 <script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>

 <script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

 <script>
$(document).ready(function() {

    <?php if($status != NULL)
            echo "var url = '" . base_url('ticket/ajax_list?status=' . $status) . "';";
        else 
            echo "var url = '" . base_url('ticket/ajax_list') . "';";
        ?>  

    var table = $('#data_table').DataTable({



        "processing": true,
        "serverSide": true,
        "order": [],

        "ajax": {
            "url": url,
            "data": {
                "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"
            },
            "type": "POST"
        },


        "language": {
            "sProcessing": "Đang xử lý...",
            "sLengthMenu": "Xem _MENU_ mục",
            "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
            "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
            "sInfoEmpty": "Đang xem 0 đến 0 trong tổng số 0 mục",
            "sInfoFiltered": "(được lọc từ _MAX_ mục)",
            "sInfoPostFix": "",
            "sSearch": "Tìm:",
            "sUrl": "",
            "oPaginate": {
                "sFirst": "Đầu",
                "sPrevious": "Trước",
                "sNext": "Tiếp",
                "sLast": "Cuối"
            }
        },

        "columnDefs": [{
                responsivePriority: 1,
                targets: 1
            },
            {
                responsivePriority: 2,
                targets: 2
            },

            {
                "visible": false,
                "targets": [0],
                "orderable": false,
            },


        ],


    });

    table.on('init', function() {
        $('*[type="search"][class="form-control form-control-sm"]').attr('style', 'max-width:120px');
        $('div.dataTables_filter').parent().attr('class', 'col-6');
        $('div.dataTables_length').parent().attr('class', 'col-6');
    });


});
 </script>