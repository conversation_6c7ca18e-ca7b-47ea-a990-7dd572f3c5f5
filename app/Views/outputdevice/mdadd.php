<div class="modal fade" id="addDeviceModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="addDeviceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addDeviceModalLabel">Thêm thiết bị mới</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addDeviceForm" action="javascript:;" class="needs-validation" novalidate method="post" accept-charset="utf-8">
                <div class="modal-body">
                    <fieldset class="border p-3 mt-3">
                    <div class="mb-3 form-group">
                            <label for="name" class="form-label"><b>Tên thiết bị<span class="text-danger">*</span></b></label>
                            <input type="text" class="form-control" id="name" name="name" value="DF" placeholder="Nhập Tên" >
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="mb-3 form-group info-server-mqtt">
                            <label for="imei" class="form-label"><b>IMEI <span class="text-danger"></span></b></label>
                            <input type="text" class="form-control" id="imei" name="imei" placeholder="Nhập mã IMEI" >
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="mb-3 form-group">
                            <label for="serial_number" class="form-label"><b>Serial Number <span class="text-danger">*</span></b></label>
                            <div class="d-flex">

                                <input type="text" class="form-control" id="serial_number" name="serial_number" placeholder="Nhập số serial" >
                                <button class="btn btn-success d-flex align-items-center ms-2 openScanner" style="width:120px;" type="button">
                                    <div class="spinner-border text-light loader me-1" style="width: 14px; height: 14px; display: none;" role="status"></div>
                                    <i class="bi bi-qr-code-scan me-1"></i> Quét QR
                                </button>
                            </div>
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <div class="mb-3 form-group">
                            <label for="phone_serial_sim" class="form-label"><b>Thuê bao SIM</b></label>
                            <input type="text" class="form-control" id="phone_serial_sim" name="phone_serial_sim" placeholder="Nhập số thuê bao SIM" >
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="mb-3 form-group">
                            <label for="bank_id" class="form-label"><b>Ngân hàng thiết bị <span class="text-danger">*</span></b></label>
                            <select class="form-control" id="bank_id" name="bank_id">
                                <option value="" selected>Không chọn </option>
                                <?php if(!empty($list_bank)) { ?>
                                    <?php foreach($list_bank as $val){ ?>
                                        <option value="<?= $val['id']?>"> <?= esc($val['brand_name'])?></option>
                                    <?php } ?>
                                <?php } ?>
                                
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="mb-3 form-group">
                            <label for="model" class="form-label"><b>Model thiết bị <span class="text-danger">*</span></b></label>
                            <select class="form-control" id="model" name="model" required>
                                <option value="" disabled selected>Chọn model thiết bị</option>
                                <?php if(!empty($list_model)) { ?>
                                    <?php foreach($list_model as $val){ ?>
                                        <option value="<?= $val['model']?>" <?= $val['model']=="S003" ?'selected':'' ?>><?= $val['model']?></option>
                                    <?php } ?>
                                <?php } ?>
                                
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>

                       

                        <div class="mb-3 form-group">
                            <label for="vendor" class="form-label"><b>Vendor thiết bị <span class="text-danger">*</span></b></label>
                            <select class="form-control" id="vendor" name="vendor" required>
                                <option value="" disabled selected>Chọn vendor thiết bị</option>
                                <option value="Flametechvn">Flametechvn</option>
                                <option value="Aisino">Aisino</option>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="mb-3 form-group info-server-mqtt">
                            <label for="mqtt_server_id" class="form-label"><b>Server MQTT <span class="text-danger">*</span></b></label>
                            <select class="form-select" id="mqtt_server_id" name="mqtt_server_id" >
                                <option data-user-mqtt="" value="" disabled selected>Chọn server MQTT</option>
                                <?php if(!empty($data_server)) { ?>
                                    <?php foreach($data_server as $val){ ?>
                                        <option data-user-mqtt="<?= esc(json_encode($val['user_mqtt']??""),"attr") ?>" value="<?= $val['id']?>"><?= $val['hostname']?> - <?= $val['port']?> - <?= $val['username_server']??""?></option>
                                    <?php } ?>
                                <?php } ?>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="mb-3 form-group info-server-mqtt">
                            <label for="mqtt_client_id" class="form-label"><b>User MQTT <span class="text-danger">*</span></b></label>
                            <select class="form-select" id="mqtt_client_id" name="mqtt_client_id" >
                                
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                    </fieldset>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary me-auto" data-bs-dismiss="modal">Đóng</button>
                    <button type="submit" class="btn btn-primary" id="saveDeviceButton">
                        <div class="spinner-border text-light loader me-2" style="width: 20px; height: 20px; display: none;" role="status"></div>
                        Tạo thiết bị
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
