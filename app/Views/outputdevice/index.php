<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/css/responsive.dataTables.min.css">

<meta name="csrf-token" content="<?= csrf_hash() ?>">
<link rel="stylesheet" href="<?php echo base_url();?>/assets/select2/select2.min.css">
<link rel="stylesheet" href="/assets/sweealert2/sweealert2.min.css">
<script src="/assets/sweealert2/sweealert2.min.js"></script>
<!-- Initialize Bootstrap tooltips -->
 <style>
    .active-btn {
    background-color: var(--bs-btn-active-bg);
    border-color: var(--bs-btn-active-border-color);
    color: var(--bs-btn-active-color);
}
.select2-container {
    width: 100% !important; /* Đặt chiều rộng thành 100% */
}

.select2-container--default .select2-selection--single {
    height: 38px; /* Chiều cao của Select2 */
    border: 1px solid #ced4da; /* Biên giống như Bootstrap */
    border-radius: 0.375rem; /* Đường viền bo góc giống Bootstrap */
    padding: 0.375rem 0.75rem; /* Padding giống Bootstrap */
}
.select2-container--default .select2-search--dropdown .select2-search__field {
    background: #fff;
    color: black;

}
:focus-visible {
    outline: none; /* Bỏ hiệu ứng outline */
}
video {
        width: 100%;
        height: 100%;
        display: block;
    }
 </style>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        var tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(function (tooltip) {
            new bootstrap.Tooltip(tooltip);
        });
    });
</script>
<main class="content">
   <div class="container-fluid">
      <div class="card mt-3" style="width:100%">
         <div class="card-body">
            <div class="row">
               <div class="col-auto d-none d-sm-block">
                  <h3>Danh sách thiết bị</h3>
               </div>
               <div class="col-auto ms-auto text-end d-flex flex-wrap flex-sm-nowrap flex-column flex-md-row gap-2 mb-2">
                    <div class="dropdown d-inline-block">
                        <button class="btn pb-0 btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            📡 Theo dõi thiết bị
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="javascript:;" onclick="checkConnection('2')">
                                    <i class="bi-wifi me-2 text-muted"></i> Kiểm tra kết nối tất cả thiết bị Flametech <span class="count-all-flametech fw-bold text-danger"></span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="javascript:;" onclick="checkConnection('1')">
                                    <i class="bi-wifi me-2 text-muted"></i> Kiểm tra kết nối thiết bị Flametech đã liên kết <span class="count-approved-flametech fw-bold text-danger"></span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="javascript:;" onclick="checkConnection('0')">
                                    <i class="bi-wifi me-2 text-muted"></i> Kiểm tra kết nối thiết bị Flametech chưa liên kết <span class="count-pending-flametech fw-bold text-danger"></span>
                                </a>
                            </li>
                            
                        </ul>
                    </div>
                    <div>
                        <a href="javascript:;" data-bs-toggle="modal" data-bs-target="#importDeviceModal" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus"></i> Import Flametechvn
                        </a>   
                    </div>
                    <div>
                        <a href="javascript:;" data-bs-toggle="modal" data-bs-target="#ExportModal" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus"></i> Lọc thiết bị
                        </a>   
                    </div>
                    <div>
                        <a href="javascript:;" data-bs-toggle="modal" data-bs-target="#addDeviceModal" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus"></i> Thêm thiết bị
                        </a>   
                    </div>
                </div>

            </div>
            <div class="row">
               <div class="filter-item mb-3">
                  <button class="btn btn-filter btn-outline-primary mb-2"  onclick="filterData('All')">
                  Tất cả
                  <span class="badge bg-danger count-all" >0</span>
                  </button>
                  <button class="btn btn-filter  btn-outline-primary mb-2" onclick="filterData('Đã liên kết')">
                  Đã liên kết
                  <span class="badge bg-danger count-approved">0</span>
                  </button>
                  <button class="btn btn-filter  btn-outline-primary mb-2"  onclick="filterData('Chưa liên kết')">
                  Chưa liên kết
                  <span class="badge bg-danger count-pending" >0</span>
                  </button>
                 
               </div>
               <div class="">
                  <table class="table dt-responsive table-hover table-striped table-bordered text-muted align-middle display nowrap" id="partner_table" style="width:100%">
                     <thead class="table-light text-muted">
                        <tr class="align-middle">
                        </tr>
                     </thead>
                     <tbody>
                     </tbody>
                  </table>
               </div>
            </div>
         </div>
      </div>
   </div>
</main>
<?php include(APPPATH . 'Views/templates/sepay/inc_footer.php');?>
<?php include(APPPATH . 'Views/outputdevice/mdadd.php');?>
<?php include(APPPATH . 'Views/outputdevice/mdedit.php');?>
<?php include(APPPATH . 'Views/outputdevice/mdimport.php');?>
<?php include(APPPATH . 'Views/outputdevice/mdexport.php');?>
<?php include(APPPATH . 'Views/outputdevice/mdintegration.php');?>

<!-- Modal Bootstrap -->
<div class="modal fade" id="qrModal" tabindex="-1" aria-labelledby="qrModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            <div class="modal-body p-0">
                <video id="scanner-container" class="w-100 h-100" autoplay playsinline></video>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="checkDeviceModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="addDeviceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="checkDeviceModalLabel">Kiểm tra thiết bị Flametechvn</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                    <div class="loading-animate my-2" style="display: none;">
                        <div class="d-flex justify-content-center bp-2">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                    <div class="text-info alert-check-device"></div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary ms-auto" data-bs-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>
</div>

</div>
</div>
<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

<script src="<?= base_url(); ?>assets/js/jquery.validate.min.js"></script>
<script src="<?= base_url(); ?>assets/js/jquery.validate.additional-methods.min.js"></script>

<script src="<?= base_url(); ?>assets/xlsx/xlsx.min.js"></script>

<script src="<?= base_url() ?>/assets/zxing/index.min.js"></script>

<script src="<?php echo base_url();?>/assets/select2/select2.min.js"></script>

<script>

$(function() {
        let codeReader;
        let previousQRCode = '';
        const startScanner = () => {
            codeReader = new ZXing.BrowserMultiFormatReader();
            codeReader.getVideoInputDevices()
                .then((devices) => {
                    if (devices.length === 0) {
                        $("#errorMsg").text("Không tìm thấy camera.");
                        return;
                    }

                    let backCamera = devices.find(device => device.label.toLowerCase().includes("back")) || devices[0];

                    return codeReader.decodeFromVideoDevice(backCamera.deviceId, "scanner-container", (result, err) => {
                        if (result && result.text !== previousQRCode) {
                            previousQRCode = result.text;
                            $("input[name='serial_number']").val(previousQRCode);
                            $("input[name='serial_number_ex']").val(previousQRCode);
                            $("#qrModal").modal("hide");
                            if ($('#ExportModal').hasClass('show')) {
                            console.log('Modal đang mở');
                            $("input[name='serial_sim_ex']").val("");
                            $("input[name='serial_number_ex']").val("");
                            let table_data_list = table.ajax.json().data;

                            // Sử dụng hàm filter đúng cách
                            let data_device_scan = table_data_list.filter(function(data) {
                                return data.serial_number == previousQRCode;
                            });

                            if (data_device_scan.length > 0) {
                             

                                $("input[name='serial_number_ex']").val(data_device_scan[0].serial_number);
                                $("input[name='serial_sim_ex']").val(data_device_scan[0].serial_sim);


                            } else {
                                $("input[name='serial_number_ex']").val("");
                                $("input[name='serial_sim_ex']").val("");
                                console.log("Thiết bị không tồn tại");
                                notyf.error({
                                    message:"Thiết bị không có trong hệ thống!"
                                })
                            }
                        } else {
                            $("input[name='serial_number_ex']").val("");
                            $("input[name='serial_sim_ex']").val("");
                            console.log('Modal đang đóng');
                        }


                        }
                    });
                })
                .catch((err) => {
                    notyf.error("Hãy cấp quyền camera trên trình duyệt");
                    console.error("Lỗi camera:", err);
                });
        }

        $(".openScanner").click(function() {
            $("input[name='serial_number']").val("");
            $("#qrModal").modal("show");
            startScanner();
        });

        $("#qrModal").on("hidden.bs.modal", function() {
            if (codeReader) codeReader.reset();
            previousQRCode = "";
        });

    });

   function checkConnection(type){
    $(".alert-check-device").html(`
        <div class="text-info">Đang kiểm tra kết nối...</div>
    `);
    $("#checkDeviceModal").modal("show");
    $(".loading-animate").show();
    $.ajax({
            url: "/outputdevice/check_connection",
            type: "POST",
            data: {vendor:"Flametechvn",'type':type},
            success: function (res) {
                if (res.code == 200) {
                    let statusText = "";
                    if (type == 0) {
                        statusText = "chưa liên kết";
                    } else if (type == 1) {
                        statusText = "đã liên kết";
                    } else if (type == 2) {
                        statusText = "trong cơ sở dữ liệu";
                    }
                    $(".alert-check-device").html(`
                       
                        - Có tất cả <b>${res.data.total_device}</b> thiết bị ${statusText}.</br>
                        - Có <b>${res.data.count_active}</b> thiết bị kết nối thành công.</br>
                        - Có <b>${res.data.count_disable}</b> thiết bị kết nối thất bại.</br>
                        <a href="#" class="text-danger" id="download-disable-connect">Tải xuống danh sách kết nối thất bại</a>
                    `);


                    // Xử lý tải file danh sách thiết bị không kích hoạt
                    $("#download-disable-connect").on("click", function (e) {
                        e.preventDefault();
                        if ($(this).hasClass("disabled")) return;

                        console.log("Danh sách thiết bị mất kết nối:", res.data.data_disable);
                        exportToExcel(res.data.data_disable, "Danh_sach_thiet_bi_ket_noi_that_bai.xlsx");
                        $(this).addClass("disabled").text("Đã tải xong");
                    });
                }
            },
            error: function (err) {
                let message = err.responseJSON?.message || "Lỗi hệ thống!";
                notyf.error({ message: message });
                $("#checkDeviceModal").modal("hide");

            },
            complete: function (xhr) {
                let newCsrfToken = xhr.getResponseHeader("X-CSRF-TOKEN");
                if (newCsrfToken) {
                    updateCsrfToken(newCsrfToken);
                }
                $(".loading-animate").hide();
            }
        });
   }
   
   

   function showConfirmation(title,text) {
        return Swal.fire({
            title: title,
            text: text,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Đồng ý",
            cancelButtonText: "Trở lại",
            customClass: {
            popup: 'small-swal-popup',  // Class tùy chỉnh
            title: 'small-swal-title',
            icon: 'small-swal-icon',
            content: 'small-swal-content',
            confirmButton: 'small-swal-confirm-button',
            cancelButton: 'small-swal-cancel-button'
        }
        });
    }

   let table;
   let list_data_export = [];
   $(document).ready(function() {

    $("#addfileEx").click(function () {
        let serial_sim_ex = $("#serial_sim_ex").val().trim();
        let serial_number_ex = $("#serial_number_ex").val().trim();

        if (serial_number_ex !== "" && serial_sim_ex !== "") {

            // Kiểm tra trùng serial_number
            let exists = list_data_export.some(item => item.serial_numer === serial_number_ex);
            
            if (exists) {
                notyf.error({
                    message: "Thiết bị đã tồn tại trong danh sách!",
                });
                return; // Dừng không push nữa
            }

            // Nếu không trùng thì thêm vào
            list_data_export.push({
                "serial_number": serial_number_ex,
                "serial_sim": serial_sim_ex
            });

            $(".count_device_ex").text(list_data_export.length);

            notyf.success({
                message: "Đã thêm thành công!",
            });

            console.log("data list : ", list_data_export);

            // Reset input
            $("#serial_sim_ex").val("");
            $("#serial_number_ex").val("");

        } else {
            notyf.error({
                message: "Không thấy thông tin!",
            });
        }
    });

        $("#exportDataExcel").click(function(){
            if(list_data_export.length == 0){
                notyf.error({
                    message:"Danh sách file đang rỗng!"
                })
                return;
            }else{
                notyf.success({
                    message:"Xuất file thành công!"
                })
                console.log("data_excel ", list_data_export);
                exportToExcel(list_data_export,"file_sim_chua_kich_hoat");
                list_data_export = [];
                $(".count_device_ex").text(list_data_export.length);
                
            }
        })

        $("#vendor").val("Flametechvn").trigger('click');
        $(".info-server-mqtt").addClass("d-none");

       table = $('#partner_table').DataTable({
           paging: true,
           scrollX: false,
           autoWidth: false,
           language: {
               processing: "Message khi đang tải dữ liệu",
               search: "Tìm kiếm",
               lengthMenu: "Hiển thị _MENU_",
               info: "Hiển thị trang _PAGE_ / _PAGES_",
               infoEmpty: "Hiển thị trang _PAGE_ / _PAGES_ / _TOTAL_",
               loadingRecords: "",
               infoFiltered: "(Được lọc trong  _MAX_ record dữ liệu / _TOTAL_)",
               zeroRecords: "Không tìm kiếm được giá trị",
               emptyTable: "Không có dữ liệu",
               paginate: {
                   first: " Đầu",
                   previous: " Trước",
                   next: " Sau",
                   last: " Cuối",
               },
           },
        ajax: {
            url: `/outputdevice/all-data`,
            method: 'GET',
            dataSrc: "data",
            
        },
           columns: [{
                   data: "id",
                   title: "short",
                   className: "d-none"
               },
              
               {
                   data: null,
                   title: "Thông tin thiết bị",
                   render: function(data, type, row, meta) {
                       
                    return `
                    <div class="d-flex flex-column" style="cursor: default">
                        <span class="fw-bold">ID: <span class="text-primary">${row.id.replace(/[<>]/g, "")}</span></span>
                        <span class="fw-bold">Tên: <span class="text-primary">${row.name.replace(/[<>]/g, "")}</span></span>
                        <span class="fw-bold">Serial Number: <span class="text-primary">${row.serial_number.replace(/[<>]/g, "")}</span></span>
                        <span class="fw-bold ${row.serial_sim ? '' : ''}">Seri Sim: <span class="text-primary">${row.serial_sim||NaN}</span></span>
                        <span class="fw-bold ${row.phone_serial_sim ? '' : ''}">Thuê bao: <span class="text-primary">${row.phone_serial_sim ||NaN}</span></span>
                    </div>`;
                   }
               },
               {
                   data: null,
                   title: "Định danh thiết bị",
                   render: function(data, type, row, meta) {
                       
                    return `
                    <div class="d-flex flex-column">
                        <span class="fw-bold">Model: <span class="text-primary">${row.model.replace(/[<>]/g, "")}</span></span>
                        <span class="fw-bold">Vendor: <span class="text-primary">${row.vendor.replace(/[<>]/g, "")}</span></span>
                        <span class="fw-bold">Ngân hàng: 
                            <span class="text-primary">
                                ${row.brand_name ? row.brand_name : (row.bank_id > 0 ? "Bank ID không hợp lệ" : "Không chọn")}
                            </span>
                        </span>
                    </div>`;

                   }
               },
               {
                    data: null,
                    title: "Trạng thái",
                    render: function(data, type, row, meta) {
                        let statusBadge = `<span class="${row.company_id > 0? "badge bg-success" : "badge bg-danger"} ">
                                            ${row.integration_status_text.replace(/[<>]/g, "")}
                                        </span>`;
                        let activeBadge = row.active == 0 
                            ? `<span class="badge bg-warning mt-1 ">Tạm ngưng</span>` 
                            : `<span class="badge bg-primary mt-1 ">Hoạt động</span>`;

                        return `${statusBadge} </br> ${activeBadge} </br><span class="fw-bold">Ngày kích hoạt: <span class="text-primary">${row.active_date || NaN}</span></span>`;
                    }
                },

               {
                    data: null,
                    title: "Thông tin MQTT",
                    className: "d-none",
                    render: function(data, type, row, meta) {
                        if (row.vendor === "Aisino") {
                            return `
                            <div class="d-flex flex-column">
                                <span class="fw-bold">Tên: <span class="text-primary">${row.hostname?.replace(/[<>]/g, "")}</span></span>
                                <span class="fw-bold">Port: <span class="text-primary">${row.port?.replace(/[<>]/g, "")}</span></span>
                                <span class="fw-bold">Username Server: <span class="text-primary">${row.username_server?.replace(/[<>]/g, "")}</span></span>
                                <span class="fw-bold">Username Client: <span class="text-primary">${row.username_client?.replace(/[<>]/g, "")}</span></span>
                            </div>`;
                        } else {
                            return '<span class="text-muted">Không có thông tin MQTT</span>';
                        }
                    }
                },


               {
                   data: null,
                   title: "Thông tin công ty",
                   render: function(data, type, row, meta) {
                       
                    if(row.company_id){

                        return `
                        <div class="d-flex flex-column">
                            <span class="fw-bold">Tên: <a href="<?= base_url()?>${'/company/details/' + row.company_id}" class="text-primary">${row.full_name.replace(/[<>]/g, "")}</a></span>
                            <span class="fw-bold">Thương hiệu: <a href="<?= base_url()?>${'/company/details/' + row.company_id}" class="text-primary">${row.short_name.replace(/[<>]/g, "")}</a></span>
                         
                        </span>
                        </div>`;
                    }else{
                        return '<span class="text-muted">Không có thông tin </span>';
                    }
                   }
               },

               {
                   data: null,
                   title: "Cấu hình bộ lọc",
                   className: "d-none",
                   render: function(data, type, row, meta) {
                       
                    return `
                    <div class="d-flex flex-column">
                        <span class="fw-bold">$ max <span class="text-primary">${row.max_amount.replace(/[<>]/g, "")}</span></span>
                        <span class="fw-bold">$ min <span class="text-primary">${row.min_amount.replace(/[<>]/g, "")}</span></span>
                        <span class="fw-bold">Kí tự bắt buộc <span class="text-primary">${row.required_content.replace(/[<>]/g, "")}</span></span>
                    </div>`;
                   }
               },
             
              
               {
                data: null,
                title: "Tác vụ",
                className: "w-fix",
                render: function(data, type, row, meta) {
                    // Tạo nội dung HTML cho ô <td>
                    return `
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-soft-danger btn-sm dropdown bg-danger text-white fw-bold" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi-three-dots-vertical align-middle"></i> <!-- BS5 icon for more options -->
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li class="cursor-pointer" onclick="removeDevice(${row.id})">
                                    <span class="dropdown-item edit-item-btn">
                                        <i class="bi-trash align-bottom me-2 text-muted"></i>Xóa <!-- BS5 trash icon -->
                                    </span>
                                </li>
                                <li class="cursor-pointer ${(!row.company_id && row.vendor == 'Aisino')?"":"d-none"} " onclick="addIntegration(${row.id},'${row.vendor}')">
                                    <span class="dropdown-item edit-item-btn">
                                        <i class="bi bi-link-45deg align-bottom me-2 text-muted"></i>Thêm công ty 
                                    </span>
                                </li>
                                <li class="cursor-pointer ${!row.company_id ?"d-none":""}" onclick="removeIntegration(${row.id})">
                                    <span class="dropdown-item edit-item-btn">
                                        <i class="bi bi-link-45deg align-bottom me-2 text-muted"></i>Gỡ công ty 
                                    </span>
                                </li>
                                <li class="cursor-pointer" onclick="editDevice('${row.id}')">
                                    <span class="dropdown-item edit-item-btn">
                                        <i class="bi-pencil align-bottom me-2 text-muted"></i> Chỉnh sửa <!-- BS5 pencil icon -->
                                    </span>
                                </li>
                                <li class="cursor-pointer ${(row.vendor == 'Flametechvn')?"":"d-none"} " onclick="checkOnlineDevice('${row.serial_number}')">
                                    <span class="dropdown-item edit-item-btn">
                                        <i class="bi bi-toggle-on align-bottom me-2 text-muted"></i>Kiểm tra bật/tắt thiết bị 
                                    </span>
                                </li>
                            </ul>
                        </div>                              
                    `;
                }

            },
           ],
           order: [
               [0, 'desc']
           ], // Sắp xếp theo cột ID từ lớn đến bé
          
       });
          // Cập nhật đếm số bản ghi khi bảng khởi tạo
        table.on('init.dt', function() {
            let data = table.ajax.json().data;
            updateRecordCounts(); 
       
        });

        // render User MQTT

        $("#mqtt_server_id").on("change",function(){
            let selected_option = $(this).find("option:selected");
            let value_user = selected_option.attr("data-user-mqtt");
            
            $("#mqtt_client_id").empty();

            try {
                let mqttArray = JSON.parse(value_user);

                mqttArray.forEach(function (item) {
                    $("#mqtt_client_id").append(`<option value="${item.id}">${item.username}</option>`);
                });
            } catch (error) {
                $("#mqtt_client_id").empty();
                console.error("Invalid JSON format in data-mqtt attribute:", error);
            }
            
        })
        $("#edit_mqtt_server_id").on("change",function(){
            let selected_option = $(this).find("option:selected");
            let value_user = selected_option.attr("data-user-mqtt");
            $("#edit_mqtt_client_id").empty();

            try {
                let mqttArray = JSON.parse(value_user);

                mqttArray.forEach(function (item) {
                    $("#edit_mqtt_client_id").append(`<option value="${item.id}">${item.username}</option>`);
                });
            } catch (error) {
                $("#edit_mqtt_client_id").empty();
                console.error("Invalid JSON format in data-mqtt attribute:", error);
            }
            
        })
       
   });

   function addIntegration(id,vendor){
        $("#integration_output_device_id").val(id)
        $("#device_vendor").val(vendor)
        $("#IntegrationModal").modal("show");
   }
   function checkOnlineDevice(serial_number){
        $.get(`/outputdevice/check_online_device/${serial_number}`, function(res){
            if(res.code == 200){
                notyf.success({
                    message:res.message
                })
            }else if(res.code == 201){
                notyf.error({
                    message:res.message
                })
            }else{
                notyf.error({
                    message:res.message
                })
            }
        })
   }
   async function removeIntegration(id){
        const confirm = await showConfirmation("Bạn có chắc chắn gỡ ?","Mọi thông tin tích hợp sẽ bị xóa!")
        
        if(confirm.isConfirmed){
            $.post(`/outputdevice/delete-integration/${id}`,function(res){
                if(res.code==200){
                    notyf.success({
                        message:"Gỡ tích hợp thành công!"
                    })
                    reloadTable()
                }else{
                    notyf.error({
                        message:"Lỗi gỡ tích hợp!"
                    })
                }
            })
        }
   }
  
   function filterData(str){
       // Loại bỏ class active-btn từ tất cả các nút
       $('.btn-filter').removeClass('active-btn');
   
       // Thêm class active-btn vào nút hiện tại
       $(event.target).addClass("active-btn");
   
       if(str == "All"){
           table.search('').columns().search('').draw();
           return;
       }
       
       if(str == "Đã liên kết"){
           table.column(3).search('Đã liên kết').draw();
           return;
       }
       if(str == "Chưa liên kết"){
        
           table.column(3).search('Chưa liên kết').draw();
           return;
       }
       table.search('').columns().search('').draw();
   }
   

   async function removeDevice(id){
        const confirm = await showConfirmation("Bạn có chắc chắn xóa ?","Mọi thông tin liên quan sẽ bị xóa!")
        
        if(confirm.isConfirmed){
            $.post(`/outputdevice/delete/${id}`,function(res){
                if(res.code==200){
                    notyf.success({
                        message:"Xóa thiết bị thành công!"
                    })
                    reloadTable()
                }else{
                    notyf.error({
                        message:"Lỗi xóa thiết bị!"
                    })
                }
            })
        }
   }

   function editDevice(id){
        $.get(`/outputdevice/edit/${id}`,function(res){
                if(res.code==200){
                    console.log(res.data);
                    if(res.data.length==0){
                        notyf.error({
                        message:"Lỗi lấy dữ liệu thiết bị!"
                        })
                        return
                    }
                    renderDataEdit(res.data)
                    
                }else{
                    notyf.error({
                        message:"Lỗi lấy dữ liệu thiết bị!"
                    })
                }
        })
   }

   function renderDataEdit(data){
    
        $("#editDeviceModal").modal("show");
        // Gán dữ liệu vào các input tương ứng
        $("#editid").val(data.id); // ID ẩn
        $("#editname").val(data.name); // ID ẩn
        $("#editimei").val(data.imei); // IMEI
        $("#editactive").val(data.active); // IMEI
        $("#editserial_number").val(data.serial_number); // Serial Number
        $("#editmodel").val(data.model); // Model thiết bị
        $("#editbank_id").val(data.bank_id || "") // Bank default
        $("#editphone_serial_sim").val(data.phone_serial_sim); // Model thiết bị

        if(data.vendor=="Flametechvn"){
            $(".info-server-mqtt-edit").addClass("d-none");
        }else{
            $(".info-server-mqtt-edit").removeClass("d-none");
        }
        $("#editvendor").val(data.vendor); // Vendor thiết bị

        // Set value cho select2 cho MQTT Server
        if(data.vendor=="Aisino"){
            $("#edit_mqtt_server_id").val(data.mqtt_server_id).trigger('change'); // Chọn giá trị trong Select2
            $("#edit_mqtt_client_id").val(data.mqtt_client_id).trigger('change'); // Chọn giá trị trong Select2
        }
       
        

   }
   
</script>
<!-- validate modal add -->
<script>
$(document).ready(function(){
    $('#company_id').select2({
        dropdownParent: $("#IntegrationModal"),
    });
    // Lấy CSRF token từ meta tag
    let csrfToken = $('meta[name="csrf-token"]').attr('content');
             
    // Cấu hình jQuery để thêm CSRF token vào tất cả các yêu cầu AJAX
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': csrfToken
        }
    });

    $("#vendor").change(function() {
        let selectedVendor = $(this).val();
       
        if (selectedVendor === "Flametechvn") {
            $(".info-server-mqtt").addClass("d-none");
        } else {
            $(".info-server-mqtt").removeClass("d-none")
        }
    });

    $("#editvendor").change(function() {
        let selectedVendor = $(this).val();
       
        if (selectedVendor == "Flametechvn") {
            $(".info-server-mqtt-edit").addClass("d-none");
        } else {
            $(".info-server-mqtt-edit").removeClass("d-none")
        }
    });


    // Validate add device form
    $("#addDeviceForm").validate({
        errorElement: 'div',
        rules: {
            "name": {
                "required": true,
                "maxlength": 30
            },
           
            "serial_number": {
                "required": true,
                "maxlength": 30
            },
            "model": {
                "required": true,
                "maxlength": 80
            },
            "vendor": {
                "required": true,
                "maxlength": 80
            },
            "mqtt_server_id": {
                "required": {
                depends: function(element) {
                    return $("#vendor").val() === "Aisino";
                    }
                }
            },
            "mqtt_client_id": {
                "required": {
                depends: function(element) {
                    return $("#vendor").val() === "Aisino";
                    }
                }
            }
        },
        messages: {
            name: {
                required: 'Tên là bắt buộc',
                maxlength: 'Tên không vượt quá 30 ký tự'
            },
           
            serial_number: {
                required: 'Serial Number là bắt buộc',
                maxlength: 'Serial Number không vượt quá 30 ký tự'
            },
            model: {
                required: 'Model thiết bị là bắt buộc',
                maxlength: 'Model thiết bị không vượt quá 80 ký tự'
            },
            vendor: {
                required: 'Vendor thiết bị là bắt buộc',
                maxlength: 'Vendor thiết bị không vượt quá 80 ký tự'
            },
            mqtt_server_id: {
                required: 'Vui lòng chọn Server MQTT'
            },
            mqtt_client_id: {
                required: 'Vui lòng chọn User MQTT'
            }
        },
        highlight: function(input) {
            $(input).addClass('is-invalid');
        },
        unhighlight: function(input) {
            $(input).removeClass('is-invalid');
        },
        errorPlacement: function(error, input) {
            // Append error inside the invalid-feedback div
            $(input).siblings('.invalid-feedback').append(error);
        },
        submitHandler: function(form, e) {
            // Prevent default form submission
            e.preventDefault();

            // Call function to save data
            saveData(form, e);
        }
    });
    // Validate add device form
    $("#editDeviceForm").validate({
        errorElement: 'div',
        rules: {
            "name": {
                "required": true,
                "maxlength": 30
            },
            "serial_number": {
                "required": true,
                "maxlength": 30
            },
            "model": {
                "required": true,
                "maxlength": 80
            },
            "vendor": {
                "required": true,
                "maxlength": 80
            },
            "mqtt_server_id": {
                "required": {
                depends: function(element) {
                    return $("#editvendor").val() === "Aisino";
                    }
                }
            },
            "mqtt_client_id": {
                "required": {
                depends: function(element) {
                    return $("#editvendor").val() === "Aisino";
                    }
                }
            }
        },
        messages: {
            name: {
                required: 'Tên là bắt buộc',
                maxlength: 'Tên không vượt quá 15 ký tự'
            },
            serial_number: {
                required: 'Serial Number là bắt buộc',
                maxlength: 'Serial Number không vượt quá 30 ký tự'
            },
            model: {
                required: 'Model thiết bị là bắt buộc',
                maxlength: 'Model thiết bị không vượt quá 50 ký tự'
            },
            vendor: {
                required: 'Vendor thiết bị là bắt buộc',
                maxlength: 'Vendor thiết bị không vượt quá 50 ký tự'
            },
            mqtt_server_id: {
                required: 'Vui lòng chọn Server MQTT'
            },
            mqtt_client_id: {
                required: 'Vui lòng chọn User MQTT'
            }
        },
        highlight: function(input) {
            $(input).addClass('is-invalid');
        },
        unhighlight: function(input) {
            $(input).removeClass('is-invalid');
        },
        errorPlacement: function(error, input) {
            // Append error inside the invalid-feedback div
            $(input).siblings('.invalid-feedback').append(error);
        },
        submitHandler: function(form, e) {
            // Prevent default form submission
            e.preventDefault();

            // Call function to save data
            saveDataEdit(form, e);
        }
    });

    // Thêm xác thực cho biểu mẫu integraion
    $("#IntegrationForm").validate({
        errorElement: 'div',
        rules: {
            "company_id": {
                required: true
            }
        },
        messages: {
            company_id: {
                required: 'Vui lòng chọn công ty'
            }
        },
        highlight: function(input) {
            $(input).addClass('is-invalid');
            $(input).siblings('.select2-container').addClass('is-invalid'); // Thêm lớp cho Select2
        },
        unhighlight: function(input) {
            $(input).removeClass('is-invalid');
            $(input).siblings('.select2-container').removeClass('is-invalid'); // Gỡ lớp cho Select2
        },
        errorPlacement: function(error, input) {
            // Đặt thông báo lỗi vào div invalid-feedback
            $(input).siblings('.invalid-feedback').html(error);
        },
        submitHandler: function(form, e) {
            // Ngăn chặn hành động gửi mặc định của biểu mẫu
            e.preventDefault();

            // Gọi hàm để lưu dữ liệu
            saveDataIntegration(form, e);
        }
    });

    $.validator.addMethod("validFileType", function(value, element) {
        if (element.files.length === 0) return false; // Không có file
        let file = element.files[0];
        let validTypes = [
            "text/csv", 
            "application/vnd.ms-excel", 
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ];
        
        return validTypes.includes(file.type);
    }, "Chỉ chấp nhận file CSV, XLS hoặc XLSX.");

    $("#uploadDeviceForm").validate({
        errorElement: "div",
        rules: {
            deviceFile: {
                required: true,
                validFileType: true
            }
        },
        messages: {
            deviceFile: {
                required: "Vui lòng chọn file hợp lệ.",
                validFileType: "Chỉ chấp nhận file CSV, XLS hoặc XLSX."
            }
        },
        highlight: function (element) {
            $(element).addClass("is-invalid");
        },
        unhighlight: function (element) {
            $(element).removeClass("is-invalid");
        },
        errorPlacement: function (error, element) {
            $(element).siblings(".invalid-feedback").html(error);
        },
        submitHandler: function (form, event) {
            event.preventDefault();
            uploadData(form, event);
        }
    });



    // Cập nhật Select2 để xác thực
    $('#company_id').on('change', function() {
        $(this).valid(); // Kiểm tra lại tính hợp lệ khi lựa chọn thay đổi
    });
});


function uploadData(form, event) {
    const btn = $(event.originalEvent.submitter);
    btn.prop("disabled", true);
    btn.find(".loader").show();

    let formData = new FormData(form);
    let fileInput = form.querySelector("input[type=file]");

    if (!fileInput.files.length) {
        notyf.error({ message: "Vui lòng chọn file!" });
        btn.prop("disabled", false);
        btn.find(".loader").hide();
        return;
    }

    let file = fileInput.files[0];
    let reader = new FileReader();

    reader.onload = function (e) {
        let data = new Uint8Array(e.target.result);
        let workbook = XLSX.read(data, { type: "array" });
        let sheet = workbook.Sheets[workbook.SheetNames[0]];
        let jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
        let requiredHeaders = ["HWID", "MappedID", "SĐT", "SIM SN", "BANKID"];
        let fileHeaders = jsonData[0] || [];

        let isValid = requiredHeaders.every(header => fileHeaders.includes(header));
        if (!isValid) {
            notyf.error({ message: "File không hợp lệ! Vui lòng kiểm tra lại tiêu đề cột." });
            btn.prop("disabled", false);
            btn.find(".loader").hide();
            return;
        }

        let formattedData = XLSX.utils.sheet_to_json(sheet);
        formattedData = formattedData.map(row => {
            if (row["HWID"] && row["MappedID"]) {
                row["HWID"] = row["MappedID"];
            }
            return row;
        });

        if (formattedData.length === 0) {
            notyf.error({ message: "Không có dữ liệu trong file." });
            btn.prop("disabled", false);
            btn.find(".loader").hide();
            return;
        }

        // CHIA DỮ LIỆU THÀNH TỪNG BATCH (100 thiết bị mỗi đợt)
        const batchSize = 100;
        const batches = [];
        for (let i = 0; i < formattedData.length; i += batchSize) {
            batches.push(formattedData.slice(i, i + batchSize));
        }

        let allResults = {
            countNewActive: 0,
            countDuplicates: 0,
            countExisting: 0,
            countNewDisable: 0,
            countChangeSNSim: 0,
            countNoBankID: 0,
            duplicateHWIDs: [],
            existingData: [],
            newDataDisable: [],
            dataChangeSNSim: [],
            dataNoBankID: [],
        };

        // GỬI LẦN LƯỢT TỪNG BATCH
        function sendNextBatch(index = 0) {
            console.log("Gửi batch:", index + 1, "với kích thước:", batches[index]?.length);
            if (index >= batches.length) {
                // Gửi xong hết => hiển thị kết quả
                console.log("Kết quả sau khi import:", allResults);
                $(".alert-import").html(`
                    Có tất cả <b>${formattedData.length}</b> thiết bị.</br>
                    Bạn đã thêm thành công <b>${allResults.countNewActive}</b> thiết bị.</br>
                    - Có <b>${allResults.countDuplicates}</b> HWID bị trùng. ${JSON.stringify(allResults.duplicateHWIDs)}</br>
                    - Có <b>${allResults.countExisting}</b> thiết bị đã tồn tại trong DB.</br>
                    - Có <b>${allResults.countNewDisable}</b> thiết bị không thể kích hoạt.</br>
                    - Có <b>${allResults.countChangeSNSim}</b> thiết bị khác S/N SIM giữa API và Excel.</br>
                    <span class=""> - Có <b>${allResults.countNoBankID}</b> thiết bị không có ngân hàng.</br><span>
                    <a href="#" class="text-danger" id="download-existing">Tải xuống danh sách tồn tại DB</a> </br> 
                    <a href="#" class="text-danger" id="download-disable">Tải xuống danh sách không kích hoạt</a> </br>
                    <a href="#" class="text-danger" id="download-change-sim">Tải xuống danh sách thiết bị không khớp SN SIM</a> </br> 
                    <a href="#" class="text-danger" id="download-no-bank">Tải xuống danh sách thiết bị không có ngân hàng</a>
                `);

                // Xử lý tải file danh sách thiết bị đã tồn tại
                $("#download-existing").on("click", function (e) {
                    e.preventDefault();
                    if ($(this).hasClass("disabled")) return;

                    console.log("Danh sách thiết bị đã tồn tại DB:", allResults.existingData);
                    exportToExcel(allResults.existingData, "Danh_sach_thiet_bi_ton_tai.xlsx");
                    $(this).addClass("disabled").text("Đã tải xong");
                });

                // Xử lý tải file danh sách thiết bị không kích hoạt
                $("#download-disable").on("click", function (e) {
                    e.preventDefault();
                    if ($(this).hasClass("disabled")) return;

                    console.log("Danh sách thiết bị không kích hoạt:", allResults.newDataDisable);
                    exportToExcel(allResults.newDataDisable, "Danh_sach_thiet_bi_khong_kich_hoat.xlsx");
                    $(this).addClass("disabled").text("Đã tải xong");
                });

                // Xử lý tải file danh sách thiết bị không khớp SN SIM
                $("#download-change-sim").on("click", function (e) {
                    e.preventDefault();
                    if ($(this).hasClass("disabled")) return;

                    console.log("Danh sách thiết bị không khớp SN SIM:", allResults.dataChangeSNSim);
                    exportToExcel(allResults.dataChangeSNSim, "Danh_sach_thiet_bi_khong_khop_sn_sim.xlsx");
                    $(this).addClass("disabled").text("Đã tải xong");
                });

                // Xử lý tải file danh sách thiết bị không có ngân hàng
                $("#download-no-bank").on("click", function (e) {
                    e.preventDefault();
                    if ($(this).hasClass("disabled")) return;

                    console.log("Danh sách thiết bị không có ngân hàng:", allResults.dataNoBankID);
                    exportToExcel(allResults.dataNoBankID, "Danh_sach_thiet_bi_khong_co_ngan_hang.xlsx");
                    $(this).addClass("disabled").text("Đã tải xong");
                });

                btn.prop("disabled", false);
                btn.find(".loader").hide();
                reloadTable();
                return;
            }

            $.ajax({
                url: "/outputdevice/import",
                type: "POST",
                dataType: "JSON",
                contentType: "application/json",
                data: JSON.stringify({ excelData: batches[index] }),
                success: function (res) {
                    if (res.code == 200) {
                        // Gộp kết quả từ server vào allResults
                        allResults.countNewActive += res.data.countNewActive || 0;
                        allResults.countDuplicates += res.data.countDuplicates || 0;
                        allResults.countExisting += res.data.countExisting || 0;
                        allResults.countNewDisable += res.data.countNewDisable || 0;
                        allResults.countChangeSNSim += res.data.countChangeSNSim || 0;
                        allResults.countNoBankID += res.data.countNoBankID || 0;

                        allResults.duplicateHWIDs.push(...(res.data.duplicateHWIDs || []));
                        allResults.existingData.push(...(res.data.existingData || []));
                        allResults.newDataDisable.push(...(res.data.newDataDisable || []));
                        allResults.dataChangeSNSim.push(...(res.data.dataChangeSNSim || []));
                        allResults.dataNoBankID.push(...(res.data.dataNoBankID || []));
                    }
                },
                error: function (err) {
                    let message = err.responseJSON?.message || "Lỗi hệ thống!";
                    notyf.error({ message: message });
                },
                complete: function (xhr) {
                    let newCsrfToken = xhr.getResponseHeader("X-CSRF-TOKEN");
                    if (newCsrfToken) {
                        updateCsrfToken(newCsrfToken);
                    }
                    // Gửi batch tiếp theo
                    sendNextBatch(index + 1);
                }
            });
        }

        sendNextBatch(); // bắt đầu gửi batch đầu tiên
    };

    reader.readAsArrayBuffer(file);
}


function exportToExcel(data, filename) {
    // Ép kiểu tất cả giá trị về dạng chuỗi
    const stringData = data.map(row => {
        const newRow = {};
        for (const key in row) {
            newRow[key] = String(row[key] ?? ''); // Chuyển null/undefined thành chuỗi rỗng
        }
        return newRow;
    });

    // Chuyển dữ liệu thành sheet
    const ws = XLSX.utils.json_to_sheet(stringData);

    // Tạo workbook và thêm sheet
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Danh sách");

    // Xuất file Excel
    XLSX.writeFile(wb, filename);
}


// Hàm cập nhật số lượng bản ghi
function updateRecordCounts() {
    let data = table.ajax.json().data;
    
    // Loại bỏ class active-btn từ tất cả các nút
    $('.btn-filter').removeClass('active-btn');

    // Thêm class active-btn vào nút hiện tại
    $(event.target).addClass("active-btn");
    // Lọc ra các bản ghi có trạng thái "Pending"
    let pendingCount = data.filter(function(record) {
        return record.integration_status_text === 'Chưa liên kết';
    }).length;

    // Lọc ra các bản ghi có trạng thái "Approved"
    let approvedCount = data.filter(function(record) {
        return record.integration_status_text === 'Đã liên kết';
    }).length;

    // Tổng số bản ghi
    let totalCount = data.length;

    $(".count-all").text(totalCount);         // Số lượng tất cả
    $(".count-approved").text(approvedCount);  // Số lượng Approved
    $(".count-pending").text(pendingCount);    // Số lượng Pending


    let totalCountFlametech = data.filter(function(record) {
        return record.vendor === 'Flametechvn';
    }).length;

    let totalpendingCountFlametech = data.filter(function(record) {
        return record.vendor === 'Flametechvn' && record.integration_status_text === 'Chưa liên kết';
    }).length;

    let totalapprovedCountFlametech = data.filter(function(record) {
        return record.vendor === 'Flametechvn' && record.integration_status_text === 'Đã liên kết';
    }).length;

    $(".count-all-flametech").text(totalCountFlametech);         // Số lượng tất cả
    $(".count-approved-flametech").text(totalapprovedCountFlametech);  // Số lượng Approved
    $(".count-pending-flametech").text(totalpendingCountFlametech);    // Số lượng Pending
}

// Hàm reloadTable (giả định)
function reloadTable() {
    table.ajax.reload(function() {
        updateRecordCounts();  // Cập nhật lại số lượng bản ghi sau khi reload
    });
}

function saveData(form, e) {
    const btn = $(e.originalEvent.submitter)
    btn.prop('disabled', true)
    btn.find('.loader').show();
    
    
    $.ajax({
        url: "/outputdevice/add",
        type: "POST",
        dataType: "JSON",
        data: $(form).serialize(), // Serialize form data
        success: function(res, textStatus, jqXHR) {
            if (res.code == 200) {
                console.log(res.message);
                notyf.success({
                    message: res.message,
                })
                $('#addDeviceModal').modal('hide');
            }
            reloadTable()
           

        },
        error: function(err, textStatus, errorThrown) {
            if (err.status == 403) {
                notyf.error({
                    message: "Hãy nhấn F5 tải lại trang để thử lại!"
                })
            }

            if (err.status == 404) {
                notyf.error({
                    message: err.responseJSON.message
                })

            }
            console.log(err.responseJSON.message);

            if (err.status == 422) {
                notyf.error({
                    message: err.responseJSON.message || "Lỗi dữ liệu!"
                })
            }
            if (err.status == 423) {
                notyf.error({
                    message: err.responseJSON.message || "Lỗi dữ liệu!"
                })
            }

            if (err.status == 500) {
                notyf.error({
                    message: "Lỗi hệ thống, Hãy liên hệ kỹ thuật!"
                })

            }
        },
        complete: (xhr, textStatus) => {
            let newCsrfToken = xhr.getResponseHeader('X-CSRF-TOKEN');

            if (newCsrfToken) {
                updateCsrfToken(newCsrfToken);
            }
            btn.prop('disabled', false)
            btn.find('.loader').hide();

        }
    })
}

function saveDataEdit(form, e) {
    const btn = $(e.originalEvent.submitter)
    btn.prop('disabled', true)
    btn.find('.loader').show();
    
    
    $.ajax({
        url: "/outputdevice/update",
        type: "POST",
        dataType: "JSON",
        data: $(form).serialize(), // Serialize form data
        success: function(res, textStatus, jqXHR) {
            if (res.code == 200) {
                console.log(res.message);
                notyf.success({
                    message: res.message,
                })
                $('#editDeviceModal').modal('hide');
            }
            reloadTable()
           

        },
        error: function(err, textStatus, errorThrown) {
            if (err.status == 403) {
                notyf.error({
                    message: "Hãy nhấn F5 tải lại trang để thử lại!"
                })
            }

            if (err.status == 404) {
                notyf.error({
                    message: err.responseJSON.message
                })

            }
            console.log(err.responseJSON.message);

            if (err.status == 422) {
                notyf.error({
                    message: err.responseJSON.data.name || "Lỗi dữ liệu!"
                })
            }
            if (err.status == 423) {
                notyf.error({
                    message: err.responseJSON.message || "Lỗi dữ liệu!"
                })
            }

            if (err.status == 500) {
                notyf.error({
                    message: "Lỗi hệ thống, Hãy liên hệ kỹ thuật!"
                })

            }
        },
        complete: (xhr, textStatus) => {
            let newCsrfToken = xhr.getResponseHeader('X-CSRF-TOKEN');

            if (newCsrfToken) {
                updateCsrfToken(newCsrfToken);
            }
            btn.prop('disabled', false)
            btn.find('.loader').hide();

        }
    })
}

function saveDataIntegration(form, e) {
    const btn = $(e.originalEvent.submitter)
    btn.prop('disabled', true)
    btn.find('.loader').show();
    
    
    $.ajax({
        url: "/outputdevice/addintegration",
        type: "POST",
        dataType: "JSON",
        data: $(form).serialize(), // Serialize form data
        success: function(res, textStatus, jqXHR) {
            if (res.code == 200) {
                console.log(res.message);
                notyf.success({
                    message: res.message,
                })
                $('#IntegrationModal').modal('hide');
            }
            reloadTable()
           

        },
        error: function(err, textStatus, errorThrown) {
            if (err.status == 403) {
                notyf.error({
                    message: "Hãy nhấn F5 tải lại trang để thử lại!"
                })
            }

            if (err.status == 404) {
                notyf.error({
                    message: err.responseJSON.message
                })

            }
            console.log(err.responseJSON.message);

            if (err.status == 422) {
                notyf.error({
                    message: err.responseJSON.data.name || "Lỗi dữ liệu!"
                })
            }
            if (err.status == 423) {
                notyf.error({
                    message: err.responseJSON.message || "Lỗi dữ liệu!"
                })
            }

            if (err.status == 500) {
                notyf.error({
                    message: "Lỗi hệ thống, Hãy liên hệ kỹ thuật!"
                })

            }
        },
        complete: (xhr, textStatus) => {
            let newCsrfToken = xhr.getResponseHeader('X-CSRF-TOKEN');

            if (newCsrfToken) {
                updateCsrfToken(newCsrfToken);
            }
            btn.prop('disabled', false)
            btn.find('.loader').hide();

        }
    })
}

function updateCsrfToken(csrfToken) {
    $('meta[name="csrf-token"]').attr('content', csrfToken);
    $.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': csrfToken
    }
    });
} 
</script>