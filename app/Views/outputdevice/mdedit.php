<div class="modal fade" id="editDeviceModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="addDeviceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addDeviceModalLabel">Chỉnh sửa thông tin thiết bị </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editDeviceForm" action="javascript:;" class="needs-validation" novalidate method="post" accept-charset="utf-8">
                <div class="modal-body">
                    <fieldset class="border p-3 mt-3">
                    <div class="mb-3 form-group">
                            <label for="editname" class="form-label"><b>Tên <span class="text-danger">*</span></b></label>
                            <input type="text" class="form-control" id="editname" name="name" placeholder="Nhập Tên" >
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="mb-3 form-group info-server-mqtt-edit">
                            <label for="editimei" class="form-label"><b>IMEI <span class="text-danger"></span></b></label>
                            <input type="text" class="form-control" id="editimei" name="imei" placeholder="Nhập mã IMEI" >
                            <input type="text" hidden class="form-control" id="editid" name="id"  >
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="mb-3 form-group">
                            <label for="editserial_number" class="form-label"><b>Serial Number <span class="text-danger">*</span></b></label>
                            <input type="text" class="form-control" id="editserial_number" name="serial_number" placeholder="Nhập số serial" >
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <div class="mb-3 form-group">
                            <label for="editphone_serial_sim" class="form-label"><b>Thuê bao SIM</b></label>
                            <input type="text" class="form-control" id="editphone_serial_sim" name="phone_serial_sim" placeholder="Nhập số thuê bao SIM" >
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="mb-3 form-group">
                            <label for="editbank_id" class="form-label"><b>Ngân hàng thiết bị</b></label>
                            <select class="form-control" id="editbank_id" name="bank_id">
                                <option value="">Chọn ngân hàng</option>
                                <?php if(!empty($list_bank)) { ?>
                                    <?php foreach($list_bank as $val){ ?>
                                        <option value="<?= $val['id']?>"> <?=$val['brand_name']?></option>
                                    <?php } ?>
                                <?php } ?>
                                
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="mb-3 form-group">
                            <label for="editmodel" class="form-label"><b>Model thiết bị <span class="text-danger">*</span></b></label>
                            <select class="form-control" id="editmodel" name="model" required>
                                <option value="">Chọn model thiết bị</option>
                                <?php if(!empty($list_model)) { ?>
                                    <?php foreach($list_model as $val){ ?>
                                        <option value="<?= $val['model']?>"><?= $val['model']?></option>
                                    <?php } ?>
                                <?php } ?>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="mb-3 form-group">
                            <label for="editvendor" class="form-label"><b>Vendor thiết bị <span class="text-danger">*</span></b></label>
                            <select class="form-control" id="editvendor" name="vendor" required>
                                <option value="" disabled selected>Chọn vendor thiết bị</option>
                                <option value="Aisino">Aisino</option>
                                <option value="Flametechvn">Flametechvn</option>
                            </select>
                            <div class="invalid-feedback">Vui lòng chọn vendor thiết bị.</div>
                        </div>
                        <div class="mb-3 form-group">
                            <label for="editactive" class="form-label"><b>Trạng thái thiết bị <span class="text-danger">*</span></b></label>
                            <select class="form-control" id="editactive" name="active" required>
                                <option value="0">Chưa kích hoạt</option>
                                <option value="1">Hoạt động</option>
                            </select>
                        </div>

                        <div class="mb-3 form-group info-server-mqtt-edit">
                            <label for="edit_mqtt_server_id" class="form-label"><b>Server MQTT <span class="text-danger">*</span></b></label>
                            <select class="form-select" id="edit_mqtt_server_id" name="mqtt_server_id" >
                                
                                <?php if(!empty($data_server)) { ?>
                                    <?php foreach($data_server as $val){ ?>
                                        <option data-user-mqtt="<?= esc(json_encode($val['user_mqtt']??""),"attr") ?>" value="<?= $val['id']?>"><?= $val['hostname']?> - <?= $val['port']?> - <?= $val['username_server']??""?></option>
                                    <?php } ?>
                                <?php } ?>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="mb-3 form-group info-server-mqtt-edit">
                            <label for="edit_mqtt_client_id" class="form-label"><b>User MQTT <span class="text-danger">*</span></b></label>
                            <select class="form-select" id="edit_mqtt_client_id" name="mqtt_client_id" >
                                
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                    </fieldset>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary me-auto" data-bs-dismiss="modal">Đóng</button>
                    <button type="submit" class="btn btn-primary" id="saveDeviceButton">
                        <div class="spinner-border text-light loader me-2" style="width: 20px; height: 20px; display: none;" role="status"></div>
                        Cập nhật thiết bị
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
