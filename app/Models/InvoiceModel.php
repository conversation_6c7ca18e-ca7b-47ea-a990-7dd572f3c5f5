<?php

namespace App\Models;

use CodeIgniter\Model;
use App\Models\CompanyModel;
use App\Models\InvoiceItemModel;
use App\Models\UserModel;
use App\Models\EmailLogModel;
use Config\Billing;
use Config\SubscriptionSInvoice;

class InvoiceModel extends Model
{
    protected $table = 'tb_autopay_invoice';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['status','type','company_id', 'date','duedate','paybefore', 'datepaid','subtotal','credit', 'total', 'public_note', 'email_sent' , 'tax','tax_rate', 'merchant_id','tax_issued','tax_issued_id','tax_info_id', 'physical_invoice_id', 'created_at', 'customer_info_id', 'vat_invoice_requested_at', 'last_update_status_at', 'is_pushed_to_google_sheets'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;
    
    protected $order = ['id'=>'desc'];
 
    protected $builder;


    private function _getDatatablesQuery($company_id) {

        $request = \Config\Services::request();
  
        $column_order = array(null,'id','company_id','type','status','total','tax_issued','date',null);
        $column_search = array('id','type','status','total','date','public_note','tax_issued_id');
 
        $this->builder = $this->db->table($this->table);

        $this->builder
            ->where('type !=', 'PhysicalProduct')
            ->select("id,company_id, type, status, total, date, duedate, paybefore, datepaid,tax_issued,tax_issued_id,tax_info_id");

        if($company_id)
            $this->builder->where("company_id",$company_id);

        if($request->getGet('tax_issued') == 1) {
            $this->builder->where("tax_issued", 1);
        }

        if($request->getGet('company_id') != NULL) {
            $this->builder->where("company_id", $request->getGet('company_id'));
        }

        if($request->getGet('status') != NULL) {
            $this->builder->where("status", $request->getGet('status'));
        }
        if($request->getGet('type') != NULL) {
            $this->builder->where("type", $request->getGet('type'));
        }

        if($request->getGet('id') != NULL) {
            $this->builder->where("id", $request->getGet('id'));
        }

        if($request->getGet('merchant_id') != NULL) {
            $this->builder->where("merchant_id", $request->getGet('merchant_id'));
        }
     
        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables($company_id=FALSE) {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery($company_id);
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll($company_id=FALSE) {
     
        $builder = $this->db->table($this->table);

        if($company_id)
            $builder->where("company_id",$company_id);
 
       
        return $builder->countAllResults();
    }

    public function countFiltered($company_id=FALSE) {
        $this->_getDatatablesQuery($company_id);
        if($company_id)
            $this->builder->where("company_id",$company_id);
         
        return $this->builder->countAllResults();
    }

    function sendEmailInvoice($invoice_id) {
        
        $companyModel = model(CompanyModel::class);
        $invoiceItemModel = model(InvoiceItemModel::class);
        $userModel = model(UserModel::class);

        $builder = $this->db->table($this->table);
        $invoice_details = $builder->where(['id' => $invoice_id])->get()->getRow();
 
        if(!is_object($invoice_details))
            return FALSE;

        $company_details = $companyModel->find($invoice_details->company_id);

        if(!is_object($company_details))
            return FALSE;

        $user_details = $userModel->select("tb_autopay_user.email, tb_autopay_user.firstname, tb_autopay_user.lastname")->join("tb_autopay_company_user","tb_autopay_company_user.user_id=tb_autopay_user.id")->where(['tb_autopay_company_user.company_id' => $invoice_details->company_id, "tb_autopay_company_user.role" => "SuperAdmin"])->get()->getRow();

        if(!is_object($user_details))
            return FALSE;

        $invoice_items = $invoiceItemModel->where(['invoice_id' => $invoice_id])->orderBy('position','ASC')->get()->getResult();

        $item_description_text = "";

        foreach($invoice_items as $item) {
            $item_description_text = $item_description_text . $item->description . ". "; 
        }

        $billingConfig = config(Billing::class);
        $bankAccount = $billingConfig->ownerBankAccount;

        $client_name = $user_details->lastname . " " . $user_details->firstname;
        $company_name = $company_details->full_name;
        $total = number_format($invoice_details->total) . " đ";
        $pay_before = date("d/m/Y", strtotime($invoice_details->paybefore));
        $email_to = $user_details->email;
        $email_from = "<EMAIL>";

        helper('general');

        $paycode = create_paycode($invoice_id);

        $bankName = $bankAccount['bank'];
        $bankAccountNumber = $bankAccount['accountNumber'];
        $bankHolderName = $bankAccount['holderName'];

        $qrcode = sprintf(
            'https://qr.sepay.vn/img?bank=%s&acc=%s&template=qronly&amount=%s&des=%s&download=true',
            $bankName,
            $bankAccountNumber,
            $invoice_details->total,
            $paycode
        );

        $html_body = <<<EOD
    
        <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html xmlns="http://www.w3.org/1999/xhtml">
        <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        </head>
        <body style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; background-color: #f9f9fb; color: #303f4d; height: 100%; line-height: 1.4; margin: 0; width: 100% !important; -webkit-text-size-adjust: none;">
            <style>
                @media  only screen and (max-width: 600px) {
                    .inner-body {
                        width: 100% !important;
                    }
        
                    .footer {
                        width: 100% !important;
                    }
                }
        
                @media  only screen and (max-width: 500px) {
                    .button {
                        width: 100% !important;
                    }
                }
            </style>
        <table class="wrapper" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;"><tr>
        <td align="center" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">
                        <table class="content" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
        <tr>
        <td class="header" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 25px 0; text-align: center;">
                <a href="https://sepay.vn" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #bbbfc3; font-size: 19px; font-weight: bold; text-decoration: none; text-shadow: 0 1px 0 white;">
                    <img width="100" src="https://my.sepay.vn/assets/images/logo/sepay-blue-359x116.png" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; max-width: 100%; border: none;"></a>
            </td>
        </tr>
        <!-- Email Body --><tr>
        <td class="body" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
                                    <table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; background-color: #FFFFFF; margin: 0 auto; padding: 0; width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px;">
        <!-- Body content --><tr>
        <td class="content-cell" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 25px;">
        <h3>Quý khách có hoá đơn cần thanh toán!</h3> 

        <p style="font-size: 13px;">Xin chào Quý khách hàng $client_name,</p>         

        <p style="font-size: 13px;">Quý khách có <span style="color:#006fba">1 hoá đơn</span> cần thanh toán với nội dung như sau:</p> 

         <hr>


         <ul  style="font-size: 13px;">
         <li>Hoá đơn: <a href="https://my.sepay.vn/invoices/details/$invoice_id" target="_blank" >#$invoice_id</a></li>

         <li>Nội dung: <b>$item_description_text</b></li>
         <li>Thuộc tài khoản: <b>$company_name</b></li>
         <li>Số tiền cần thanh toán: <b>$total</b></li>
         </ul>
         <p style="font-size: 13px; padding-top: 20px;padding-bottom: 10px;">Quý khách vui lòng thanh toán trước ngày $pay_before để dịch vụ không bị gián đoạn.</b></p> 
        <hr>

        <div style="padding: 20px; border: 1px solid #EDEFF2; margin-top: 20px;">
            <h5 style="text-align: center;">Hướng dẫn thanh toán</h5>
            <table width="100%" style="font-size: 13px; margin-top: 15px;">
                <tr>
                    <td width="40%" style="text-align: center;">
                        <img src="$qrcode" style="max-width: 100px; margin-bottom: 6px;">
                        <div><a download="qr-invoice-$invoice_details->id.png" href="$qrcode" style="font-size: 12px; text-decoration: none;">Tải ảnh QR</a></div>
                    </td>
                    <td width="60%">
                        <table style="font-size: 13px;">
                            <tr>
                                <td>Ngân hàng</td>
                                <td class="fw-bold">$bankName</td>
                            </tr>
                            <tr>
                                <td>Số tài khoản</td>
                                <td class="fw-bold">$bankAccountNumber</td>
                            </tr>
                            <tr>
                                <td>Thụ hưởng</td>
                                <td class="fw-bold">$bankHolderName</td>
                            </tr>
                            <tr>
                                <td>Nội dung CK</td>
                                <td class="fw-bold">$paycode</td>
                            </tr>
                            <tr>
                                <td>Số tiền</td>
                                <td class="fw-bold">$total</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" style="text-align: center; font-size: 12px; padding-top: 10px;">
                        <i class="bi bi-qr-code-scan"></i> Dùng ứng dụng ngân hàng quét mã QR để chuyển khoản
                    </td>
                </tr>
            </table>
        </div>

        <p style="text-align: center;">
        <a href="https://my.sepay.vn/invoices/details/$invoice_id" style="text-decoration:none;color:#ffffff;font-size:14px;border-style:solid;border-color:#2d6bcf;border-width:10px 20px 10px 20px;display:inline-block;background:#2d6bcf;border-radius:30px;line-height:22px;width:auto;text-align:center" target="_blank" >Xem hoá đơn #$invoice_id</a>
          </p>      
          <p style="font-size: 13px; padding-top: 20px;">Xem tất cả các hoá đơn tại <b>Cấu hình công ty</b> -> <b><a href="https://my.sepay.vn/invoices/">Hoá đơn</a></b></p>   
          <p style="font-size: 13px; padding-bottom: 10px;">Mọi yêu cầu hỗ trợ, quý khách vui lòng liên hệ <b>SePay</b>: Hotline <b><a href="tel:02873059589">02873.059.589</a>, Email <EMAIL></b> hoặc fanpage <a href="https://fb.me/sepay.vn" target="_blank">fb.me/sepay.vn</a></p>   


          <p style="font-size: 13px; ">Cảm ơn Quý khách đã sử dụng dịch vụ của SePay!</p>   


         <p style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #303f4d; line-height: 1.5em; margin-top: 0; text-align: center; font-size: 13px;">Vui lòng bỏ qua email này nếu Quý khách đã thanh toán.</p>

    
        <!-- Subcopy -->
        <table class="subcopy" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; border-top: 1px solid #EDEFF2; margin-top: 25px; padding-top: 25px;"><tr>
        <td style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">


            <p style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #303f4d; line-height: 1.5em; margin-top: 0; text-align: center; font-size: 12px;">Copyright © 2023 SePay - All rights reserved.</p>
                </td>
            </tr></table>
        </td>
                                        </tr>
        </table>
        </td>
                            </tr>
        </table>
        </td>
        </tr>
        </table>
        </td>
                </tr></table>
        </body>
        </html>
EOD;
 

        $email = \Config\Services::email();

           
        $config['wordWrap'] = true;
        $config['mailType'] = 'html';
        $email->initialize($config);

        
        $email->setFrom($email_from, 'SePay');
        $email->setTo($email_to);

        $subject = "Hoá đơn số #". $invoice_id . " đã được tạo";

        $email->setSubject($subject);

     
        $email->setMessage($html_body);

        $result = $email->send();
 
        $email_log = [
            "company_id" => $invoice_details->company_id,
            "email_type" => "Invoice_Created",
            "data_id" => $invoice_id,
            "email_to" => $email_to,
            "email_from" => $email_from,
            "subject" => $subject,
            "message" => $html_body,
           
        ];

        $emailLogModel = model(EmailLogModel::class);

        if($result) {
            $email_log['status'] = "Sent";
            $emailLogModel->insert($email_log);
            $builder->set('email_sent', 'email_sent+1', false)->where(['id' => $invoice_id])->update();

            return TRUE;
        }
        
        $email_log['status'] = "Error";
        $emailLogModel->insert($email_log);

        return FALSE;

    }

    public function getOverdueInvoices(bool $excludeFreeSubscription = false, bool $excludeInactivatedCompany = true)
    {
        $invoices = $this
            ->select(['tb_autopay_invoice.id', 'tb_autopay_invoice.company_id', 'tb_autopay_product.name as product_name', 'tb_autopay_company_subscription.id as subscription_id', 'tb_autopay_company_subscription.plan_id as product_id', 'tb_autopay_invoice.paybefore', 'tb_autopay_invoice.created_at', 'tb_autopay_invoice.date', 'tb_autopay_invoice.total', 'tb_autopay_company.short_name as company_name', 'tb_autopay_company.status as company_status'])
            ->join('tb_autopay_company', 'tb_autopay_company.id = tb_autopay_invoice.company_id')
            ->join('tb_autopay_company_subscription', 'tb_autopay_company_subscription.company_id = tb_autopay_invoice.company_id')
            ->join('tb_autopay_product', 'tb_autopay_product.id = tb_autopay_company_subscription.plan_id')
            ->where('tb_autopay_invoice.status', 'Unpaid')
            ->where('tb_autopay_invoice.paybefore <', date('Y-m-d'))
            ->where('tb_autopay_company_subscription.disable_auto_suspend', false)
            ->groupStart()
                ->where('tb_autopay_company_subscription.disable_suspension_until IS NULL')
                ->orWhere('tb_autopay_company_subscription.disable_suspension_until <=', date('Y-m-d'))
                ->orWhere('tb_autopay_invoice.paybefore < tb_autopay_company_subscription.disable_suspension_until')
            ->groupEnd()
            ->where('tb_autopay_invoice.type !=', 'SubscriptionChange')
            ->where('tb_autopay_invoice.total >', 0);

        if ($excludeInactivatedCompany) {
            $invoices->where('tb_autopay_company.status', 'Active');
        } else {
            $invoices->whereIn('tb_autopay_company.status', ['Active', 'Suspended']);
        }

        if ($excludeFreeSubscription === true) {
            $invoices
                ->groupStart()
                    ->where('tb_autopay_company_subscription.billing_cycle !=', 'free')
                    ->orWhere('tb_autopay_product.name !=', 'Free')
                ->groupEnd();
        }

        return $invoices;
    }

    public function getTotalEligibleInvoices()
    {
        return $this
            ->where('customer_info_id IS NOT NULL')
            ->where('status', 'Paid')
            ->where('total >', 0)
            ->where('created_at >=', model(SubscriptionSInvoice::class)->issueInvoiceFrom)
            ->countAllResults();
    }

    public function getTotalIssuedInvoices()
    {
        return $this
            ->where('tax_issued_id !=', '')
            ->where('vat_invoice_requested_at IS NOT NULL')
            ->where('status', 'Paid')
            ->where('total >', 0)
            ->where('created_at >=', model(SubscriptionSInvoice::class)->issueInvoiceFrom)
            ->countAllResults();
    }
}
