<?php

namespace App\Models;

use CodeIgniter\Model;

class PartnerCommissionModel extends Model
{
    protected $table = 'tb_autopay_partner_commission';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['order_id','partner_id','invoice_id','rate','commission','status','notes','reject_reason','commission_approved','approve_date'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;
    
    protected $order = ['tb_autopay_partner_commission.id'=>'desc'];
 
    protected $builder;



}