<?php

namespace App\Models;

use CodeIgniter\Model;

class TransactionsModel extends Model
{
    protected $table = 'tb_autopay_sms_parsed';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['sms_id','gateway','transaction_date', 'account_number','amount_in','amount_out', 'accumulated','transaction_content','reference_number','body','parser_status', 'code','webhooks_success', 'webhooks_failed','sub_account','webhooks_verify_payment','chat_push_message'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;
    
    protected $order = ['tb_autopay_sms_parsed.transaction_date'=>'desc'];
 
    protected $builder;

    private function _getDatatablesQuery($company_id, $bank_account_id = FALSE, $role='User', $hide_sub_account=FALSE) {

        $request = \Config\Services::request();

        if($hide_sub_account === TRUE)
            $column_order = array(null,'tb_autopay_sms_parsed.id','tb_autopay_bank_account.account_number',null,'tb_autopay_sms_parsed.accumulated','tb_autopay_sms_parsed.transaction_date','tb_autopay_sms_parsed.transaction_content','tb_autopay_sms_parsed.webhooks_verify_payment',null,null);
        else
            $column_order = array(null,'tb_autopay_sms_parsed.id','tb_autopay_bank_account.account_number','tb_autopay_sms_parsed.sub_account',null,'tb_autopay_sms_parsed.accumulated','tb_autopay_sms_parsed.transaction_date','tb_autopay_sms_parsed.transaction_content','tb_autopay_sms_parsed.webhooks_verify_payment',null,null);
        $column_search = array('tb_autopay_sms_parsed.id','tb_autopay_bank.brand_name','tb_autopay_sms_parsed.account_number','tb_autopay_sms_parsed.sub_account','tb_autopay_sms_parsed.transaction_content','tb_autopay_sms_parsed.code','tb_autopay_sms_parsed.amount_out','tb_autopay_sms_parsed.amount_in','tb_autopay_sms_parsed.accumulated','tb_autopay_sms_parsed.reference_number','tb_autopay_sms_parsed.transaction_date');

        $session = session();
        $user_session = $session->get('user_logged_in');
 
        $this->builder = $this->db->table($this->table);

        $this->builder->select("tb_autopay_sms_parsed.id ,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.label, tb_autopay_sms_parsed.transaction_date, tb_autopay_sms_parsed.amount_out,tb_autopay_sms_parsed.amount_in,tb_autopay_sms_parsed.accumulated,tb_autopay_sms_parsed.transaction_content,tb_autopay_sms_parsed.reference_number,tb_autopay_bank_account.company_id, tb_autopay_sms_parsed.code,tb_autopay_sms_parsed.webhooks_success,tb_autopay_sms_parsed.webhooks_failed,tb_autopay_sms_parsed.sub_account,tb_autopay_bank.icon_path,tb_autopay_sms_parsed.webhooks_verify_payment,tb_autopay_sms_parsed.chat_push_message");

        $this->builder->join("tb_autopay_bank","tb_autopay_bank.brand_name=tb_autopay_sms_parsed.gateway");
        $this->builder->join("tb_autopay_bank_account","tb_autopay_bank_account.account_number=tb_autopay_sms_parsed.account_number");
        
        $this->builder->where("tb_autopay_sms_parsed.parser_status","Success");

        // permission for user access bank account
        if(!in_array($role,['Admin','SuperAdmin'])) {
            $this->builder->join("tb_autopay_user_permission_bank","tb_autopay_user_permission_bank.bank_account_id=tb_autopay_bank_account.id");
           
            $this->builder->where("tb_autopay_user_permission_bank.user_id",$user_session['user_id']);

            
            // permission sub account
            /* $this->builder->join("tb_autopay_bank_sub_account","tb_autopay_bank_sub_account.sub_account=tb_autopay_sms_parsed.sub_account");
            $this->builder->join("tb_autopay_user_permission_bank_sub","tb_autopay_user_permission_bank_sub.sub_account_id=tb_autopay_bank_sub_account.id");
            */


            /* 
            $this->builder->groupStart();
            $this->builder->where("tb_autopay_user_permission_bank.view_transactions_in",1);
            $this->builder->orWhere("tb_autopay_user_permission_bank.view_amount_out",1);
            $this->builder->groupEnd();
            */

        }

        if($company_id)
            $this->builder->where("tb_autopay_bank_account.company_id",$company_id);

        if($bank_account_id)
            $this->builder->where("tb_autopay_bank_account.id",$bank_account_id);

        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = str_replace(',', '',trim($request->getVar('search')['value']));
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables($company_id, $bank_account_id = FALSE, $role='User', $hide_sub_account=FALSE) {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery($company_id, $bank_account_id, $role, $hide_sub_account);
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countTransactionsAll($company_id, $bank_account_id = FALSE, $role='User') {
     
        $builder = $this->db->table("tb_autopay_sms_parsed");
        $builder->join("tb_autopay_bank_account","tb_autopay_bank_account.account_number=tb_autopay_sms_parsed.account_number");
        $builder->join("tb_autopay_bank","tb_autopay_bank.brand_name=tb_autopay_sms_parsed.gateway");

        if($company_id)
            $builder->where("tb_autopay_bank_account.company_id",$company_id);

        if($bank_account_id)
            $builder->where("tb_autopay_bank_account.id",$bank_account_id);
       
        return $builder->countAllResults();
    }

    public function countTransactionsFiltered($company_id, $bank_account_id = FALSE, $role) {
        $this->_getDatatablesQuery($company_id, $bank_account_id, $role);
        if($company_id)
            $this->builder->where("tb_autopay_bank_account.company_id",$company_id);

        if($bank_account_id)
            $this->builder->where("tb_autopay_bank_account.id",$bank_account_id);
         
        return $this->builder->countAllResults();
    }

    public function getRecentTransactions($company_id, $limit=10, $start_date = FALSE) {
        $builder = $this->db->table($this->table);
        $builder->select("tb_autopay_sms_parsed.id ,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_sms_parsed.transaction_date, tb_autopay_sms_parsed.amount_out,tb_autopay_sms_parsed.amount_in,tb_autopay_sms_parsed.accumulated,tb_autopay_sms_parsed.transaction_content,tb_autopay_sms_parsed.reference_number,tb_autopay_bank_account.company_id, tb_autopay_sms_parsed.code,tb_autopay_sms_parsed.webhooks_success,tb_autopay_sms_parsed.webhooks_failed,tb_autopay_bank_account.company_id,tb_autopay_sms_parsed.sub_account,tb_autopay_sms_parsed.datecreated,tb_autopay_bank.icon_path");

        $builder->join("tb_autopay_bank","tb_autopay_bank.brand_name=tb_autopay_sms_parsed.gateway","left");
        $builder->join("tb_autopay_bank_account","tb_autopay_bank_account.account_number=tb_autopay_sms_parsed.account_number","left");
        $builder->where([
            'tb_autopay_sms_parsed.parser_status' => 'Success',
            'tb_autopay_sms_parsed.accumulated!=' => '',
            'tb_autopay_bank_account.company_id' => $company_id,
        ]);
        if($start_date)
            $builder->where("tb_autopay_sms_parsed.transaction_date >=", $start_date);
        $builder->orderBy('tb_autopay_sms_parsed.transaction_date', 'desc');

        return $builder->get($limit)->getResult();
    }


    function sum_income_by_account_number($company_id,$account_number=FALSE,$type,$start_date=FALSE,$end_date=FALSE) {
        $builder = $this->db->table($this->table);
        $builder->join("tb_autopay_bank_account","tb_autopay_bank_account.account_number=tb_autopay_sms_parsed.account_number","left");
        if($start_date === FALSE)
            $start_date = date('Y-m-d 00:00:00');

        if($end_date === FALSE) 
            $end_date = date('Y-m-d 23:59:59');
        
        if($type == "in")
            $builder->select("sum(tb_autopay_sms_parsed.amount_in) as `sum`");
        else if($type == "out")
            $builder->select("sum(tb_autopay_sms_parsed.amount_out) as `sum`");
        else
            return FALSE;

        $builder->where("tb_autopay_bank_account.company_id", $company_id);

        if($account_number)
            $builder->where('tb_autopay_sms_parsed.account_number',$account_number);

        $builder->where('tb_autopay_sms_parsed.transaction_date>=',$start_date);
        $builder->where('tb_autopay_sms_parsed.transaction_date<=',$end_date);
         
        $query = $builder->get();
        return $query->getRow();
    }

    public function get_account_balance($account_number, $datetime=FALSE) {
        if($datetime == FALSE)
            $datetime = date("Y-m-d 00:00:00");

        $builder = $this->db->table($this->table);
        $first_transaction = $builder->where(['account_number' => $account_number, 'transaction_date<=' => $datetime,'accumulated!=' => NULL,'accumulated!='=>''])->orderBy('transaction_date','DESC')->limit(1)->get()->getRow();

        if(is_object($first_transaction))
            return $first_transaction->accumulated;
        else
            return FALSE; 
    }

    public function get_account_credit($account_number, $start_date, $end_date) {
    
        $builder = $this->db->table($this->table);
        $result = $builder->select("sum(amount_in) as total")->where(['account_number' => $account_number, 'transaction_date>=' => $start_date,'transaction_date<=' => $end_date,'amount_in>'=>0])->get()->getRow();

         
        if(is_object($result))
            return $result->total;
        else
            return FALSE; 
    }

    public function get_account_debit($account_number, $start_date, $end_date) {
    
        $builder = $this->db->table($this->table);
        $result = $builder->select("sum(amount_out) as total")->where(['account_number' => $account_number, 'transaction_date>=' => $start_date,'transaction_date<=' => $end_date,'amount_out>'=>0])->get()->getRow();

        
        if(is_object($result))
            return $result->total;
        else
            return FALSE; 
    }


    public function addTransactionInDemo($account_number, $amount_in, $transaction_content) {
         
        $accounts_demo = ['*************','*************'];
        $sub_accounts_demo = ['VCB0011ABC001', 'VCB0011ABC002', 'VCB0011ABC003', 'VCB0011ABC004', 'VCB0011ABC005'];

        $sim = "**********";

        // random reference_number
        $reference_number = rand(100000,999999) . '.' . date("dmy") . '.' . date("his");

        // calculate accumulated
        $transactionsModel = model(TransactionsModel::class);

        $last_transaction  = $transactionsModel->where(['account_number' => $account_number])->orderBy('id','desc')->get()->getRow();

        
        if(!is_object($last_transaction))
            $last_accumulated = 0;
        else
            $last_accumulated = $last_transaction->accumulated;

        $accumulated = $last_accumulated + $amount_in;

        //SD TK ********** +3,342,000VND luc 12-02-2023 20:36:53. SD 26,223,981,662VND. Ref MBVCB.**********.Anh Hien 198HVT.CT tu ************* TRAN THI THUY LINH toi...
        $body = "SD TK " . $account_number . " +" . number_format($amount_in) . 'VND luc ' . date("d-m-Y H:i:s", strtotime("14 seconds ago")) . ". SD " . number_format($accumulated) . "VND. Ref " . $reference_number . "." . $transaction_content;

        $post_data = ["from" => "Vietcombank", "body" => $body, "to" => $sim];

        
        // Start the request
        $curl = curl_init();

        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 5); 
        curl_setopt($curl, CURLOPT_TIMEOUT, 8); //timeout in seconds
        curl_setopt($curl, CURLOPT_URL, base_url('coreapi/sms/create'));
        curl_setopt($curl, CURLOPT_USERAGENT, "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36");
        curl_setopt($curl, CURLOPT_POST, TRUE);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($post_data));
        curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        
        $response = curl_exec($curl);

        return TRUE;

    }

    public function getExitingSmsTransactionVcb($account_number, $content, $date, $amount, $type) {
        // Get reference number from content.
        //  MBVCB.**********.Ma so 104905.CT tu ************* SON THI CAM HONG toi0331000417458 CTY TNHH LUU TRU SO
        // signature in sms: date, amount, content
        // sms string from ibanking

        // Replacing multiple spaces with a single space
        $content = preg_replace('!\s+!', ' ', $content);

        $content_to_sms = "Ref " . $content;
        $content_to_sms = substr($content_to_sms, 0, 70);

        $account_to_sms = "SD TK " . $account_number;

       /* $date_to_sms = date_create_from_format( "Y-m-d", $date);

        var_dump($content_to_sms);
        if(!$date_to_sms)
            return FALSE;

        $date_to_sms = "luc " . date_format($date_to_sms,'d-m-Y'); */

        $date_to_sms = "luc " . date("d-m-Y" , strtotime($date));

        if(!$date_to_sms)
            return FALSE;

        
        if($type == "credit")
            $amount_to_sms = "+". number_format($amount). "VND";
        else if($type == "debit")
            $amount_to_sms = "-". number_format($amount). "VND";

        $db = db_connect();

        $sql =   "select * from tb_autopay_sms where `from`='Vietcombank' and body like '".$account_to_sms."%' and body like '%" . $content_to_sms . "%' and body like '%" . $amount_to_sms . "%' and body like '%" . $date_to_sms . "%'";

        $query = $db->query($sql);
        $results =  $query->getResult();

        if(is_array($results) && count($results) == 1)
            return $results[0];
        else {
           // var_dump((string)$db->getLastQuery());
         
       
            return FALSE;
        }
        
      
        
    }

    public function getExitingIbankingTransactionVcb($account_number, $content, $date_time, $amount_in, $amount_out) {

        // find existing transaction

        $smsParserModel = model(SmsParserModel::class);

        $start_date_compare = date("Y-m-d H:i:s", strtotime($date_time . ' -1 day'));
        $end_date_compare = date("Y-m-d H:i:s", strtotime($date_time . ' +1 day'));

        $results = $smsParserModel->where(['account_number' => $account_number, "source" => "IBanking", "amount_in" => $amount_in, "amount_out"=>$amount_out, 'transaction_date>' => $start_date_compare, 'transaction_date<' => $end_date_compare])->like(["transaction_content" => $content])->get()->getResult();

        var_dump($results);

        // 
    }
    
}