<?php

namespace App\Models;

use CodeIgniter\Model;

class BidvEnterpriseAccountModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_bidv_enterprise_account';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['prefix_id', 'bank_account_id', 'va_prefix', 'custom_va_name', 'merchant_id'];
}
