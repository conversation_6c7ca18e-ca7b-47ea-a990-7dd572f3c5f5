<?php

namespace App\Models;

use CodeIgniter\Model;

class OutputDeviceDecalModel extends Model
{
    protected $table = 'tb_autopay_output_device_decal';

    protected $returnType = 'object';

    protected $allowedFields = [
        'physical_order_id',
        'physical_order_item_id',
        'bank_id',
        'output_device_id',
        'account_number',
        'virtual_account_number',
        'account_holder_name',
        'content',
        'qrcode',
        'qrcode_printed',
    ];

    public function getPreparationProgress(int $orderId): array
    {
        $items = $this->where('physical_order_id', $orderId)->findAll();

        $total = count($items);
        $completed = 0;
        $needsQR = 0;
        $needsSpeaker = 0;

        foreach ($items as $item) {
            if (empty($item->output_device_id)) {
                $needsSpeaker++;
            }

            if (!empty($item->account_number) && empty($item->virtual_account_number)) {
                $needsQR++;
            }

            if (!empty($item->output_device_id) && 
                (empty($item->account_number) || !empty($item->virtual_account_number))) {
                $completed++;
            }
        }

        return [
            'total' => $total,
            'completed' => $completed,
            'needs_qr' => $needsQR,
            'needs_speaker' => $needsSpeaker,
            'progress_percentage' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
            'is_ready' => $completed === $total && $total > 0,
        ];
    }
}
