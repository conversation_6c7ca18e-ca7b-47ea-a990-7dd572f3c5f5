<?php

namespace App\Models;

use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\Model;

class PhysicalOrderModel extends Model
{
    protected $table = 'tb_autopay_physical_orders';

    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType = 'object';

    protected $allowedFields = [
        'order_code',
        'customer_type',
        'customer_name',
        'customer_phone',
        'customer_email',
        'province',
        'district',
        'ward',
        'address',
        'payment_method',
        'payment_status',
        'subtotal',
        'discount_amount',
        'total_amount',
        'shipping_company',
        'shipping_fee',
        'tracking_code',
        'tracking_id',
        'status',
        'notes',
        'last_update_status',
        'user_id',
        'partner_id',
        'created_at',
        'bank_referral_code',
        'create_shipping_api_response',
    ];
    
    protected $beforeUpdate = ['parseNewTrackingIdFromTrackingCode'];
    
    protected function parseNewTrackingIdFromTrackingCode(array $data)
    {
        if (isset($data['data']['tracking_code']) && ! empty($data['data']['tracking_code'])) {
            $data['data']['tracking_id'] = preg_match('/\.(\d+)$/', $data['data']['tracking_code'], $matches) ? $matches[1] : null;
        }

        return $data;
    }

    protected function _getDatatablesQuery(RequestInterface $request)
    {
        $orderColumns = [
            'tb_autopay_physical_orders.order_code',
            'tb_autopay_physical_orders.customer_name',
            'tb_autopay_physical_orders.customer_phone',
            'total_quantity',
            'tb_autopay_physical_orders.total_amount',
            'tb_autopay_physical_orders.status',
            'tb_autopay_physical_orders.payment_status',
            null,
            'tb_autopay_physical_orders.created_at',
            null,
        ];

        $searchColumns = [
            'tb_autopay_physical_orders.order_code',
            'tb_autopay_physical_orders.customer_name',
            'tb_autopay_physical_orders.bank_referral_code',
            'tb_autopay_physical_orders.customer_phone',
            'tb_autopay_physical_orders.customer_email',
            'tb_autopay_physical_orders.total_amount',
            'tb_autopay_physical_orders.tracking_code',
            'tb_autopay_physical_orders.address',
            'tb_autopay_physical_orders.status',
            'tb_autopay_physical_orders.payment_status',
            'tb_autopay_physical_order_tracking.ip_address',
            'tb_autopay_physical_order_tracking.utm_source',
            'tb_autopay_physical_order_tracking.utm_medium',
            'tb_autopay_physical_order_tracking.utm_campaign',
        ];

        $this
            ->select([
                'tb_autopay_physical_orders.id',
                'tb_autopay_physical_orders.order_code',
                'tb_autopay_physical_orders.customer_name',
                'tb_autopay_physical_orders.customer_phone',
                'tb_autopay_physical_orders.bank_referral_code',
                'tb_autopay_physical_orders.total_amount',
                'tb_autopay_physical_orders.status',
                'tb_autopay_physical_orders.payment_status',
                'tb_autopay_physical_orders.created_at',
                'tb_autopay_physical_order_tracking.ip_address',
                'tb_autopay_physical_order_tracking.utm_source',
                'tb_autopay_physical_order_tracking.utm_medium',
                'tb_autopay_physical_order_tracking.utm_campaign',
                'COALESCE(SUM(tb_autopay_physical_order_items.quantity), 0) as total_quantity',
            ])
            ->join('tb_autopay_physical_order_tracking', 'tb_autopay_physical_order_tracking.order_id = tb_autopay_physical_orders.id', 'left')
            ->join('tb_autopay_physical_order_items', 'tb_autopay_physical_order_items.order_id = tb_autopay_physical_orders.id', 'left')
            ->groupBy('tb_autopay_physical_orders.id');

        if ($request->getVar('search') && ! empty($request->getVar('search')['value'])) {
            $searchTerm = trim($request->getVar('search')['value']);
            $this->groupStart();
            foreach ($searchColumns as $i => $item) {
                if ($i === 0) {
                    $this->like($item, $searchTerm);
                } else {
                    $this->orLike($item, $searchTerm);
                }
            }
            $this->groupEnd();
        }

        foreach ($searchColumns as $key => $item) {
            if ($item !== null && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] !== '') {
                $this->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }

        if (
            $request->getVar('order')
            && isset($request->getVar('order')[0]['column'])
            && isset($orderColumns[$request->getVar('order')[0]['column']])
        ) {
            $this->orderBy($orderColumns[$request->getVar('order')[0]['column']], $request->getVar('order')[0]['dir']);
        } else {
            $this->orderBy('id', 'DESC');
        }

        $dateFrom = $request->getPost('date_from');
        $dateTo = $request->getPost('date_to');

        if (! empty($dateFrom)) {
            $this->where('tb_autopay_physical_orders.created_at >=', $dateFrom . ' 00:00:00');
        }

        if (! empty($dateTo)) {
            $this->where('tb_autopay_physical_orders.created_at <=', $dateTo . ' 23:59:59');
        }

        if ($status = $request->getPost('status')) {
            if (is_array($status)) {
                if (! empty($status)) {
                    $this->whereIn('tb_autopay_physical_orders.status', $status);
                }
            } else {
                $this->where('tb_autopay_physical_orders.status', $status);
            }
        } else {
            $this->where('tb_autopay_physical_orders.status !=', 'Cancelled');
        }

        if ($paymentStatus = $request->getPost('payment_status')) {
            $this->where('tb_autopay_physical_orders.payment_status', $paymentStatus);
        }

        if ($customerType = $request->getPost('customer_type')) {
            $this->where('tb_autopay_physical_orders.customer_type', $customerType);
        }

        if ($source = $request->getPost('source')) {
            if ($source === 'Sale order') {
                $this->whereIn('tb_autopay_physical_order_tracking.ip_address', ['**************', '***************']);
            } elseif ($source === 'Unknown') {
                $this
                    ->groupStart()
                        ->where('tb_autopay_physical_order_tracking.utm_source IS NULL')
                        ->orWhere('tb_autopay_physical_order_tracking.utm_source', '')
                    ->groupEnd();
            } else {
                $this->where('tb_autopay_physical_order_tracking.utm_source', $source);
            }
        }
    }

    public function getDatatables(RequestInterface $request): array
    {
        $this->_getDatatablesQuery($request);

        if ($request->getVar('length') !== null && $request->getVar('length') !== -1) {
            $this->limit($request->getVar('length'), $request->getVar('start'));
        }

        return $this->get()->getResult();
    }

    public function countAll(): int
    {
        return $this->countAllResults();
    }

    public function countFiltered(RequestInterface $request): int
    {
        $this->_getDatatablesQuery($request);

        return $this->countAllResults();
    }

    public function getPaymentStatusCounts(array $filters = []): array
    {
        $builder = $this->builder();

        if (! empty($filters)) {
            if (! empty($filters['date_from'])) {
                $builder->where('created_at >=', $filters['date_from'] . ' 00:00:00');
            }

            if (! empty($filters['date_to'])) {
                $builder->where('created_at <=', $filters['date_to'] . ' 23:59:59');
            }

            if (! empty($filters['status']) && is_array($filters['status'])) {
                $builder->whereIn('status', $filters['status']);
            } elseif (! empty($filters['status'])) {
                $builder->where('status', $filters['status']);
            } else {
                $builder->where('status !=', 'Cancelled');
            }

            if (! empty($filters['customer_type'])) {
                $builder->where('customer_type', $filters['customer_type']);
            }
        }

        $result = $builder->select('payment_status, COUNT(*) as count')
            ->groupBy('payment_status')
            ->get()
            ->getResultArray();

        $counts = [
            'Unpaid' => 0,
            'Paid' => 0,
            'Refunded' => 0,
        ];

        foreach ($result as $row) {
            $counts[$row['payment_status']] = (int) $row['count'];
        }

        return $counts;
    }

    public function getCustomerTypeCounts(array $filters = []): array
    {
        $builder = $this->builder();

        if (! empty($filters)) {
            if (! empty($filters['date_from'])) {
                $builder->where('created_at >=', $filters['date_from'] . ' 00:00:00');
            }

            if (! empty($filters['date_to'])) {
                $builder->where('created_at <=', $filters['date_to'] . ' 23:59:59');
            }

            if (! empty($filters['status']) && is_array($filters['status'])) {
                $builder->whereIn('status', $filters['status']);
            } elseif (! empty($filters['status'])) {
                $builder->where('status', $filters['status']);
            } else {
                $builder->where('status !=', 'Cancelled');
            }

            if (! empty($filters['payment_status'])) {
                $builder->where('payment_status', $filters['payment_status']);
            }
        }

        $result = $builder->select('customer_type, COUNT(*) as count')
            ->groupBy('customer_type')
            ->get()
            ->getResultArray();

        $counts = [
            'retail' => 0,
            'wholesale' => 0,
        ];

        foreach ($result as $row) {
            $counts[$row['customer_type']] = (int) $row['count'];
        }

        return $counts;
    }

    public function getSourceList(): array
    {
        $sourceExpr = "CASE 
            WHEN tb_autopay_physical_order_tracking.ip_address IN ('**************', '***************') THEN 'Sale order'
            WHEN tb_autopay_physical_order_tracking.utm_source IS NULL OR TRIM(tb_autopay_physical_order_tracking.utm_source) = '' THEN 'Unknown'
            ELSE tb_autopay_physical_order_tracking.utm_source 
        END";

        $result = $this->db->table('tb_autopay_physical_orders')
            ->select([
                "{$sourceExpr} as source",
                "COUNT(DISTINCT tb_autopay_physical_orders.id) as count"
            ])
            ->join('tb_autopay_physical_order_tracking', 'tb_autopay_physical_orders.id = tb_autopay_physical_order_tracking.order_id', 'left')
            ->groupBy('source')
            ->orderBy('count', 'DESC')
            ->get()
            ->getResultArray();

        return $result;
    }
}
