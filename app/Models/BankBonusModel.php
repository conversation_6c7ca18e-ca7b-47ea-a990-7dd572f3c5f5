<?php

namespace App\Models;

use CodeIgniter\Model;

class BankBonusModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_bank_bonus';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'id', 'company_id', 'bank_id', 'account_number', 'bank_bonus', 'active', 'created_at', 'deleted_at'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    public function getSummaryData($start_date = null, $end_date = null)
    {
        $builder = $this->builder();
        
        if ($start_date && $end_date) {
            $builder->where('created_at >=', $start_date . ' 00:00:00')
                   ->where('created_at <=', $end_date . ' 23:59:59');
        }
        
        $builderBanks = clone $builder;
        $builderCompanies = clone $builder;
        
        return [
            'total_transactions' => (int)($builder->selectSum('bank_bonus')->get()->getRow()->bank_bonus ?? 0),
            'total_banks' => $builderBanks->distinct()->select('bank_id')->where('tb_autopay_bank_bonus.bank_bonus >', 0, false)->countAllResults(),
            'total_companies' => $builderCompanies->distinct()->select('company_id')->where('tb_autopay_bank_bonus.bank_bonus >', 0, false)->countAllResults()
        ];
    }

    private function _getDatatablesQuery() {

        $request = \Config\Services::request();
  
        $column_order = array(
            null,
            'tb_autopay_company.short_name',
            'tb_autopay_user.email',
            'tb_autopay_bank.brand_name'
        );

        $column_search = array(
            'tb_autopay_company.short_name',
            'tb_autopay_user.email',
            'tb_autopay_bank.brand_name',
            'tb_autopay_bank_bonus.bank_bonus',
            'tb_autopay_bank_bonus.account_number'
        );
    
        $this->builder = $this->db->table($this->table);

        $this->builder->select(
            "tb_autopay_company.short_name,
            tb_autopay_company.id,
            tb_autopay_user.email,
            tb_autopay_bank.brand_name,
            tb_autopay_bank_bonus.bank_bonus");

        $this->builder->select("
            GROUP_CONCAT(
                CONCAT(
                    tb_autopay_bank.brand_name,
                    ' - ',
                    '<b>',
                    tb_autopay_bank_bonus.account_number,
                    '</b>',
                    ' <span class=\"text-success\">(+',
                    tb_autopay_bank_bonus.bank_bonus,
                    ')</span>'
                )
                ORDER BY tb_autopay_bank.brand_name ASC
                SEPARATOR ', '
            ) as bank_bonus_info
        ");

        $this->builder->join('tb_autopay_company', 'tb_autopay_company.id = tb_autopay_bank_bonus.company_id')
        ->join('tb_autopay_company_user', 'tb_autopay_company_user.company_id = tb_autopay_company.id')
        ->join('tb_autopay_user', 'tb_autopay_user.id = tb_autopay_company_user.user_id AND tb_autopay_company_user.role = "SuperAdmin"')
        ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_bonus.bank_id');
        
        $this->builder->where('tb_autopay_bank_bonus.bank_bonus >', 0, false);

        $this->builder->groupBy('tb_autopay_bank_bonus.company_id');

        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {
            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);
        }

        $this->builder->orderBy('tb_autopay_bank_bonus.created_at', 'DESC');
    }

    public function getDatatables() {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery();
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll() {
     
        $builder = $this->db->table($this->table);
        $builder->groupBy('tb_autopay_bank_bonus.company_id');
        $builder->where('tb_autopay_bank_bonus.bank_bonus >', 0, false);

        return $builder->countAllResults();
    }

    public function countFiltered() {
        $this->_getDatatablesQuery();
         
        return $this->builder->countAllResults();
    }

}
