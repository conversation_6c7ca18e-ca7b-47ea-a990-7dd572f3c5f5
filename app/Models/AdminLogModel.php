<?php

namespace App\Models;

use CodeIgniter\Model;

class AdminLogModel extends Model
{
    protected $table = 'tb_autopay_admin_log';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['data_id', 'data_type', 'description','ip','user_agent', 'status', 'created_at','admin_id'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;

    protected $order = ['tb_autopay_admin_log.id'=>'desc'];

    protected $builder;

  
    
}