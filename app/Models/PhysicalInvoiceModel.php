<?php

namespace App\Models;

use CodeIgniter\Model;
use Config\SInvoice;

class PhysicalInvoiceModel extends Model
{
    protected $table = 'tb_autopay_physical_invoices';

    protected $returnType = 'object';

    protected $allowedFields = [
        'order_id',
        'invoice_id',
        'invoice_date',
        'tax_rate',
        'total_amount',
        'tax_amount',
        'shipping_fee',
        'discount_amount',
        'final_amount',
        'payment_method',
        'status',
        'created_at'
    ];

    public function getTotalEligibleInvoices($fromDate)
    {
        return $this
            ->select('tb_autopay_physical_invoices.id')
            ->join('tb_autopay_physical_orders', 'tb_autopay_physical_orders.id = tb_autopay_physical_invoices.order_id')
            ->join('tb_autopay_invoice', 'tb_autopay_invoice.physical_invoice_id = tb_autopay_physical_invoices.id', 'left')
            ->join('(SELECT invoice_id, SUM(`in`) as total_paid FROM tb_autopay_stransaction GROUP BY invoice_id) as st', 'st.invoice_id = tb_autopay_invoice.id', 'left')
            ->where('tb_autopay_physical_orders.customer_type', 'retail')
            ->where('tb_autopay_physical_orders.payment_status', 'Paid')
            ->where('tb_autopay_physical_orders.status', 'Completed')
            ->whereIn('tb_autopay_physical_orders.shipping_company', ['GHTK', 'Self Delivery'])
            ->where('tb_autopay_physical_orders.created_at >=', $fromDate)
            ->groupStart()
                ->where('tb_autopay_physical_orders.shipping_company !=', 'Self Delivery')
                ->orGroupStart()
                    ->where('tb_autopay_physical_orders.shipping_company', 'Self Delivery')
                    ->where('tb_autopay_invoice.id IS NOT NULL')
                    ->where('st.total_paid >= tb_autopay_invoice.total')
                ->groupEnd()
            ->groupEnd()
            ->countAllResults();;
    }

    public function getTotalIssuedInvoices($fromDate)
    {
        return $this
            ->join('tb_autopay_physical_orders', 'tb_autopay_physical_orders.id = tb_autopay_physical_invoices.order_id')
            ->where('tb_autopay_physical_invoices.status', 'issued')
            ->where('tb_autopay_physical_orders.created_at >=', $fromDate)
            ->countAllResults();
    }
}
