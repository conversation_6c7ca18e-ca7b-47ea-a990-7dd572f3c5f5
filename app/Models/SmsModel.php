<?php

namespace App\Models;

use CodeIgniter\Model;

class SmsModel extends Model
{
    protected $table = 'tb_autopay_sms';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['from','to','body','status', 'date_sms','time_taken','processed_result', 'processed_alert','match_percent'];

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $useSoftDeletes = false;

    protected $useTimestamps     = false;

    protected $dateFormat        = 'datetime';

    protected $order = ['id'=>'desc'];

    protected $validationRules    = [
        'from'        => 'required',
        'body'        => 'required',
        'to'        => 'required'
    ];
    protected $skipValidation     = false;

    protected $builder;

    public function getByID($id) {
        $builder = $this->db->table($this->table);

        $builder->where("id",$id);

        if($client_id)
            $builder->where("client_id",$client_id);
 
        $query = $builder->get();

        return $query->getRow();
    }


   
}