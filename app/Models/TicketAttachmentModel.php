<?php

namespace App\Models;

use CodeIgniter\Model;

class TicketAttachmentModel extends Model
{
    protected $table = 'tb_autopay_ticket_attachments';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $allowedFields = [
        'ticket_id', 
        'reply_id', 
        'file_name', 
        'file_path', 
        'file_type', 
        'file_size',
        'company_id',
        'created_by',
        's3_key'
    ];

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    protected $useSoftDeletes = false;
    protected $useTimestamps = true;

    protected $dateFormat = 'datetime';

    protected $order = ['id' => 'desc'];

    /**
     * Get attachments for a ticket
     *
     * @param int $ticketId Ticket ID
     * @return array Attachment objects
     */
    public function getTicketAttachments($ticketId)
    {
        return $this->where('ticket_id', $ticketId)
                    ->where('reply_id IS NULL')
                    ->orderBy('created_at', 'DESC')
                    ->get()
                    ->getResult();
    }

    /**
     * Get attachments for a ticket reply
     *
     * @param int $replyId Reply ID
     * @return array Attachment objects
     */
    public function getReplyAttachments($replyId)
    {
        return $this->where('reply_id', $replyId)
                    ->orderBy('created_at', 'DESC')
                    ->get()
                    ->getResult();
    }
}