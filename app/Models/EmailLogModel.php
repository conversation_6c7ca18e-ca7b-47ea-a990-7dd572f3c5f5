<?php

namespace App\Models;

use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\Model;

class EmailLogModel extends Model
{
    protected $table = 'tb_autopay_email_log';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['company_id', 'email_type', 'data_id', 'email_to', 'email_from', 'subject', 'message', 'status', 'error'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [];
    protected $skipValidation     = false;

    protected $order = ['id' => 'desc'];

    protected function _getDatatablesQuery(RequestInterface $request, $companyId = null)
    {
        if ($companyId) {
            $this->where('company_id', $companyId);
        }
    }

    public function getDatatables(RequestInterface $request, $companyId = null)
    {
        $this->_getDatatablesQuery($request, $companyId);

        if ($request->getVar('length') !== null && $request->getVar('length') !== -1) {
            $this->limit($request->getVar('length'), $request->getVar('start'));
        }

        return $this->get()->getResult();
    }

    public function countAll($companyId = null): int
    {
        if ($companyId) {
            $this->where('company_id', $companyId);
        }

        return $this->countAllResults();
    }

    public function countFiltered(RequestInterface $request, $companyId = null): int
    {
        $this->_getDatatablesQuery($request, $companyId);

        return $this->countAllResults();
    }
}
