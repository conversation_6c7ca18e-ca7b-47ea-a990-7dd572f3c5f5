<?php

namespace App\Models;

use CodeIgniter\Model;

class AdminPermissionFeatureModel extends Model
{
    protected $table = 'tb_autopay_admin_permission_feature';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['admin_id','feature_slug','can_view_all','can_add','can_edit','can_delete'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [];
    protected $skipValidation     = false;

    protected $features = ['Invoice','Company','Stransaction','User','Log','Sim','LoginAsClient', 'Subscription','CrmActivity','Partner','Referral', 'ChannelPartner', 'CampaignAnalytic','Outputdevice', 'BankBonus', 'OtherIntergation'];
    

    public function delete_permission_by_admin($admin_id) {
        $builder = $this->db->table($this->table);
        $builder->where(['admin_id' => $admin_id])->delete();
    }

    public function initialize_permission($admin_id) {
        $this->delete_permission_by_admin($admin_id);

        $builder = $this->db->table($this->table);
        foreach($this->features as $feature_slug) {
            $builder->insert(['admin_id' => $admin_id, 'feature_slug' => $feature_slug, 'can_view_all' => 0, 'can_add' => 0,'can_edit' => 0,'can_delete' => 0]);
        }
    }

    public function get_features() {
        return $this->features;
    }
    
}