<?php

namespace App\Models;

use CodeIgniter\Model;

class ConfigurationModel extends Model
{
    protected $table = 'tb_autopay_configuration';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['company_id', 'setting', 'value', 'merchant_id'];

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    protected $useSoftDeletes = false;

    protected $useTimestamps     = false;

    protected $dateFormat        = 'datetime';

    protected $order = ['id' => 'desc'];

    protected $validationRules    = [];
    protected $skipValidation     = false;

    protected $builder;

}
