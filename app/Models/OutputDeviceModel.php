<?php

namespace App\Models;

use CodeIgniter\Model;

class OutputDeviceModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_output_device';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'mqtt_server_id',
        'mqtt_client_id',
        'imei',
        'serial_number',
        'model',
        'vendor',
        'online_status',
        'max_amount',
        'min_amount',
        'required_content',
        'qr_content',
        'qr_bank_name',
        'qr_bank_account',
        'qr_amount',
        'qr_des',
        'external_device_id',
        'external_device_token',
        'name',
        'serial_sim',
        'active',
        'company_id',
        'active_date',
        'bank_id',
        'va_id',
        'phone_serial_sim',
    ];
    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
}
