<?php

namespace App\Models;

use CodeIgniter\Model;

class PartnerWithdrawModel extends Model
{
    protected $table = 'tb_autopay_partner_withdraw';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['partner_id','amount','body','status','reject_reason','public_note', 'ticket_alert','admin_answer'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [];
    protected $skipValidation     = false;
    protected $order = ['tb_autopay_partner_withdraw.id'=>'desc'];

    protected $builder;


    private function _getDatatablesQuery($partner_id) {

    
        $request = \Config\Services::request();
  
        $column_order = array(null,'id','partner_id', 'amount', 'status', null,'created_at');
        $column_search = array('amount','id', 'body','status', 'admin_answer');
    
        $this->builder = $this->db->table($this->table);

        $this->builder->select("id, amount, status, body, admin_answer, admin_note, reject_reason, partner_id, created_at");
 
        if($partner_id)
            $this->builder->where("partner_id",$partner_id);

        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables($partner_id=FALSE) {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery($partner_id);
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll($partner_id=FALSE) {
     
        $builder = $this->db->table($this->table);
        if($partner_id)
            $builder->where(['partner_id' => $partner_id]);

       
        return $builder->countAllResults();
    }

    public function countFiltered($partner_id=FALSE) {
        $this->_getDatatablesQuery($partner_id);
         
        return $this->builder->countAllResults();
    }


    
}