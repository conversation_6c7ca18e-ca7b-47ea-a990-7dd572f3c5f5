<?php

namespace App\Models;

use CodeIgniter\Model;

class OtherIntergationModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_other_intergation';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['id_other_type_intergation', 'path_image', 'name', 'description', 'content', 'url_path', 'position', 'active'];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    private function _getDatatablesQuery() {

        $request = \Config\Services::request();
  
        $column_order = array(
            'tb_autopay_other_intergation.id',
            'tb_autopay_other_intergation.id_other_type_intergation',
            'tb_autopay_other_intergation.path_image',
            'tb_autopay_other_intergation.name',
            'tb_autopay_other_intergation.description',
            null,
            'tb_autopay_other_intergation.position',
        );

        $column_search = array(
            'tb_autopay_other_type_intergation.name',
            'tb_autopay_other_intergation.name',
            'tb_autopay_other_intergation.description',
            'tb_autopay_other_intergation.position',
        );
    
        $this->builder = $this->db->table($this->table);

        $this->builder->select(
            "tb_autopay_other_intergation.id,
            tb_autopay_other_intergation.path_image,
            tb_autopay_other_intergation.name,
            tb_autopay_other_intergation.description,
            tb_autopay_other_intergation.active,
            tb_autopay_other_intergation.position,
            tb_autopay_other_type_intergation.name as name_type"
        );        
        
        $this->builder->join('tb_autopay_other_type_intergation', 'tb_autopay_other_type_intergation.id = tb_autopay_other_intergation.id_other_type_intergation');

        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {
            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);
        }

        $this->builder->orderBy('tb_autopay_other_intergation.position', 'ASC');
    }

    public function getDatatables() {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery();
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll() {
     
        $builder = $this->db->table($this->table);

        return $builder->countAllResults();
    }

    public function countFiltered() {
        $this->_getDatatablesQuery();
         
        return $this->builder->countAllResults();
    }

}
