<?php

namespace App\Models;

use CodeIgniter\Model;

class NotificationLarkMessengerModel extends Model
{
    protected $table = 'tb_autopay_notification_larkmessenger';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['company_id', 'bank_account_id', 'sub_account_id','contains_content', 'transaction_type','webhook_type', 'bot_webhook_url', 'description','hide_accumulated', 'hide_details_link', 'ignore_phrases', 'verify_payment', 'active', 'is_template_custom', 'template_custom', 'template_name', 'amount_in_less_than_equal_to', 'amount_in_great_than_equal_to'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;
    protected $order = ['tb_autopay_notification_larkmessenger.id'=>'desc'];


    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;
    
    
}