<?php

namespace App\Models;

use CodeIgniter\Model;

class PhysicalOrderHistoryModel extends Model
{
    protected $table = 'tb_autopay_physical_order_history';

    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType = 'object';

    protected $allowedFields = [
        'admin_id',
        'order_id',
        'status',
        'payment_status',
        'note',
        'created_at',
    ];

    public function recordHistory(object $order, string $note, $adminId = 0)
    {
        $this->insert([
            'admin_id' => $adminId,
            'order_id' => $order->id,
            'status' => $order->status,
            'payment_status' => $order->payment_status,
            'note' => $note,
        ]);
    }
}
