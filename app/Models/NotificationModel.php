<?php

namespace App\Models;

use CodeIgniter\Model;

class NotificationModel extends Model
{
    protected $table = 'tb_autopay_notification';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['title','body','send_mail','step','channels', 'description', 'user_filter', 'user_filter_payload', 'sent_at', 'notificable_type', 'notificable_id', 'hidden'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;

    protected $useTimestamps     = false;

    protected $validationRules    = [];
    
    protected $skipValidation     = false;
    
    protected $order = ['tb_autopay_notification.id'=>'DESC'];
 
    protected $builder;

    private function _getDatatablesQuery() {

        $request = \Config\Services::request();
  
        $column_order = array(null,'tb_autopay_notification.id','tb_autopay_notification.name','tb_autopay_notification.class_id','tb_autopay_notification.start_time','tb_autopay_notification.end_time','tb_autopay_notification.location_name','tb_autopay_notification.created_at', 'hide_in_user');
        $column_search = array('tb_autopay_notification.name');
    
        $this->builder = $this->db->table($this->table);

        //$this->builder->join("tb_autopay_class","tb_autopay_class.id=tb_autopay_notification.class_id","left");


        $this->builder->select("tb_autopay_notification.id,tb_autopay_notification.step,tb_autopay_notification.name,tb_autopay_notification.class_id,tb_autopay_notification.created_at,tb_autopay_notification.start_time,tb_autopay_notification.end_time,tb_autopay_notification.location_name, tb_autopay_notification.homework_deadline, tb_autopay_notification.slug");
        $this->builder->where(['tb_autopay_notification.hide_in_user' => 0]);
 
        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables() {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery();
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll() {
     
        $builder = $this->db->table($this->table);

       
        return $builder->countAllResults();
    }

    public function countFiltered() {
        $this->_getDatatablesQuery();
         
        return $this->builder->countAllResults();
    }


    private function _getDatatablesQueryForAdmin() {

        $request = \Config\Services::request();
  
        $column_order = array(null,'tb_autopay_notification.id','tb_autopay_notification.title','tb_autopay_notification.send_mail',null,'tb_autopay_notification.created_at');
        $column_search = array('tb_autopay_notification.title','tb_autopay_notification.created_at', 'tb_autopay_notification.id');
    
        $this->builder = $this->db->table($this->table);

        $this->builder->select("tb_autopay_notification.id,tb_autopay_notification.step,tb_autopay_notification.title,tb_autopay_notification.send_mail,tb_autopay_notification.sent_at,tb_autopay_notification.hidden,tb_autopay_notification.channels,tb_autopay_notification.created_at");
 
        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatablesForAdmin() {
        $request = \Config\Services::request();
        $this->_getDatatablesQueryForAdmin();
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAllForAdmin() {
     
        $builder = $this->db->table($this->table);

       
        return $builder->countAllResults();
    }

    public function countFilteredForAdmin() {
        $this->_getDatatablesQueryForAdmin();
         
        return $this->builder->countAllResults();
    }

}
