<?php

namespace App\Models;

use CodeIgniter\Model;

class CounterModel extends Model
{
    protected $table = 'tb_autopay_counter';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['company_id','date','transaction','transaction_in','transaction_out','chat','telegram','lark_messenger','webhook', 'webhooks_pay_success','webhook_success', 'webhook_failed'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;

    protected $useTimestamps     = false;

    protected $validationRules    = [];
    protected $skipValidation     = false;
    protected $builder;

    public function count_transaction_by_interval($company_id, $begin_date, $end_date) {
        $builder = $this->db->table($this->table);

        $result = $builder->select("sum(transaction) as `sumrows`")->where(['company_id' => $company_id, 'date>=' => $begin_date, 'date<=' => $end_date])->get()->getRow();

        return $result->sumrows;
    }
    
}