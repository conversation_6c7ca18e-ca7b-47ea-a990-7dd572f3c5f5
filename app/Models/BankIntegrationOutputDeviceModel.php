<?php

namespace App\Models;

use CodeIgniter\Model;

class BankIntegrationOutputDeviceModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_bank_integration_output_device';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['output_device_id','company_id','bank_account_id','sub_account_id'];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    public function deleteData($where)
    {
        if (empty($where)) {
            return ['status' => false, 'error' => 'điều kiện không được để trống.'];
        }

        try {
            $deleted = $this->where($where)->delete();
            if ($deleted === false) {
                return ['status' => false, 'errors' => $this->errors()];
            }
            return ['status' => true, 'affected_rows' => $this->db->affectedRows()];
        } catch (\Exception $e) {
            return ['status' => false, 'error' => $e->getMessage()];
        }
    }

    // Hàm insert batch dữ liệu
    public function insertBatchData(array $data)
    {
        try {
            $this->insertBatch($data);
            $lastInsertId = $this->db->insertID();
            $insertedCount = count($data);
            $insertedIds = range($lastInsertId - $insertedCount + 1, $lastInsertId);
            return ['status' => true, 'inserted_ids' => $insertedIds];
        } catch (\Exception $e) {
            return ['status' => false, 'error' => $e->getMessage()];
        }
    }
}
