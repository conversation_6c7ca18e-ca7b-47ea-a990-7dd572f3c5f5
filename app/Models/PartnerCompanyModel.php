<?php

namespace App\Models;

use CodeIgniter\Model;

class PartnerCompanyModel extends Model
{
    protected $table = 'tb_autopay_partner_company';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['company_id','partner_id','notes','type'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;
    
    protected $order = ['tb_autopay_partner_company.id'=>'desc'];
 
    protected $builder;

 


}