<?php

namespace App\Models;

use CodeIgniter\Model;

class TicketReplyModel extends Model
{
    protected $table = 'tb_autopay_ticket_reply';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['ticket_id','body','owner_id','owner_type','name', 'email', 'alert_admin'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;

    protected $useTimestamps     = false;

    protected $validationRules    = [];
    protected $skipValidation     = false;

    protected $builder;
    
    function sendEmailReplyTicket($ticket_id) {
        
        $companyModel = model(CompanyModel::class);
        $userModel = model(UserModel::class);
        $ticketModel = model(TicketModel::class);

        $ticket_details = $ticketModel->where(['id' => $ticket_id])->get()->getRow();
 
        if(!is_object($ticket_details))
            return FALSE;

        $company_details = $companyModel->find($ticket_details->company_id);

        if(!is_object($company_details))
            return FALSE;

        $user_details = $userModel->select("tb_autopay_user.email, tb_autopay_user.firstname, tb_autopay_user.lastname")->join("tb_autopay_company_user","tb_autopay_company_user.user_id=tb_autopay_user.id")->where(['tb_autopay_company_user.company_id' => $ticket_details->company_id, "tb_autopay_company_user.role" => "SuperAdmin"])->get()->getRow();

        if(!is_object($user_details))
            return FALSE;

        $company_name = esc($company_details->full_name);
        $email_to = esc($user_details->email);
        $client_name = esc($user_details->lastname . ' ' . $user_details->firstname);
        $email_from = "<EMAIL>";

        $html_body = <<<EOD
    
        <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html xmlns="http://www.w3.org/1999/xhtml">
        <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        </head>
        <body style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; background-color: #f9f9fb; color: #303f4d; height: 100%; line-height: 1.4; margin: 0; width: 100% !important; -webkit-text-size-adjust: none;">
            <style>
                @media  only screen and (max-width: 600px) {
                    .inner-body {
                        width: 100% !important;
                    }
        
                    .footer {
                        width: 100% !important;
                    }
                }
        
                @media  only screen and (max-width: 500px) {
                    .button {
                        width: 100% !important;
                    }
                }
            </style>
        <table class="wrapper" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;"><tr>
        <td align="center" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">
                        <table class="content" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
        <tr>
        <td class="header" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 25px 0; text-align: center;">
                <a href="https://sepay.vn" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #bbbfc3; font-size: 19px; font-weight: bold; text-decoration: none; text-shadow: 0 1px 0 white;">
                    <img width="100" src="https://my.sepay.vn/assets/images/logo/sepay-blue-359x116.png" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; max-width: 100%; border: none;"></a>
            </td>
        </tr>
        <!-- Email Body --><tr>
        <td class="body" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
                                    <table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; background-color: #FFFFFF; margin: 0 auto; padding: 0; width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px;">
        <!-- Body content --><tr>
        <td class="content-cell" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 25px;">
        <h3>Quý khách có một phản hồi mới!</h3> 

        <p style="font-size: 13px;">Xin chào Quý khách hàng $client_name,</p>         

        <p style="font-size: 13px;">Đội ngũ SePay vừa phản hồi một <span style="color:#006fba">yêu cầu hỗ trợ</span>. <a href="https://my.sepay.vn/ticket/details/$ticket_id" target="_blank" >Xem phản hồi</a></p>
 
  
          <p style="font-size: 13px;">Quý khách cũng có thể xem, trả lời các yêu cầu hỗ trợ bằng cách đăng nhập vào <b>my.sepay.vn</b> -> <b><a href="https://my.sepay.vn/ticket/">Yêu cầu hỗ trợ</a></b></p> 

 

          <p style="font-size: 13px; ">Cảm ơn Quý khách đã sử dụng dịch vụ của SePay!</p>   

          <p style="font-size: 13px; ">Đây là email tự động được gửi từ hệ thống, vui lòng không phản hồi email này.</p>   

    
        <!-- Subcopy -->
        <table class="subcopy" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; border-top: 1px solid #EDEFF2; margin-top: 25px; padding-top: 25px;"><tr>
        <td style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">


            <p style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #303f4d; line-height: 1.5em; margin-top: 0; text-align: center; font-size: 12px;">Copyright © 2023 SePay - All rights reserved.</p>
                </td>
            </tr></table>
        </td>
                                        </tr>
        </table>
        </td>
                            </tr>
        </table>
        </td>
        </tr>
        </table>
        </td>
                </tr></table>
        </body>
        </html>
EOD;
 

        $email = \Config\Services::email();

           
        $config['wordWrap'] = true;
        $config['mailType'] = 'html';
        $email->initialize($config);

        
        $email->setFrom($email_from, 'SePay');
        $email->setTo($email_to);

        $subject = "[SePay] Phản hồi Yêu cầu hỗ trợ: #" . esc($ticket_details->id) . ' - '. esc($ticket_details->subject);

        $email->setSubject($subject);

     
        $email->setMessage($html_body);

        $result = $email->send();
 
        $email_log = [
            "company_id" => $ticket_details->company_id,
            "email_type" => "Ticket_Reply_By_Admin",
            "data_id" => $ticket_id,
            "email_to" => $email_to,
            "email_from" => $email_from,
            "subject" => $subject,
            "message" => $html_body,
           
        ];

        $emailLogModel = model(EmailLogModel::class);

        if($result) {
            $email_log['status'] = "Sent";
            $emailLogModel->insert($email_log);

            return TRUE;
        }
        
        $email_log['status'] = "Error";
        $emailLogModel->insert($email_log);

        return FALSE;

    }
}