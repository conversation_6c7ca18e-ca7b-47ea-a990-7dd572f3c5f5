<?php

namespace App\Models;

use CodeIgniter\Model;
use Config\Services;

class ReferralUseModel extends Model
{
    protected $table = 'tb_autopay_referral_uses';

    protected $returnType = 'object';

    protected $allowedFields = [
        'referral_code_id',
        'referred_company_id',
        'value',
        'is_valid',
        'action',
        'bank_id',
        'bank_account_number',
    ];

    protected $order = ['tb_autopay_referral_uses.id' => 'desc'];

    protected function _getDatatablesQuery()
    {
        $request = Services::request();

        $searchColumns = [
            'tb_autopay_referral_uses.value',
            'tb_autopay_referral_uses.created_at',
        ];

        $orderColumns = [
            null,
            'referrer_user.lastname',
            'referred_user.lastname',
            'tb_autopay_referral_uses.value',
            'tb_autopay_referral_uses.action',
            'tb_autopay_referral_uses.is_valid',
            'tb_autopay_referral_uses.created_at',
        ];

        $this->builder = $this->db
            ->table($this->table)
            ->join('tb_autopay_referral_codes', 'tb_autopay_referral_codes.id = tb_autopay_referral_uses.referral_code_id');

        $i = 0;

        foreach ($searchColumns as $item) {
            if ($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);

                if ($i === 0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }

                if (count($searchColumns) - 1 == $i) {
                    $this->builder->groupEnd();
                }
            }
            $i++;
        }

        foreach ($searchColumns as $key => $item) {
            if ($item != NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }

        if ($request->getVar('order') != NULL && isset($orderColumns[$request->getVar('order')[0]['column']])  && $orderColumns[$request->getVar('order')[0]['column']] != NULL) {
            $this->builder->orderBy($orderColumns[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);
        }
    }

    public function getDataTables()
    {
        $request = Services::request();

        $this->_getDatatablesQuery();

        $this->builder
            ->select([
                'tb_autopay_referral_uses.value',
                'tb_autopay_referral_uses.action',
                'tb_autopay_referral_uses.is_valid',
                'tb_autopay_referral_uses.created_at',
                'tb_autopay_referral_codes.company_id as referrer_id',
                'CONCAT(referred_user.lastname, " ", referred_user.firstname) as referred_name',
                'referred_user.email as referred_email',
                'referred_company_id as referred_id',
                'CONCAT(referrer_user.lastname, " ", referrer_user.firstname) as referrer_name',
                'referrer_user.email as referrer_email',
            ])
            ->join('tb_autopay_company_user as referred_company_user', 'referred_company_user.company_id = tb_autopay_referral_uses.referred_company_id', 'left')
            ->join('tb_autopay_user as referred_user', 'referred_user.id = referred_company_user.user_id', 'left')
            ->join('tb_autopay_company_user as referrer_company_user', 'referrer_company_user.company_id = tb_autopay_referral_codes.company_id', 'left')
            ->join('tb_autopay_user as referrer_user', 'referrer_user.id = referrer_company_user.user_id', 'left')
            ->groupBy('tb_autopay_referral_uses.id');

        if ($request->getPost('length') != null && $request->getPost('length') != -1) {
            $this->builder->limit($request->getPost('length'), $request->getPost('start'));
        }

        return $this->builder->get()->getResult();
    }

    public function countTotal()
    {
        return $this->builder->countAllResults();
    }

    public function countFiltered()
    {
        $this->_getDatatablesQuery();

        return $this->countAllResults();
    }
}
