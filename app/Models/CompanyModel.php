<?php

namespace App\Models;

use CodeIgniter\Model;
use Config\Services;

class CompanyModel extends Model
{
    protected $table = 'tb_autopay_company';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['full_name','short_name','status','lead_alert','active'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [];

    protected $skipValidation     = false;
    
    protected $order = ['tb_autopay_company.id'=>'DESC'];

    private function _getDatatablesQuery() {

        $request = \Config\Services::request();

        $column_order = array(null,'tb_autopay_company.id','tb_autopay_company.short_name',null,'tb_autopay_user.email','tb_autopay_user.phonenumber',null,null,null,'tb_autopay_tracking.utm_source','tb_autopay_company.created_at');
        $column_search = array('tb_autopay_company.id','tb_autopay_company.full_name','tb_autopay_company.short_name','tb_autopay_user.phonenumber','tb_autopay_user.firstname', 'tb_autopay_user.lastname', 'tb_autopay_user.email', 'tb_autopay_user.phonenumber','tb_autopay_tracking.utm_source');
    
        $this->builder = $this->db->table($this->table);

        $this->builder->select("tb_autopay_company.id,tb_autopay_company.full_name,tb_autopay_company.short_name,tb_autopay_company.created_at,tb_autopay_company.status,tb_autopay_user.email,tb_autopay_user.phonenumber,tb_autopay_user.firstname, tb_autopay_user.lastname, tb_autopay_tracking.utm_source as tracking_source, tb_autopay_company.tr_gcid");

        $this->builder->join('tb_autopay_tracking', 'tb_autopay_tracking.company_id = tb_autopay_company.id', 'left');
        $this->builder->join("tb_autopay_company_user","tb_autopay_company_user.company_id=tb_autopay_company.id");
        $this->builder->join("tb_autopay_user","tb_autopay_company_user.user_id=tb_autopay_user.id");
        $this->builder->where("tb_autopay_company_user.role",'SuperAdmin');


        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }

                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }

        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {

            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables() {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery();
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll() {
     
        $builder = $this->db->table($this->table);

       
        return $builder->countAllResults();
    }

    public function countFiltered() {
        $this->_getDatatablesQuery();
         
        return $this->builder->countAllResults();
    }

    public function sendStatusChangeNotification($companyId, $type, $invoices = null)
    {
        $company = $this->find($companyId);

        if (! $company) {
            return false;
        }

        $user = model(UserModel::class)
            ->select('tb_autopay_user.email, tb_autopay_user.firstname, tb_autopay_user.lastname')
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id=tb_autopay_user.id')
            ->where('tb_autopay_company_user.company_id', $companyId)
            ->where('tb_autopay_company_user.role', 'SuperAdmin')
            ->first();

        if (! $user) {
            return false;
        }

        $clientName = esc($user->lastname . ' ' . $user->firstname);
        $companyName = esc($company->full_name);
        $emailFrom = '<EMAIL>';
        $emailTo = esc($user->email);

        if ($type === 'Suspended') {
            $subject = 'Thông báo tạm khóa dịch vụ do trễ hạn thanh toán';
            $body = <<<EOD
                <p>Xin chào Quý khách hàng <b>$clientName</b>,</p>
                <p>Dịch vụ SePay thuộc tài khoản <b>$companyName</b> đã bị <b>tạm khóa</b>. Nguyên nhân có thể do chưa thanh toán hóa đơn. Quý khách vui lòng thanh toán hóa đơn để tiếp tục sử dụng dịch vụ của SePay nhé.</p>
                <p>Danh sách hóa đơn chưa thanh toán:</p>
                <ul>
            EOD;

            foreach ($invoices as $invoice) {
                $invoiceLink = "https://my.sepay.vn/invoices/details/{$invoice->id}";
                $body .= "<li><a href=\"$invoiceLink\">Hóa đơn #" . $invoice->id . "</a> - " . number_format($invoice->total) . " đ</li>";
            }

            $body .= "</ul>";
            $body .= '<p>Nếu quý khách đã thanh toán hóa đơn, vui lòng bỏ qua thông báo này.</p>';
        } else {
            $subject = 'Dịch vụ của bạn đã được mở khóa';
            $body = <<<EOD
                <p>Xin chào Quý khách hàng <b>$clientName</b>,</p>
                <p>Dịch vụ SePay thuộc tài khoản <b>$companyName</b> đã được <b>mở khóa</b> và hoạt động bình thường trở lại.</p>
            EOD;
        }

        $body .= <<<EOD
            <p>Nếu cần hỗ trợ, vui lòng liên hệ SePay qua các kênh sau:</p>
            <ul>
                <li>Tạo vé hỗ trợ: <a href="https://my.sepay.vn/ticket">https://my.sepay.vn/ticket</a></li>
                <li>Hotline: <a href="tel:02873 059 589">02873.059.589</a></li>
                <li>Kênh chat Facebook: <a href="https://m.me/117903214582465">https://m.me/117903214582465</a></li>
                <li>Kênh chat Zalo: <a href="https://zalo.me/1966404590345410935">https://zalo.me/1966404590345410935</a></li>
            </ul>
        EOD;

        $htmlBody = <<<EOD
            <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
            <html xmlns="http://www.w3.org/1999/xhtml">
            <head>
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
            </head>
            <body style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; background-color: #f9f9fb; color: #303f4d; height: 100%; line-height: 1.4; margin: 0; width: 100% !important; -webkit-text-size-adjust: none;">
                <style>
                @media only screen and (max-width: 600px) {
                    .inner-body {
                        width: 100% !important;
                    }

                    .footer {
                        width: 100% !important;
                    }
                }

                @media only screen and (max-width: 500px) {
                    .button {
                        width: 100% !important;
                    }
                }
                </style>
                <table class="wrapper" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
                <tr>
                    <td align="center" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">
                    <table class="content" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
                        <tr>
                        <td class="header" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 25px 0; text-align: center;">
                            <a href="https://my.sepay.vn" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #bbbfc3; font-size: 19px; font-weight: bold; text-decoration: none; text-shadow: 0 1px 0 white;">
                            <img width="100" src="https://my.sepay.vn/assets/images/logo/sepay-blue-359x116.png" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; max-width: 100%; border: none;">
                            </a>
                        </td>
                        </tr>
                        <!-- Email Body -->
                        <tr>
                        <td class="body" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
                            <table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; background-color: #FFFFFF; margin: 0 auto; padding: 0; width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px;">
                            <!-- Body content -->
                            <tr>
                                <td class="content-cell" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 25px;">
                                <h3>$subject</h3>
                                $body
                                <p style="font-size: 13px;">Cảm ơn Quý khách đã sử dụng dịch vụ của SePay!</p>   
                                <!-- Subcopy -->
                                <table class="subcopy" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; border-top: 1px solid #EDEFF2; margin-top: 25px; padding-top: 25px;">
                                    <tr>
                                    <td style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">
                                        <p style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #303f4d; line-height: 1.5em; margin-top: 0; text-align: center; font-size: 11px;">Email được gởi tự động từ hệ thống. Vui lòng không phản hồi</p>
                                        <p style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #303f4d; line-height: 1.5em; margin-top: 0; text-align: center; font-size: 12px;">Copyright © 2023 SePay - All rights reserved.</p>
                                    </td>
                                    </tr>
                                </table>
                                </td>
                            </tr>
                            </table>
                        </td>
                        </tr>
                        <tr>
                        <td style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">
                            <table class="footer" align="center" width="570" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0 auto; padding: 0; text-align: center; width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px;">
                            <tr>
                                <td class="content-cell" align="center" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 35px;">
                                <p style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; line-height: 1.5em; margin-top: 0; color: #AEAEAE; font-size: 12px; text-align: center;" @ 2023 SePay. All rights reserved.</p>
                                </td>
                            </tr>
                            </table>
                        </td>
                        </tr>
                    </table>
                    </td>
                </tr>
                </table>
            </body>
            </html>
        EOD;

        $sent = Services::email()
            ->initialize([
                'mailType' => 'html',
                'wordWrap' => true,
            ])
            ->setFrom($emailFrom, 'SePay')
            ->setTo($emailTo)
            ->setSubject("[SePay] $subject")
            ->setMessage($htmlBody)
            ->send();

        $emailLogModel = model(EmailLogModel::class);
   
        $emailLogModel->insert([
            'company_id' => $companyId,
            'email_type' => $type === 'Suspended' ? 'Auto_Suspend_Company' : 'Auto_Unsuspend_Company',
            'data_id' => $companyId,
            'email_to' => $emailTo,
            'email_from' => $emailFrom,
            'subject' => "[SePay] $subject",
            'message' => $htmlBody,
            'status' => $sent ? 'Sent' : 'Error',
        ]);

        return $sent;
    }
}
