<?php

namespace App\Models;

use CodeIgniter\Model;

class MerchantModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_merchant';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['name','trans_type','client_id','client_secret','notify_url','notify_api_key'];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    protected $order = ['tb_autopay_merchant.id'=>'DESC'];

    private function _getDatatablesQuery() {

        $request = \Config\Services::request();
  
        $column_order = array(null,'tb_autopay_merchant.id','tb_autopay_merchant.name','tb_autopay_merchant.type','tb_autopay_merchant.company_name','tb_autopay_merchant.credit',null,null,null,null,'tb_autopay_merchant.created_at');
        $column_search = array('tb_autopay_merchant.id','tb_autopay_merchant.name','tb_autopay_merchant.notify_url','tb_autopay_merchant.type','tb_autopay_merchant.company_name','tb_autopay_merchant.email','tb_autopay_merchant.phonenumber','tb_autopay_merchant.note','tb_autopay_merchant.type');
    
        $this->builder = $this->db->table($this->table);

        $this->builder->select("tb_autopay_merchant.id,tb_autopay_merchant.name,tb_autopay_merchant.created_at,tb_autopay_merchant.company_name,tb_autopay_merchant.phonenumber,tb_autopay_merchant.email,tb_autopay_merchant.note,tb_autopay_merchant.credit,tb_autopay_merchant.type");
 
        
        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables() {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery();
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll() {
     
        $builder = $this->db->table($this->table);

       
        return $builder->countAllResults();
    }

    public function countFiltered() {
        $this->_getDatatablesQuery();
         
        return $this->builder->countAllResults();
    }

}