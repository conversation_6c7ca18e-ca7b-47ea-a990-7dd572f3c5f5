<?php

namespace App\Models;

use CodeIgniter\Model;

class WebhooksQueueModel extends Model
{
    protected $table = 'tb_autopay_webhooks_queue';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['transaction_id','webhook_id','status','last_retry_time','retries_count'];

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $useSoftDeletes = false;

    protected $useTimestamps     = false;

    protected $dateFormat        = 'datetime';

    protected $order = ['id'=>'desc'];

    protected $validationRules    = [
         
    ];
    protected $skipValidation     = false;

    protected $builder;

}