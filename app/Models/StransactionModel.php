<?php

namespace App\Models;

use CodeIgniter\Model;
use App\Models\UserModel;
use App\Models\EmailLogModel;
use App\Models\CompanyModel;
use App\Models\InvoiceModel;
use App\Models\InvoiceItemModel;

class StransactionModel extends Model
{
    protected $table = 'tb_autopay_stransaction';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['company_id','invoice_id','merchant_id','date','description','in','out','fee','trans_id','type','notes','email_sent', 'payment_method', 'sms_parsed_id'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [];
    protected $skipValidation     = false;
    

    protected $order = ['id'=>'desc'];
 
    protected $builder;


    private function _getDatatablesQuery($company_id) {

        $request = \Config\Services::request();
  
        $column_order = array(null,'id','date','in','out','invoice_id');
        $column_search = array('id','date','in','out','invoice_id');
 
        $this->builder = $this->db->table($this->table);

        $this->builder->select("id, company_id, type, invoice_id, date, description, in, out, fee");

        if($company_id)
           $this->builder->where("company_id",$company_id);

        if($request->getGet('merchant_id') != NULL) {
            $this->builder->where("merchant_id", $request->getGet('merchant_id'));
        }

     
        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables($company_id=FALSE) {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery($company_id);
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll($company_id=FALSE) {
     
        $builder = $this->db->table($this->table);

        if($company_id)
            $builder->where("company_id",$company_id);
 
       
        return $builder->countAllResults();
    }

    public function countFiltered($company_id=FALSE) {
        $this->_getDatatablesQuery($company_id);
        if($company_id)
            $this->builder->where("company_id",$company_id);
         
        return $this->builder->countAllResults();
    }

    function sendEmailPayment($transaction_id) {
        
        $companyModel = model(CompanyModel::class);
        $invoiceItemModel = model(InvoiceItemModel::class);
        $invoiceModel = model(InvoiceModel::class);
        $userModel = model(UserModel::class);

        $builder = $this->db->table($this->table);
        $payment_details = $builder->where(['id' => $transaction_id])->get()->getRow();
 
        if(!is_object($payment_details) || $payment_details->in <=0)
            return FALSE;

        $company_details = $companyModel->find($payment_details->company_id);

        if(!is_object($company_details))
            return FALSE;

        $user_details = $userModel->select("tb_autopay_user.email, tb_autopay_user.firstname, tb_autopay_user.lastname")->join("tb_autopay_company_user","tb_autopay_company_user.user_id=tb_autopay_user.id")->where(['tb_autopay_company_user.company_id' => $payment_details->company_id, "tb_autopay_company_user.role" => "SuperAdmin"])->get()->getRow();
 

        if(!is_object($user_details))
            return FALSE;

        if(is_numeric($payment_details->invoice_id) && $payment_details->invoice_id>0) {
            $invoice_details = $invoiceModel->where(['id' => $payment_details->invoice_id])->get()->getRow();
            $invoice_text = "<li>Thanh toán cho Hoá đơn: <a href='https://my.sepay.vn/invoices/details/".$payment_details->invoice_id."' target='_blank' >#".$payment_details->invoice_id."</a></li>";

        }

        $amount_in_text = number_format($payment_details->in) . 'đ';
  
        $client_name = $user_details->lastname . " " . $user_details->firstname;
        $company_name = $company_details->full_name;
        $pay_date = date("d/m/Y H:i:s",strtotime($payment_details->date));
        $email_to = $user_details->email;
        $email_from = "<EMAIL>";

        $html_body = <<<EOD
    
        <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
</head>
<body style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; background-color: #f9f9fb; color: #303f4d; height: 100%; line-height: 1.4; margin: 0; width: 100% !important; -webkit-text-size-adjust: none;">
    <style>
        @media  only screen and (max-width: 600px) {
            .inner-body {
                width: 100% !important;
            }

            .footer {
                width: 100% !important;
            }
        }

        @media  only screen and (max-width: 500px) {
            .button {
                width: 100% !important;
            }
        }
    </style>
<table class="wrapper" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;"><tr>
<td align="center" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">
                <table class="content" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
<tr>
<td class="header" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 25px 0; text-align: center;">
        <a href="https://sepay.vn" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #bbbfc3; font-size: 19px; font-weight: bold; text-decoration: none; text-shadow: 0 1px 0 white;">
            <img width="100" src="https://my.sepay.vn/assets/images/logo/sepay-blue-359x116.png" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; max-width: 100%; border: none;"></a>
    </td>
</tr>
<!-- Email Body --><tr>
<td class="body" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
                            <table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; background-color: #FFFFFF; margin: 0 auto; padding: 0; width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px;">
<!-- Body content --><tr>
<td class="content-cell" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 25px;">
<h3>Đã nhận thanh toán $amount_in_text!</h3> 

<p style="font-size: 13px;">Xin chào Quý khách hàng $client_name,</p>         

<p style="font-size: 13px;">SePay đã nhận được thanh toán từ quý khách. Thông tin:</p> 

 <hr>


 <ul  style="font-size: 13px;">
 $invoice_text
 <li>Số tiền đã nhận: <b>$amount_in_text</b></li>
 <li>Phương thức: <b>Chuyển khoản ngân hàng</b></li>
<li>Thanh toán lúc: <b>$pay_date</b></li>
<li>Tài khoản SePay: <b>$company_name</b></li>

 </ul>
<hr>

<p style="text-align: center;">
<a href="https://my.sepay.vn/invoices" style="text-decoration:none;color:#ffffff;font-size:14px;border-style:solid;border-color:#2d6bcf;border-width:10px 20px 10px 20px;display:inline-block;background:#2d6bcf;border-radius:30px;line-height:22px;width:auto;text-align:center" target="_blank" >Xem tất cả hóa đơn</a>
  </p>      
  <p style="font-size: 13px; padding-bottom: 10px;">Mọi yêu cầu hỗ trợ, quý khách vui lòng liên hệ <b>SePay</b>: Hotline <b><a href="tel:02873059589">02873.059.589</a>, Email <EMAIL></b> hoặc fanpage <a href="https://fb.me/sepay.vn" target="_blank">fb.me/sepay.vn</a></p>   


  <p style="font-size: 13px; ">Cảm ơn Quý khách đã sử dụng dịch vụ của SePay!</p>   



<!-- Subcopy -->
<table class="subcopy" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; border-top: 1px solid #EDEFF2; margin-top: 25px; padding-top: 25px;"><tr>
<td style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">


    <p style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #303f4d; line-height: 1.5em; margin-top: 0; text-align: center; font-size: 12px;">Copyright © 2023 SePay - All rights reserved.</p>
        </td>
    </tr></table>
</td>
                                </tr>
</table>
</td>
                    </tr>
</table>
</td>
</tr>
</table>
</td>
        </tr></table>
</body>
</html>
EOD;
 

        $email = \Config\Services::email();

           
        $config['wordWrap'] = true;
        $config['mailType'] = 'html';
        $email->initialize($config);

        
        $email->setFrom($email_from, 'SePay');
        $email->setTo($email_to);

        $subject = "Đã nhận thanh toán ". $amount_in_text;

        $email->setSubject($subject);

        //var_dump($config);

     
        $email->setMessage($html_body);

        $result = $email->send();
 
        $email_log = [
            "company_id" => $payment_details->company_id,
            "email_type" => "Payment_Received",
            "data_id" => $transaction_id,
            "email_to" => $email_to,
            "email_from" => $email_from,
            "subject" => $subject,
            "message" => $html_body,
           
        ];

        //var_dump($email_log);


        $emailLogModel = model(EmailLogModel::class);

        if($result) {
            $email_log['status'] = "Sent";
            $emailLogModel->insert($email_log);
            $builder->set('email_sent', 'email_sent+1', false)->where(['id' => $transaction_id])->update();

            return TRUE;
        }
        
        $email_log['status'] = "Error";
        $emailLogModel->insert($email_log);

        return FALSE;

    }
    
}