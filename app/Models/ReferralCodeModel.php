<?php

namespace App\Models;

use CodeIgniter\Model;
use Config\Referral;

class ReferralCodeModel extends Model
{
    protected $table = 'tb_autopay_referral_codes';

    protected $returnType = 'object';

    protected $allowedFields = [
        'company_id',
        'code',
        'total_bonus_received',
        'is_active',
    ];

    protected $order = ['tb_autopay_referral_codes.created_at' => 'desc'];

    public function getReferralStatistics(): object
    {
        $referralUses = model(ReferralUseModel::class)
            ->select([
                'COUNT(*) AS total_referrals',
                'SUM(is_valid) AS total_successful_referrals',
                'SUM(is_valid = 0) AS total_pending_referrals',
            ])
            ->where('action', 'first_connect_bank')
            ->first();

        $referralData = $this
            ->select('SUM(total_bonus_received) AS total_transactions')
            ->first();

        $referralsThisMonth = model(ReferralUseModel::class)
            ->select('COUNT(*) AS referrals_this_month')
            ->where('MONTH(created_at)', date('m'))
            ->where('YEAR(created_at)', date('Y'))
            ->where('action', 'first_connect_bank')
            ->first();

        $totalBankAccountConnected = model(BankAccountModel::class)
            ->select('COUNT(*) AS total_bank_account_connected')
            ->join('tb_autopay_referral_uses', 'tb_autopay_referral_uses.referred_company_id = tb_autopay_bank_account.company_id')
            ->first();

        $totalPaidInvoices = model(InvoiceModel::class)
            ->join('tb_autopay_referral_uses', 'tb_autopay_referral_uses.referred_company_id = tb_autopay_invoice.company_id')
            ->where('status', 'Paid')
            ->select('COUNT(DISTINCT tb_autopay_invoice.company_id) AS total_paid_invoices')
            ->first();

        $totalTransactions = slavable_model(CounterModel::class, 'Referral')
            ->select('SUM(tb_autopay_counter.transaction) AS total_used_transactions')
            ->join('tb_autopay_referral_uses', 'tb_autopay_referral_uses.referred_company_id = tb_autopay_counter.company_id')
            ->first();

        return (object) [
            'total_referrals' => $referralUses->total_referrals,
            'total_successful_referrals' => $referralUses->total_successful_referrals,
            'total_pending_referrals' => $referralUses->total_pending_referrals,
            'total_transactions' => $referralData->total_transactions,
            'referrals_this_month' => $referralsThisMonth->referrals_this_month,
            'total_bank_account_connected' => $totalBankAccountConnected->total_bank_account_connected,
            'total_paid_invoices' => $totalPaidInvoices->total_paid_invoices,
            'total_used_transactions' => $totalTransactions->total_used_transactions,
        ];
    }

    public function getTopReferrals(int $limit = 5, ?string $fromDate = null, ?string $endDate = null): array
    {
        $query = $this
            ->where('tb_autopay_referral_uses.is_valid', true)
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.company_id = tb_autopay_referral_codes.company_id')
            ->join('tb_autopay_user', 'tb_autopay_user.id = tb_autopay_company_user.user_id')
            ->join('tb_autopay_referral_uses', 'tb_autopay_referral_uses.referral_code_id = tb_autopay_referral_codes.id')
            ->select('COUNT(DISTINCT tb_autopay_referral_uses.id) as total_uses, CONCAT(tb_autopay_user.lastname, " ", tb_autopay_user.firstname) as fullname, tb_autopay_user.email, tb_autopay_referral_codes.company_id')
            ->groupBy('tb_autopay_referral_codes.id')
            ->orderBy('total_uses', 'DESC')
            ->where('tb_autopay_referral_uses.action', 'first_connect_bank')
            ->limit($limit);

        if ($fromDate) {
            $query->where('tb_autopay_referral_uses.created_at >=', $fromDate);
        }

        if ($endDate) {
            $query->where('tb_autopay_referral_uses.created_at <=', $endDate);
        }

        return $query->findAll();
    }

    public function applyPaidReferral(int $referredCompanyId, float $amount): void
    {
        $referralUseModel = model(ReferralUseModel::class);

        $referralUse = $referralUseModel
            ->where('referred_company_id', $referredCompanyId)
            ->where('is_valid', true)
            ->first();

        if (! $referralUse) {
            return;
        }

        $referralCode = $this->find($referralUse->referral_code_id);

        if (! $referralCode) {
            return;
        }

        $config = config(Referral::class);

        if (is_null($percentage = $config->paidBonusPercentage)) {
            return;
        }

        if ($amount < $config->paidMinAmountForBonus) {
            return;
        }

        $remainingBonus = $config->maxReferralBonus - $referralCode->total_bonus_received;
        $actualBonus = ($remainingBonus <= 0) ? 0 : min($remainingBonus, round($amount * $percentage / 100000));

        if ($actualBonus <= 0) {
            return;
        }

        $existingPaidRecord = $referralUseModel
            ->where('referral_code_id', $referralCode->id)
            ->where('referred_company_id', $referredCompanyId)
            ->where('action', 'paid')
            ->first();

        if ($existingPaidRecord) {
            return;
        }

        if ($referralCode->total_bonus_received < $config->maxReferralBonus) {
            $this
                ->set('total_bonus_received', "total_bonus_received + $actualBonus", false)
                ->update($referralCode->id);

            model(CompanySubscriptionModel::class)
                ->set('monthly_transaction_limit', "monthly_transaction_limit + $actualBonus", false)
                ->where('company_id', $referralCode->company_id)
                ->update();

            $referralUseModel->insert([
                'referral_code_id' => $referralCode->id,
                'referred_company_id' => $referredCompanyId,
                'value' => $actualBonus,
                'is_valid' => true,
                'action' => 'paid',
            ]);

            add_system_log([
                'company_id' => $referralCode->company_id,
                'data_type' => 'apply_referral_bonus',
                'description' => "Referral bonus applied from paid | Bonus: $actualBonus | Referred Company ID: $referredCompanyId | Referrer Company ID: $referralCode->company_id",
                'level' => 'Info',
                'by' => 'ReferralCode Model',
            ]);
        }
    }
}
