<?php

namespace App\Models;

use CodeIgniter\Model;

class PartnerModel extends Model
{
    protected $table = 'tb_autopay_partner';

    protected $primaryKey = 'id';

    protected $returnType     = 'object';
    protected $allowedFields = ['email', 'active','password','firstname','lastname','theme','sidebar_toggle', 'sidebar_behavior', 'company_name','current_commission','withdrawn_commission','total_commission', 'uid'];

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $dateFormat        = 'datetime';

    protected $validationRules    = [];
    protected $skipValidation     = false;

    protected $order = ['tb_autopay_partner.id'=>'desc'];


    private function _getDatatablesQuery() {

        $request = \Config\Services::request();
  
        $column_order = array(null,'tb_autopay_partner.id',null,'tb_autopay_partner.email','tb_autopay_partner.phonenumber',null,'tb_autopay_partner.total_commission','tb_autopay_partner.withdrawn_commission',null,'tb_autopay_partner.created_at');
        $column_search = array('tb_autopay_partner.id','tb_autopay_partner.firstname','tb_autopay_partner.lastname','tb_autopay_partner.phonenumber');
    
        $this->builder = $this->db->table($this->table);

        $this->builder->select("tb_autopay_partner.id,tb_autopay_partner.lastname,tb_autopay_partner.firstname,tb_autopay_partner.created_at,tb_autopay_partner.email,tb_autopay_partner.phonenumber, tb_autopay_partner.total_commission, tb_autopay_partner.withdrawn_commission");

        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables() {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery();
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll() {
     
        $builder = $this->db->table($this->table);

       
        return $builder->countAllResults();
    }

    public function countFiltered() {
        $this->_getDatatablesQuery();
         
        return $this->builder->countAllResults();
    }
     
 
}