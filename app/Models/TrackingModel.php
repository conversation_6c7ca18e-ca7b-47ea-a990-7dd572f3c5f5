<?php

namespace App\Models;

use CodeIgniter\Model;
use Config\Services;

class TrackingModel extends Model
{
    protected $table = 'tb_autopay_tracking';

    protected $returnType = 'object';

    protected $order = ['tb_autopay_tracking.id' => 'desc'];

    protected function _getDatatablesQuery()
    {
        $request = Services::request();

        $orderColumns = [null, 'company_name', 'utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term', 'referer', 'created_at'];
        $searchColumns = [
            'tb_autopay_company.full_name',
            'tb_autopay_tracking.utm_source',
            'tb_autopay_tracking.utm_medium',
            'tb_autopay_tracking.utm_campaign',
            'tb_autopay_tracking.utm_content',
            'tb_autopay_tracking.utm_term',
            'tb_autopay_tracking.referer',
        ];

        $sourceExpression = "CASE 
            WHEN tb_autopay_company.tr_gcid IS NOT NULL AND TRIM(tb_autopay_company.tr_gcid) != '' THEN 'Affiliate'
            ELSE tb_autopay_tracking.utm_source 
        END";

        $this->builder = $this->db
            ->table('tb_autopay_company')
            ->join('tb_autopay_tracking', 'tb_autopay_company.id = tb_autopay_tracking.company_id', 'left')
            ->select([
                'tb_autopay_tracking.company_id',
                'tb_autopay_company.full_name as company_name',
                "{$sourceExpression} as utm_source",
                'tb_autopay_tracking.utm_medium',
                'tb_autopay_tracking.utm_campaign',
                'tb_autopay_tracking.utm_content',
                'tb_autopay_tracking.utm_term',
                'tb_autopay_tracking.referer',
                'tb_autopay_tracking.created_at',
            ]);

        $sources = array_filter($request->getPost('source') ?: []);
        $mediums = array_filter($request->getPost('medium') ?: []);
        $campaigns = array_filter($request->getPost('campaign') ?: []);
        $contents = array_filter($request->getPost('content') ?: []);
        $terms = array_filter($request->getPost('term') ?: []);
        $startDate = $request->getPost('start_date');
        $endDate = $request->getPost('end_date');

        if ($startDate) {
            $this->builder->where('tb_autopay_company.created_at >=', date('Y-m-d 00:00:00', strtotime($startDate)));
        }

        if ($endDate) {
            $this->builder->where('tb_autopay_company.created_at <=', date('Y-m-d 23:59:59', strtotime($endDate)));
        }

        if (! empty($sources)) {
            if (in_array('Affiliate', $sources)) {
                $key = array_search('Affiliate', $sources);
                unset($sources[$key]);

                if (empty($sources)) {
                    $this->builder->where('tb_autopay_company.tr_gcid IS NOT NULL AND TRIM(tb_autopay_company.tr_gcid) != ""');
                } else {
                    $this->builder->groupStart()
                        ->where('tb_autopay_company.tr_gcid IS NOT NULL AND TRIM(tb_autopay_company.tr_gcid) != ""')
                        ->orWhereIn('tb_autopay_tracking.utm_source', $sources)
                        ->groupEnd();
                }
            } else {
                $this->builder->whereIn('tb_autopay_tracking.utm_source', $sources);
            }
        }

        if (! empty($mediums)) {
            $this->builder->whereIn('tb_autopay_tracking.utm_medium', $mediums);
        }

        if (! empty($campaigns)) {
            $this->builder->whereIn('tb_autopay_tracking.utm_campaign', $campaigns);
        }

        if (! empty($contents)) {
            $this->builder->whereIn('tb_autopay_tracking.utm_content', $contents);
        }

        if (! empty($terms)) {
            $this->builder->whereIn('tb_autopay_tracking.utm_term', $terms);
        }

        $i = 0;

        foreach ($searchColumns as $item) {
            if ($request->getVar('search')['value'] !== null) {
                $search_term = trim($request->getVar('search')['value']);

                if ($i === 0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }

                if (count($searchColumns) - 1 == $i) {
                    $this->builder->groupEnd();
                }
            }

            $i++;
        }

        foreach ($searchColumns as $key => $item) {
            if (
                $item !== null
                && isset($request->getPost('columns')[$key]['search']['value'])
                && $request->getPost('columns')[$key]['search']['value'] !== ''
            ) {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }

        if (
            $request->getVar('order') !== null
            && isset($orderColumns[$request->getVar('order')[0]['column']])
            && $orderColumns[$request->getVar('order')[0]['column']] !== null
        ) {
            $this->builder->orderBy($orderColumns[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);
        } elseif (isset($this->order)) {
            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);
        }
    }

    public function getDatatables()
    {
        $request = Services::request();

        $this->_getDatatablesQuery();

        if ($request->getVar('length') !== null && $request->getVar('length') != -1) {
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
        }

        return $this->builder->get()->getResult();
    }

    public function countAll()
    {
        return $this->db
            ->table('tb_autopay_company')
            ->join('tb_autopay_tracking', 'tb_autopay_company.id = tb_autopay_tracking.company_id', 'left')
            ->countAllResults();
    }

    public function countFiltered()
    {
        $this->_getDatatablesQuery();

        return $this->builder->countAllResults();
    }

    public function getDistinct(string $column): array
    {
        if ($column === 'utm_source') {
            $sourceExpr = 'CASE 
                WHEN tb_autopay_company.tr_gcid IS NOT NULL AND TRIM(tb_autopay_company.tr_gcid) != "" THEN "Affiliate" 
                WHEN tb_autopay_tracking.utm_source IS NULL OR TRIM(tb_autopay_tracking.utm_source) = "" THEN "Unknown" 
                ELSE tb_autopay_tracking.utm_source 
            END';

            $results = $this->db->table('tb_autopay_company')
                ->select([
                    "$sourceExpr as $column",
                    "COUNT(DISTINCT tb_autopay_company.id) as count"
                ])
                ->join('tb_autopay_tracking', 'tb_autopay_company.id = tb_autopay_tracking.company_id', 'left')
                ->groupBy($sourceExpr)
                ->orderBy('count', 'DESC')
                ->get()
                ->getResultArray();

            $hasAffiliate = false;
            foreach ($results as $result) {
                if ($result[$column] === 'Affiliate') {
                    $hasAffiliate = true;
                    break;
                }
            }

            if (!$hasAffiliate) {
                $affiliateCount = $this->db->table('tb_autopay_company')
                    ->where('tr_gcid IS NOT NULL AND TRIM(tr_gcid) != ""')
                    ->countAllResults();

                if ($affiliateCount > 0) {
                    $results[] = [$column => 'Affiliate', 'count' => $affiliateCount];

                    usort($results, function ($a, $b) {
                        return $b['count'] - $a['count'];
                    });
                }
            }

            return $results;
        }

        return $this->db->table($this->table)
            ->select([
                "$column",
                "COUNT(*) as count"
            ])
            ->where("$column IS NOT NULL")
            ->where("$column !=", '')
            ->groupBy($column)
            ->orderBy('count', 'DESC')
            ->get()
            ->getResultArray();
    }

    public function getGroupedStatsBySource(?string $startDate = null, ?string $endDate = null): array
    {
        $sourceExpr = 'CASE 
            WHEN tb_autopay_company.tr_gcid IS NOT NULL AND TRIM(tb_autopay_company.tr_gcid) != "" THEN "Affiliate" 
            WHEN tb_autopay_tracking.utm_source IS NULL OR TRIM(tb_autopay_tracking.utm_source) = "" THEN "Unknown" 
            ELSE tb_autopay_tracking.utm_source 
        END';

        $builder = $this->db->table('tb_autopay_company')
            ->select([
                "$sourceExpr as utm_source",
                'COUNT(DISTINCT tb_autopay_company.id) as total_companies',
                'COUNT(DISTINCT CASE WHEN tb_autopay_invoice.total > 0 AND tb_autopay_invoice.status = "Paid" THEN tb_autopay_company.id END) as total_paid_companies',
                'SUM(CASE WHEN tb_autopay_invoice.total > 0 AND tb_autopay_invoice.status = "Paid" THEN tb_autopay_invoice.total ELSE 0 END) as total_paid',
                'ROUND(SUM(CASE WHEN tb_autopay_invoice.total > 0 AND tb_autopay_invoice.status = "Paid" THEN tb_autopay_invoice.total ELSE 0 END) / 
                NULLIF((SELECT SUM(CASE WHEN total > 0 AND status = "Paid" THEN total ELSE 0 END) FROM tb_autopay_invoice), 0) * 100, 2) as percentage_share',
            ])
            ->join('tb_autopay_tracking', 'tb_autopay_company.id = tb_autopay_tracking.company_id', 'left')
            ->join('tb_autopay_invoice', 'tb_autopay_company.id = tb_autopay_invoice.company_id', 'left');

        if ($startDate) {
            $builder->where('tb_autopay_company.created_at >=', date('Y-m-d 00:00:00', strtotime($startDate)));
        }

        if ($endDate) {
            $builder->where('tb_autopay_company.created_at <=', date('Y-m-d 23:59:59', strtotime($endDate)));
        }

        return $builder
            ->groupBy($sourceExpr)
            ->orderBy('total_companies', 'desc')
            ->get()
            ->getResultArray();
    }
}
