<?php

namespace App\Models;

use CodeIgniter\Model;

class ProductModel extends Model
{
    protected $table = 'tb_autopay_product';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['name','description','monthly_transaction_limit', 'bank_account_limit','telegram_intergration_limit','webhook_intergration_limit', 'monthly_telegram_send_limit','monthly_webhooks_send_limit','price_monthly', 'price_annually', 'hidden','sort_order','dedicated_sim','tax_rate','active', 'sms_allow'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;
    
    protected $order = ['tb_autopay_product.sort_order'=>'asc'];
 
    protected $builder;


    private function _getDatatablesQuery() {

        $request = \Config\Services::request();
        

        $column_order = array(null,'tb_autopay_product.id','tb_autopay_product.name','tb_autopay_product.monthly_transaction_limit',null,'tb_autopay_product.price_monthly','tb_autopay_product.price_annually','tb_autopay_product.billing_type','tb_autopay_product.sort_order','tb_autopay_product.hidden','tb_autopay_product.active',null);
        $column_search = array('tb_autopay_product.id','tb_autopay_product.name');
    
        $this->builder = $this->db->table($this->table);

        $this->builder->select("tb_autopay_product.id,tb_autopay_product.name,tb_autopay_product.monthly_transaction_limit,tb_autopay_product.billing_type,tb_autopay_product.price_monthly,tb_autopay_product.active,tb_autopay_product.price_annually,tb_autopay_product.sort_order,,tb_autopay_product.hidden");

 
        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables() {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery();
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll() {
     
        $builder = $this->db->table($this->table);

       
        return $builder->countAllResults();
    }

    public function countFiltered() {
        $this->_getDatatablesQuery();
         
        return $this->builder->countAllResults();
    }
}