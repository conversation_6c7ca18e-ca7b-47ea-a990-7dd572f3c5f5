<?php

namespace App\Models;

use CodeIgniter\Model;
use App\Models\UserModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use App\Models\CompanyModel;
use App\Models\WebhooksQueueModel;

class WebhooksModel extends Model
{
    protected $table = 'tb_autopay_webhooks';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['company_id','authen_type','webhook_url', 'secret_key','oauth2_client_id','oauth2_client_secret', 'oauth2_access_token','event_type','bank_account_id','active','oauth2_access_token_created_at','oauth2_access_token_url','oauth2_refresh_token','oauth2_expired_in','active','name','is_verify_payment','skip_if_no_code','request_content_type','api_id','api_key'];

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $useSoftDeletes = false;

    protected $useTimestamps     = false;

    protected $dateFormat        = 'datetime';

    protected $order = ['id'=>'desc'];

    protected $validationRules    = [
         
    ];
    protected $skipValidation     = false;

    protected $builder;

    // do webhooks by hooks (obj), use for both do and retry webhook
    public function doWebhook($hooks, $transaction_details, $is_retry=FALSE) {
        
        $transactionsModel = model(TransactionsModel::class);

        if($transaction_details->amount_in > 0) {
            $amount = $transaction_details->amount_in;
            $transferType = 'in';
        }
        else if($transaction_details->amount_out >0) {
            $amount = $transaction_details->amount_out;
            $transferType = 'out';
        } else
            return FALSE;

        $do_hooks = FALSE;

        $transaction_id = $transaction_details->id;

        if($hooks->event_type == "All")
            $do_hooks = TRUE;
        else {
            if($hooks->event_type == "In_only" && $transaction_details->amount_in > 0)
                $do_hooks = TRUE;
            else if($hooks->event_type == "Out_only" && $transaction_details->amount_out > 0)
                $do_hooks = TRUE;
        }

        if($hooks->skip_if_no_code == 1 && ($transaction_details->code == NULL || $transaction_details->code == ''))
            $do_hooks = FALSE;

        
        if($do_hooks) {
        
            $post_data = [
                'gateway' => $transaction_details->gateway,
                'transactionDate' => $transaction_details->transaction_date,
                'accountNumber' => $transaction_details->account_number,
                'subAccount' => $transaction_details->sub_account,
                'code' => $transaction_details->code,
                'content' => $transaction_details->transaction_content,
                'transferType' => $transferType,
                'description' => $transaction_details->body,
                'transferAmount' => intval($amount),
                'referenceCode' => $transaction_details->reference_number,
                'accumulated' => intval($transaction_details->accumulated),
            ];

            $webhooksQueueModel = model(WebhooksQueueModel::class);

            
            if($hooks->authen_type == "OAuth2.0") {        
                $oauth2 = new \App\Libraries\Oauth2;

                $result = $oauth2->do_webhooks($hooks->id, $post_data, $transaction_id);
                
                if(is_array($result) && $result['success'] === TRUE && $result['response_status_code'] == 201) {
                    $transactionsModel->set('webhooks_success', 'webhooks_success+1', false)->where(['id' => $transaction_id])->update();
                    if($hooks->is_verify_payment == 1 && $transferType =='in')
                        $transactionsModel->set(['webhooks_verify_payment' => 'Success'])->where(['id' => $transaction_id])->update();
                } else {
                    $transactionsModel->set('webhooks_failed', 'webhooks_failed+1', false)->where(['id' => $transaction_id])->update();
                    if($hooks->is_verify_payment == 1 && $transferType =='in')
                        $transactionsModel->set(['webhooks_verify_payment' => 'Failed'])->where(['id' => $transaction_id])->update();
                }

                // retry if network connect failed
                if(isset($result['connect_success']) && $result['connect_success'] == 0 && $is_retry === FALSE) {

                    $webhooksQueueModel->insert([
                        'transaction_id' => $transaction_id,
                        'webhook_id' => $hooks->id,
                        'status' => 'SoftFailed',
                        'last_retry_time' => date("Y-m-d H:i:s")
                    ]);
                }
                    

                return $result;

            } else if($hooks->authen_type == "No_Authen" || $hooks->authen_type == "Api_Key") {
                    
                $webhooks_lib = new \App\Libraries\Webhooks;

                $result = $webhooks_lib->do_webhooks($hooks->id, $post_data, $transaction_id);

              
                if(is_array($result))
                    $response = json_decode($result['response_body']);
                else
                    $response = FALSE;

                if(is_array($result) && in_array($result['response_status_code'],[200,201]) && $response && isset($response->success) && $response->success == TRUE) {
                    $transactionsModel->set('webhooks_success', 'webhooks_success+1', false)->where(['id' => $transaction_id])->update();
                    if($hooks->is_verify_payment == 1 && $transferType =='in')
                        $transactionsModel->set(['webhooks_verify_payment' => 'Success'])->where(['id' => $transaction_id])->update();
                } else {
                    $transactionsModel->set('webhooks_failed', 'webhooks_failed+1', false)->where(['id' => $transaction_id])->update();
                    if($hooks->is_verify_payment == 1 && $transferType =='in')
                        $transactionsModel->set(['webhooks_verify_payment' => 'Failed'])->where(['id' => $transaction_id])->update();
                }

                // retry if network connect failed
                if(isset($result['connect_success']) && $result['connect_success'] == 0 && $is_retry === FALSE) {

                    $webhooksQueueModel->insert([
                        'transaction_id' => $transaction_id,
                        'webhook_id' => $hooks->id,
                        'status' => 'SoftFailed',
                        'last_retry_time' => date("Y-m-d H:i:s")
                    ]);
                }

                return $result;
            }

        } 

    }

    // do webhooks by account number
    public function doWebhooks($account_number, $transaction_id) {

        $bankAccountModel = model(BankAccountModel::class);
 
        // check bank account first
        $bank_account_details = $bankAccountModel->where(['account_number' => $account_number, 'active' => 1])->get()->getRow();
        if(!is_object($bank_account_details) || !is_numeric($bank_account_details->company_id))
            return FALSE;

        
        // check transaction is success
        $transactionsModel = model(TransactionsModel::class);
        $transaction_details = $transactionsModel->find($transaction_id);
 
        if(!is_object($transaction_details))
            return FALSE;

        if($transaction_details->parser_status != 'Success')
            return FALSE;

        if($transaction_details->amount_in > 0)
            $amount = $transaction_details->amount_in;
        else if($transaction_details->amount_out >0)
            $amount = $transaction_details->amount_out;
        else
            return FALSE;

        // check company is active
        $companyModel = model(CompanyModel::class);
        $company_details = $companyModel->where(['id' => $bank_account_details->company_id, 'active' => 1])->get()->getRow();
        if(!is_object($company_details))
            return FALSE;

        // get all webhooks belong this account number
        $webhooks_list = $this->where(['bank_account_id' => $bank_account_details->id, 'active' => 1])->get()->getResult();

        $oauth2 = new \App\Libraries\Oauth2;

        $result = [];

        foreach($webhooks_list as $hooks) {
            
            $result = $this->doWebhook($hooks, $transaction_details,FALSE);
            
        }
        
        return $result;

    }


    private function _getDatatablesQuery($company_id) {

        $request = \Config\Services::request();
  
        $column_order = array(null,'tb_autopay_webhooks.id','tb_autopay_webhooks.name','tb_autopay_webhooks.event_type','tb_autopay_bank_account.account_number','tb_autopay_webhooks.webhook_url','tb_autopay_webhooks.authen_type','tb_autopay_webhooks.active');
        $column_search = array('tb_autopay_bank_account.account_number','tb_autopay_webhooks.name','tb_autopay_webhooks.authen_type','tb_autopay_webhooks.webhook_url');
 
        $this->builder = $this->db->table($this->table);

        $this->builder->select("tb_autopay_webhooks.id,tb_autopay_bank_account.account_number,tb_autopay_webhooks.event_type,tb_autopay_webhooks.authen_type,tb_autopay_webhooks.webhook_url,tb_autopay_webhooks.active,tb_autopay_webhooks.created_at,tb_autopay_bank.brand_name,tb_autopay_bank_account.account_holder_name,tb_autopay_webhooks.name,tb_autopay_webhooks.is_verify_payment,tb_autopay_webhooks.skip_if_no_code");

        $this->builder->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_webhooks.bank_account_id","left");
        $this->builder->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left");

        if($company_id)
            $this->builder->where("tb_autopay_webhooks.company_id",$company_id);

     
        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables($company_id) {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery($company_id);
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll($company_id) {
     
        $builder = $this->db->table($this->table);
        if($company_id)
            $builder->where("company_id",$company_id);
 
       
        return $builder->countAllResults();
    }

    public function countFiltered($company_id) {
        $this->_getDatatablesQuery($company_id);
        if($company_id)
            $this->builder->where("tb_autopay_webhooks.company_id",$company_id);
         
        return $this->builder->countAllResults();
    }

    

 
}