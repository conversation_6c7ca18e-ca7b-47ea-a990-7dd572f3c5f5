<?php

namespace App\Models;

use CodeIgniter\Model;

class SimModel extends Model
{
    protected $table = 'tb_autopay_sim';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['sim_phonenumber', 'seri', 'location', 'description', 'is_shared', 'is_primary', 'active','device_type','dinstar_imsi'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [];
    protected $skipValidation     = false;
    

    protected $order = ['tb_autopay_sim.id'=>'DESC'];

    private function _getDatatablesQuery() {

        $request = \Config\Services::request();
  
        $column_order = array(null,'tb_autopay_sim.id','tb_autopay_sim.sim_phonenumber','tb_autopay_sim.device_type','tb_autopay_sim.description',null,null,'tb_autopay_sim.active','tb_autopay_sim.created_at');
        $column_search = array('tb_autopay_sim.id','tb_autopay_company.id','tb_autopay_company.full_name','tb_autopay_company.short_name','tb_autopay_sim.sim_phonenumber','tb_autopay_sim.seri','tb_autopay_sim.description','tb_autopay_sim.device_type','tb_autopay_sim.dinstar_imsi');
    
        $this->builder = $this->db->table($this->table);
        $this->builder->join("tb_autopay_sim_company","tb_autopay_sim_company.sim_id=tb_autopay_sim.id","left");

        $this->builder->join("tb_autopay_company","tb_autopay_sim_company.company_id=tb_autopay_company.id","left");

        $this->builder->select("tb_autopay_sim.id,tb_autopay_sim.sim_phonenumber,tb_autopay_sim.seri,tb_autopay_sim.created_at,tb_autopay_sim.description,tb_autopay_sim.active,tb_autopay_sim.device_type,tb_autopay_sim.dinstar_imsi");

        $this->builder->groupBy("tb_autopay_sim.id");

 
        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables() {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery();
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll() {
     
        $builder = $this->db->table($this->table);

       
        return $builder->countAllResults();
    }

    public function countFiltered() {
        $this->_getDatatablesQuery();
         
        return $this->builder->countAllResults();
    }
    
}
