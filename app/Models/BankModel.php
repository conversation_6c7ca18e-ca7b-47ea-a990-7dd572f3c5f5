<?php

namespace App\Models;

use CodeIgniter\Model;
use Config\Services;

class BankModel extends Model
{
    protected $table = 'tb_autopay_bank';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = [
        'invididual_order',
        'invididual_api_connection',
        'invididual_speed',
        'invididual_connection_procedure',
        'invididual_promotion',
        'invididual_description',
        'invididual_features',
        'invididual_promotion_short_description',
        'invididual_promotion_button_label',
        'invididual_promotion_description',
        'enterprise_api_connection',
        'enterprise_order',
        'enterprise_speed',
        'enterprise_connection_procedure',
        'enterprise_promotion',
        'enterprise_description',
        'enterprise_features',
        'enterprise_promotion_short_description',
        'enterprise_promotion_button_label',
        'enterprise_promotion_description',
        'brand_name', 'short_name', 'full_name', 'bin', 'code', 'logo_path', 'icon_path', 'active', 'bank_bonus', 'bank_path', 'referral_code', 'guide', 'updated_at', 'created_at'
    ];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [];
    protected $skipValidation     = false;

    protected function _getDatatablesQuery()
    {
        $request = Services::request();

        $orderColumns = [null, 'brand_name'];
        $searchColumns = [];

        $this->builder = $this->db
            ->table($this->table)
            ->select(['id', 'brand_name', 'icon_path', 'active']);

        $i = 0;

        foreach ($searchColumns as $item) {
            if ($request->getVar('search')['value'] != NULL) {
                $searchTerm = trim($request->getVar('search')['value']);

                if ($i === 0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $searchTerm);
                } else {
                    $this->builder->orLike($item, $searchTerm);
                }

                if (count($searchColumns) - 1 == $i) {
                    $this->builder->groupEnd();
                }
            }

            $i++;
        }

        foreach ($searchColumns as $key => $item) {
            if ($item != NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }

        if ($request->getVar('order') != NULL && isset($orderColumns[$request->getVar('order')[0]['column']])  && $orderColumns[$request->getVar('order')[0]['column']] != NULL) {
            $this->builder->orderBy($orderColumns[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);
        }
    }

    public function getDatatables()
    {
        $this->_getDatatablesQuery();

        $request = Services::request();

        if ($request->getVar('length') != NULL && $request->getVar('length') != -1) {
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
        }

        return $this->builder->get()->getResult();
    }

    public function countAll()
    {

        $builder = $this->db->table($this->table);

        return $builder->countAllResults();
    }

    public function countFiltered()
    {
        $this->_getDatatablesQuery();

        return $this->builder->countAllResults();
    }

    public function getDatatablesBankBonus()
    {
        $this->_getDatatablesQueryBankBonus();

        $request = Services::request();

        if ($request->getVar('length') != NULL && $request->getVar('length') != -1) {

            $this->builder->limit($request->getVar('length'), $request->getVar('start'));

        }

        return $this->builder->get()->getResult();
    }

    public function _getDatatablesQueryBankBonus()
    {

        $request = \Config\Services::request();

        $column_order = array(

            'tb_autopay_bank.id',

            'tb_autopay_bank.brand_name',

            'tb_autopay_bank.short_name',

            'tb_autopay_bank.full_name',

            'tb_autopay_bank.icon_path',

            'tb_autopay_bank.active',

            'tb_autopay_bank.bank_bonus',

            'tb_autopay_bank.bank_path',

        ); 

        $column_search = array(

            'tb_autopay_bank.id','tb_autopay_bank.brand_name',

            'tb_autopay_bank.short_name','tb_autopay_bank.full_name',

            'tb_autopay_bank.bank_bonus', 'tb_autopay_bank.bank_path',

            'tb_autopay_bank.referral_code', 'tb_autopay_bank.guide'

        );
        $this->builder = $this->db->table($this->table);
        $this->builder->select("tb_autopay_bank.id, tb_autopay_bank.short_name, tb_autopay_bank.brand_name, tb_autopay_bank.full_name, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path, tb_autopay_bank.active, tb_autopay_bank.bank_bonus, tb_autopay_bank.bank_path");
        $i = 0;
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                if($i===0) {

                    $this->builder->groupStart();

                    $this->builder->like($item, $search_term);

                } else {

                    $this->builder->orLike($item, $search_term);

                }
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search

        foreach ($column_search as $key => $item) { 
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {

                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }

        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {
            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);
        }
    }
}
