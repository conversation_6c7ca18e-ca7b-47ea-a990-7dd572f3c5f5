<?php

namespace App\Models;

use CodeIgniter\Model;

class OutputDeviceReplayMessageQueueModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_output_device_replay_message_queue';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['output_device_id','company_id','transaction_id','status','last_retry_time','retries_count'];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    public function insertIfNotExist($where, array $data)
    {
        try {
            // Kiểm tra xem bản ghi đã tồn tại chưa
            $existingRecord = $this->where($where)->first();
            if ($existingRecord) {
                // Nếu bản ghi đã tồn tại, không làm gì cả
                return ['status' => false, 'message' => 'Record already exists.'];
            } else {
                // Nếu bản ghi chưa tồn tại, thực hiện insert
                $inserted = $this->insert($data);
                if ($inserted === false) {
                    return ['status' => false, 'errors' => $this->errors()];
                }
                return ['status' => true, 'action' => 'inserted', 'inserted_id' => $this->getInsertID()];
            }
        } catch (\Exception $e) {
            return ['status' => false, 'error' => $e->getMessage()];
        }
    }

}
