<?php

namespace App\Models;

use CodeIgniter\Model;

class PhysicalOrderTrackingModel extends Model
{
    protected $table = 'tb_autopay_physical_order_tracking';

    protected $returnType = 'object';

    protected $allowedFields = [
        'order_id',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
        'user_agent',
        'ip_address',
        'referrer',
    ];

    public function addTracking(int $orderId, array $data)
    {
        return $this->insert(
            array_merge(['order_id' => $orderId], $data)
        );
    }
}
