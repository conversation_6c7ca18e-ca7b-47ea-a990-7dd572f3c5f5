<?php

namespace App\Models;

use CodeIgniter\Model;

class CompanyUserModel extends Model
{
    protected $table = 'tb_autopay_company_user';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['user_id', 'company_id', 'role'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;

    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;

    protected $order = ['tb_autopay_company_user.id'=>'desc'];

    protected $builder;
 
    private function _getDatatablesQuery($company_id) {

        $request = \Config\Services::request();
  
        $column_order = array(null,'tb_autopay_user.id', null, 'tb_autopay_user.email', 'tb_autopay_user.updated_at',null);
        $column_search = array('tb_autopay_user.firstname','tb_autopay_user.lastname','tb_autopay_user.email','tb_autopay_user.id');
 
        $this->builder = $this->db->table($this->table);

        $this->builder->select("tb_autopay_user.id,tb_autopay_user.email,tb_autopay_user.firstname,tb_autopay_user.lastname, tb_autopay_user.email,tb_autopay_company.full_name,tb_autopay_company.short_name,tb_autopay_user.active,tb_autopay_user.created_at,tb_autopay_company.short_name,tb_autopay_company.user_owner_id,tb_autopay_company_user.role");

        $this->builder->join("tb_autopay_user","tb_autopay_user.id=tb_autopay_company_user.user_id");
        $this->builder->join("tb_autopay_company","tb_autopay_company.id=tb_autopay_company_user.company_id");

        if($company_id)
            $this->builder->where("tb_autopay_company_user.company_id",$company_id);

     
        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables($company_id) {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery($company_id);
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll($company_id) {
     
        $builder = $this->db->table($this->table);

        if($company_id)
            $builder->where("company_id",$company_id);
 
       
        return $builder->countAllResults();
    }

    public function countFiltered($company_id) {
        $this->_getDatatablesQuery($company_id);
        if($company_id)
            $this->builder->where("tb_autopay_company_user.company_id",$company_id);
         
        return $this->builder->countAllResults();
    }
}