<?php

namespace App\Models;

use CodeIgniter\Model;
use App\Models\CompanyModel;
use App\Models\CompanyUserModel;

class AdminModel extends Model
{
    protected $table = 'tb_autopay_admin';

    protected $primaryKey = 'id';

    protected $returnType     = 'object';
    protected $allowedFields = ['email', 'active','password','firstname','lastname','theme','sidebar_toggle', 'sidebar_behavior', 'role'];

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $dateFormat        = 'datetime';

    protected $validationRules    = [];
    protected $skipValidation     = false;
    


    function login($username, $password, $remember=FALSE) {

        $request = \Config\Services::request();

        $login_ip = $request->getIPAddress();
        $login_agent = $request->getUserAgent()->getAgentString();

        $builder = $this->db->table($this->table);
        $builder->where("email",$username);
        $builder->where("active",1);
        $query = $builder->get();
        $admin_details = $query->getRow();


        helper(['cookie','general']);
      
        if($admin_details) {

            if (password_verify($password, $admin_details->password)) { 
                $session = session();
                $session_data = array(
                    'admin_id' => $admin_details->id,
                    'firstname' => $admin_details->firstname,
                    'lastname' => $admin_details->lastname,
                    'email' => $admin_details->email,
                    'role' => $admin_details->role,
                    'active' => 1,
                );

                $session->set('admin_logged_in', $session_data);

                    
                add_admin_log(array('data_id'=>$admin_details->id,'data_type'=>'login','description'=>'Đăng nhập','admin_id'=> $admin_details->id,'ip'=>$login_ip, 'user_agent'=>$login_agent,'status'=>'Success'));
                
                return TRUE;
            } else {
                add_admin_log(array('data_id'=>$admin_details->id, 'data_type'=>'login','description'=>$username . ' đăng nhập sai mật khẩu','admin_id'=> $admin_details->id,'ip'=>$login_ip, 'user_agent'=>$login_agent,'status'=>'Failed'));
                return FALSE;
            }
        } else {
            add_admin_log(array('data_id'=>0, 'data_type'=>'login','description'=>$username . ' đăng nhập thất bại','admin_id'=> 0,'ip'=>$login_ip, 'user_agent'=>$login_agent,'status'=>'Failed'));
        }
    }


 
}