<?php

namespace App\Models;

use CodeIgniter\Model;

class NotificationUserModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_notification_user';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['notification_id', 'user_id', 'seen_at'];
}
