<?php

namespace App\Models;

use CodeIgniter\Model;

class FormModel extends Model
{
    protected $table = 'tb_autopay_form';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['id','company_id','user_id', 'title','body'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;
    
    protected $order = ['tb_autopay_product.sort_order'=>'asc'];
 
    protected $builder;

}