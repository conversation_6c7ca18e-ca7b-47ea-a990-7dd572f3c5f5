<?php

namespace App\Models;

use CodeIgniter\Model;

class DistrictModel extends Model
{
    protected $table = 'tb_autopay_districts';

    protected $primaryKey = 'code';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = [];

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $useSoftDeletes = true;

    protected $useTimestamps     = false;

    protected $validationRules    = [];

    protected $skipValidation     = false;
    
    protected $order = ['code'=>'ASC'];
 
    protected $builder;

}