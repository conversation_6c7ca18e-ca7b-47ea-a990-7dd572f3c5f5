<?php

namespace App\Models;

use CodeIgniter\Model;

class NotificationTelegramModel extends Model
{
    protected $table = 'tb_autopay_notification_telegram';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['company_id', 'bank_account_id', 'sub_account_id','contains_content', 'transaction_type','webhook_type', 'chat_id', 'description','hide_accumulated', 'hide_details_link', 'ignore_phrases', 'verify_payment', 'active'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;
    protected $order = ['tb_autopay_notification_telegram.id'=>'desc'];


    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;
    
    

    private function _getDatatablesQuery($company_id) {

        $request = \Config\Services::request();
  
        $column_order = array(null,'tb_autopay_notification_telegram.id','tb_autopay_notification_telegram.description',null,null,'tb_autopay_notification_telegram.chat_id','tb_autopay_notification_telegram.active');
        $column_search = array('tb_autopay_notification_telegram.id','tb_autopay_notification_telegram.bank_account_id','tb_autopay_notification_telegram.chat_id','tb_autopay_notification_telegram.contains_content','tb_autopay_notification_telegram.description','tb_autopay_bank_sub_account.sub_account');
 
        $this->builder = $this->db->table($this->table);

        $this->builder->select("tb_autopay_notification_telegram.id,tb_autopay_notification_telegram.company_id,tb_autopay_notification_telegram.bank_account_id,tb_autopay_notification_telegram.sub_account_id,tb_autopay_notification_telegram.transaction_type,tb_autopay_notification_telegram.webhook_type,tb_autopay_notification_telegram.contains_content,tb_autopay_notification_telegram.chat_id,tb_autopay_notification_telegram.created_at,tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank.brand_name,tb_autopay_bank_account.account_holder_name,tb_autopay_notification_telegram.active,tb_autopay_notification_telegram.description,tb_autopay_notification_telegram.ignore_phrases,tb_autopay_notification_telegram.verify_payment");

        $this->builder->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_notification_telegram.bank_account_id","left");
        $this->builder->join("tb_autopay_bank_sub_account","tb_autopay_bank_sub_account.id=tb_autopay_notification_telegram.sub_account_id","left");
        $this->builder->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left");

        if($company_id)
            $this->builder->where("tb_autopay_notification_telegram.company_id",$company_id);

     
        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables($company_id) {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery($company_id);
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll($company_id) {
     
        $builder = $this->db->table($this->table);
        if($company_id)
            $builder->where("company_id",$company_id);
 
       
        return $builder->countAllResults();
    }

    public function countFiltered($company_id) {
        $this->_getDatatablesQuery($company_id);
        if($company_id)
            $this->builder->where("tb_autopay_notification_telegram.company_id",$company_id);
         
        return $this->builder->countAllResults();
    }

    public function checkAndAddQueue($transaction_id) {
        // get all telegram notify conditions
            // check if match conditions
                // add to queue

        // get transaction details
        $transactionsModel = model(TransactionsModel::class);
        $transaction_details = $transactionsModel->select("tb_autopay_sms_parsed.id,tb_autopay_sms_parsed.transaction_date,tb_autopay_sms_parsed.account_number,tb_autopay_sms_parsed.sub_account,tb_autopay_sms_parsed.amount_in,tb_autopay_sms_parsed.amount_out,tb_autopay_sms_parsed.accumulated,tb_autopay_sms_parsed.code,tb_autopay_sms_parsed.transaction_content,tb_autopay_sms_parsed.reference_number,tb_autopay_sms_parsed.body,tb_autopay_sms_parsed.parser_status,tb_autopay_sms_parsed.webhooks_success,tb_autopay_sms_parsed.webhooks_failed,tb_autopay_bank_account.company_id,tb_autopay_bank.brand_name,tb_autopay_bank_account.id as `bank_account_id`,tb_autopay_bank_sub_account.id as `sub_account_id`,tb_autopay_sms_parsed.webhooks_verify_payment, tb_autopay_bank_sub_account.label")->join("tb_autopay_bank_account","tb_autopay_bank_account.account_number=tb_autopay_sms_parsed.account_number","left")->join("tb_autopay_bank_sub_account","tb_autopay_bank_sub_account.sub_account=tb_autopay_sms_parsed.sub_account","left")->join("tb_autopay_company","tb_autopay_company.id=tb_autopay_bank_account.company_id","left")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_sms_parsed.id' => $transaction_id,'tb_autopay_company.active' => 1])->get()->getRow();

        if(!is_object($transaction_details))
            return FALSE;

        // get all notifications
        $builder = $this->db->table($this->table);
        $results  = $builder->where(['company_id' => $transaction_details->company_id, 'active' => 1])->get()->getResult();
         
        foreach($results as $result) {
            // check sub account
            if(is_numeric($result->sub_account_id) && $result->sub_account_id > 0) {
                if($transaction_details->sub_account_id != $result->sub_account_id)
                    continue;
            // check bank account
            } else if(is_numeric($result->bank_account_id) && $result->bank_account_id > 0) {
                if($transaction_details->bank_account_id != $result->bank_account_id)
                    continue;
            }

            // check transaction type
            if($result->transaction_type == 'In_only' && $transaction_details->amount_out > 0)
                continue;

            if($result->transaction_type == 'Out_only' && $transaction_details->amount_in > 0)
                continue;
    
            if($result->webhook_type == 'Failed' && $transaction_details->webhooks_failed == 0)
                continue;
    
            if($result->webhook_type == 'Success' && $transaction_details->webhooks_success == 0)
                continue;

            if($result->webhook_type == 'All' && $transaction_details->webhooks_success == 0 && $transaction_details->webhooks_failed == 0)
                continue;

            if($result->contains_content != NULL && trim($result->contains_content) != '' && strpos($transaction_details->transaction_content,$result->contains_content) === false)
                continue;


            if($result->ignore_phrases) {
                $ignore_phrases_array = explode("|", $result->ignore_phrases);
                if($ignore_phrases_array) {
                    foreach($ignore_phrases_array as $pharse) {
                        if($pharse && strpos($transaction_details->transaction_content,$pharse) !== false) {
                            continue 2;
                        }
                            
                    }
                }
            }
            
            if($result->verify_payment == 'Success' && $transaction_details->webhooks_verify_payment != 'Success' || $result->verify_payment == 'Failed' && $transaction_details->webhooks_verify_payment != 'Failed' || $result->verify_payment == 'No' && $transaction_details->webhooks_verify_payment != 'No' ||  $result->verify_payment == 'Success_Or_Failed' && ($transaction_details->webhooks_verify_payment != 'Success' && $transaction_details->webhooks_verify_payment != 'Failed') )
                continue;
            
            
            $accumulated = "

⛳️ Số dư: <code>" . number_format($transaction_details->accumulated) . ' đ</code>
';
            if($result->hide_accumulated ==1)
                $accumulated = '
';

            if($result->hide_details_link == 1)
                $details_link = '
';
            else
                $details_link = "
Xem <a href='".base_url('transactions/details/' . $transaction_details->id)."'>chi tiết</a>.
"; 

            if($transaction_details->webhooks_verify_payment == 'Success')
                $webhooks_verify_payment = '✅ Thành công';
            else if($transaction_details->webhooks_verify_payment == 'Failed')
                $webhooks_verify_payment = '❗️ Thất bại';
            else if($transaction_details->webhooks_verify_payment == 'No')
                $webhooks_verify_payment = '⚪️ Không thực hiện';
            else
                $webhooks_verify_payment = '';

            $message = "--------- " . $transaction_details->label ." - ID: " . $transaction_details->id . " ---------
Có giao dịch mới:

✳️ Tiền vào: <code>" . number_format($transaction_details->amount_in) . " đ</code>

➖ Tiền ra: <code>" . number_format($transaction_details->amount_out) . " đ</code>" . $accumulated . 
"
⛪️ Tài khoản chính: <code>" . $transaction_details->account_number . "</code> " . $transaction_details->brand_name . " | Tài khoản phụ: <code>" . $transaction_details->sub_account . "</code> " . $transaction_details->label . "

ℹ️ Nội dung thanh toán: <code>" . $transaction_details->transaction_content . "</code>

#️⃣ Mã code thanh toán: <code>" . $transaction_details->code . "</code>

⚓️ Mã tham chiếu: <code>" . $transaction_details->reference_number . "</code>

⏰ Giao dịch lúc: <code>" . $transaction_details->transaction_date . "</code>" .
$details_link .
"
------------------------------";
 

            $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
            // add to queue
            $notificationTelegramQueueModel->insert([
                'transaction_id' => $transaction_id,
                'notify_id' => $result->id,
                'chat_id' => $result->chat_id,
                'status' => 'Pending',
                'message' => $message
            ]);

        }
    }
    
}