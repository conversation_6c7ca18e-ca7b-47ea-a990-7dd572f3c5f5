<?php

namespace App\Models;

use CodeIgniter\Model;

class SystemLogModel extends Model
{
    protected $table = 'tb_autopay_system_log';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['company_id','data_type','description','ip','user_agent','level', 'by'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;

    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;
    
    protected $order = ['sort_order'=>'desc'];
 
    protected $builder;

}