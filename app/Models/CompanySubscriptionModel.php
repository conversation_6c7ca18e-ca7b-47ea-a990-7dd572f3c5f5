<?php

namespace App\Models;

use CodeIgniter\Model;
use App\Models\InvoiceItemModel;
use App\Models\InvoiceModel;
use App\Models\CounterModel;
use Config\Subscription;
use Config\Zns;

class CompanySubscriptionModel extends Model
{
    protected $table = 'tb_autopay_company_subscription';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['company_id','order_id','plan_id', 'begin_date','end_date','first_payment', 'recurring_payment','billing_cycle','auto_renew', 'status', 'monthly_transaction_limit','bank_account_limit','telegram_intergration_limit','webhook_intergration_limit','monthly_telegram_send_limit','monthly_webhooks_send_limit','notes', 'disable_auto_suspend', 'disable_suspension_until', 'last_notified_at', 'last_notification_type', 'shop_limit', 'is_trial', 'allow_exceed_limit'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;
    
    protected $order = ['id'=>'desc'];
 
    protected $builder;

    protected $afterUpdate = ['afterUpdateBankBonus'];

    private function _billing_cycle_to_month($b) {
        $map_b = [
            'monthly' => 1,
            'quarterly' => 3,
            'semi-annually' => 6,
            'annually' => 12,
            'biennially' => 24,
            'triennially' => 36,
        ];
    
        if(isset($map_b[$b]))
            return $map_b[$b];
        else
            return FALSE;
    }

    private function _getDatatablesQuery($company_id, $plan_id, $status) {

        $request = \Config\Services::request();
  
        $column_order = array(null,'id','company_id','plan_id','billing_cycle','status','begin_date','end_date','first_payment','recurring_payment','created_at');
        $column_search = array('id','billing_cycle','status');
 
        $this->builder = $this->db->table($this->table);

        $this->builder->select("id,company_id, plan_id, billing_cycle, begin_date, end_date, first_payment, recurring_payment, created_at,status");

        if($company_id)
            $this->builder->where("company_id",$company_id);
        if($status)
            $this->builder->where("status",$status);
        if($plan_id)
            $this->builder->where("plan_id",$plan_id);

     
        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables($company_id=FALSE, $plan_id=FALSE, $status=FALSE) {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery($company_id , $plan_id, $status);
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll($company_id=FALSE, $plan_id=FALSE, $status=FALSE) {
     
        $builder = $this->db->table($this->table);

        if($company_id)
            $builder->where("company_id",$company_id);
        if($plan_id)
            $builder->where("plan_id",$plan_id);
        if($status)
            $builder->where("status",$status);
 
       
        return $builder->countAllResults();
    }

    public function countFiltered($company_id=FALSE, $plan_id=FALSE, $status=FALSE) {
        $this->_getDatatablesQuery($company_id, $plan_id, $status);
        if($company_id)
            $this->builder->where("company_id",$company_id);
         
        return $this->builder->countAllResults();
    }

    public function generate_next_invoice($subscription_id) {
         
        $invoiceModel = model(InvoiceModel::class);
        $invoiceItemModel = model(InvoiceItemModel::class);

        $productModel = model(ProductModel::class);

        $builder = $this->db->table($this->table);


        $subscription_details = $builder->where(['id' => $subscription_id])->get()->getRow();
 
        if(!is_object($subscription_details))
            return array('status'=>FALSE,'message'=>"Không tìm thấy Subscription này");


        $product_details = $productModel->find($subscription_details->plan_id);

        if(!is_object($product_details))
            return array('status'=>FALSE,'message'=>"Không tìm thấy Plan");

        $new_begin_date = date('Y-m-d', strtotime($subscription_details->end_date  . ' +1 day'));
        
        if($subscription_details->billing_cycle === 'monthly') {
            $new_end_date =  date('Y-m-d', strtotime($subscription_details->end_date  . ' +1 month'));
        } else if($subscription_details->billing_cycle == "quarterly") {
            $new_end_date =  date('Y-m-d', strtotime($subscription_details->end_date  . ' +3 months'));
        } else if($subscription_details->billing_cycle == "semi-annually") {
            $new_end_date =  date('Y-m-d', strtotime($subscription_details->end_date  . ' +6 months'));
        } else if($subscription_details->billing_cycle == "annually") {
            $new_end_date =  date('Y-m-d', strtotime($subscription_details->end_date  . ' +1 year'));
        } else if($subscription_details->billing_cycle == "biennially") {
            $new_end_date =  date('Y-m-d', strtotime($subscription_details->end_date  . ' +2 years'));
        } else if($subscription_details->billing_cycle == "triennially") {
            $new_end_date =  date('Y-m-d', strtotime($subscription_details->end_date  . ' +3 years'));
        } else
            return array('status'=>FALSE,'message'=>"Không thể tạo hoá đơn");

        $subscription_data = [ 
            "begin_date" => $new_begin_date,
            "end_date" => $new_end_date
        ];

        if(is_numeric($product_details->tax_rate) && $product_details->tax_rate >0) {
            $is_product_taxed = 1;
            $tax = $subscription_details->recurring_payment * ($product_details->tax_rate/100);
        } else {
            $is_product_taxed = 0;
            $tax  = 0;
        }
            
        // create new invoice
        $invoice_data = [
            "company_id" => $subscription_details->company_id,
            "type" => "Recurring",
            "status" => "Unpaid",
            "date" => $new_begin_date,
            "duedate" => $new_begin_date,
            "paybefore" => date('Y-m-d', strtotime($new_begin_date  . ' +7 day')),
            "subtotal" => $subscription_details->recurring_payment,
            "total" => $subscription_details->recurring_payment + $tax,
            'tax' => $tax,
            'tax_rate' => $product_details->tax_rate,
        ];

        $invoice_id = $invoiceModel->insert($invoice_data);

        if($invoice_id) {
            $invoice_item_data = [
                "invoice_id" => $invoice_id,
                "type" => "Product",
                "item_id" => $product_details->id,
                "description" => "SePay - " .  $product_details->name . " (" . $new_begin_date . " - ". $new_end_date .")",
                "amount" => $subscription_details->recurring_payment,
                "start_date" => $new_begin_date,
                "end_date" => $new_end_date,
                'taxed' => $is_product_taxed,
                'tax_rate' => $product_details->tax_rate,
                'tax' => $tax,
            ];
            
            if ($product_details->shop_limit > 0) {
                $invoice_item_data['details'] = sprintf(
                    '<br> <small>%s cửa hàng</small>',
                    $subscription_details->shop_limit,
                );
            }

            $invoiceItemModel->insert( $invoice_item_data);

            if(!$this->sendNotificationToZnsNext($invoice_id, $new_begin_date)){
                log_message('error', "Save Queue ZNS failed invoice ID: " . $invoice_id);
            }

            $builder->set($subscription_data)->where(['id'=>$subscription_id])->update();
        } else
            return array('status'=>FALSE,'message'=>"Không thể tạo hoá đơn");

        return ['status' => TRUE, 'message' => 'Tạo hóa đơn thành công','invoice_id' => $invoice_id];
    }

    public function generate_excess_invoice($subscription_id) {
         
        $invoiceModel = model(InvoiceModel::class);
        $invoiceItemModel = model(InvoiceItemModel::class);

        $productModel = model(ProductModel::class);
        $counterModel = model(CounterModel::class);

        $builder = $this->db->table($this->table);


        $subscription_details = $builder->where(['id' => $subscription_id])->get()->getRow();

     
        if(!is_object($subscription_details))
            return array('status'=>FALSE,'message'=>"Không tìm thấy Subscription này");


        $product_details = $productModel->find($subscription_details->plan_id);

        if(!is_object($product_details))
            return array('status'=>FALSE,'message'=> "Không tìm thấy Plan");


        $map_b = [
            'monthly' => 1,
            'quarterly' => 3,
            'semi-annually' => 6,
            'annually' => 12,
            'biennially' => 24,
            'triennially' => 36,
        ];

        helper('general');

        $billing_to_months = billing_cycle_to_month($subscription_details->billing_cycle);

        if(!is_numeric($billing_to_months))
            return array('status'=>FALSE,'message'=> "Không thể xác định được chu kỳ thanh toán."); 

        if($subscription_details->monthly_transaction_limit<=0)
            return array('status'=>FALSE,'message'=> "monthly_transaction_limit đang <=0.");     

        $beginDate = $subscription_details->begin_date;

        if ($subscription_details->billing_cycle === 'free') {
            $beginDate = date('Y-m-d', strtotime('-1 month +1 day', strtotime($subscription_details->end_date)));
        }

        $transaction_usage = $counterModel->count_transaction_by_interval($subscription_details->company_id, $beginDate, $subscription_details->end_date);

        if($transaction_usage <= ($subscription_details->monthly_transaction_limit * $billing_to_months)) { 
            return array('status'=>FALSE,'message'=> "Giao dịch sử dụng không vượt quá hạn mức."); 
        }

        $trans_excess = $transaction_usage - ($subscription_details->monthly_transaction_limit * $billing_to_months);
        $price_per_transaction = ($subscription_details->recurring_payment)/($subscription_details->monthly_transaction_limit * $billing_to_months);
        $description = "SePay - " . number_format($trans_excess) . " giao dịch vượt hạn mức (" . $beginDate . " - ". $subscription_details->end_date .")";

        if ($subscription_details->billing_cycle === 'free') {
            $price_per_transaction = config(Subscription::class)->freeSubscriptionExcessLimitPrice ?? 0;
            $description .= " với giá " . number_format($price_per_transaction) . "đ mỗi giao dịch";
        }

        $subtotal = intval($price_per_transaction * $trans_excess);

        if(is_numeric($product_details->tax_rate) && $product_details->tax_rate >0) {
            $is_product_taxed = 1;
            $tax = $subtotal * ($product_details->tax_rate/100);
        } else {
            $is_product_taxed = 0;
            $tax  = 0;
        }

        $total = intval($price_per_transaction * $trans_excess) + $tax;

  
        // create new invoice
        $invoice_data = [
            "company_id" => $subscription_details->company_id,
            "type" => "Excess",
            "status" => "Unpaid",
            "date" => $subscription_details->end_date,
            "duedate" => $subscription_details->end_date,
            "paybefore" => date('Y-m-d', strtotime($subscription_details->end_date  . ' +5 day')),
            "subtotal" => $subtotal,
            "total" => $total,
            'tax' => $tax,
            'tax_rate' => $product_details->tax_rate,
        ];

        $invoice_id = $invoiceModel->insert($invoice_data);

        if($invoice_id) {
            $invoice_item_data = [
                "invoice_id" => $invoice_id,
                "type" => "Product",
                "item_id" => $product_details->id,
                "description" => $description,
                "amount" => $total,
                "start_date" => $beginDate,
                "end_date" => $subscription_details->end_date,
                'taxed' => $is_product_taxed,
                'tax_rate' => $product_details->tax_rate,
                'tax' => $tax,
            ];

            $invoiceItemModel->insert( $invoice_item_data);

            if (!$this->sendNotificationToZnsExcess($invoice_id, $trans_excess, $beginDate, $subscription_details->end_date, $price_per_transaction)) {
                log_message('error', "Save Queue ZNS failed invoice ID: " . $invoice_id);
            }            
            

        } else
            return array('status'=>FALSE,'message'=>"Không thể tạo hoá đơn");

        return ['status' => TRUE, 'message' => 'Tạo hóa đơn thành công','invoice_id' => $invoice_id];
    }

    public function sendNotificationToZnsExcess($invoice_id, $transaction_number, $beginDate, $endDate, $price_per_transaction)
    {
        helper('general');

        $companyModel = model(CompanyModel::class);
        $userModel = model(UserModel::class);
        $invoiceModel = model(InvoiceModel::class);
        $znsQueueModel = model(ZnsQueueModel::class);
        
        $invoice_details = $invoiceModel->where(['id' => $invoice_id])->get()->getRow();
        
 
        if(!is_object($invoice_details))
            return FALSE;

        $company_details = $companyModel->find($invoice_details->company_id);

        if(!is_object($company_details))
            return FALSE;

        $user_details = $userModel->select("tb_autopay_user.email, tb_autopay_user.firstname, tb_autopay_user.lastname, tb_autopay_company_user.user_id, tb_autopay_zns_notification.default_phone, tb_autopay_zns_notification.accounting_phone, tb_autopay_zns_notification.active_phone_type")
        ->join("tb_autopay_company_user","tb_autopay_company_user.user_id=tb_autopay_user.id")
        ->join("tb_autopay_zns_notification","tb_autopay_zns_notification.company_id=tb_autopay_company_user.company_id")
        ->where(['tb_autopay_company_user.company_id' => $invoice_details->company_id, "tb_autopay_company_user.role" => "SuperAdmin"])->get()->getRow();

        if(!is_object($user_details))
            return FALSE;

        $ZNSNotificationEnabled = get_configuration('ZNSNotificationEnabled', $invoice_details->company_id);

        if($ZNSNotificationEnabled == 'off')
            return FALSE;

        $active_phone_type = $user_details->active_phone_type;
        $default_phone = $user_details->default_phone;
        $accounting_phone = $user_details->accounting_phone;

        if(!$default_phone && !$accounting_phone){
            return FALSE;
        }

        if($active_phone_type == 'default'){
            $phonenumber = $default_phone;
        } else {
            $phonenumber = $accounting_phone;
        }

        if (!preg_match('/^0\d{9}$/', $phonenumber)) {
            return FALSE;
        }
        
        $znsConfig = config(Zns::class);
        $zns_total_invoice = $znsConfig->zns_total_invoice;

        $paycode = create_paycode($invoice_details->id);

        $customer_name = $user_details->lastname . " " . $user_details->firstname;
        $customer_ID = $user_details->user_id;
        $bill_number = '#'.$invoice_details->id;
        $time_transaction =  date('d/m/Y', strtotime($beginDate)) . " - " . date('d/m/Y', strtotime($endDate));
        $total_payment = $invoice_details->total;
        $shd = $invoice_details->id;
        $type = $invoice_details->type;

        if(floatval($total_payment) < floatval($zns_total_invoice))
            return FALSE;

        $template_data = [
            'customer_name' => $customer_name,
            'customer_ID' => $customer_ID,
            'bill_number' => $bill_number,
            'transaction_number' => $transaction_number,
            'time_transaction' => $time_transaction,
            'price_per_transaction' => $price_per_transaction,
            'total_payment' => (int) $total_payment,
            'content_payment' => $paycode,
            'shd' => $shd,
            'type' => $type,
        ];
        

        $data = [
            'client_req_id' => bin2hex(random_bytes(16)),
            'from' => $znsConfig->zns_oa_id,
            'to' => '84'.trim($phonenumber, '0'),
            'template_id' => $znsConfig->zns_template_id_invoice_recurring_excess,
            'dlr' => 1,
            'template_data' => $template_data,
        ];

        $znsQueueModel->insert([
            'smsid' => $data['client_req_id'],
            'from' => $data['from'],
            'to' => $data['to'],
            'template_id' => $data['template_id'],
            'dlr' => 1,
            'template_data' => json_encode($data['template_data']),
            'raw_data' => json_encode($data),
            'status' => 'Pending',
            'retryable' => true,
        ]);

    }

    public function sendNotificationToZnsNext($invoice_id, $beginDate)
    {
        helper('general');

        $companyModel = model(CompanyModel::class);
        $userModel = model(UserModel::class);
        $invoiceModel = model(InvoiceModel::class);
        $znsQueueModel = model(ZnsQueueModel::class);
        
        $invoice_details = $invoiceModel->where(['id' => $invoice_id])->get()->getRow();
        
 
        if(!is_object($invoice_details))
            return FALSE;

        $company_details = $companyModel->find($invoice_details->company_id);

        if(!is_object($company_details))
            return FALSE;

        $user_details = $userModel->select("tb_autopay_user.email, tb_autopay_user.firstname, tb_autopay_user.lastname, tb_autopay_company_user.user_id, tb_autopay_zns_notification.default_phone, tb_autopay_zns_notification.accounting_phone, tb_autopay_zns_notification.active_phone_type")
        ->join("tb_autopay_company_user","tb_autopay_company_user.user_id=tb_autopay_user.id")
        ->join("tb_autopay_zns_notification","tb_autopay_zns_notification.company_id=tb_autopay_company_user.company_id")
        ->where(['tb_autopay_company_user.company_id' => $invoice_details->company_id, "tb_autopay_company_user.role" => "SuperAdmin"])->get()->getRow();

        if(!is_object($user_details))
            return FALSE;

        $ZNSNotificationEnabled = get_configuration('ZNSNotificationEnabled', $invoice_details->company_id);

        if($ZNSNotificationEnabled == 'off')
            return FALSE;

        $active_phone_type = $user_details->active_phone_type;
        $default_phone = $user_details->default_phone;
        $accounting_phone = $user_details->accounting_phone;

        if(!$default_phone && !$accounting_phone){
            return FALSE;
        }

        if($active_phone_type == 'default'){
            $phonenumber = $default_phone;
        } else {
            $phonenumber = $accounting_phone;
        }

        if (!preg_match('/^0\d{9}$/', $phonenumber)) {
            return FALSE;
        }
        
        $znsConfig = config(Zns::class);
        $zns_total_invoice = $znsConfig->zns_total_invoice;

        $paycode = create_paycode($invoice_details->id);

        $customer_name = $user_details->lastname . " " . $user_details->firstname;
        $customer_ID = $user_details->user_id;
        $bill_number = '#'.$invoice_details->id;
        $total_payment = $invoice_details->total;
        $shd = $invoice_details->id;
        $type = $invoice_details->type;
        $data_payment = date('Y-m-d', strtotime($beginDate  . ' +7 day'));

        if(floatval($total_payment) < floatval($zns_total_invoice))
            return FALSE;

        $template_data = [
            'customer_name' => $customer_name,
            'customer_ID' => $customer_ID,
            'bill_number' => $bill_number,
            'date_payment' => $data_payment,
            'total_payment' => (int) $total_payment,
            'content_payment' => $paycode,
            'shd' => $shd,
            'type' => $type,
        ];
        

        $data = [
            'client_req_id' => bin2hex(random_bytes(16)),
            'from' => $znsConfig->zns_oa_id,
            'to' => '84'.trim($phonenumber, '0'),
            'template_id' => $znsConfig->zns_template_id_invoice_recurring_next,
            'dlr' => 1,
            'template_data' => $template_data,
        ];

        $znsQueueModel->insert([
            'smsid' => $data['client_req_id'],
            'from' => $data['from'],
            'to' => $data['to'],
            'template_id' => $data['template_id'],
            'dlr' => 1,
            'template_data' => json_encode($data['template_data']),
            'raw_data' => json_encode($data),
            'status' => 'Pending',
            'retryable' => true,
        ]);
    }

    protected function afterUpdateBankBonus(array $data)
    {
        if (isset($data['id']) && $data['id']) {
            $companySubscriptionBuilder = $this->db->table('tb_autopay_company_subscription');
            $companySubscriptionBuilderInfo = $companySubscriptionBuilder->select(['id', 'company_id', 'monthly_transaction_limit', 'plan_id'])
                ->where('id', $data['id'])->get()->getRow();

            if (!$companySubscriptionBuilderInfo) {
                return $data;
            }

            $builder = $this->db->table('tb_autopay_bank_bonus');
            $totalBonusForCompany = $builder->selectSum('bank_bonus')
                ->where(['company_id' => $companySubscriptionBuilderInfo->company_id])
                ->get()
                ->getRow();
            $totalBonusForCompany = $totalBonusForCompany ? $totalBonusForCompany->bank_bonus : 0;

            $configBankBonus = config(\Config\BankBonus::class);

            if (!$configBankBonus->maxBankBonus) {
                return $data;
            }

            $bankBonusLimit = $configBankBonus->maxBankBonus;

            if ($totalBonusForCompany > $bankBonusLimit) {
                $totalBonusForCompany = $bankBonusLimit;
            }

            $newBonusTransactions = $companySubscriptionBuilderInfo->monthly_transaction_limit + $totalBonusForCompany;

            $this->where(['id' => $companySubscriptionBuilderInfo->id])
                ->set(['monthly_transaction_limit' => $newBonusTransactions])
                ->update();
        }

        return $data;
    }
    
}