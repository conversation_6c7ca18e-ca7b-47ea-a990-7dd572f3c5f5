<?php

namespace App\Models;

use CodeIgniter\Model;

class BankAccountModel extends Model
{
    protected $table = 'tb_autopay_bank_account';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['company_id', 'bank_id', 'account_holder_name', 'account_number','label','sim_id', 'active'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;

    protected $order = ['tb_autopay_bank_account.id'=>'desc'];

    protected $builder;

    public function getUserByAccountNumber($account_number) {
        $builder = $this->db->table($this->table);
        $builder->select("tb_autopay_user.active");
        $builder->join("tb_autopay_user", "tb_autopay_user.id=tb_autopay_bank_account.company_id");

        $builder->where("tb_autopay_bank_account.account_number", $account_number);
        $query = $this->builder->get();

        return $query->getRow();
    }
 
    private function _getDatatablesQuery($company_id) {

        $request = \Config\Services::request();
  
        $column_order = array(null,'tb_autopay_bank_account.id','tb_autopay_bank_account.company_id','tb_autopay_bank_account.merchant_id','tb_autopay_bank_account.account_number',null,null,null);
        $column_search = array('tb_autopay_bank_account.id','tb_autopay_bank_account.account_number','tb_autopay_bank_account.account_number','tb_autopay_bank_account.account_holder_name');
    
        $this->builder = $this->db->table($this->table);

        $this->builder->select("tb_autopay_bank_account.id,tb_autopay_bank_account.bank_id, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank_account.updated_at,tb_autopay_bank_account.last_transaction,tb_autopay_bank_account.company_id,tb_autopay_bank_account.bank_sms,tb_autopay_bank_account.bank_sms_connected,tb_autopay_bank_account.bank_api,tb_autopay_bank_account.bank_api_connected,tb_autopay_bank_account.merchant_id");

        if($company_id)
            $this->builder->where("tb_autopay_bank_account.company_id",$company_id);

        if($request->getGet('merchant_id') != NULL) {
            $this->builder->where("merchant_id", $request->getGet('merchant_id'));
        }
     
        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables($company_id='') {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery($company_id);
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll($company_id) {
     
        $builder = $this->db->table($this->table);

        if($company_id)
            $builder->where("tb_autopay_bank_account.company_id",$company_id);
 
       
        return $builder->countAllResults();
    }

    public function countFiltered($company_id) {
        $this->_getDatatablesQuery($company_id);
        if($company_id)
            $this->builder->where("tb_autopay_bank_account.company_id",$company_id);
         
        return $this->builder->countAllResults();
    }

    public function get_bank_account_company($company_id) {
        
        $builder = $this->db->table($this->table);
        $builder->select("tb_autopay_bank.brand_name,tb_autopay_bank_account.id, tb_autopay_bank.short_name, tb_autopay_bank.full_name, tb_autopay_bank.logo_path,tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active");
        $builder->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","inner");
        $builder->where('tb_autopay_bank_account.company_id', $company_id);
        $query = $builder->get();

        return $query->getResult();
    }
}