<?php

namespace App\Models;

use App\Libraries\ProxyCURLRequest;
use CodeIgniter\Model;
use Config\TelegramBot;

class NotificationTelegramQueueModel extends Model
{
    protected $table = 'tb_autopay_notification_telegram_queue';

    protected $returnType     = 'object';

    protected $allowedFields = ['company_id', 'transaction_id', 'notify_id', '', 'chat_id', 'message', 'status', 'retries_count', 'last_log', 'retries_count', 'last_retry_time', 'last_retry_log'];

    public function sendTelegramMessage($msg, $chat_id, $parse_mode = false)
    {
        $botParams = [
            'chat_id' => $chat_id,
            'text' => $msg,
            'disable_web_page_preview' => true,
        ];

        if ($parse_mode) {
            $botParams['parse_mode'] = $parse_mode;
        }

        $telegramBotConfig = config(TelegramBot::class);

        $response = ProxyCURLRequest::make()
            ->setProxy('telegram')
            ->request(
                'POST',
                "https://api.telegram.org/bot{$telegramBotConfig->id}:{$telegramBotConfig->token}/sendMessage",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                    ],
                    'body' => json_encode($botParams),
                    'http_errors' => false,
                ]
            );

        $data = [
            'success' => false,
            'response' => $response->getBody()
        ];

        if ($response->getStatusCode() === 200) {
            $data['success'] = true;
        }

        return $data;
    }
}
