<?php

namespace App\Models;

use CodeIgniter\Model;
use App\Models\CompanyModel;
use App\Models\CompanyUserModel;

class UserModel extends Model
{
    protected $table = 'tb_autopay_user';

    protected $primaryKey = 'id';
    //protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['company_name','email', 'active','password','phonenumber','firstname','lastname','theme','sidebar_toggle', 'sidebar_behavior'];

    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $dateFormat        = 'datetime';

    protected $validationRules    = [
       // 'company_id'        => 'required',
       // 'email'        => 'required|valid_email|is_unique[tb_waf_user.email]',
    ];
    protected $skipValidation     = false;
    


    function login($username, $password, $remember=FALSE) {

    

        $request = \Config\Services::request();

        $login_ip = $request->getIPAddress();
        $login_agent = $request->getUserAgent()->getAgentString();

        $builder = $this->db->table($this->table);
        $builder->where("email",$username);
        $builder->where("active",1);
        $query = $builder->get();
        $user = $query->getRow();

        $companyModel = model(CompanyModel::class);
        $companyUserModel = model(CompanyUserModel::class);


        helper(['cookie','general']);
      
        if($user) {
            $company_details = $companyUserModel->select("tb_autopay_company_user.company_id,tb_autopay_company.user_owner_id")->join("tb_autopay_company","tb_autopay_company.id=tb_autopay_company_user.company_id")->where(['tb_autopay_company_user.user_id' => $user->id])->get()->getRow();

            if (password_verify($password, $user->password) && is_object($company_details) && $company_details->company_id > 0) { 
                $session = session();
                $session_data = array(
                    'user_id' => $user->id,
                    'company_id' => $company_details->company_id,
                    //'company_user_owner_id' => $company_details->user_owner_id,
                    'firstname' => $user->firstname,
                    'lastname' => $user->lastname,
                    'email' => $user->email,
                    'active' => 1,
                );

                $session->set('user_logged_in', $session_data);

                    
                add_user_log(array('data_id'=>$user->id,'company_id' => $company_details->company_id, 'data_type'=>'login','description'=>'Đăng nhập','user_id'=> $user->id,'ip'=>$login_ip, 'user_agent'=>$login_agent,'status'=>'Success'));
                
                return TRUE;
            } else {
                add_user_log(array('data_id'=>$user->id,'company_id' => 0, 'data_type'=>'login','description'=>'Đăng nhập thất bại','user_id'=> $user->id,'ip'=>$login_ip, 'user_agent'=>$login_agent,'status'=>'Failed'));
                return FALSE;
            }
        } else {
            add_user_log(array('data_id'=>0,'company_id' => 0, 'data_type'=>'login','description'=>'Đăng nhập thất bại','user_id'=> 0,'ip'=>$login_ip, 'user_agent'=>$login_agent,'status'=>'Failed'));
        }
    }


    function login_as_user($username='') {

        $builder = $this->db->table($this->table);
        $builder->where("email",$username);
        $builder->where("active",1);
        $query = $builder->get();
        $user = $query->getRow();

        $companyModel = model(CompanyModel::class);
        $companyUserModel = model(CompanyUserModel::class);

        helper(['cookie','general']);
      
        if($user) {
            $company_details = $companyUserModel->select("tb_autopay_company_user.company_id,tb_autopay_company.user_owner_id")->join("tb_autopay_company","tb_autopay_company.id=tb_autopay_company_user.company_id")->where(['tb_autopay_company_user.user_id' => $user->id])->get()->getRow();

            if (is_object($company_details) && $company_details->company_id > 0) { 
                $session = session();
                $session_data = array(
                    'user_id' => $user->id,
                    'company_id' => $company_details->company_id,
                    'firstname' => $user->firstname,
                    'lastname' => $user->lastname,
                    'email' => $user->email,
                    'active' => 1,
                );

                $session->set('user_logged_in', $session_data);
                return TRUE;
            } else {
                return FALSE;
            }
        }
        return FALSE;
    }
 
}
