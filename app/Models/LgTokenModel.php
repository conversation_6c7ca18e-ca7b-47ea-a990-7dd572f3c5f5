<?php

namespace App\Models;

use CodeIgniter\Model;

class LgTokenModel extends Model
{
    protected $table = 'tb_autopay_lg_token';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['user_id','admin_id','token', 'partner_id'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;

    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;
    
    protected $order = ['sort_order'=>'desc'];
 
    protected $builder;

}