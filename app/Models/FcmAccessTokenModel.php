<?php

namespace App\Models;

use CodeIgniter\Model;

class FcmAccessTokenModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_fcm_access_token';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['access_token', 'ttl', 'expires_at'];
}
