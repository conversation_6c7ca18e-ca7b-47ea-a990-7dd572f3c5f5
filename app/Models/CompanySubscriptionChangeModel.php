<?php

namespace App\Models;

use CodeIgniter\Model;

class CompanySubscriptionChangeModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_company_subscription_change';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['company_id', 'order_id', 'plan_id', 'begin_date', 'end_date', 'first_payment', 'recurring_payment', 'billing_cycle', 'monthly_transaction_limit', 'bank_account_limit', 'telegram_intergration_limit', 'webhook_intergration_limit', 'monthly_telegram_send_limit', 'monthly_webhooks_send_limit', 'shop_limit', 'merchant_id'];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
}
