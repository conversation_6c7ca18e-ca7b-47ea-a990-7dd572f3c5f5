<?php

namespace App\Models;

use CodeIgniter\Model;

class NotificationToModel extends Model
{
    protected $table = 'tb_autopay_notification_to';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['notification_id','data_type','data_id'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;

    protected $useTimestamps     = false;

    protected $validationRules    = [];
    
    protected $skipValidation     = false;
    
    protected $order = ['id'=>'ASC'];
 
    protected $builder;

}