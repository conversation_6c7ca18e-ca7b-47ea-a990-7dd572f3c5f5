<?php

namespace App\Controllers;
use App\Controllers\BaseController;

use App\Models\OtherIntergationModel;
use App\Models\OtherTypeIntergationModel;

class OtherIntergation extends BaseController
{

    public function type()
    {
        if (!has_permission('OtherIntergation', 'can_view_all')) {
            show_404();
        }
        $data = [
            'page_title' => 'Loại tích hợp',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        echo view('templates/sepay/header', $data);
        echo view('othertypeintergation/index', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajax_get_list_type()
    {
        if(!has_permission('OtherIntergation', 'can_view_all'))
        show_404();
 
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $otherTypeIntergationModel = model(OtherTypeIntergationModel::class);

        $type = $otherTypeIntergationModel->getDatatables();

        $data = array();
        $draw = $this->request->getVar('draw');

        foreach ($type as $type) {
            $row = array();

            $row[] = $type->id;
            $row[] = esc($type->name);
            $row[] = esc($type->slug);
            $row[] = esc($type->position);
            $row[] = $type->created_at;
            $row[] = $type->updated_at;
            $row[] = "<button onclick='edit_type(". $type->id.")' class='btn btn-sm btn-warning'><i class='bi bi-pencil'>Cập nhật</button>";

            $data[] = $row;
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => $otherTypeIntergationModel->countAll(),
            "recordsFiltered" => $otherTypeIntergationModel->countFiltered(),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }

    public function ajax_get_type($id = '')
    {
        if(!is_numeric($id)) 
        return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy loại tích hợp này"));

        if(!has_permission('OtherIntergation', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không chưa được cấp quyền"));
        
        $otherTypeIntergationModel = model(OtherTypeIntergationModel::class);
        
        $result = $otherTypeIntergationModel->find($id);
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu"));
    }

    public function ajax_add_type()
    {
        if (!has_permission('OtherIntergation', 'can_add')) {
            show_404();
        }
        if ($this->request->getMethod(true) != 'POST') {
            return '';
        }
    
        $validation = \Config\Services::validation();
        helper('text');
    
        $otherTypeIntergationModel = model(OtherTypeIntergationModel::class);

        if (!$this->validate([
            'name' => [
                'label' => 'Tên loại tích hợp',
                'rules' => 'required|max_length[255]',
            ],
            'slug' => [
                'label' => 'Đường dẫn loại tích hợp',
                'rules' => 'required|max_length[255]|is_unique[tb_autopay_other_type_intergation.slug]',
                'errors' => [
                    'is_unique' => 'Đường dẫn loại tích hợp đã tồn tại.',
                ],
            ],
            'position' => [
                'label' => 'Vị trí loại tích hợp',
                'rules' => 'permit_empty|is_natural',
            ],
        ])) {
            return $this->response->setJSON([
                'status' => FALSE,
                'message' => implode(". ", $validation->getErrors())
            ]);
        }

        $data = [
            'name' => $this->request->getVar('name'),
            'slug' => $this->request->getVar('slug'),
            'position' => $this->request->getVar('position') ?: 0,
        ];
        $result = $otherTypeIntergationModel->insert($data);

        add_admin_log([
            'data_id' => $result,
            'data_type' => 'other_type_integration_add',
            'description' => 'Thêm loại tích hợp thành công',
            'admin_id' => $this->admin_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);
    
        return $this->response->setJSON(["status" => TRUE, "data" => $result]);
    }
    
    public function ajax_update_type()
    {
        if (!has_permission('OtherIntergation', 'can_edit')) {
            show_404();
        }

        if ($this->request->getMethod(true) != 'POST') {
            return '';
        }

        $validation = \Config\Services::validation();
        helper('text');

        $otherTypeIntergationModel = model(OtherTypeIntergationModel::class);

        $id = $this->request->getVar('id');
        if (!$id || !$otherTypeIntergationModel->find($id)) {
            return $this->response->setJSON(['status' => FALSE, 'message' => 'Loại tích hợp không tồn tại.']);
        }

        if (!$this->validate([
            'name' => [
                'label' => 'Tên loại tích hợp',
                'rules' => 'required|max_length[255]',
            ],
            'slug' => [
                'label' => 'Đường dẫn loại tích hợp',
                'rules' => "required|max_length[255]|is_unique[tb_autopay_other_type_intergation.slug,id,{$id}]",
                'errors' => [
                    'is_unique' => 'Đường dẫn loại tích hợp đã tồn tại.',
                ],
            ],
            'position' => [
                'label' => 'Vị trí loại tích hợp',
                'rules' => 'permit_empty|is_natural',
            ],
        ])) {
            return $this->response->setJSON([
                'status' => FALSE,
                'message' => implode(". ", $validation->getErrors()),
            ]);
        }

        $data = [
            'name' => $this->request->getVar('name'),
            'slug' => $this->request->getVar('slug'),
            'position' => $this->request->getVar('position') ?: 0,
        ];

        $otherTypeIntergationModel->update($id, $data);

        add_admin_log([
            'data_id' => $id,
            'data_type' => 'other_type_integration_update',
            'description' => 'Cập nhật loại tích hợp thành công',
            'admin_id' => $this->admin_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);

        return $this->response->setJSON(["status" => TRUE, "message" => 'Cập nhật thành công']);
    }

    public function index() 
    {
        if (!has_permission('OtherIntergation', 'can_view_all')) {
            show_404();
        }

        $otherTypeIntergationModel = model(OtherTypeIntergationModel::class);

        $data = [
            'page_title' => 'Danh sách tích hợp',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
            'other_types' => $otherTypeIntergationModel->orderBy('position', 'ASC')->findAll(),
        ];

        echo view('templates/sepay/header', $data);
        echo view('otherintergation/index', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajax_get_list_other ()
    {
        if(!has_permission('OtherIntergation', 'can_view_all'))
        show_404();
 
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $otherIntergationModel = model(OtherIntergationModel::class);

        $other = $otherIntergationModel->getDatatables();

        $data = array();
        $draw = $this->request->getVar('draw');

        foreach ($other as $o) {
            $row = array();

            $row[] = $o->id;
            $row[] = "<span class='text-info'>".$o->name_type."</span>";
            $row[] = '<img src="'.$o->path_image.'" alt="'.$o->name.'" height="35px">';
            $row[] = esc($o->name);
            $row[] = esc(mb_strimwidth($o->description, 0, 50, '...'));
            $row[] = $o->active == 1 ? "<span class='text-success'>Hiển thị</span>" : "<span class='text-danger'>Ẩn</span>";
            $row[] = esc($o->position);
            $row[] = "<button onclick='edit_other(". $o->id.")' class='btn btn-sm btn-warning'><i class='bi bi-pencil'>Cập nhật</button>";

            $data[] = $row;
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => $otherIntergationModel->countAll(),
            "recordsFiltered" => $otherIntergationModel->countFiltered(),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }

    public function ajax_other_add() 
    {
        if (!has_permission('OtherIntergation', 'can_add')) {
            show_404();
        }

        if ($this->request->getMethod(true) != 'POST') {
            return $this->response->setJSON(['status' => false, 'message' => 'Phương thức không hợp lệ']);
        }

        $validation = \Config\Services::validation();

        $rules = [
            'id_other_type_intergation' => ['label' => 'Loại tích hợp', 'rules' => 'required|integer|is_not_unique[tb_autopay_other_type_intergation.id]'],
            'path_image'                => ['label' => 'Đường dẫn ảnh', 'rules' => 'required|max_length[255]'],
            'name'                      => ['label' => 'Tên tích hợp', 'rules' => 'required|max_length[255]'],
            'description'               => ['label' => 'Mô tả', 'rules' => 'required'],
            'content'                   => ['label' => 'Nội dung', 'rules' => 'permit_empty'],
            'url_path'                  => ['label' => 'URL Path', 'rules' => 'permit_empty|max_length[255]'],
            'position'                  => ['label' => 'Vị trí', 'rules' => 'permit_empty|integer'],
            'active'                    => ['label' => 'Trạng thái', 'rules' => 'required|integer'],
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON(['status' => false, 'message' => implode(". ", $validation->getErrors())]);
        }

        $otherIntergationModel = model(OtherIntergationModel::class);

        $data = [
            'id_other_type_intergation' => $this->request->getVar('id_other_type_intergation'),
            'path_image'                => $this->request->getVar('path_image'),
            'name'                      => $this->request->getVar('name'),
            'description'               => $this->request->getVar('description'),
            'content'                   => $this->request->getVar('content'),
            'url_path'                  => $this->request->getVar('url_path'),
            'position'                  => $this->request->getVar('position') ?? 0,
            'active'                    => $this->request->getVar('active'),
        ];

        $result = $otherIntergationModel->insert($data);

        if ($result) {
            add_admin_log([
                'data_id'    => $result,
                'data_type'  => 'other_integration_add',
                'description'=> 'Thêm tích hợp thành công',
                'admin_id'   => $this->admin_details->id,
                'ip'         => $this->request->getIPAddress(),
                'user_agent' => $this->request->getUserAgent()->getAgentString(),
                'status'     => 'Success'
            ]);

            return $this->response->setJSON(['status' => true, 'message' => 'Thêm tích hợp thành công', 'data' => $result]);
        }

        return $this->response->setJSON(['status' => false, 'message' => 'Lỗi khi thêm tích hợp']);
    }

    public function ajax_get_other($id = '')
    {
        if (!has_permission('OtherIntergation', 'can_view_all')) {
            show_404();
        }

        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy loại tích hợp này"));

        $otherIntergationModel = model(OtherIntergationModel::class);

        $other = $otherIntergationModel->select(
            'tb_autopay_other_intergation.*, tb_autopay_other_type_intergation.name as name_type'
        )
        ->join('tb_autopay_other_type_intergation', 'tb_autopay_other_type_intergation.id = tb_autopay_other_intergation.id_other_type_intergation')
        ->where('tb_autopay_other_intergation.id', $id)
        ->first();

        if (!$other) {
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tích hợp']);
        }

        return $this->response->setJSON(['status' => true, 'data' => $other]);
    }


    public function ajax_other_update()
    {
        if (!has_permission('OtherIntergation', 'can_edit')) {
            show_404();
        }

        if ($this->request->getMethod(true) != 'POST') {
            return $this->response->setJSON(['status' => false, 'message' => 'Phương thức không hợp lệ']);
        }

        $validation = \Config\Services::validation();

        $rules = [
            'id'                        => ['label' => 'ID', 'rules' => 'required|integer|is_not_unique[tb_autopay_other_intergation.id]'],
            'id_other_type_intergation' => ['label' => 'Loại tích hợp', 'rules' => 'required|integer|is_not_unique[tb_autopay_other_type_intergation.id]'],
            'path_image'                => ['label' => 'Đường dẫn ảnh', 'rules' => 'required|max_length[255]'],
            'name'                      => ['label' => 'Tên tích hợp', 'rules' => 'required|max_length[255]'],
            'description'               => ['label' => 'Mô tả', 'rules' => 'required'],
            'content'                   => ['label' => 'Nội dung', 'rules' => 'permit_empty'],
            'url_path'                  => ['label' => 'URL Path', 'rules' => 'permit_empty|max_length[255]'],
            'position'                  => ['label' => 'Vị trí', 'rules' => 'permit_empty|integer'],
            'active'                    => ['label' => 'Trạng thái', 'rules' => 'required|integer'],
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON(['status' => false, 'message' => implode(". ", $validation->getErrors())]);
        }

        $otherIntergationModel = model(OtherIntergationModel::class);

        $id = $this->request->getVar('id');

        $data = [
            'id_other_type_intergation' => $this->request->getVar('id_other_type_intergation'),
            'path_image'                => $this->request->getVar('path_image'),
            'name'                      => $this->request->getVar('name'),
            'description'               => $this->request->getVar('description'),
            'content'                   => $this->request->getVar('content'),
            'url_path'                  => $this->request->getVar('url_path'),
            'position'                  => $this->request->getVar('position') ?? 0,
            'active'                    => $this->request->getVar('active'),
        ];

        $result = $otherIntergationModel->update($id, $data);

        if ($result) {
            add_admin_log([
                'data_id'    => $id,
                'data_type'  => 'other_integration_update',
                'description'=> 'Cập nhật tích hợp thành công',
                'admin_id'   => $this->admin_details->id,
                'ip'         => $this->request->getIPAddress(),
                'user_agent' => $this->request->getUserAgent()->getAgentString(),
                'status'     => 'Success'
            ]);

            return $this->response->setJSON(['status' => true, 'message' => 'Cập nhật tích hợp thành công']);
        }

        return $this->response->setJSON(['status' => false, 'message' => 'Lỗi khi cập nhật tích hợp']);
    }

}