<?php

namespace App\Controllers;
use App\Models\TransactionsModel;
use App\Models\StransactionsModel;
use App\Models\AdminModel;
use App\Models\UserModel;
use App\Models\CompanyModel;
use App\Models\CrmActivityModel;

use CodeIgniter\Controller;

class CrmActivity extends BaseController
{

    public function index() {
        $data = [
            'page_title' => 'CSKH',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('CrmActivity', 'can_view_all'))
            show_404();

        echo view('templates/sepay/header',$data);
        echo view('crmactivity/index',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function ajax_list() {

        if(!has_permission('CrmActivity', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $crmActivityModel = model(CrmActivityModel::class);
        $adminModel = model(AdminModel::class);
        $companyModel = model(CompanyModel::class);

        $activities = $crmActivityModel->getDatatables();
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($activities as $activity) {
            $admin_details = $adminModel->select('firstname, lastname, email')->where(['id' => $activity->admin_id])->get()->getRow();
            if($admin_details)
                $admin_full_name = esc($admin_details->lastname .  " " . $admin_details->firstname);
            else 
                $admin_full_name = "";

            $company_details = $companyModel->where(['id' => $activity->company_id])->get()->getRow();

            $no++;
            $row = array();
            
            $row[] = $no;
             
            
            $row[] = date("H:i:s d/m/Y", strtotime($activity->activity_at)) . "<br><em>" . timespan($activity->activity_at,1) . "</em>";

            if(is_object($company_details))
                $row[] = "<a href='".base_url('company/details/' . $activity->company_id)."'>" . esc($company_details->short_name) . "</a>";
            else
                $row[] = "";
            $row[] = nl2br(esc($activity->content));
            $row[] = esc($activity->type);
            $row[] = esc($admin_full_name);
           
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $crmActivityModel->countAll(),
            "recordsFiltered" => $crmActivityModel->countFiltered(),
            "data" => $data,
        );
        return $this->response->setJSON($output); 
    }


    public function ajax_add() {

        if(!has_permission('CrmActivity', 'can_add'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'content' => ['label' => 'Nội dung', "rules" => "required"],
            'activity_at' => ['label' => 'Thời gian diễn ra', "rules" => "required"],
            'type' => ['label' => 'Activity Type', "rules" => "required|in_list[Call,Chat,Note,Meet]"],
            'company_id' => ['label' => 'Company ID', "rules" => "required|integer"],

        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } else {

            $crmActivityModel = model(CrmActivityModel::class);
            
            $data = [
                'content' => $this->request->getVar('content'),
                'activity_at' => $this->request->getVar('activity_at'),
                'type' => $this->request->getVar('type'),
                'company_id' => $this->request->getVar('company_id'),
                'admin_id' => $this->admin_details->id,

            ];
 
            $result = $crmActivityModel->insert($data);
            
            add_admin_log(array('data_id'=> $result, 'data_type'=>'crm_activity_add','description'=>'Thêm Hoạt động','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);

                

        }
    }
    
    public function ajax_update() {

        if(!has_permission('CrmActivity', 'can_edit'))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không có quyền sửa"));

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'id' => ['label' => 'ID', "rules" => "required|is_natural"],
            'content' => ['label' => 'Nội dung', "rules" => "required"],
            'activity_at' => ['label' => 'Thời gian diễn ra', "rules" => "required"],
            'type' => ['label' => 'Activity Type', "rules" => "required|in_list[Call,Chat,Note,Meet]"],   
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $id = $this->request->getVar('id');
         
        $data_update = [
            'content' => $this->request->getVar('content'),
            'activity_at' => $this->request->getVar('activity_at'),
            'type' => $this->request->getVar('type'),
        ];

        $crmActivityModel = model(CrmActivityModel::class);

        $crmActivityModel->set($data_update)->where(['id' => $id])->update();
 
        add_admin_log(array('data_id'=> $id, 'data_type'=>'crm_activity_edit','description'=>'Sửa hoạt động','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));


        return $this->response->setJSON(["status"=>TRUE]);

    }
 
    public function ajax_get($id='') {
        
        if(!has_permission('CrmActivity', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa"));
 
        
        $crmActivityModel = model(CrmActivityModel::class);
        
        $result = $crmActivityModel->find($id);
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu"));
    }


    public function ajax_delete() {
        
        if(!has_permission('CrmActivity', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xóa"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $id = $this->request->getPost('id');

        $crmActivityModel = model(CrmActivityModel::class);

        $crmActivityModel->delete($id);

        add_admin_log(array('data_id'=> $id, 'data_type'=>'crm_activity_delete','description'=>'Xóa hoạt động','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    
        return $this->response->setJSON(array("status"=>true));
         
    
    }

    
 
 
}