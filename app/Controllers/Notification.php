<?php

namespace App\Controllers;

use CodeIgniter\Controller;

class Notification extends BaseController
{
    public function index()
    {
 
        $data = [
            'page_title' => 'Thông báo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        

        if(!has_permission('Notification', 'can_view_all'))
            show_404();
      
        
        echo view('templates/sepay/header',$data);
        echo view('notification/index',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function compose()
    {
 

        $data = [
            'page_title' => 'Thông báo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('Notification', 'can_add'))
            show_404();


        $bankModel = model(BankModel::class);
        $data['groups_tree'] = [ ["value" => "All_0", "name" => "Toàn bộ khách hàng"]];
        $banks = $bankModel->findAll();
        foreach($banks as $bank) {
            array_push($data['groups_tree'],['value' => 'Bank_' . $bank->id, "name" => "Ngân hàng " . $bank->brand_name]);
        }
        
        echo view('templates/sepay/header',$data);
        echo view('notification/compose',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function preview($id)
    {
 
        $data = [
            'page_title' => 'Thông báo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        if(!has_permission('Notification', 'can_add'))
            show_404();


        $notificationModel = model(NotificationModel::class);
        $notificationToModel = model(NotificationToModel::class);
        $userModel = model(UserModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankModel = model(BankModel::class);

        $data['noti_details'] = $notificationModel->find($id);

        if(!is_object($data['noti_details']))
            show_404();
        
        $results = $notificationToModel->where(['notification_id' => $id])->orderBy('id','ASC')->get()->getResult();

        $data['noti_to_text'] = [];

        foreach($results as $result) {
            if($result->data_type == "All") {
                $count_result = $userModel->select("count(id) as `numrows`")->where(['active' => 1])->get()->getRow();
                $data['noti_to_text'] = ["Toàn bộ khách hàng(" . $count_result->numrows . ")"];
            }  else if($result->data_type == "Bank") {
                $bank_details = $bankModel->where(['id' => $result->data_id])->get()->getRow();
                if(is_object($bank_details)){
                    $count_users_to = count(get_send_to($id));
                    
                    $data['noti_to_text'] = ["Ngân hàng " . $bank_details->brand_name . " (" . $count_users_to . ")"];
                }
            }
        }

        $data['noti_to_text'] = implode(", ",  $data['noti_to_text'] );

        
        echo view('templates/sepay/header',$data);
        echo view('notification/preview',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function edit($id)
    {
 

        $data = [
            'page_title' => 'Thông báo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        if(!has_permission('Notification', 'can_add'))
            show_404();


        $notificationModel = model(NotificationModel::class);
        $notificationToModel = model(NotificationToModel::class);
        $userModel = model(UserModel::class);

        $data['noti_details'] = $notificationModel->find($id);

        if(!is_object($data['noti_details']))
            show_404();

        if($data['noti_details']->step == 1) {
            set_alert('error',"Chiến dịch này đã được gửi. Bạn không thể sửa nội dung");
            return redirect()->to('notification/result/' . $id);

        }

        
        $bankModel = model(BankModel::class);
        $data['groups_tree'] = [ ["value" => "All_0", "name" => "Toàn bộ khách hàng"]];
        $banks = $bankModel->findAll();
        foreach($banks as $bank) {
            array_push($data['groups_tree'],['value' => 'Bank_' . $bank->id, "name" => "Ngân hàng " . $bank->brand_name]);
        }
        
        $results = $notificationToModel->where(['notification_id' => $id])->orderBy('id','ASC')->get()->getResult();

        $data['noti_to'] = [];
        

        foreach($results as $result) {
            array_push($data['noti_to'], $result->data_type . '_' . $result->data_id);
        }
         
        echo view('templates/sepay/header',$data);
        echo view('notification/edit',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function send($id)
    {

      
        $data = [
            'page_title' => 'Thông báo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        if(!has_permission('Notification', 'can_add'))
            show_404();


        $notificationModel = model(NotificationModel::class);
        $notificationToModel = model(NotificationToModel::class);
        $userModel = model(UserModel::class);
        $bankModel = model(BankModel::class);

        $data['noti_details'] = $notificationModel->find($id);

        if(!is_object($data['noti_details']))
            show_404();
        if($data['noti_details']->step == 1) {
            set_alert('error',"Chiến dịch này đã được gửi. Bạn không thể gửi lại");
            return redirect()->to('notification/result/' . $id);

        }
        $results = $notificationToModel->where(['notification_id' => $id])->orderBy('id','ASC')->get()->getResult();

        $data['noti_to_text'] = [];
        $data['count_to'] = 0;

        foreach($results as $result) {
            if($result->data_type == "All") {
                $count_result = $userModel->select("count(id) as `numrows`")->where(['active' => 1])->get()->getRow();
                $data['noti_to_text'] = ["Toàn bộ khách hàng(" . $count_result->numrows . ")"];
                $data['count_to'] = $count_result->numrows;
            } else if($result->data_type == "Bank") {
                $bank_details = $bankModel->where(['id' => $result->data_id])->get()->getRow();
                if(is_object($bank_details)){
                    $count_users_to = count(get_send_to($id));
                    
                    $data['noti_to_text'] = ["Ngân hàng " . $bank_details->brand_name . " (" . $count_users_to . ")"];
                    $data['count_to'] = $count_users_to;

                }

                
            }
        }

        $data['noti_to_text'] = implode(", ",  $data['noti_to_text'] );

        
        echo view('templates/sepay/header',$data);
        echo view('notification/send',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function result($id)
    {
 

        $data = [
            'page_title' => 'Thông báo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        if(!has_permission('Notification', 'can_add'))
            show_404();


        $notificationModel = model(NotificationModel::class);
        $notificationToModel = model(NotificationToModel::class);
        $userModel = model(UserModel::class);
        $emailQueueModel = model(EmailQueueModel::class);

        $data['noti_details'] = $notificationModel->find($id);

        if(!is_object($data['noti_details']))
            show_404();

        $finished = $this->request->getGet('finished');

        if($finished == "yes") {
            set_alert('success', "Khởi tạo thông báo thành công! Hệ thống sẽ xử lý dữ liệu và gửi mail trong vài phút");
            return redirect()->to('notification/result/' . $id);
        }

        
        $data['sents'] = $emailQueueModel->where(['data_type' => 'notification', 'data_id' => $id])->orderBy('id','ASC')->get()->getResult();

        $data['count_pending'] = $emailQueueModel->select("count(id) as `numrows`")->where(['data_type' => 'notification', 'data_id' => $id, 'status' => 'Pending'])->orderBy('id','ASC')->get()->getRow();
        $data['count_pending'] = $data['count_pending']->numrows;

        $data['count_sent'] = $emailQueueModel->select("count(id) as `numrows`")->where(['data_type' => 'notification', 'data_id' => $id, 'status' => 'Sent'])->orderBy('id','ASC')->get()->getRow();
        $data['count_sent'] = $data['count_sent']->numrows;

        $data['count_failed'] = $emailQueueModel->select("count(id) as `numrows`")->where(['data_type' => 'notification', 'data_id' => $id, 'status' => 'Failed'])->orderBy('id','ASC')->get()->getRow();
        $data['count_failed'] = $data['count_failed']->numrows;

        
        echo view('templates/sepay/header',$data);
        echo view('notification/result',$data);
        echo view('templates/sepay/footer',$data);
    }


     

    public function ajax_list() {

     
      
        if(!has_permission('Notification', 'can_view_all'))
            show_404();

        $notificationModel = model(NotificationModel::class);
        $emailQueueModel = model(EmailQueueModel::class);

        $rows = $notificationModel->getDatatablesForAdmin();

        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
 
        foreach ($rows as $result) {

            $no++;
            $row = array();

            $count_email_queue = $emailQueueModel->select("count(id) as `numrows`")->where(['data_id' => $result->id, 'data_type' => 'notification'])->get()->getRow();

            $count_email_queue_sent = $emailQueueModel->select("count(id) as `numrows`")->where(['data_id' => $result->id, 'data_type' => 'notification','status' => 'Sent'])->get()->getRow();

            $count_email_queue = $count_email_queue->numrows;
            $count_email_queue_sent = $count_email_queue_sent->numrows;

  
            $row[] = $no;
            $row[] = esc($result->id);

            if ($result->channels) {
                $row[] = "<a href='#' class='d-flex align-items-center link-secondary' style='text-decoration: none;'><span class='badge bg-primary me-1'>Beta</span> ". esc($result->title) . "</a>";
            } else {
                if($result->step == 0)
                    $row[] = "<a href='". base_url('notification/preview/'. $result->id)."'>". esc($result->title) . "</a>";
                else
                    $row[] = "<a href='". base_url('notification/result/'. $result->id)."'>". esc($result->title) . "</a>";
            }

            
            $row[] = number_format($count_email_queue_sent) . '/' . number_format($count_email_queue);
            $row[] = esc($result->created_at);

 

            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $notificationModel->countAllForAdmin(),
            "recordsFiltered" => $notificationModel->countFilteredForAdmin(),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }


    public function ajax_add() {

        

        if(!has_permission('Notification', 'can_add'))
            show_404();

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'title' => ['label' => 'Tiêu đề', 'rules' => 'required'],
            'body' => ['label' => 'Nội dung', 'rules' => 'required'],
            'send_to' => ['label' => 'Gửi đến', 'rules'=> 'required']
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } else {

            $notificationModel = model(NotificationModel::class);
            $notificationToModel = model(NotificationToModel::class);

            $data = array(
                'title' => $this->request->getVar('title'),
                'body' => $this->request->getVar('body'),
            );

            $send_tos = $this->request->getVar('send_to');

            $send_tos = json_decode($send_tos);

            if(!is_array($send_tos) || count($send_tos) == 0)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Vui lòng chọn Gửi đến"));

            $result_send_tos = [];
            foreach($send_tos as $value) {
                if(strpos($value,"_") != FALSE) {
                    $tmp1 = explode("_", $value);
                    if(in_array($tmp1[0], ['All', 'Group', 'Bank']) && is_numeric($tmp1[1])) {
                        
                        array_push($result_send_tos, ['data_type' => $tmp1[0],'data_id' => $tmp1[1]]);
                        if($tmp1[0] == "All")
                            break;
                    }
                }
            }

            $result = $notificationModel->insert($data);

            foreach($result_send_tos as $value) {
                $notificationToModel->insert(['notification_id' => $result,'data_type' => $value['data_type'], 'data_id' => $value['data_id']]);
            }

            
            add_admin_log(array('data_id'=> $result, 'data_type'=>'notification_add','description'=>'Tạo thông báo','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            

            return $this->response->setJSON(["status"=>TRUE, "id"=>$result]);

                

        }
    }


    public function ajax_edit() {
 

        if(!has_permission('Notification', 'can_edit'))
            show_404();

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => ['label' => 'ID', 'rules' => 'required|integer'],
            'title' => ['label' => 'Tiêu đề', 'rules' => 'required'],
            'body' => ['label' => 'Nội dung', 'rules' => 'required'],
            'send_to' => ['label' => 'Gửi đến', 'rules'=> 'required']
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } else {

            $notificationModel = model(NotificationModel::class);
            $notificationToModel = model(NotificationToModel::class);

            $data = array(
                'title' => $this->request->getVar('title'),
                'body' => $this->request->getVar('body'),
            );

            $id = $this->request->getVar('id');

            $notification_details = $notificationModel->where(['id' => $id])->get()->getRow();
            if(!is_object($notification_details))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy thông báo này"));

            $send_tos = $this->request->getVar('send_to');

            $send_tos = json_decode($send_tos);

            if(!is_array($send_tos) || count($send_tos) == 0)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Vui lòng chọn Gửi đến"));


            $notificationToModel->where(['notification_id' => $id])->delete();

            $result_send_tos = [];
            foreach($send_tos as $value) {
                if(strpos($value,"_") != FALSE) {
                    $tmp1 = explode("_", $value);
                    if(in_array($tmp1[0], ['All', 'Group', 'Bank']) && is_numeric($tmp1[1])) {
                        
                        array_push($result_send_tos, ['data_type' => $tmp1[0],'data_id' => $tmp1[1]]);
                        if($tmp1[0] == "All")
                            break;
                    }
                }
            }

            foreach($result_send_tos as $value) {
                $notificationToModel->insert(['notification_id' => $id,'data_type' => $value['data_type'], 'data_id' => $value['data_id']]);
            }

            $notificationModel->set($data)->where(['id' => $id])->update();

            add_admin_log(array('data_id'=> $id, 'data_type'=>'notification_edit','description'=>'Sửa thông báo','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            

            return $this->response->setJSON(["status"=>TRUE, "id"=>$id]);

                

        }
    }


    public function ajax_send() {

       
        if(!has_permission('Notification', 'can_add'))
            show_404();

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => ['label' => 'ID thông báo', 'rules' => 'required|integer'],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } else {

            $notificationModel = model(NotificationModel::class);
            $emailQueueModel = model(EmailQueueModel::class);
            $userModel = model(UserModel::class);
            $unsubscribeModel = model(UnsubscribeModel::class);

            $id = $this->request->getVar('id');
            $test = $this->request->getVar('test');;

            $notification_details = $notificationModel->where(['id' => $id])->get()->getRow();

            if(!is_object($notification_details))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy thông báo này"));

            if($notification_details->step == 1) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Chiến dịch này đã được gửi. Vui lòng kiểm tra lại!"));

            }


            $users_to = [];

            if(is_numeric($test) && $test == 1) {
                $user_test = $userModel->where(['email' => '<EMAIL>'])->orderBy('id','ASC')->get()->getRow();
                $users_to = [$user_test->id];
            }
            else {
                 
                $users_to = get_send_to($id);

            }

            
            $title = $notification_details->title;

            $html_body = $notification_details->body;


            foreach($users_to as $user_id) {
                
                $user_details = $userModel->where(['id' => $user_id])->get()->getRow();

                $check = $unsubscribeModel->where(['email' => $user_details->email])->get()->getRow();

                $status ="Pending";

                if(!is_object($check)) {
                    
                    $emailQueueModel->insert([
                        'user_id' => $user_id,
                        'data_id' => $id,
                        'uuid' => random_string('alnum', 16),
                        'data_type' => 'notification',
                        'mail_to' => $user_details->email,
                        'title' => $title,
                        'body' => $html_body,
                        'status' =>  $status,
                    ]);
                }
               

            }


            if(!is_numeric($test) || $test != 1)
                $notificationModel->set(['step' => 1])->where(['id' => $id])->update();

                    
            add_admin_log(array('data_id'=> $id, 'data_type'=>'notification_send','description'=>'Gửi thông báo','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(["status"=>TRUE]);

                

        }
    }

    
}