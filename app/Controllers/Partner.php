<?php

namespace App\Controllers;
use App\Models\LgTokenModel;
use App\Models\PartnerModel;
use App\Models\PartnerCompanyModel;
use App\Models\PartnerCommissionModel;

use CodeIgniter\Controller;

class Partner extends BaseController
{
    public function index() {
        $data = [
            'page_title' => 'Đ<PERSON>i tác',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('Partner', 'can_view_all'))
            show_404();

        $partnerModel = model(PartnerModel::class);

        $result = $partnerModel->select("sum(total_commission) as `sum_total_commission`")->get()->getRow();

        $data['total_commission'] = $result->sum_total_commission;
        
        $result = $partnerModel->select("sum(withdrawn_commission) as `sum_withdrawn_commission`")->get()->getRow();

        $data['total_withdraw'] =  $result->sum_withdrawn_commission;

        $data['pending_withdraw_commission'] = $data['total_commission'] - $data['total_withdraw'];

        echo view('templates/sepay/header',$data);
        echo view('partner/index',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function ajax_list() {

        if(!has_permission('Partner', 'can_view_all'))
            show_404();
             
        $partnerModel = model(PartnerModel::class);
        $partnerCompanyModel = model(PartnerCompanyModel::class);
        $partnerCommissionModel = model(PartnerCommissionModel::class);

        
        $partners = $partnerModel->getDatatables();
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        $login_as_partner = has_permission('LoginAsPartner', 'can_view_all');
 
        foreach ($partners as $partner) {

            $result = $partnerCompanyModel->select("count(id) as `numrows`")->where(['partner_id' => $partner->id])->get()->getRow();
            $count_company = $result->numrows;

            $balance = $partner->total_commission  - $partner->withdrawn_commission;

            $no++;
            $row = array();

            $row[] = $no;
            $row[] =  esc($partner->id);
            $row[] = esc($partner->lastname . ' ' . $partner->firstname);
            $row[] = esc($partner->email);
            $row[] = esc($partner->phonenumber);

            $row[] = $count_company;
            $row[] = number_format($partner->total_commission) . 'đ';
            $row[] = number_format($partner->withdrawn_commission) . 'đ';
            $row[] = number_format($balance) . 'đ';

            if($login_as_partner)
                $row[] = "<a target='_blank' href='".base_url('partner/login_as_partner/' . $partner->id)."' class='btn btn-sm btn-outline-primary'>Login</a>";
 
            $row[] = esc($partner->created_at);

            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $partnerModel->countAll(),
            "recordsFiltered" => $partnerModel->countFiltered(),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }

    public function login_as_partner($partner_id='') {
        if(!has_permission('LoginAsPartner', 'can_edit'))
            show_404();

        $lgToken = model(LgTokenModel::class);

        $token = random_string('alnum',36);
        
        $lgToken->insert(['partner_id' => $partner_id,'admin_id' => $this->admin_details->id, 'token' => $token]);


        return redirect()->to("https://doitac.sepay.vn/login/token/" . $token);
    } 

    public function withdraw() {
        $data = [
            'page_title' => 'Y/C Rút tiền',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('Partner', 'can_view_all') || !has_permission('LoginAsPartner', 'can_view_all'))
            show_404();


        echo view('templates/sepay/header',$data);
        echo view('partner/withdraw',$data);
        echo view('templates/sepay/footer',$data);
    }


    public function withdraw_ajax_list() {

        if(!has_permission('Partner', 'can_view_all') || !has_permission('LoginAsPartner', 'can_view_all'))
            show_404();

             
        $login_as_partner = has_permission('LoginAsPartner', 'can_view_all');

        $partnerModel = model(PartnerModel::class);
        $partnerCompanyModel = model(PartnerCompanyModel::class);
        $partnerWithdrawModel = model(PartnerWithdrawModel::class);

        
        $withdraws = $partnerWithdrawModel->getDatatables();
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

  
        foreach ($withdraws as $withdraw) {

            $partner = $partnerModel->select('id, firstname, lastname, total_commission, withdrawn_commission')->where(['id' => $withdraw->partner_id])->get()->getRow();

            $no++;
            $row = array();

            $row[] = $no;
            $row[] = esc($withdraw->id);
            $row[] = "<a href='".base_url('partner/withdraw_details/' . $withdraw->id)."'>".esc($partner->lastname . ' ' . $partner->firstname) . "</a>";
            $row[] = "<a href='".base_url('partner/withdraw_details/' . $withdraw->id)."'>".number_format($withdraw->amount) . "đ</a>";
            if($withdraw->status == "Completed")
                $row[] = "<span class='text-success'>Hoàn tất</span>";
            else if($withdraw->status == "Pending")
                $row[] = "<span class='text-danger'>Chờ xử lý</span>";
            else if($withdraw->status == "InProgress")
                $row[] = "<span class='text-warning'>Đang xử lý</span>";
            else if($withdraw->status == "Rejected")
                $row[] = "<span class='text-secondary'>Bị từ chối</span>";
            else if($withdraw->status == "Closed")
                $row[] = "<span class='text-secondary'>Đã đóng</span>";
            else
                $row[] = esc($withdraw->status);

            if($login_as_partner)
                $row[] = "<a target='_blank' href='".base_url('partner/login_as_partner/' . $withdraw->partner_id)."' class='btn btn-sm btn-outline-primary'>Login</a>";
 
            $row[] = esc($withdraw->created_at);

            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $partnerWithdrawModel->countAll(),
            "recordsFiltered" => $partnerWithdrawModel->countFiltered(),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }
 

    public function withdraw_details($id) {
        $data = [
            'page_title' => 'Rút tiền',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('Partner', 'can_view_all'))
            show_404();

        $partnerWithdrawModel = model(PartnerWithdrawModel::class);
        $partnerModel = model(PartnerModel::class);

        $data['withdraw_details'] = $partnerWithdrawModel->find($id);
        $data['partner_details'] = $partnerModel->find($data['withdraw_details']->partner_id);

        if(!is_object($data['partner_details']))
            show_404();
        echo view('templates/sepay/header',$data);
        echo view('partner/withdraw_details',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function ajax_get_withdraw($id) {
            
        $partnerWithdrawModel = model(PartnerWithdrawModel::class);
        
        $result = $partnerWithdrawModel->find($id);
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu"));
    }

    public function ajax_withdraw_update() {

        if(!has_permission('Partner', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
            'amount' => ['label' => 'Số tiền', 'rules' => "required|integer|is_natural"],
            'body' => ['label' => 'Tài khoản nhận tiền', 'rules' => "required"],
            'status' => ['label' => 'Trạng thái xử lý', 'rules' => "required|in_list[Pending,InProgress,Completed,Closed]"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }  
        $partnerWithdrawModel = model(PartnerWithdrawModel::class);
        $id = $this->request->getPost('id');

        $result = $partnerWithdrawModel->where(['id'=>$id])->get()->getRow();
        
        if(!is_object($result))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu"));

        $data = array(
            'amount' => trim($this->request->getVar('amount')),
            'body' => $this->request->getVar('body'),
            'status' => $this->request->getVar('status'),
            'admin_answer' => $this->request->getVar('admin_answer'),
           
        );

 
           
        $result = $partnerWithdrawModel->set($data)->where("id",$id)->update();
        
        add_admin_log(array('data_id'=> $id, 'data_type'=>'withdraw_edit','description'=>'Sửa Yêu cầu rút tiền','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);

    }

    public function ajax_make_withdraw_complete() {

        if(!has_permission('Partner', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }  
        $partnerWithdrawModel = model(PartnerWithdrawModel::class);
        $partnerModel = model(PartnerModel::class);

        $id = $this->request->getPost('id');

        $result = $partnerWithdrawModel->where(['id'=>$id])->get()->getRow();
        
        if(!is_object($result))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu"));

        $data = array(
             
            'status' => "Completed",
            
        );

 
           
        $partnerWithdrawModel->set($data)->where("id",$id)->update();
       // $partnerModel->set("withdrawn_commission","withdrawn_commission+" . intval($result->amount),false)->where(['id' => $result->partner_id])->update();

        $withdraw = $partnerWithdrawModel->select("sum(amount) as `sum_withdraw`")->where(['status' => 'Completed','partner_id' => $result->partner_id])->get()->getRow();
 
    
        $partnerModel->set(['withdrawn_commission' => $withdraw->sum_withdraw])->where(['id' => $result->partner_id])->update();
 
    
        
        add_admin_log(array('data_id'=> $id, 'data_type'=>'withdraw_complete','description'=>'Đánh dấu hoàn tất rút tiền','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        return $this->response->setJSON(["status"=>TRUE]);

    }
 
}