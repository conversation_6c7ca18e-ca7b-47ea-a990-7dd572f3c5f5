<?php

namespace App\Controllers;
use App\Models\TransactionsModel;
use App\Models\StransactionsModel;
use App\Models\BankAccountModel;
use App\Models\AdminModel;
use App\Models\UserModel;
use App\Models\CompanyModel;
use App\Models\CompanyUserModel;
use App\Models\StransactionModel;
use App\Models\CounterModel;
use App\Models\CrmActivityModel;
use App\Models\MerchantDetailsModel;

use CodeIgniter\Controller;

class Merchant extends BaseController
{
    public function index() {
        $data = [
            'page_title' => 'Merchant',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('Merchant', 'can_view_all'))
            show_404();

        echo view('templates/sepay/header',$data);
        echo view('merchant/index',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function ajax_list() {

        if(!has_permission('Merchant', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $companyModel = model(CompanyModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $stransactionModel = model(StransactionModel::class);

        $bankAccountModel = model(bankAccountModel::class);
   
        $counterModel = model(CounterModel::class);
        $merchantDetailsModel = model(MerchantDetailsModel::class);
        $invoiceModel = model(InvoiceModel::class);

        $merchants = $merchantDetailsModel->getDatatables();
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
 
        foreach ($merchants as $merchant) {

            $no++;
            $row = array();
 
            $result = $counterModel->select("sum(tb_autopay_counter.transaction) as `count_rows`")->where(['tb_autopay_counter.merchant_id' => $merchant->merchant_id])->get()->getRow();
            if($result)
                $count_transactions = $result->count_rows;
            else
                $count_transactions = 0;


            $result = $companyModel->select("count(id) as `count_rows`")->where(['merchant_id' => $merchant->merchant_id])->get()->getRow();
            if($result)
                $count_companies = $result->count_rows;
            else
                $count_companies = 0;

            $result = $bankAccountModel->select("count(id) as `count_rows`")->where(['merchant_id' => $merchant->merchant_id])->get()->getRow();
            if($result)
                $count_bank_accounts = $result->count_rows;
            else
                $count_bank_accounts = 0;

            $result = $invoiceModel->select("sum(subtotal) as `sum_rows`")->where(['merchant_id' => $merchant->merchant_id])->get()->getRow();
            if($result)
                $sum_subtotal = $result->sum_rows;
            else
                $sum_subtotal = 0;

            $row[] = $no;
            $row[] =  esc($merchant->merchant_id);
          
            $row[] = "<a href='" . base_url('merchant/details/' . $merchant->merchant_id) . "'>" . esc($merchant->brand_name) . "</a>";

            $row[] = "<a href='" . base_url('merchant/details/' . $merchant->merchant_id) . "'>" . esc($merchant->company_name) . "</a>" ;


            if($merchant->type == 'Production')
                $row[] = "<span class='text-success'>Production</span>";
            else
                $row[] = esc($merchant->type);

            if($merchant->credit > 0)
                $row[] = "<span class='text-success'>" . number_format($merchant->credit) . ' đ </span>';
            else
                $row[] = '';
            $row[] = number_format($count_companies);
            $row[] = number_format($count_bank_accounts);
            $row[] = number_format($count_transactions);
            if($sum_subtotal > 0)
                $row[] = "<span class='text-success'>" . number_format($sum_subtotal) . ' đ </span>';
            else
                $row[] = '';

            $row[] = esc($merchant->created_at);

            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $merchantDetailsModel->countAll(),
            "recordsFiltered" => $merchantDetailsModel->countFiltered(),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }

    


    public function ajax_merchant_add() {

        if(!has_permission('Merchant', 'can_view_all'))
            show_404();

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'company_name' => ['label' => 'Tên công ty', 'rules' => "required|max_length[500]"],
            'brand_name' => ['label' => 'Tên thương hiệu', 'rules' => "required|max_length[100]"],
            'phonenumber' => ['label' => 'Số điện thoại', 'rules' => 'permit_empty|integer|min_length[9]|max_length[12]|is_natural'],
            'email' => ['label' => 'Email', 'rules' => "permit_empty|valid_email"],
            'note' => ['label' => 'Ghi chú', 'rules' => "max_length[500]"],
            'type' => ['label' => 'Loại Merchant', 'rules' => "required|in_list[Production,Development,Staging]"],
            'trans_type' => ['label' => 'Đồng bộ giao dịch', 'rules' => "required|in_list[D,C,DC]"]
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } else {

            $merchantModel = model(MerchantModel::class);
            $merchantDetailsModel = model(MerchantDetailsModel::class);

            $result = $merchantModel->where(['name' => $this->request->getVar('brand_name') . ' ' . $this->request->getVar('type')])->get()->getRow();

            if(is_object($result))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Thương hiệu này đã tồn tại trên hệ thống, vui lòng điền tên khác"));


            $data_merchant = array(
                'name' => $this->request->getVar('brand_name') . ' ' . $this->request->getVar('type'),
                'trans_type' => $this->request->getVar('trans_type'),
                'client_id' => random_string('alnum', 42),
                'client_secret' => random_string('alnum', 32),
                'notify_api_key' => random_string('alnum', 32),
            );
 
            $merchant_id = $merchantModel->insert($data_merchant);

            if(is_numeric($merchant_id) && $merchant_id > 0) {

                $data_merchant_details = array(
                    'merchant_id' => $merchant_id,
                    'company_name' => $this->request->getVar('company_name'),
                    'type' => $this->request->getVar('type'),
                    'brand_name' => $this->request->getVar('brand_name'),
                    'email' => $this->request->getVar('email'),
                    'phonenumber' => $this->request->getVar('phonenumber'),
                    'note' => $this->request->getVar('note'),
                );

                $merchantDetailsModel->insert($data_merchant_details);

                add_admin_log(array('data_id'=> $merchant_id, 'data_type'=>'merchant_add','description'=>'Thêm Merchant','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            
                return $this->response->setJSON(["status"=>TRUE, "data"=>$merchant_id]);

            } else 
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Lỗi! Không thể thêm merchant"));

        }
    }

    public function details($id='') {
        if(!is_numeric($id))
            show_404();

        $data = [
            'page_title' => 'Merchant',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        
        if(!has_permission('Merchant', 'can_view_all'))
            show_404();
        
        $companyModel = model(CompanyModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $stransactionModel = model(StransactionModel::class);
        $invoiceModel = model(InvoiceModel::class);

        $bankAccountModel = model(bankAccountModel::class);
        $counterModel = model(CounterModel::class);
        $merchantDetailsModel = model(MerchantDetailsModel::class);

        $data['merchant_details'] = $merchantDetailsModel->find($id);

        if(!is_object($data['merchant_details']))
            show_404();

       

        $data['paid_invoice_count'] = $invoiceModel->where(['merchant_id' => $id, 'status' => 'Paid'])->countAllResults();
        $data['unpaid_invoice_count'] = $invoiceModel->where(['merchant_id' => $id, 'status' => 'Unpaid'])->countAllResults();

        $data['stransaction_count'] = $stransactionModel->where(['merchant_id' => $id])->countAllResults();

        
        $data['paid_invoice_sum'] = $invoiceModel->select("sum(total) as `total_amount`")->where(['merchant_id' => $id, 'status' => 'Paid'])->get()->getRow();

        $data['unpaid_invoice_sum'] = $invoiceModel->select("sum(total) as `total_amount`")->where(['merchant_id' => $id, 'status' => 'Unpaid'])->get()->getRow();

        $data['stransaction_sum'] = $stransactionModel->select("sum(`in`) as `total_in`")->where(['merchant_id' => $id])->get()->getRow();

        $data['bank_account_count'] = $bankAccountModel->where(['merchant_id' => $id])->countAllResults();
         
        $result = $counterModel->select("sum(transaction) as transaction_count")->where(['merchant_id'=> $id])->get()->getRow();

        if(is_object($result))
            $data['transaction_count'] = $result->transaction_count;
        else 
            $data['transaction_count'] = 0;
        
        $result = $bankAccountModel->select("last_transaction")->where(['merchant_id' => $id])->orderBy("last_transaction","DESC")->get()->getRow();

        if(is_object($result) && $result->last_transaction)
            $data['last_transaction'] = $result;
        else
            $data['last_transaction'] = FALSE;

 

        echo view('templates/sepay/header',$data);
        echo view('merchant/details',$data);
        echo view('templates/sepay/footer',$data);

    }


    public function invoice($id='') {
        if(!is_numeric($id))
            show_404();
        
        if(!has_permission('Invoice', 'can_view_all'))
            show_404();

        $data = [
            'page_title' => 'Hoá đơn',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        
        $merchantDetailsModel = model(MerchantDetailsModel::class);
    
        $invoiceModel = model(InvoiceModel::class);

      
        $data['merchant_details'] = $merchantDetailsModel->find($id);

        if(!is_object($data['merchant_details']))
            show_404();
  
        $data['unpaid_invoice_count'] = $invoiceModel->where(['merchant_id' => $id, 'status' => 'Unpaid'])->countAllResults();
        
        $data['paid_invoice_count'] = $invoiceModel->where(['merchant_id' => $id, 'status' => 'Paid'])->countAllResults();

        $data['paid_invoice_sum'] = $invoiceModel->select("sum(total) as `total_amount`")->where(['merchant_id' => $id, 'status' => 'Paid'])->get()->getRow();

        $data['unpaid_invoice_sum'] = $invoiceModel->select("sum(total) as `total_amount`")->where(['merchant_id' => $id, 'status' => 'Unpaid'])->get()->getRow();
        

        $data['cancelled_invoice_count'] = $invoiceModel->where(['merchant_id' => $id, 'status' => 'Cancelled'])->countAllResults();

        $data['cancelled_invoice_sum'] = $invoiceModel->select("sum(total) as `total_amount`")->where(['merchant_id' => $id, 'status' => 'Cancelled'])->get()->getRow();

        
        echo view('templates/sepay/header',$data);
        echo view('merchant/invoice',$data);
        echo view('templates/sepay/footer',$data);

    }

    public function invoice_ajax_list() {

        if(!has_permission('Invoice', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $invoiceModel = model(InvoiceModel::class);

        $merchant_id = $this->request->getGet('merchant_id');
        
        if(!is_numeric($merchant_id) || $merchant_id == 0) {
            show_404();
        }

        $invoices = $invoiceModel->getDatatables();
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($invoices as $invoice) {

         
            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = "<a href='" . base_url('invoice/edit/' . $invoice->id) . "'>#". esc($invoice->id) . "</a>";

            if($invoice->type == "NewOrder")
                $row[] = "<span class='text-info'>Đăng ký mới</span>";  
            else if($invoice->type == "Recurring")
                $row[] = "<span class='text-warning'>Gia hạn</span>";  
            else if($invoice->type == "Credit")
                $row[] = "<span class=''>Thêm tiền</span>";  
            else if($invoice->type == "Excess")
                $row[] = "<span class='text-danger'>Vượt hạn mức</span>";  
            else if($invoice->type == "SubscriptionChange")
                $row[] = "<span class='text-success'>Đổi gói dịch vụ</span>";
            else
                $row[] = esc($invoice->type);
                
            if($invoice->status == "Unpaid")
                $row[] = "<span class='badge rounded-pill bg-danger'>Chưa thanh toán</span>";  
            else if($invoice->status == "Paid")
                $row[] = "<span class='badge rounded-pill bg-success'>Đã thanh toán</span>";  
            else if($invoice->status == "Cancelled")
                $row[] = "<span class='badge rounded-pill bg-secondary'>Đã hủy</span>";  
            else if($invoice->status == "Refunded")
                $row[] = "<span class='badge rounded-pill bg-warning'>Đã hoàn tiền</span>";  
            else
                $row[] = esc($invoice->status);  

            $row[] = number_format($invoice->total) . " đ";

            if($invoice->tax_issued == 1)
                $row[] = "<span class='badge rounded-pill bg-success'>Đã xuất</span><br><span class='text-info'>" . esc($invoice->tax_issued_id) . "</span>";  
            else
                $row[] = "<span class='badge rounded-pill bg-danger'>Chưa xuất</span>";  
            
            
            $row[] = esc($invoice->date);
            $row[] = "<a href='" . base_url('invoice/edit/' . $invoice->id) . "'>Xem <i class='bi bi-chevron-right'></i></a>";

           
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $invoiceModel->countAll($id),
            "recordsFiltered" => $invoiceModel->countFiltered($id),
            "data" => $data,
        );
        return $this->response->setJSON($output); 
    }

    public function bankaccount($id='') {
        if(!is_numeric($id))
            show_404();
        
        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();

        $data = [
            'page_title' => 'Bank Account',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        
        $merchantDetailsModel = model(MerchantDetailsModel::class);
    
        $bankAccountModel = model(BankAccountModel::class);
      
        $data['merchant_details'] = $merchantDetailsModel->find($id);

        if(!is_object($data['merchant_details']))
            show_404();
  
    
        echo view('templates/sepay/header',$data);
        echo view('merchant/bankaccount',$data);
        echo view('templates/sepay/footer',$data);

    }

    public function bankaccount_ajax_list() {

        if(!in_array($this->admin_details->role,['Admin','SuperAdmin']))
            show_404();
       
        if ($this->request->getMethod(true) != 'POST')
            return '';


        $merchant_id = $this->request->getGet('merchant_id');
    
        if(!is_numeric($merchant_id) || $merchant_id <= 0) {
            show_404();
        }
 
        $bankAccountModel = model(BankAccountModel::class);
        $companyModel = model(CompanyModel::class);
        $bankModel = model(BankModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $merchantDetailsModel = model(MerchantDetailsModel::class);

        $bank_accounts = $bankAccountModel->getDatatables();
        
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($bank_accounts as $bank_account) {

            $company_details = $companyModel->find($bank_account->company_id);
            $bank_details = $bankModel->find($bank_account->bank_id);
            $result = $transactionsModel->select("count(id) as `num_rows`")->where(['bank_account_id' => $bank_account->id])->get()->getRow();
            $count_trans = $result->num_rows;

            if($bank_account->merchant_id > 0) {
                $merchant_details = $merchantDetailsModel->find($bank_account->merchant_id);
                if(is_object($merchant_details))
                    $merchant_info = esc($merchant_details->brand_name);
                else
                    $merchant_info = "";
            } else {
                $merchant_info = "";
            }

            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = esc($bank_account->id);

            if(is_object($company_details))
                $row[] = "<a href='" . base_url("company/details/" . $company_details->id) . "'>#" . $company_details->id . " " . esc($company_details->short_name) . "</a>";
            else
                $row[] = "";

            $row[] = $merchant_info;


            if(is_object($bank_details))
                $bank_icon = "<img class='img-fluid me-2' style='width:25px' src='https://my.sepay.vn/assets/images/banklogo/" . esc($bank_details->icon_path) . "'>";
            else
                $bank_icon = "";


            if(in_array($this->admin_details->role,['Admin','SuperAdmin'])) {
                $row[] = $bank_icon . esc($bank_account->account_number);
                $row[] = esc($bank_account->account_holder_name);
            } else {
                $row[] = $bank_icon . "********";
                $row[] = "********";
            }


            if($bank_account->bank_api == 1) {
                $connect_type =  "<span class='text-warning me-2'><i class='bi bi-lightning-charge'></i> API</span>"; 
                if($bank_account->bank_api_connected == 1)
                    $connect_result = "<span><i class='bi bi-lightbulb-fill text-warning'></i></span>";
                else
                    $connect_result = "<span><i class='bi bi-lightbulb-fill text-muted'></i></span>";

            } else {
                $connect_type =  "<span class='text-info me-2'><i class='bi bi-chat-left-text-fill'></i> SMS</span>"; 

                if($bank_account->bank_sms_connected == 1)
                    $connect_result = "<span><i class='bi bi-lightbulb-fill text-warning'></i></span>";
                else
                    $connect_result = "<span><i class='bi bi-lightbulb-fill text-muted'></i></span>";

            }



            $row[] = $connect_type . $connect_result;
        
            if($count_trans > 0)
                $row[] = "<span class='text-success'>" . number_format($count_trans) . "</span>";

            else
                $row[] = number_format($count_trans);
            
            if($bank_account->last_transaction)
                $row[] = timespan($bank_account->last_transaction,1);
            else
                $row[] = "";
            $row[] = esc($bank_account->created_at) . "<br><em>" . timespan($bank_account->created_at,1). "</em>";
           
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $bankAccountModel->countAll($company_id),
            "recordsFiltered" => $bankAccountModel->countFiltered($company_id),
            "data" => $data,
        );
        return $this->response->setJSON($output); 
    }

    public function stransaction($id='') {
        if(!is_numeric($id))
            show_404();
        
        if(!has_permission('Stransaction', 'can_view_all'))
            show_404();

        $data = [
            'page_title' => 'Giao dịch',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        
        $merchantDetailsModel = model(MerchantDetailsModel::class);
    
        $stransactionModel = model(StransactionModel::class);
 
        $data['merchant_details'] = $merchantDetailsModel->find($id);

        if(!is_object($data['merchant_details']))
            show_404();
  
        $data['in_stransaction_count'] = $stransactionModel->where(['merchant_id' => $data['merchant_details']->merchant_id, 'in>' => 0])->countAllResults();
    
        $data['out_stransaction_count'] = $stransactionModel->where(['merchant_id' => $data['merchant_details']->merchant_id, 'out>' => 0])->countAllResults();

        $data['in_stransaction_sum'] = $stransactionModel->select("sum(`in`) as `total_amount`")->where(['merchant_id' => $data['merchant_details']->merchant_id])->get()->getRow();

        $data['out_stransaction_sum'] = $stransactionModel->select("sum(`out`) as `total_amount`")->where(['merchant_id' => $data['merchant_details']->merchant_id])->get()->getRow();
         
        echo view('templates/sepay/header',$data);
        echo view('merchant/stransaction',$data);
        echo view('templates/sepay/footer',$data);

    }

    public function stransaction_ajax_list() {

        if(!has_permission('Stransaction', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $stransactionModel = model(StransactionModel::class);

        $merchant_id = $this->request->getGet('merchant_id');
    
        if(!is_numeric($merchant_id) || $merchant_id <= 0) {
            show_404();
        }

        $stransactions = $stransactionModel->getDatatables();
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($stransactions as $stransaction) {

         
            $no++;
            $row = array();
            
            $row[] = $no;

            $row[] = esc($stransaction->id);

            $row[] = esc($stransaction->date);


            $row[] = number_format($stransaction->in) . " đ";
            $row[] = number_format($stransaction->out) . " đ";

            $row[] = "<a href='" . base_url('invoice/edit/' . $stransaction->invoice_id) . "'>" . $stransaction->invoice_id . "</a>";

           
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $stransactionModel->countAll(),
            "recordsFiltered" => $stransactionModel->countFiltered(),
            "data" => $data,
        );
        return $this->response->setJSON($output); 
    }
 
    public function ajax_merchant_update() {
        if(!has_permission('Merchant', 'can_edit'))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không có quyền thực hiện thao tác này"));
 
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'id' => ['label' => 'Merchant ID', "rules" => "required|is_natural"],
            'email' => ['label' => 'Email', 'rules' => "permit_empty|valid_email"],
            'phonenumber' => ['label' => 'Số điện thoại', 'rules' => "permit_empty|is_natural"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $id = $this->request->getVar('id');

        $merchantDetailsModel = model(MerchantDetailsModel::class);
       
        $merchant_details = $merchantDetailsModel->find($id);

        if(!is_object($merchant_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy Merchant này"));


        $data_update = [
            'brand_name' => $this->request->getVar('brand_name'),
            'company_name' => $this->request->getVar('company_name'),
            'customer_name' => $this->request->getVar('customer_name'),
            'phonenumber' => $this->request->getVar('phonenumber'),
            'email' => $this->request->getVar('email'),
            'note' => $this->request->getVar('note'),
        ];

     
        $result = $merchantDetailsModel->set($data_update)->where(['merchant_id' => $id])->update();

        add_admin_log(array('data_id'=> $id, 'data_type'=>'merchant_details_edit','description'=>'Sửa thông tin Merchant','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));


        set_alert("success", "Sửa thông tin thành công");

        return $this->response->setJSON(["status"=>TRUE]);
    }


    public function tax_info($id='') {
        if(!is_numeric($id))
            show_404();
        

        $data = [
            'page_title' => 'Tax Info',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        
        if(!has_permission('TaxInfo', 'can_view_all'))
            show_404();
        
        $merchantDetailsModel = model(MerchantDetailsModel::class);
       
        $taxInfoModel = model(TaxInfoModel::class);


        $data['merchant_details'] = $merchantDetailsModel->find($id);

        if(!is_object($data['merchant_details']))
            show_404();

    
        $data['tax_infos'] = $taxInfoModel->select(
            "tb_autopay_tax_info.id,
            tb_autopay_tax_info.type,
            tb_autopay_tax_info.company_id,
            tb_autopay_tax_info.merchant_id,
            tb_autopay_tax_info.name,
            tb_autopay_tax_info.email,
            tb_autopay_tax_info.tax_code,
            tb_autopay_tax_info.address1,
            tb_autopay_tax_info.note,
            tb_autopay_tax_info.province,
            tb_autopay_tax_info.district,
            tb_autopay_tax_info.ward,
            tb_autopay_tax_info.created_at,
            tb_autopay_provinces.name as `province_name`,
            tb_autopay_districts.name as `district_name`,
            tb_autopay_wards.name as `ward_name`
        ")
        ->join("tb_autopay_provinces","tb_autopay_provinces.code=tb_autopay_tax_info.province", "left")
        ->join("tb_autopay_districts","tb_autopay_districts.code=tb_autopay_tax_info.district", "left")
        ->join("tb_autopay_wards","tb_autopay_wards.code=tb_autopay_tax_info.ward", "left")
        ->where(['tb_autopay_tax_info.merchant_id' => $id])->orderBy('id','DESC')->get()->getResult();

        echo view('templates/sepay/header',$data);
        echo view('merchant/taxinfo',$data);
        echo view('templates/sepay/footer',$data);

    }
 
}