<?php

namespace App\Controllers;
use App\Models\TransactionsModel;
use App\Models\AdminModel;
use App\Models\UserModel;
use App\Models\CompanyModel;

use CodeIgniter\Controller;
use App\Models\BankAccountModel;
use App\Models\BankModel;
use App\Models\MerchantDetailsModel;

class Bankaccount extends BaseController
{
    public function index() {
        $data = [
            'page_title' => 'Bank Account',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        if(!in_array($this->admin_details->role,['Admin','SuperAdmin']))
            show_404();
        echo view('templates/sepay/header',$data);
        echo view('bankaccount/index',$data);
        echo view('templates/sepay/footer',$data);
    }


    public function ajax_list($company_id='') {

        if(is_numeric($company_id) && $company_id >0) {
            if(!has_permission('BankAccount', 'can_view_all'))
                show_404();
        } else if(!in_array($this->admin_details->role,['Admin','SuperAdmin']))
            show_404();
       
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $bankAccountModel = slavable_model(BankAccountModel::class, 'Bankaccount');
        $companyModel = slavable_model(CompanyModel::class, 'Bankaccount');
        $bankModel = slavable_model(BankModel::class, 'Bankaccount');
        $transactionsModel = slavable_model(TransactionsModel::class, 'Bankaccount');
        $merchantDetailsModel = slavable_model(MerchantDetailsModel::class, 'Bankaccount');

        $bank_accounts = $bankAccountModel->getDatatables($company_id);
        
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($bank_accounts as $bank_account) {

            $company_details = $companyModel->find($bank_account->company_id);
            $bank_details = $bankModel->find($bank_account->bank_id);
            $result = $transactionsModel->select("count(id) as `num_rows`")->where(['bank_account_id' => $bank_account->id])->get()->getRow();
            $count_trans = $result->num_rows;

            if($bank_account->merchant_id > 0) {
                $merchant_details = $merchantDetailsModel->find($bank_account->merchant_id);
                if(is_object($merchant_details))
                    $merchant_info = esc($merchant_details->brand_name);
                else
                    $merchant_info = "";
            } else {
                $merchant_info = "";
            }

            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = esc($bank_account->id);

            if(is_object($company_details))
                $row[] = "<a href='" . base_url("company/details/" . $company_details->id) . "'>#" . $company_details->id . " " . esc($company_details->short_name) . "</a>";
            else
                $row[] = "";

            $row[] = $merchant_info;


            if(is_object($bank_details))
                $bank_icon = "<img class='img-fluid me-2' style='width:25px' src='https://my.sepay.vn/assets/images/banklogo/" . esc($bank_details->icon_path) . "'>";
            else
                $bank_icon = "";


            if(in_array($this->admin_details->role,['Admin','SuperAdmin']) || in_array($this->admin_details->id, [2, 5])) {
                if ($bank_account->bank_id == 9) {
                    $row[] = $bank_icon . sprintf('<a href="%s">%s</a>', base_url('bidv/details/' . $bank_account->id), esc($bank_account->account_number));
                } else {
                    $row[] = $bank_icon . esc($bank_account->account_number);
                }
                $row[] = esc($bank_account->account_holder_name);
            } else {
                $row[] = $bank_icon . "********";
                $row[] = "********";
            }


            if($bank_account->bank_api == 1) {
                $connect_type =  "<span class='text-warning me-2'><i class='bi bi-lightning-charge'></i> API</span>"; 
                if($bank_account->bank_api_connected == 1)
                    $connect_result = "<span><i class='bi bi-lightbulb-fill text-warning'></i></span>";
                else
                    $connect_result = "<span><i class='bi bi-lightbulb-fill text-muted'></i></span>";

            } else {
                $connect_type =  "<span class='text-info me-2'><i class='bi bi-chat-left-text-fill'></i> SMS</span>"; 

                if($bank_account->bank_sms_connected == 1)
                    $connect_result = "<span><i class='bi bi-lightbulb-fill text-warning'></i></span>";
                else
                    $connect_result = "<span><i class='bi bi-lightbulb-fill text-muted'></i></span>";

            }



            $row[] = $connect_type . $connect_result;
        
            if($count_trans > 0)
                $row[] = "<span class='text-success'>" . number_format($count_trans) . "</span>";

            else
                $row[] = number_format($count_trans);
            
            if($bank_account->last_transaction)
                $row[] = timespan($bank_account->last_transaction,1);
            else
                $row[] = "";
            $row[] = esc($bank_account->created_at) . "<br><em>" . timespan($bank_account->created_at,1). "</em>";
           
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $bankAccountModel->countAll($company_id),
            "recordsFiltered" => $bankAccountModel->countFiltered($company_id),
            "data" => $data,
        );
        return $this->response->setJSON($output); 
    }
 
 
}