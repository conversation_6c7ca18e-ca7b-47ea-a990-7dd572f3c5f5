<?php

namespace App\Controllers;
use App\Models\TransactionsModel;
use App\Models\BankAccountModel;
use App\Models\CompanySubscriptionModel;

class Report extends BaseController
{
    public function bank()
    {
        $data = [
            'page_title' => 'Báo cáo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!in_array($this->admin_details->role,['Admin','SuperAdmin']))
            show_404();

        $companySubscriptionModel = slavable_model(CompanySubscriptionModel::class, 'Report');
        $transactionsModel = slavable_model(TransactionsModel::class, 'Report');
        $bankAccountModel = slavable_model(BankAccountModel::class, 'Report');
  

        $result = $bankAccountModel->select("sum(accumulated) as `total_accumulated`")->where(['company_id!=' => 5])->get()->getRow();

        $data['total_accumulated'] = $result->total_accumulated;
        $data['total_bank_account_connected'] = $transactionsModel->groupBy("tb_autopay_sms_parsed.account_number")->countAllResults() - 9;
        $data['total_bank_transactions'] = $transactionsModel->where(['parser_status' => 'Success', 'account_number!=' => '*************','account_number!=' => '*************'])->countAllResults();

        $data['top_banks_accumulated'] = $bankAccountModel->select("sum(tb_autopay_bank_account.accumulated) as `total_accumulated`, tb_autopay_bank_account.bank_id,tb_autopay_bank.brand_name, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id!=' => 5])->groupBy("tb_autopay_bank_account.bank_id")->orderBy("total_accumulated","DESC")->get()->getResult();






        echo view('templates/sepay/header',$data);
        echo view('report/bank',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function invoice_item_paid($year=FALSE)
    {
        $data = [
            'page_title' => 'Báo cáo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('ReportInvoicePaid', 'can_view_all'))
            show_404();

        if(!$year)
            $year = date("Y");

 
        $invoiceModel = model(InvoiceModel::class);
        $invoiceItemModel = model(InvoiceItemModel::class);

        $data['invoice_items'] = $invoiceItemModel->select("tb_autopay_invoice_item.invoice_id, tb_autopay_invoice_item.amount,tb_autopay_invoice_item.id, tb_autopay_invoice_item.description,tb_autopay_invoice_item.type as `item_type,tb_autopay_invoice_item.start_date, tb_autopay_invoice_item.end_date,tb_autopay_invoice.company_id,tb_autopay_invoice.datepaid,tb_autopay_invoice.subtotal,tb_autopay_invoice.type as `invoice_type`")->join("tb_autopay_invoice", "tb_autopay_invoice.id=tb_autopay_invoice_item.invoice_id")->where(['tb_autopay_invoice.status' => "Paid"])->orderBy("tb_autopay_invoice_item.id","DESC")->get()->getResult();
 
        echo view('templates/sepay/header',$data);
        echo view('report/invoice_item_paid',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function revenue_allocation($year=FALSE)
    {
        $data = [
            'page_title' => 'Báo cáo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('RecurringRevenue', 'can_view_all'))
            show_404();

        if(!$year) {
            $year = date("Y");
        }  

        $begin_of_year = $year . "-01-01";
        $end_of_year = $year . "-12-31";
 
        $invoiceModel = model(InvoiceModel::class);
        $invoiceItemModel = model(InvoiceItemModel::class);

        $invoice_items = $invoiceItemModel->select("tb_autopay_invoice_item.invoice_id, tb_autopay_invoice_item.amount,tb_autopay_invoice_item.id, tb_autopay_invoice_item.description,tb_autopay_invoice_item.type as `item_type,tb_autopay_invoice_item.start_date, tb_autopay_invoice_item.end_date,tb_autopay_invoice.company_id,tb_autopay_invoice.datepaid,tb_autopay_invoice.subtotal,tb_autopay_invoice.type as `invoice_type`")->join("tb_autopay_invoice", "tb_autopay_invoice.id=tb_autopay_invoice_item.invoice_id")->where(['tb_autopay_invoice.status' => "Paid", 'tb_autopay_invoice.datepaid>=' => $begin_of_year, 'tb_autopay_invoice.datepaid<=' => $end_of_year])->orderBy("tb_autopay_invoice_item.id","DESC")->get()->getResult();
            
        $revenue = [];
        for($i = 1; $i <= 12; $i++) {
            $revenue[$i]['new'] = 0;
            $revenue[$i]['recurring'] = 0;
        }

        foreach($invoice_items as $key => $value) {
           // var_dump($value);
            // Tính tổng số ngày của item
            $day_interval = date_diff(date_create($value->start_date),date_create($value->end_date))->format("%a");

            for($i=1; $i<=12; $i++) {
               // $revenue[$i]['new'] = 0;
               // $revenue[$i]['recurring'] = 0;
    
                $begin_of_month = $year . "-".$i."-01";
                $end_of_month = date("Y-m-t",strtotime($begin_of_month));

		if(
			   ( strtotime($value->start_date) >= strtotime($begin_of_month) && strtotime($value->start_date) <= strtotime($end_of_month) ) 
			|| ( strtotime($value->end_date) >= strtotime($begin_of_month) && strtotime($value->end_date) <= strtotime($end_of_month) ) 
			|| ( strtotime($value->start_date) <= strtotime($begin_of_month) && strtotime($value->end_date) >= strtotime($end_of_month) ) 
			|| ( strtotime($value->start_date) >=strtotime($begin_of_month) && strtotime($value->end_date) <= strtotime($end_of_month) ) 
		) {

                // Số ngày của hóa đơn trong tháng
                
                if(strtotime($begin_of_month) <= strtotime($value->start_date))
                    $flag_first_day = $value->start_date;
                else
                    $flag_first_day = $begin_of_month;

                if(strtotime($end_of_month) <= strtotime($value->end_date))
                    $flag_last_day = $end_of_month;
                else
                    $flag_last_day = $value->end_date;
                

                  
                // Tính giá tiền dịch vụ / ngày
                if($day_interval !=0)
                    $rev_per_day = round($value->amount / $day_interval);
                else
                    $rev_per_day = 0;

                $day_this_month_interval = date_diff(date_create($flag_first_day),date_create($flag_last_day))->format("%a");
                $rev_item_this_month = $day_this_month_interval*$rev_per_day;

                if($value->invoice_type == "NewOrder") {
                    $revenue[$i]['new'] = $revenue[$i]['new']+  $rev_item_this_month;
                } else {
                    $revenue[$i]['recurring'] = $revenue[$i]['recurring']+  $rev_item_this_month;
		}

		}
     
            }
        }

        $data['year'] = $year;
        $data['revenue'] = $revenue;
        
        echo view('templates/sepay/header',$data);
        echo view('report/revenue_allocation',$data);
        echo view('templates/sepay/footer',$data);
    }



    public function revenue_cash($year=FALSE)
    {
        $data = [
            'page_title' => 'Báo cáo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('CashRevenue', 'can_view_all'))
            show_404();

        if(!$year) {
            $year = date("Y");
        }

        $begin_of_year = $year . "-01-01";
        $end_of_year = $year . "-12-31";


 
        $invoiceModel = model(InvoiceModel::class);

    
        $invoices = $invoiceModel->where(['status' => 'Paid', 'datepaid>=' => $begin_of_year, 'datepaid<=' => $end_of_year,'total>' => 0 ])->get()->getResult();


    
        $revenue = [];
        for($i = 1; $i <= 12; $i++) {
            $revenue[$i]['new'] = 0;
            $revenue[$i]['recurring'] = 0;
            $revenue[$i]['physical_product'] = 0;
            $revenue[$i]['new_invoice_count'] = 0;
            $revenue[$i]['recurring_invoice_count'] = 0;
            $revenue[$i]['physical_product_invoice_count'] = 0;
        }

        foreach($invoices as $key => $value) {

            for($i = 1; $i <= 12; $i++) {
                    $begin_of_month = $year . "-".$i."-01";
                    $end_of_month = date("Y-m-t",strtotime($begin_of_month));
                    if(strtotime($value->datepaid) >= strtotime($begin_of_month) && strtotime($value->datepaid) <= strtotime($end_of_month)) {
                        if($value->type == 'PhysicalProduct') {
                            $revenue[$i]['physical_product'] = $revenue[$i]['physical_product'] + $value->subtotal;
                            $revenue[$i]['physical_product_invoice_count'] = $revenue[$i]['physical_product_invoice_count'] + 1;
                        } elseif(in_array($value->type, ['NewOrder', 'SubscriptionChange'])) {
                            $revenue[$i]['new'] = $revenue[$i]['new']+  $value->subtotal;
                            $revenue[$i]['new_invoice_count'] = $revenue[$i]['new_invoice_count'] + 1;
                        } else {
                            $revenue[$i]['recurring'] = $revenue[$i]['recurring']+  $value->subtotal;
                            $revenue[$i]['recurring_invoice_count'] = $revenue[$i]['recurring_invoice_count'] + 1;
                        }

                    }
            }
    }
        $data['year'] = $year;
        $data['revenue'] = $revenue;
        
        echo view('templates/sepay/header',$data);
        echo view('report/revenue_cash',$data);
        echo view('templates/sepay/footer',$data);
    }


    public function bank_count()
    {
        $data = [
            'page_title' => 'Báo cáo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!in_array($this->admin_details->role,['Admin','SuperAdmin']))
            show_404();

        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $bankAccountModel = model(BankAccountModel::class);
  

        $result = $bankAccountModel->select("count(id) as `count_bank_account`")->where(['bank_sms_connected' => 1])->orWhere(['bank_api_connected' => 1])->get()->getRow();

        $data['total_bank_account_connected'] = $result->count_bank_account;


        $data['count_per_bank'] = $bankAccountModel->select("count(tb_autopay_bank_account.id) as `bank_count`, tb_autopay_bank.brand_name, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path, tb_autopay_bank.id")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.bank_sms_connected' => 1])->orWhere(['tb_autopay_bank_account.bank_api_connected' => 1])->groupBy("tb_autopay_bank_account.bank_id")->orderBy("bank_count","DESC")->get()->getResult();






        echo view('templates/sepay/header',$data);
        echo view('report/bank_count',$data);
        echo view('templates/sepay/footer',$data);
    }


    public function bank_details($bank_id)
    {
        $data = [
            'page_title' => 'Báo cáo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!in_array($this->admin_details->role,['Admin','SuperAdmin']))
            show_404();

        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankModel = model(BankModel::class);


        $data['bank_details'] = $bankModel->select("tb_autopay_bank.id, tb_autopay_bank.brand_name, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path")->where(['tb_autopay_bank.id' => $bank_id])->get()->getRow();

        if(!is_object($data['bank_details']))
            show_404();

        //$result = $bankAccountModel->select("count(id) as `count_bank_account`")->where(['bank_id' => $bank_id, 'last_transaction!=' => NULL])->get()->getRow();
        //$data['total_bank_account_connected'] = $result->count_bank_account;

        $result = $bankAccountModel->select("count(id) as `count_bank_account`")->where(['bank_id' => $bank_id])->groupStart()->where(['bank_sms_connected' => 1])->orWhere(['bank_api_connected' => 1])->groupEnd()->get()->getRow();

        $data['total_bank_account_connected'] = $result->count_bank_account;


        $data['bank_details'] = $bankModel->select("tb_autopay_bank.id, tb_autopay_bank.brand_name, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path")->where(['tb_autopay_bank.id' => $bank_id])->get()->getRow();

        $result = $transactionsModel->select("count(id) as `count_row`")->where(['gateway' => $data['bank_details']->brand_name])->get()->getRow();

        $data['count_trans'] = $result->count_row;

        $result = $transactionsModel->select("sum(amount_in) as `sum_amount_in`")->where(['gateway' => $data['bank_details']->brand_name])->get()->getRow();

        $data['sum_amount_in'] = $result->sum_amount_in;


        $result = $transactionsModel->select("sum(amount_out) as `sum_amount_out`")->where(['gateway' => $data['bank_details']->brand_name])->get()->getRow();

        $data['sum_amount_out'] = $result->sum_amount_out;

        $data['recent_banks'] = $bankAccountModel->select()->where(['bank_id' => $bank_id])->groupStart()->where(['bank_sms_connected' => 1])->orWhere(['bank_api_connected' => 1])->groupEnd()->orderBy("id",'DESC')->limit(100)->get()->getResult();

        // last 30 days
        $result = $bankAccountModel->select("count(id) as `count_bank_account`")->where(['bank_id' => $bank_id,'created_at>=' => date("Y-m-d 00:00:00", strtotime("30 days ago"))])->groupStart()->where(['bank_sms_connected' => 1])->orWhere(['bank_api_connected' => 1])->groupEnd()->get()->getRow();
        $data['total_bank_account_connected_30days'] = $result->count_bank_account;

        $result = $transactionsModel->select("count(id) as `count_row`")->where(['gateway' => $data['bank_details']->brand_name, 'datecreated>=' => date("Y-m-d 00:00:00", strtotime("30 days ago"))])->get()->getRow();
        $data['count_trans_30days'] = $result->count_row;

        $result = $transactionsModel->select("sum(amount_in) as `sum_amount_in`")->where(['gateway' => $data['bank_details']->brand_name, 'datecreated>=' => date("Y-m-d 00:00:00", strtotime("30 days ago"))])->get()->getRow();

        $data['sum_amount_in_30days'] = $result->sum_amount_in;


        $result = $transactionsModel->select("sum(amount_out) as `sum_amount_out`")->where(['gateway' => $data['bank_details']->brand_name, 'datecreated>=' => date("Y-m-d 00:00:00", strtotime("30 days ago"))])->get()->getRow();

        $data['sum_amount_out_30days'] = $result->sum_amount_out;

        echo view('templates/sepay/header',$data);
        echo view('report/bank_details',$data);
        echo view('templates/sepay/footer',$data);
    }


  
}
