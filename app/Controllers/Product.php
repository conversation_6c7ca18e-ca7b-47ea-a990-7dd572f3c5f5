<?php

namespace App\Controllers;
use App\Models\ProductModel;

use CodeIgniter\Controller;

class Product extends BaseController
{
    public function index() {

        if(!has_permission('Product', 'can_view_all'))
            show_404();

        $data = [
            'page_title' => 'Product',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        echo view('templates/sepay/header',$data);
        echo view('product/index',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function ajax_list() {

        if(!has_permission('Product', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
            
        $productModel = model(ProductModel::class);
        $subscriptionModel = model(CompanySubscriptionModel::class);

        $products = $productModel->getDatatables();
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
 
        foreach ($products as $product) {
 
            $count_usage_pending = $subscriptionModel->where(['status' => 'Pending', 'plan_id' => $product->id])->countAllResults();
            $count_usage_active = $subscriptionModel->where(['status' => 'Active', 'plan_id' => $product->id])->countAllResults();
            $count_usage_suspended = $subscriptionModel->where(['status' => 'Suspended', 'plan_id' => $product->id])->countAllResults();
            $count_usage_terminated = $subscriptionModel->where(['status' => 'Terminated', 'plan_id' => $product->id])->countAllResults();



            $no++;
            $row = array();

           
            $row[] = $no;
            $row[] = esc($product->id);
            $row[] = "<b>".esc($product->name)."</b>";
            $row[] = number_format($product->monthly_transaction_limit);
            $row[] = "<a href='".base_url('subscription?plan_id=' . $product->id . '&status=Pending')."' class='link-danger'>" . number_format($count_usage_pending) . "</a> / <a href='".base_url('subscription?plan_id=' . $product->id . '&status=Active')."' class='link-success'>" . number_format($count_usage_active) . "</a> / <a href='".base_url('subscription?plan_id=' . $product->id . '&status=Suspended')."' class='link-warning'>" . number_format($count_usage_suspended) . "</a> / <a href='".base_url('subscription?plan_id=' . $product->id . '&status=Terminated')."' class='link-dark'>" . number_format($count_usage_terminated) . "</a>";
            $row[] = number_format($product->price_monthly) . 'đ';
            $row[] = number_format($product->price_annually) . 'đ';

            $row[] = esc($product->billing_type);
            $row[] = esc($product->sort_order);

            if($product->hidden == 1)
                $row[] = "<span class='text-danger'>Ẩn</span>";
            else
            $row[] = "<span class='text-success'>Hiện</span>";


            if($product->active == 1)
                $row[] = "<span class='text-success'>Kích hoạt</span>";
            else
                $row[] = "<span class='text-danger'>Tạm khóa</span>";


            $row[] = "<button onclick='edit_product(". $product->id.")' class='btn btn-sm btn-warning'><i class='bi bi-pencil'> Sửa</button>";


            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $productModel->countAll(),
            "recordsFiltered" => $productModel->countFiltered(),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }


    public function ajax_product_add() {

        if(!has_permission('Product', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'name' => ['label' => 'Tên Product', 'rules' => 'required'],
            'description' => ['label' => 'Description', 'rules' => "required"],
            'sms_allow' => ['label' => 'Is Shared', 'rules' => "required|in_list[0,1]"],
            'dedicated_sim' => ['label' => 'Is Primary', 'rules' => "required|in_list[0,1]"],
            'monthly_transaction_limit' => ['label' => 'monthly_transaction_limit', 'rules' => 'required|integer|is_natural'],
            'price_monthly' => ['label' => 'price_monthly', 'rules' => 'required|integer|is_natural'],
            'price_annually' => ['label' => 'price_annually', 'rules' => 'required|integer|is_natural'],

            'active' => ['label' => 'Trạng thái', 'rules' => "required|in_list[0,1]"],
            'billing_type' => ['label' => 'billing_type', 'rules' => "required|in_list[Recurring,Onetime,Free]"],
            'hidden' => ['label' => 'hidden', 'rules' => "required|in_list[0,1]"],
            'sort_order' => ['label' => 'sort_order', 'rules' => 'required|integer'],

        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } else {

            $productModel = model(ProductModel::class);
            
            $data = array(
                'name' => $this->request->getVar('name'),
                'description' => $this->request->getVar('description'),
                'sms_allow' => $this->request->getVar('sms_allow'),
                'dedicated_sim' => $this->request->getVar('dedicated_sim'),
                'monthly_transaction_limit' => $this->request->getVar('monthly_transaction_limit'),
                'price_monthly' => $this->request->getVar('price_monthly'),
                'price_annually' => $this->request->getVar('price_annually'),
                'billing_type' => $this->request->getVar('billing_type'),
                'hidden' => $this->request->getVar('hidden'),
                'active' => $this->request->getVar('active'),
                'sort_order' => $this->request->getVar('sort_order'),

            );
 
            $result = $productModel->insert($data);
            
            add_admin_log(array('data_id'=> $result, 'data_type'=>'product_add','description'=>'Thêm Product','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);

                

        }
    }

    public function ajax_product_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('Product', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
            'name' => ['label' => 'Tên Product', 'rules' => 'required'],
            'description' => ['label' => 'Description', 'rules' => "required"],
            'sms_allow' => ['label' => 'Is Shared', 'rules' => "required|in_list[0,1]"],
            'dedicated_sim' => ['label' => 'Is Primary', 'rules' => "required|in_list[0,1]"],
            'monthly_transaction_limit' => ['label' => 'monthly_transaction_limit', 'rules' => 'required|integer|is_natural'],
            'price_monthly' => ['label' => 'price_monthly', 'rules' => 'required|integer|is_natural'],
            'price_annually' => ['label' => 'price_annually', 'rules' => 'required|integer|is_natural'],

            'active' => ['label' => 'Trạng thái', 'rules' => "required|in_list[0,1]"],
            'billing_type' => ['label' => 'billing_type', 'rules' => "required|in_list[Recurring,Onetime,Free]"],
            'hidden' => ['label' => 'hidden', 'rules' => "required|in_list[0,1]"],
            'sort_order' => ['label' => 'sort_order', 'rules' => 'required|integer'],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }  
        $productModel = model(ProductModel::class);
        $sim_id = $this->request->getPost('id');

        $result = $productModel->where(['id'=>$sim_id])->get()->getRow();
        
        if(!is_object($result))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu"));

        $data = array(
            'name' => $this->request->getVar('name'),
            'description' => $this->request->getVar('description'),
            'sms_allow' => $this->request->getVar('sms_allow'),
            'dedicated_sim' => $this->request->getVar('dedicated_sim'),
            'monthly_transaction_limit' => $this->request->getVar('monthly_transaction_limit'),
            'price_monthly' => $this->request->getVar('price_monthly'),
            'price_annually' => $this->request->getVar('price_annually'),
            'billing_type' => $this->request->getVar('billing_type'),
            'hidden' => $this->request->getVar('hidden'),
            'active' => $this->request->getVar('active'),
            'sort_order' => $this->request->getVar('sort_order'),

        );

 
           
        $result = $productModel->set($data)->where("id",$sim_id)->update();
        
        add_admin_log(array('data_id'=> $result, 'data_type'=>'product_edit','description'=>'Sửa Product','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);


    
    } 



    public function ajax_get_product($id='') {
        
        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy SIM này"));

        if(!has_permission('Product', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không chưa được cấp quyền"));
 
        
        $productModel = model(ProductModel::class);
        
        $result = $productModel->find($id);
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu"));
    }

     
    
 
}