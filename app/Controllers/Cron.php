<?php

namespace App\Controllers;

use App\Models\CompanySubscriptionModel;
use CodeIgniter\Controller;
use App\Models\NotificationTelegramQueueModel;
use App\Models\TransactionsModel;
use App\Models\CounterModel;
use App\Models\EmailQueueModel;
use App\Models\InvoiceModel;
use App\Models\ProductModel;
use App\Models\StransactionModel;
use App\Models\TicketModel;
use App\Models\TicketReplyModel;
use Config\Subscription;

class Cron extends Controller
{
    public function delay_alert()
    {
        if(!is_cli()) {
            return false;
        }

        $transactionsModel = model(TransactionsModel::class);
        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);

        $results = $transactionsModel->select("tb_autopay_sms_parsed.id,tb_autopay_sms_parsed.gateway,tb_autopay_sms_parsed.account_number,tb_autopay_bank_account.company_id,tb_autopay_company.short_name,tb_autopay_sms_parsed.datecreated,TIMESTAMPDIFF(SECOND,tb_autopay_sms_parsed.transaction_date,tb_autopay_sms_parsed.datecreated) as `diff_seconds`")->join("tb_autopay_bank_account", "tb_autopay_bank_account.account_number=tb_autopay_sms_parsed.account_number")->join("tb_autopay_company", "tb_autopay_company.id=tb_autopay_bank_account.company_id")->where(['tb_autopay_sms_parsed.parser_status' => 'Success', 'TIMESTAMPDIFF(SECOND,tb_autopay_sms_parsed.transaction_date,tb_autopay_sms_parsed.datecreated)>=' => 180, 'tb_autopay_sms_parsed.source' => 'SMS','tb_autopay_sms_parsed.datecreated>=' => date("Y-m-d H:i:s", strtotime("2 minutes ago"))])->orderBy('tb_autopay_sms_parsed.id', 'DESC')->get()->getResult();

        $chat_id = "-*********";

        if($results) {
$message = "Delay Alert! 
- Transaction ID: " . $results[0]->id . "
- Delay(s): " . $results[0]->diff_seconds . "
- Bank Account: " . esc($results[0]->account_number) . "
- Bank Name: " . esc($results[0]->gateway) . "
- Company: " . esc($results[0]->short_name) . "
- Company ID : " . $results[0]->company_id . "
- Lúc: " . $results[0]->datecreated . "
- Xem thêm: https://ad.sepay.vn/home/<USER>";

            $notificationTelegramQueueModel->sendTelegramMessage($message,$chat_id, 'html');
            
        }

    }

    public function generate_recurring_invoice() {
        // check subscription 
        $subscriptionModel = model(CompanySubscriptionModel::class);
        $invoiceModel = model(InvoiceModel::class);
        $counterModel = model(CounterModel::class);
        $subscriptionConfig = config(Subscription::class);

        $writeLogOnly = $subscriptionConfig->genFreeExcessInvoiceWriteLogOnly ?? true;

        // Kiểm tra các subscription đang active
        $results = $subscriptionModel->where(['auto_renew' => 1,'status' => 'Active'])->get()->getResult();
        $today = date("Y-m-d");
        foreach($results as $result) {
            if($result->end_date && strtotime($today) > strtotime($result->end_date)) {
                $beginDate = $result->begin_date;

                if ($result->billing_cycle === 'free') {
                    $beginDate = date('Y-m-d', strtotime('-1 month +1 day', strtotime($result->end_date)));

                    $invoices = $invoiceModel
                        ->where('company_id', $result->company_id)
                        ->whereIn('status', ['Unpaid', 'Paid'])
                        ->where('type', 'Excess')
                        ->where('date >=', $beginDate)
                        ->countAllResults();

                    if ($invoices > 0) {
                        continue;
                    }
                }

                // generate excess invoice
                $transaction_usage = $counterModel->count_transaction_by_interval($result->company_id, $beginDate, $result->end_date);
                
                if($result->monthly_transaction_limit > 0 && $transaction_usage > $result->monthly_transaction_limit && ($transaction_usage - $result->monthly_transaction_limit) >= 20) {
                    echo "Company ID " . $result->company_id . " used " . $transaction_usage . ' .. generate excess invoice.. \n';

                    if ($writeLogOnly && $result->billing_cycle === 'free') {
                        log_message('error', "Company ID {$result->company_id} used {$transaction_usage}/{$result->monthly_transaction_limit} transactions.");
                        continue;
                    }

                    $gen_result = $subscriptionModel->generate_excess_invoice($result->id);

                    if(is_array($gen_result) && $gen_result['status'] == TRUE && isset($gen_result['invoice_id'])) {
                        $send_result = $invoiceModel->sendEmailInvoice($gen_result['invoice_id']);
                    } else {
                        log_message('error', 'Cron::generate_recurring_invoice: Cannot generate excess invoice for subscription id ' . $result->id);
                    }
                }

                if ($writeLogOnly && $result->billing_cycle === 'free') {
                    continue;
                }

                // generate next invoice
                $gen_result = $subscriptionModel->generate_next_invoice($result->id);
                echo "Generate next invoice for company ID " . $result->company_id . '\n';
                if(is_array($gen_result) && $gen_result['status'] == TRUE && isset($gen_result['invoice_id'])) {
                    $send_result = $invoiceModel->sendEmailInvoice($gen_result['invoice_id']);
                } else {
                    log_message('error', 'Cron::generate_recurring_invoice: Cannot generate next invoice for subscription id ' . $result->id);
                }
            }
        }
        

    }

    public function test_pay() {
        $model = model(StransactionModel::class);

        $result = $model->sendEmailPayment(19);

        var_dump($result);
    }

    public function add_product() {
        $model = model(ProductModel::class);

        $result = $model->insert([
            'name' => 'Startup',
            'dedicated_sim' => '1',
            'monthly_transaction_limit' => 120,
            'price_monthly' => 99000,
            'price_annually' => 99000,
            'sort_order' => 0,
            'hidden' =>0,
            'description' => '<ul class="list-unstyled">
            <li class="mb-2">
            120 giao dịch/ tháng
            </li>
            <li class="mb-2">
            Tích hợp Telegram
            </li>
            <li class="mb-2">
            Hỗ trợ WebHooks
            </li>
            <li class="mb-2">
            SIM nhận SMS: Dùng riêng
            </li>
        </ul>'
        ]);

        var_dump($result);
    }

    public function test1() {
        echo getenv('CLIENT_BASE_URL');
    }



    public function email_queue() {

        if(!is_cli()) {
            return false;
        }

        $emailQueueModel = model(EmailQueueModel::class);

        $results = $emailQueueModel->where(['status' => 'Pending'])->orderBy('id', 'ASC')->get()->getResult();

        foreach($results as $queue) {

           
            $check = $emailQueueModel->where(['id' => $queue->id,'status' => 'Pending'])->get()->getRow();
            if(!is_object($check))
                continue;
 
            $unsubscribe_url = getenv('CLIENT_BASE_URL') . 'pub/u/' . $queue->uuid;
            $trackingUrl = rtrim(getenv('CLIENT_BASE_URL'), '/') . '/pub/e/' . $queue->uuid;
$html_body = <<<EOD


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
</head>
<body style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; background-color: #f9f9fb; color: #303f4d; height: 100%; line-height: 1.4; margin: 0; width: 100% !important; -webkit-text-size-adjust: none;">
    <style>
        @media  only screen and (max-width: 600px) {
            .inner-body {
                width: 100% !important;
            }

            .footer {
                width: 100% !important;
            }
        }

        @media  only screen and (max-width: 500px) {
            .button {
                width: 100% !important;
            }
        }
    </style>
<table class="wrapper" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;"><tr>
<td align="center" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">
                <table class="content" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
<tr>
<td class="header" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 25px 0; text-align: center;">
        <a href="https://sepay.vn" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #bbbfc3; font-size: 19px; font-weight: bold; text-decoration: none; text-shadow: 0 1px 0 white;">
            <img width="100" src="https://my.sepay.vn/assets/images/logo/sepay-blue-359x116.png" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; max-width: 100%; border: none;"></a>
    </td>
</tr>
<!-- Email Body --><tr>
<td class="body" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
                            <table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; background-color: #FFFFFF; margin: 0 auto; padding: 0; width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px;">
<!-- Body content --><tr>
<td class="content-cell" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 25px;">

$queue->body

 
<!-- Subcopy -->
<table class="subcopy" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; border-top: 1px solid #EDEFF2; margin-top: 25px; padding-top: 25px;"><tr>
<td style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">


<p style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #303f4d; line-height: 1.5em; margin-top: 0; text-align: center; font-size: 12px;">Copyright © 2023 Công Ty Cổ Phần SePay - All rights reserved.</p>
    <p style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #303f4d; line-height: 1.5em; margin-top: 0; text-align: center; font-size: 12px;">168 Đường số 2, Vạn Phúc City, P. Hiệp Bình Phước, Thủ Đức, TP. Hồ Chí Minh, Việt Nam</p>
    <p style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #303f4d; line-height: 1.5em; margin-top: 0; text-align: center; font-size: 12px;"><a href="https://www.facebook.com/sepay.vn">Facebook</a> | <a href="https://t.me/s/sepaychannel">Telegram Channel</a> | <a href="https://www.youtube.com/@SePayVN">Youtube</a> | <a href="https://sepay.vn/lien-he.html">Liên hệ</a></p>
        <p  style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #303f4d; line-height: 1.5em; margin-top: 0; text-align: center; font-size: 12px;"><a href="$unsubscribe_url" style="font-size:12px;color:#555555;text-decoration: none;">Không muốn nhận email này vào tương lai? Unsubscribe</a></p>

        </td>
    </tr></table>
</td>
                                </tr>
</table>
</td>
                    </tr>
<tr>
<td style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">
        <table class="footer" align="center" width="570" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0 auto; padding: 0; text-align: center; width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px;"><tr>
<td class="content-cell" align="center" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 35px;">
                    <p style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; line-height: 1.5em; margin-top: 0; color: #AEAEAE; font-size: 12px; text-align: center;"@ 2023 SePay. All rights reserved.</p>
                </td>
            </tr></table>
</td>
</tr>
</table>
</td>
        </tr></table>
    <img src="$trackingUrl" />
</body>
</html>
EOD;

            $email = \Config\Services::email();

            
            $config['wordWrap'] = true;
            $config['mailType'] = 'html';
            $email->initialize($config);

            
            if(getenv('SEPAY_ENV') == 'STAGING')
                $email->setFrom('<EMAIL>', 'Team SePay');
            else
                $email->setFrom('<EMAIL>', 'Team SePay');
            $email->setTo($queue->mail_to);

            $email->setSubject($queue->title);

            $email->setMessage($html_body);

            $result = $email->send();
    
            if($result) {
                $emailQueueModel->set(['status' => 'Sent','sent_at' => date("Y-m-d H:i:s")])->where(['id' => $queue->id])->update();
            } else {
                $emailQueueModel->set(['status' => 'Failed'])->where(['id' => $queue->id])->update();

            }

        }
        
    }


    public function alert_ticket() {

        if(!is_cli())
            return FALSE;

        $chat_id = "-1002471013155";

        $ticketModel = model(TicketModel::class);
        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
        helper('general');

        $results = $ticketModel->where(['alert_admin' => 0,'owner_type' => 'User'])->orderBy('id','ASC')->get()->getResult();
        foreach($results as $ticket) {
          
            echo "\n";
            $message = "Có ticket mới
- ID: " . $ticket->id. "
- Tiêu đề: " . esc($ticket->subject). "
- Tạo bởi: " . esc($ticket->name). "
- Xem ticket: " . base_url('ticket/details/' . $ticket->id). "
- Tạo lúc: " . $ticket->created_at;
            $send_result = $notificationTelegramQueueModel->sendTelegramMessage($message,$chat_id, 'html');

            if(isset($send_result['success']) && $send_result['success'] == TRUE)
                $ticketModel->set(['alert_admin' => 1])->where('id', $ticket->id)->update();
            else
                sleep(2);

            sleep(1);
        }
        
    }


    public function alert_ticket_reply() {

        if(!is_cli())
            return FALSE;

        $chat_id = "-1002471013155";

        $ticketModel = model(TicketModel::class);
        $ticketReplyModel = model(TicketReplyModel::class);
        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
        helper('general');

        $results = $ticketReplyModel->where(['alert_admin' => 0,'owner_type' => 'User'])->orderBy('id','ASC')->get()->getResult();
        foreach($results as $reply) {

            $ticket_details = $ticketModel->where(['id' => $reply->ticket_id])->get()->getRow();

            if(!is_object($ticket_details)) {
                $ticketReplyModel->set(['alert_admin' => 1])->where('id', $reply->id)->update();
                continue;
            }
          
            echo "\n";
            $message = "Có phản hồi ticket mới
- ID: " . $reply->ticket_id. "
- Tiêu đề: " . esc($ticket_details->subject). "
- Tạo bởi: " . esc($ticket_details->name). "
- Xem ticket: " . base_url('ticket/details/' . $reply->ticket_id). "
- Phản hồi lúc: " . $reply->created_at;
            $send_result = $notificationTelegramQueueModel->sendTelegramMessage($message,$chat_id, 'html');

            if(isset($send_result['success']) && $send_result['success'] == TRUE)
                $ticketReplyModel->set(['alert_admin' => 1])->where('id', $reply->id)->update();
            else
                sleep(2);

            sleep(1);
        }
        
    }


     
}