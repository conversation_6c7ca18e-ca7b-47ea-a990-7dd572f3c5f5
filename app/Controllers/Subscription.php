<?php

namespace App\Controllers;
use App\Models\AdminModel;
use App\Models\UserModel;
use App\Models\CompanyModel;
use App\Models\ProductModel;
use App\Models\CompanySubscriptionModel;
use App\Models\InvoiceModel;
use App\Models\InvoiceItemModel;

use CodeIgniter\Controller;

class Subscription extends BaseController
{
    public function index()
    {
        $data = [
            'page_title' => 'Subscription',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        if(!has_permission('Subscription', 'can_view_all')) {
            show_404();
        }

        $data['plan_id'] = $this->request->getGet('plan_id');
        $data['status'] = $this->request->getGet('status');

        echo view('templates/sepay/header', $data);
        echo view('subscription/index', $data);
        echo view('templates/sepay/footer', $data);
    }


    public function edit($id='')
    {
        $data = [
            'page_title' => 'Subscription',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('Subscription', 'can_view_all')) {
            show_404();
        }

        if(!is_numeric($id)) {
            show_404();
        }

        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        $companyModel = model(CompanyModel::class);
        $productModel = model(ProductModel::class);

        $data['subscription_details'] = $companySubscriptionModel->where(['id' => $id])->get()->getRow();
        $data['products'] = $productModel->orderBy("sort_order","ASC")->get()->getResult();

        if(!is_object($data['subscription_details'])) {
            show_404();
        }

        $data['company_details'] = $companyModel->find($data['subscription_details']->company_id);

        if(!is_object($data['company_details'])) {
            show_404();
        }

        echo view('templates/sepay/header', $data);
        echo view('subscription/edit', $data);
        echo view('templates/sepay/footer', $data);
    }

 


    public function ajax_subscription_update() {

        if(!has_permission('Subscription', 'can_edit'))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không có quyền sửa Subscription"));

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        $dateOptions = [
            '+1 day' => '1 day',
            '+2 day' => '2 days',
            '+3 day' => '3 days',
            '+5 day' => '5 days',
            '+1 week' => '1 week',
            '+2 week' => '2 weeks'
        ];

        $ruleDates = [];

        foreach ($dateOptions as $interval => $label) {
            $ruleDates[] = date('Y-m-d', strtotime($interval));
        }

        $ruleDates = implode(',', $ruleDates);

        if(! $this->validate([
            'id' => ['label' => 'ID', "rules" => "required|is_natural"],
            'begin_date' => ['label' => 'Begin date', "rules" => "required|valid_date[Y-m-d]"],
            'end_date' => ['label' => 'End date', "rules" => "required|valid_date[Y-m-d]"],
            'plan_id' => ['label' => 'Plan', "rules" => "required|is_natural"],
            'first_payment' => ['label' => 'First payment', "rules" => "required|is_natural"],     
            'recurring_payment' => ['label' => 'Recurring payment', "rules" => "required|is_natural"], 
            'monthly_transaction_limit' => ['label' => 'Monthly Transaction Limit', "rules" => "required|is_natural"],         
            'billing_cycle' => ['label' => 'Billing cycle', "rules" => "required|in_list[free,monthly,quarterly,semi-annually,annually,biennially,triennially]"],
            'disable_auto_suspend' => ['label' => 'Auto suspend', "rules" => "permit_empty|in_list[0,1]"],
            'disable_suspension_days' => ['label' => 'Disable suspension days', "rules" => 'required_with[disable_auto_suspend]|in_list['.$ruleDates.',custom]'],
            'disable_suspension_until' => ['label' => 'Suspension over date', "rules" => "permit_empty|valid_date[Y-m-d]"],
            'allow_exceed_limit' => ['label' => 'Allow exceed limit', "rules" => "permit_empty|in_list[0,1]"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $subscription_id = $this->request->getVar('id');

 
        if(strtotime($this->request->getVar('begin_date')) >= strtotime($this->request->getVar('end_date')))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Begin date phải nhỏ hơn End date"));
        
        if ($this->request->getVar('disable_suspension_days') === 'custom') {
            $disable_suspension_until = $this->request->getVar('disable_suspension_until');
        } else {
            $disable_suspension_until = $this->request->getVar('disable_suspension_days');
        }

        $data_update = [
            'begin_date' => $this->request->getVar('begin_date'),
            'end_date' => $this->request->getVar('end_date'),
            'plan_id' => $this->request->getVar('plan_id'),
            'first_payment' => $this->request->getVar('first_payment'),
            'recurring_payment' => $this->request->getVar('recurring_payment'),
            'billing_cycle' => $this->request->getVar('billing_cycle'),
            'monthly_transaction_limit' => $this->request->getVar('monthly_transaction_limit'),
            'notes' => trim($this->request->getVar('notes')),
            'disable_auto_suspend' => $this->request->getVar('disable_auto_suspend') == 1,
            'disable_suspension_until' => $disable_suspension_until,
            'allow_exceed_limit' => $this->request->getVar('allow_exceed_limit') == 1,
        ];

        $companySubscriptionModel = model(CompanySubscriptionModel::class);

        $companySubscriptionModel->set($data_update)->where(['id' => $subscription_id])->update();
 
        add_admin_log(array('data_id'=> $subscription_id, 'data_type'=>'subscription_edit','description'=>'Sửa Subscription','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));


        return $this->response->setJSON(["status"=>TRUE]);

    }

    public function ajax_status_update() {

        if(!has_permission('Subscription', 'can_edit'))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không có quyền sửa Subscription"));

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'id' => ['label' => 'ID', "rules" => "required|is_natural"],
            'status' => ['label' => 'Status', "rules" => "required|in_list[Pending,Active,Suspended,Terminated,Cancelled]"],     
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $subscription_id = $this->request->getVar('id');
         
        $data_update = [
            'status' => $this->request->getVar('status')
        ];

        $companySubscriptionModel = model(CompanySubscriptionModel::class);

        $companySubscriptionModel->set($data_update)->where(['id' => $subscription_id])->update();
 
        add_admin_log(array('data_id'=> $subscription_id, 'data_type'=>'subscription_edit','description'=>'Sửa Subscription','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));


        return $this->response->setJSON(["status"=>TRUE]);

    }


    public function ajax_generate_invoice() {

        if(!has_permission('Subscription', 'can_edit'))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không có quyền sửa Subscription"));

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'id' => ['label' => 'ID', "rules" => "required|is_natural"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $subscription_id = $this->request->getVar('id');
         
        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        $invoiceModel = model(InvoiceModel::class);
        $invoiceItemModel = model(InvoiceItemModel::class);

        $productModel = model(ProductModel::class);

        $subscription_details = $companySubscriptionModel->where(['id' => $subscription_id])->get()->getRow();
 
        if(!is_object($subscription_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy Subscription này"));


        $product_details = $productModel->find($subscription_details->plan_id);

        if(!is_object($product_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy Plan"));

        $new_begin_date = date('Y-m-d', strtotime($subscription_details->end_date  . ' +1 day'));
        
        if($subscription_details->billing_cycle == "monthly") {
            $new_end_date =  date('Y-m-d', strtotime($subscription_details->end_date  . ' +1 month'));
        } else if($subscription_details->billing_cycle == "quarterly") {
            $new_end_date =  date('Y-m-d', strtotime($subscription_details->end_date  . ' +3 months'));
        } else if($subscription_details->billing_cycle == "semi-annually") {
            $new_end_date =  date('Y-m-d', strtotime($subscription_details->end_date  . ' +6 months'));
        } else if($subscription_details->billing_cycle == "annually") {
            $new_end_date =  date('Y-m-d', strtotime($subscription_details->end_date  . ' +1 year'));
        } else if($subscription_details->billing_cycle == "biennially") {
            $new_end_date =  date('Y-m-d', strtotime($subscription_details->end_date  . ' +2 years'));
        } else if($subscription_details->billing_cycle == "triennially") {
            $new_end_date =  date('Y-m-d', strtotime($subscription_details->end_date  . ' +3 years'));
        } else
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không thể tạo hoá đơn"));

        $subscription_data = [ 
            "begin_date" => $new_begin_date,
            "end_date" => $new_end_date
        ];

        if(is_numeric($product_details->tax_rate) && $product_details->tax_rate >0) {
            $is_product_taxed = 1;
            $tax = $subscription_details->recurring_payment * ($product_details->tax_rate/100);
        } else {
            $is_product_taxed = 0;
            $tax  = 0;
        }
            
        // create new invoice
        $invoice_data = [
            "company_id" => $subscription_details->company_id,
            "type" => "Recurring",
            "status" => "Unpaid",
            "date" => $new_begin_date,
            "duedate" => $new_begin_date,
            "paybefore" => date('Y-m-d', strtotime($new_begin_date  . ' +7 day')),
            "subtotal" => $subscription_details->recurring_payment,
            "total" => $subscription_details->recurring_payment + $tax,
            'tax' => $tax,
            'tax_rate' => $product_details->tax_rate,
        ];

        $invoice_id = $invoiceModel->insert($invoice_data);

        if($invoice_id) {
            $invoice_item_data = [
                "invoice_id" => $invoice_id,
                "type" => "Product",
                "item_id" => $product_details->id,
                "description" => "SePay - " .  $product_details->name . " (" . $new_begin_date . " - ". $new_end_date .")",
                "amount" => $subscription_details->recurring_payment,
                "start_date" => $new_begin_date,
                "end_date" => $new_end_date,
                'taxed' => $is_product_taxed,
                'tax_rate' => $product_details->tax_rate,
                'tax' => $tax,
            ];

            $invoiceItemModel->insert( $invoice_item_data);

            $companySubscriptionModel->set($subscription_data)->where(['id'=>$subscription_id])->update();
        } else
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không thể tạo hoá đơn"));
    
        add_admin_log(array('data_id'=> $invoice_id, 'data_type'=>'subscription_generate_invoice','description'=>'Tạo hoá đơn gia hạn cho Subscription ' , $subscription_id,'admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));


        return $this->response->setJSON(["status"=>TRUE]);



    }


    public function all_ajax_list() {

        if(!has_permission('Subscription', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $invoiceModel = model(InvoiceModel::class);
        $companyModel = model(CompanyModel::class);
        $companySubscriptionModel = model(CompanySubscriptionModel::class);

        $plan_id = $this->request->getGet('plan_id');
        $status = $this->request->getGet('status');
        if(!is_numeric($plan_id))
            $plan_id = FALSE;
        if(!in_array($status, ['Pending', 'Active', 'Suspended', 'Terminated', 'Cancelled', 'Fraud']))
            $status = FALSE;

        $subscriptions = $companySubscriptionModel->getDatatables(FALSE, $plan_id, $status);


        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($subscriptions as $subscription) {

            $company_details = $companyModel->find($subscription->company_id);
            $productModel = model(ProductModel::class);
            $product_details = $productModel->find($subscription->plan_id);

         
            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = "<a href='" . base_url('subscription/edit/' . $subscription->id) . "'>". esc($subscription->id) . "</a>";
            if($company_details)
                $row[] = "<a href='" . base_url('company/details/' . $subscription->company_id) . "'>". esc($company_details->short_name) . "</a>";
            else
                $row[] = "";

            if(is_object($product_details))
                $row[] = esc($product_details->name);
            else
                $row[] = "";

            if($subscription->billing_cycle !="free")
                $row[] = "<span class='text-info'>" . esc(ucfirst($subscription->billing_cycle)) . "</span>";
            else
                $row[] = esc(ucfirst($subscription->billing_cycle));

          
            if($subscription->status == "Pending")
                $row[] = "<span class='badge rounded-pill bg-danger'>".esc($subscription->status)."</span>";  
            else if($subscription->status == "Active")
                $row[] = "<span class='badge rounded-pill bg-success'>".esc($subscription->status)."</span>";  
            else if($subscription->status == "Suspended")
                $row[] = "<span class='badge rounded-pill bg-warning'>".esc($subscription->status)."</span>";  
            else if(in_array($subscription->status,['Terminated','Cancelled','Fraud']))
                $row[] = "<span class='badge rounded-pill bg-secondary'>".esc($subscription->status)."</span>";  
            else
                $row[] = esc($subscription->status);  
            

            $row[] = esc($subscription->begin_date);
            $row[] = esc($subscription->end_date);
            $row[] = number_format($subscription->first_payment) . " đ";
            $row[] = number_format($subscription->recurring_payment) . " đ";
            $row[] = esc($subscription->created_at);

          
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $companySubscriptionModel->countAll(FALSE,$plan_id, $status),
            "recordsFiltered" => $companySubscriptionModel->countFiltered(FALSE, $plan_id, $status),
            "data" => $data,
        );
        return $this->response->setJSON($output); 
    }
 
}