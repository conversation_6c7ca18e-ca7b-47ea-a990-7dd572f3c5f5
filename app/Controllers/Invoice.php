<?php

namespace App\Controllers;

use App\Libraries\SInvoice;
use App\Models\TransactionsModel;
use App\Models\StransactionsModel;
use App\Models\AdminModel;
use App\Models\UserModel;
use App\Models\CompanyModel;
use App\Models\CompanySubscriptionModel;
use App\Models\InvoiceModel;
use App\Models\InvoiceItemModel;
use App\Models\ProductModel;
use App\Models\TaxInfoModel;

use CodeIgniter\Controller;
use Config\Services;
use Config\SubscriptionSInvoice;
use Exception;

class Invoice extends BaseController
{
    public function index() {
        $data = [
            'page_title' => 'Hoá đơn',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        if(!has_permission('Invoice', 'can_view_all'))
            show_404();

        $filter_args = [];

        if($this->request->getGet('status'))
            $filter_args['status'] = esc($this->request->getGet('status'));
        
        if($this->request->getGet('company_id'))
            $filter_args['company_id'] = esc($this->request->getGet('company_id'));

        if(is_numeric($this->request->getGet('tax_issued')))
            $filter_args['tax_issued'] = esc($this->request->getGet('tax_issued'));
        
        if($this->request->getGet('type'))
            $filter_args['type'] = esc($this->request->getGet('type'));

        if($this->request->getGet('id'))
            $filter_args['id'] = esc($this->request->getGet('id'));

        $data['filter_args'] = $filter_args;
        echo view('templates/sepay/header',$data);
        echo view('invoice/index',$data);
        echo view('templates/sepay/footer',$data);
    }
 

    public function details($invoice_id='') {
        $data = [
            'page_title' => 'Hoá đơn',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('Invoice', 'can_view_all'))
            show_404();

        if(!is_numeric($invoice_id))
            show_404();

        $invoiceModel = model(InvoiceModel::class);
        $invoiceItemModel = model(InvoiceItemModel::class);
        $companyModel = model(CompanyModel::class);
 
        $data['invoice_details'] = $invoiceModel->where(['id' => $invoice_id])->get()->getRow();
 
        if(!is_object($data['invoice_details']))
            show_404();

        $data['company_details'] = $companyModel->find($data['invoice_details']->company_id);

        if(!is_object($data['company_details']))
            show_404();

        $data['invoice_items'] = $invoiceItemModel->where(['invoice_id' => $invoice_id])->orderBy('position','ASC')->get()->getResult();

        $data['paycode'] = create_paycode($invoice_id);
        $data['qrcode'] = '';

        if($data['invoice_details']->status == "Unpaid") {
            $gen = new \tttran\viet_qr_generator\Generator;
            $res =  $gen->bankId("MBBank") // BankId, bankname
            ->accountNo("*************")// Account number
            ->amount(intval($data['invoice_details']->total))// Money
            ->info($data['paycode']) // Ref
            ->returnText(false)
            ->generate();
            $qrcode = json_decode($res);

            if(is_object($qrcode) && isset($qrcode->data))
                $data['qrcode'] = $qrcode->data;
        }

        echo view('templates/sepay/header',$data);
        echo view('company/invoice_details',$data);
        echo view('templates/sepay/footer',$data);

  
    }

    public function all_ajax_list() {

        if(!has_permission('Invoice', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $invoiceModel = model(InvoiceModel::class);
        $companyModel = model(CompanyModel::class);

        $invoices = $invoiceModel->getDatatables();

        
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($invoices as $invoice) {

            $company_details = $companyModel->find($invoice->company_id);

         
            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = "<a href='" . base_url('invoice/edit/' . $invoice->id) . "'>". esc($invoice->id) . "</a>";

            if(is_object($company_details))
                $row[] = "<a href='" . base_url("company/details/" . $company_details->id) . "'>#" . esc($company_details->id . ' - ' . $company_details->short_name) . "</a>";
            else
                $row[] = "";
            if($invoice->type == "NewOrder")
                $row[] = "<span class='text-info'>Đăng ký mới</span>";  
            else if($invoice->type == "Recurring")
                $row[] = "<span class='text-warning'>Gia hạn</span>";  
            else if($invoice->type == "Credit")
                $row[] = "<span class=''>Thêm tiền</span>";  
            else if($invoice->type == "Excess")
                $row[] = "<span class='text-danger'>Vượt hạn mức</span>";  
            else if($invoice->type == "SubscriptionChange")
                $row[] = "<span class='text-success'>Đổi gói dịch vụ</span>";  
            else
                $row[] = esc($invoice->type);
                
            if($invoice->status == "Unpaid")
                $row[] = "<span class='badge rounded-pill bg-danger'>Chưa thanh toán</span>";  
            else if($invoice->status == "Paid")
                $row[] = "<span class='badge rounded-pill bg-success'>Đã thanh toán</span>";  
            else if($invoice->status == "Cancelled")
                $row[] = "<span class='badge rounded-pill bg-secondary'>Đã hủy</span>";  
            else if($invoice->status == "Refunded")
                $row[] = "<span class='badge rounded-pill bg-warning'>Đã hoàn tiền</span>";  
            else
                $row[] = esc($invoice->status);  

            $row[] = number_format($invoice->total) . " đ";

            if($invoice->tax_issued == 1)
                $row[] = "<span class='badge rounded-pill bg-success'>Đã xuất</span><br><span class='text-info'>" . esc($invoice->tax_issued_id) . "</span>";  
            else
                $row[] = "<span class='badge rounded-pill bg-danger'>Chưa xuất</span>";  
            
            
            $row[] = esc($invoice->date);
            $actions = '';

            if (has_permission('Invoice', 'can_edit')) {
                $actions .= "<a href='" . base_url('invoice/edit/' . $invoice->id) . "' class='me-3'>Sửa</a>";;
            }

            if (has_permission('Invoice', 'can_delete')) {
                $actions .= "<a href='javascript:;' onclick='delete_invoice(" . $invoice->id . ")' class='text-danger'>Xóa</a>";
            }

            $row[] = $actions;

           
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $invoiceModel->countAll(),
            "recordsFiltered" => $invoiceModel->countFiltered(),
            "data" => $data,
        );
        return $this->response->setJSON($output); 
    }

    public function edit($invoice_id='') {
        $data = [
            'page_title' => 'Hoá đơn',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        if(!has_permission('Invoice', 'can_edit'))
            show_404();

        if(!is_numeric($invoice_id))
            show_404();

        $invoiceModel = model(InvoiceModel::class);
        $invoiceItemModel = model(InvoiceItemModel::class);
        $companyModel = model(CompanyModel::class);
        $stransactionModel = model(StransactionModel::class);
        $taxInfoModel = model(TaxInfoModel::class);
        $merchantDetailsModel = model(MerchantDetailsModel::class);
        $productModel = model(ProductModel::class);

        $data['invoice_details'] = $invoiceModel->where(['id' => $invoice_id])->get()->getRow();
 
        if(!is_object($data['invoice_details']))
            show_404();
 
        if(is_numeric($data['invoice_details']->company_id) && $data['invoice_details']->company_id > 0)
            $invoice_owner = "company";
        else if(is_numeric($data['invoice_details']->merchant_id) && $data['invoice_details']->merchant_id > 0)
            $invoice_owner = "merchant";
        else
            show_404();

        $data['products'] = $productModel->orderBy('id', 'ASC')->get()->getResult();

        if($invoice_owner == "company") {
            $data['company_details'] = $companyModel->find($data['invoice_details']->company_id);

            if(!is_object($data['company_details']))
                show_404();
    
            $data['invoice_items'] = $invoiceItemModel->where(['invoice_id' => $invoice_id])->orderBy('position','ASC')->get()->getResult();
    
      
            $data['stransactions'] = $stransactionModel->where(['invoice_id' => $invoice_id])->get()->getResult();
    
            $data['tax_infos'] = $taxInfoModel->where(['company_id' => $data['invoice_details']->company_id])->orderBy('id','DESC')->get()->getResult();
    
            echo view('templates/sepay/header',$data);
            echo view('invoice/company_edit',$data);
            echo view('templates/sepay/footer',$data);
        } else {
            $data['merchant_details'] = $merchantDetailsModel->find($data['invoice_details']->merchant_id);

            if(!is_object($data['merchant_details']))
                show_404();
    
            $data['invoice_items'] = $invoiceItemModel->where(['invoice_id' => $invoice_id])->orderBy('position','ASC')->get()->getResult();
    
      
            $data['stransactions'] = $stransactionModel->where(['invoice_id' => $invoice_id])->get()->getResult();
    
            $data['tax_infos'] = $taxInfoModel->where(['merchant_id' => $data['invoice_details']->merchant_id])->orderBy('id','DESC')->get()->getResult();
    
            echo view('templates/sepay/header',$data);
            echo view('invoice/merchant_edit',$data);
            echo view('templates/sepay/footer',$data);
        }

       
    }


    public function ajax_invoice_update() {

        if(!has_permission('Invoice', 'can_edit'))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không có quyền sửa hoá đơn"));

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'id' => ['label' => 'ID', "rules" => "required|is_natural"],
            'tax_rate' => ['label' => 'Tax Rate', "rules" => "required|is_natural|greater_than_equal_to[0]|less_than_equal_to[100]"],
            'date' => ['label' => 'Invoice date', "rules" => "required|valid_date[Y-m-d]"],
            'duedate' => ['label' => 'Due date', "rules" => "required|valid_date[Y-m-d]"],
            'paybefore' => ['label' => 'Pay before', "rules" => "required|valid_date[Y-m-d]"],
            'type' => ['label' => 'Invoice Type', "rules" => "required|in_list[NewOrder,Recurring,Credit,Excess,SubscriptionChange]"],     
            'tax_issued' => ['label' => 'Trạng thái xuất hoá đơn', "rules" => "required|in_list[0,1,2]"],
            'tax_info_id' => ['label' => 'Thông tin xuất', "rules" => "is_natural"], 
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $invoice_id = $this->request->getVar('id');
         
        $data_update = [
            'date' => $this->request->getVar('date'),
            'duedate' => $this->request->getVar('duedate'),
            'paybefore' => $this->request->getVar('paybefore'),
            'type' => $this->request->getVar('type'),
            'tax_rate' => $this->request->getVar('tax_rate'),
            'public_note' => trim($this->request->getVar('public_note')),
            'tax_issued' => $this->request->getVar('tax_issued'),
            'tax_issued_id' => $this->request->getVar('tax_issued_id'),
            'tax_info_id' => $this->request->getVar('tax_info_id')
        ];

        if($data_update['tax_issued'] == 1 && $data_update['tax_info_id'] == 0 && empty($data_update['tax_issued_id']))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Nếu chọn đã phát hành hoá đơn VAT, bạn phải chọn thông tin xuất hoá đơn"));


        $invoiceModel = model(InvoiceModel::class);

        $invoiceModel->set($data_update)->where(['id' => $invoice_id])->update();
 
        add_admin_log(array('data_id'=> $invoice_id, 'data_type'=>'invoice_edit','description'=>'Sửa hoá đơn','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));


        return $this->response->setJSON(["status"=>TRUE]);

    }

    public function ajax_status_update() {

        if(!has_permission('Invoice', 'can_edit'))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không có quyền sửa hoá đơn"));

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'id' => ['label' => 'ID', "rules" => "required|is_natural"],
            'status' => ['label' => 'Status', "rules" => "required|in_list[Paid,Unpaid,Cancelled,Refunded]"],     
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $invoice_id = $this->request->getVar('id');
         
        $data_update = [
            'status' => $this->request->getVar('status')
        ];

        $invoiceModel = model(InvoiceModel::class);

        $invoiceModel->set($data_update)->where(['id' => $invoice_id])->update();
 
        add_admin_log(array('data_id'=> $invoice_id, 'data_type'=>'invoice_edit','description'=>'Sửa hoá đơn','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));


        return $this->response->setJSON(["status"=>TRUE]);

    }

    public function ajax_send_mail_invoice() {

        if(!has_permission('Invoice', 'can_edit'))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không có quyền"));

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'id' => ['label' => 'ID', "rules" => "required|is_natural"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $invoice_id = $this->request->getVar('id');
        $invoiceModel = model(InvoiceModel::class);

        $result = $invoiceModel->sendEmailInvoice($invoice_id);

        if($result) {
            add_admin_log(array('data_id'=> $invoice_id, 'data_type'=>'invoice_send_mail','description'=>'Gửi email tạo hoá đơn','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

            return $this->response->setJSON(["status"=>TRUE]);
        }
        
        return $this->response->setJSON(["status"=>FALSE]);


    }

    
    public function ajax_get_item($id='') {
        
        if(!has_permission('Invoice', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa"));
 
        
        $invoiceItemModel = model(InvoiceItemModel::class);
        
        $result = $invoiceItemModel->find($id);
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ngân hàng này"));
    }

    public function ajax_item_update() {

        if(!has_permission('Invoice', 'can_edit'))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không có quyền sửa hoá đơn"));

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'id' => ['label' => 'ID', "rules" => "required|is_natural"],
            'type' => ['label' => 'Loại', "rules" => "required|in_list[Product,Addon,Discount,Upgrade,Other]"],
            'item_id' => ['label' => 'Product', "rules" => "permit_empty|is_natural"],
            'taxed' => ['label' => 'Taxed?', "rules" => "required|in_list[0,1]"],
            'tax_rate' => ['label' => 'Tax Rate', "rules" => "required|is_natural"],
            'start_date' => ['label' => 'Start date', "rules" => "required|valid_date[Y-m-d]"],
            'end_date' => ['label' => 'End date', "rules" => "required|valid_date[Y-m-d]"],
            'amount' => ['label' => 'Amount', "rules" => "required|integer"],
            'description' => ['label' => 'Description', "rules" => "required"],

        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $id = $this->request->getVar('id');

        $invoiceItemModel = model(InvoiceItemModel::class);

        $item_details = $invoiceItemModel->find($id);

        if(!$item_details)
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy item này"));

         
        $data_update = [
            'type' => $this->request->getVar('type'),
            'item_id' => $this->request->getVar('item_id'),
            'start_date' => $this->request->getVar('start_date'),
            'end_date' => $this->request->getVar('end_date'),
            'amount' => $this->request->getVar('amount'),
            'taxed' => $this->request->getVar('taxed'),
            'tax_rate' => $this->request->getVar('tax_rate'),
            'description' => $this->request->getVar('description'),
        ];

        $invoiceModel = model(InvoiceModel::class);

        $invoiceItemModel->set($data_update)->where(['id' => $id])->update();

        if($data_update['type'] == 'Product' && $data_update['item_id'] <= 0)
        return $this->response->setJSON(array('status'=>FALSE,'message'=>"Nếu chọn item là product thì bạn phải chọn product tương ứng"));

        // update invoice subtotal and total
        $invoice_details = $invoiceModel->find($item_details->invoice_id);
        if(is_object($invoice_details)) {

            $items = $invoiceItemModel->where(['invoice_id' => $item_details->invoice_id])->get()->getResult();

            $new_subtotal = 0;
            $new_total = 0;
            $new_total_tax = 0;

            if($items) {
                foreach($items as $item) {
                    $new_subtotal = $new_subtotal + $item->amount;

                    if($item->taxed == 1)
                        $this_item_tax = $item->amount * ($item->tax_rate/100);
                    else
                        $this_item_tax = 0;

                    $new_total = $new_total + $item->amount + $this_item_tax;
                    $new_total_tax = $new_total_tax + $this_item_tax;
                }
            }

            if($new_subtotal != $invoice_details->subtotal || $new_total != $invoice_details->total)
                $invoiceModel->set(['total' => $new_total, 'subtotal' => $new_subtotal,'tax' => $new_total_tax])->where(['id' => $item_details->invoice_id])->update();

           
        }
        

 
        add_admin_log(array('data_id'=> $id, 'data_type'=>'invoice_item_edit','description'=>'Sửa invoice item','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));


        return $this->response->setJSON(["status"=>TRUE]);

    }


    public function ajax_create_draft() {
       
        if(!has_permission('Invoice', 'can_add'))
            show_404();

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'id' => ['label' => 'ID', "rules" => "required|is_natural"],
            'invoice_owner' => ['label' => 'ID', "rules" => "required|in_list[company,merchant]"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $id = $this->request->getVar('id');
        $invoice_owner = $this->request->getVar('invoice_owner');

    
        $invoiceModel = model(InvoiceModel::class);
        $companyModel = model(CompanyModel::class);
        $merchantDetailsModel = model(MerchantDetailsModel::class);

        if($invoice_owner == "company") {
            $company_details = $companyModel->find($id);
            if(!is_object($company_details))
                return $this->response->setJSON(["status"=>FALSE, "message"=>"Không tìm thấy công ty này"]);

            $invoice_id = $invoiceModel->insert(['status' => 'Draft', 'company_id' => $id]);

        } else {
            $merchant_details = $merchantDetailsModel->find($id);
            if(!is_object($merchant_details))
                return $this->response->setJSON(["status"=>FALSE, "message"=>"Không tìm thấy merchant này"]);

            $invoice_id = $invoiceModel->insert(['status' => 'Draft', 'merchant_id' => $id]);

        }
        return $this->response->setJSON(["status"=>TRUE, "id"=>$invoice_id]);
       
    }

    public function ajax_vat_invoice($id)
    {
        if(! has_permission('Invoice', 'can_edit')) {
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa hoá đơn']);
        }

        $invoice = model(InvoiceModel::class)->find($id);

        if(! $invoice) {
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy hoá đơn này']);
        }

        if(! $invoice->tax_issued || empty($invoice->tax_issued_id)) {
            return $this->response->setJSON(['status' => false, 'message' => 'Hoá đơn chưa được phát hành']);
        }

        $action = $this->request->getGet('action');

        try {
            $cache = Services::cache();
            $cacheKey = "vat_invoice_{$invoice->id}";
            $cacheTime = 60 * 60 * 24;

            $result = $cache->get($cacheKey);

            if ($result === null) {
                $sInvoice = (new SInvoice())->setConfig(config(SubscriptionSInvoice::class));

                $result = $sInvoice->getInvoiceFile(
                    $invoice->tax_issued_id,
                    null,
                    'PDF',
                    $invoice->status === 'Paid'
                );

                if (empty($result['fileName']) || empty($result['fileToBytes'])) {
                    throw new Exception('Không thể tải file hóa đơn.');
                }

                $cache->save($cacheKey, $result, $cacheTime);
            }

            if ($action === 'preview') {
                return $this->response->setJSON($result);
            }

            return $this->response->download($result['fileName'], base64_decode($result['fileToBytes']));
        } catch (Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Có lỗi xảy ra khi tải hóa đơn: ' . $e->getMessage(),
            ]);
        }
    }

    public function ajax_item_add() {

        if(!has_permission('Invoice', 'can_edit'))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không có quyền sửa hoá đơn"));

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'invoice_id' => ['label' => 'Invoice ID', "rules" => "required|is_natural"],
            'type' => ['label' => 'type', "rules" => "required|in_list[Product,Addon,Discount,Upgrade,Other]"],
            'item_id' => ['label' => 'Product', "rules" => "permit_empty|is_natural"],
            'taxed' => ['label' => ' taxed', "rules" => "required|in_list[0,1]"],
            'tax_rate' => ['label' => 'tax_rate', "rules" => "required|is_natural"],
            'start_date' => ['label' => 'Start date', "rules" => "required|valid_date[Y-m-d]"],
            'end_date' => ['label' => 'End date', "rules" => "required|valid_date[Y-m-d]"],
            'amount' => ['label' => 'Amount', "rules" => "required|integer"],
            'description' => ['label' => 'Description', "rules" => "required"],

        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $invoice_id = $this->request->getVar('invoice_id');
        $item_id = $this->request->getVar('item_id');

        $invoiceItemModel = model(InvoiceItemModel::class);
        $invoiceModel = model(InvoiceModel::class);
        $productModel = model(ProductModel::class);

        $invoice_details = $invoiceModel->find($invoice_id);

        if(!is_object($invoice_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy hoá đơn này"));

        $product_details = $productModel->find($item_id);

        if(!$product_details)
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy product mà bạn chọn"));

         
        $data = [
            'invoice_id' => $this->request->getVar('invoice_id'),
            'type' => $this->request->getVar('type'),
            'item_id' => $this->request->getVar('item_id'),
            'start_date' => $this->request->getVar('start_date'),
            'end_date' => $this->request->getVar('end_date'),
            'amount' => $this->request->getVar('amount'),
            'taxed' => $this->request->getVar('taxed'),
            'tax_rate' => $this->request->getVar('tax_rate'),
            'description' => $this->request->getVar('description'),
        ];

        if($data['type'] == 'Product' && $data['item_id'] <= 0)
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Nếu chọn item là product thì bạn phải chọn product tương ứng"));

        $item_id = $invoiceItemModel->insert($data);

        // update invoice subtotal and total

        $items = $invoiceItemModel->where(['invoice_id' => $invoice_id])->get()->getResult();

        $new_subtotal = 0;
        $new_total = 0;
        $new_total_tax = 0;

        if($items) {
            foreach($items as $item) {
                $new_subtotal = $new_subtotal + $item->amount;

                if($item->taxed == 1)
                    $this_item_tax = $item->amount * ($item->tax_rate/100);
                else
                    $this_item_tax = 0;

                $new_total = $new_total + $item->amount + $this_item_tax;
                $new_total_tax = $new_total_tax + $this_item_tax;
            }
        }

        if($new_subtotal != $invoice_details->subtotal || $new_total != $invoice_details->total)
            $invoiceModel->set(['total' => $new_total, 'subtotal' => $new_subtotal,'tax' => $new_total_tax])->where(['id' => $invoice_id])->update();
 
        add_admin_log(array('data_id'=> $item_id, 'data_type'=>'invoice_item_add','description'=>'Thêm invoice item','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));


        return $this->response->setJSON(["status"=>TRUE]);

    }

    public function ajax_item_delete() {

        if(!has_permission('Invoice', 'can_edit'))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không có quyền sửa hoá đơn"));

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'id' => ['label' => 'ID', "rules" => "required|is_natural"],

        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $item_id = $this->request->getVar('id');

        $invoiceItemModel = model(InvoiceItemModel::class);
        $invoiceModel = model(InvoiceModel::class);

        $item_details = $invoiceItemModel->find($item_id);
        $invoice_details = $invoiceModel->find($item_details->invoice_id);

        if(!$item_details)
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy item này"));

        if(!$invoice_details)
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy hoá đơn liên quan đến item này"));

        $invoiceItemModel->where(['id' => $item_id])->delete();

        // update invoice subtotal and total
        $items = $invoiceItemModel->where(['invoice_id' => $invoice_details->id])->get()->getResult();

        $new_subtotal = 0;
        $new_total = 0;
        $new_total_tax = 0;

        if($items) {
            foreach($items as $item) {
                $new_subtotal = $new_subtotal + $item->amount;

                if($item->taxed == 1)
                    $this_item_tax = $item->amount * ($item->tax_rate/100);
                else
                    $this_item_tax = 0;

                $new_total = $new_total + $item->amount + $this_item_tax;
                $new_total_tax = $new_total_tax + $this_item_tax;
            }
        }

        if($new_subtotal != $invoice_details->subtotal || $new_total != $invoice_details->total)
            $invoiceModel->set(['total' => $new_total, 'subtotal' => $new_subtotal,'tax' => $new_total_tax])->where(['id' => $invoice_details->id])->update();

 
        add_admin_log(array('data_id'=> $invoice_details->id, 'data_type'=>'invoice_item_delete','description'=>'Xoá invoice item . ' . esc($item_id),'admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));


        return $this->response->setJSON(["status"=>TRUE]);

    }
    
    public function overdue()
    {
        if (! has_permission('Invoice', 'can_view_all')) {
            show_404();
        }

        $data = [
            'page_title' => 'Hoá đơn trễ hạn',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
        ];

        $requestData = [
            'company_id' => $this->request->getGet('company_id'),
            'product_id' => $this->request->getGet('product_id'),
            'with_inactivated' => $this->request->getGet('with_inactivated'),
            'sort' => $this->request->getGet('sort'),
        ];

        if (! $this->validateData($requestData, [
            'company_id' => 'permit_empty|is_natural',
            'product_id' => 'permit_empty|is_natural',
            'with_inactivated' => 'permit_empty|in_list[0,1]',
            'sort' => 'permit_empty|in_list[most_overdue,least_overdue,oldest,newest,amount_asc,amount_desc]',
        ])) {
            return redirect()->to(base_url('invoice/overdue'));
        }

        $invoiceModel = model(InvoiceModel::class);
        $this->queryOverdueInvoice($invoiceModel, $requestData);

        $invoices = $invoiceModel->paginate();

        $data['invoices'] = $invoices;
        $data['pager'] = $invoiceModel->pager;
        $data['filters'] = $requestData;
        $data['products'] = model(ProductModel::class)->select('id, name')->where('active', true)->findAll();
        $data['companies'] = model(InvoiceModel::class)
            ->getOverdueInvoices(false, true)
            ->groupBy('tb_autopay_invoice.company_id')
            ->select('tb_autopay_invoice.company_id, COUNT(tb_autopay_invoice.company_id) as invoices_count')
            ->findAll();

        foreach($data['invoices'] as $key => $invoice) {
            $data['invoices'][$key]->days_overdue = date_diff(date_create($invoice->paybefore), date_create(date('Y-m-d')))->format('%a');
        }

        echo view('templates/sepay/header', $data);
        echo view('invoice/overdue', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function suspendcompany($id)
    {
        if (! has_permission('Company', 'can_edit')) {
            show_404();
        }

        $companyModel = model(CompanyModel::class);

        $company = $companyModel->find($id);

        if(! $company) {
            show_404();
        }

        $overdueInvoices = model(InvoiceModel::class)
            ->getOverdueInvoices(false, true)
            ->where('tb_autopay_invoice.company_id', $id)
            ->findAll();

        if (empty($overdueInvoices)) {
            show_404();
        }

        $companyModel->update($id, ['status' => 'Suspended']);
        model(CompanySubscriptionModel::class)
            ->set('status', 'Suspended')
            ->where('company_id', $id)
            ->where('status', 'Active')
            ->update();

        $companyModel->sendStatusChangeNotification($company->id, 'Suspended', $overdueInvoices);

        add_admin_log([
            'data_id' => $id,
            'data_type' => 'company_suspend',
            'description' => 'Tạm ngưng dịch vụ của công ty',
            'admin_id' => $this->admin_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);

        session()->setFlashdata('alert-success', 'Đã tạm ngưng dịch vụ của công ty');

        return redirect()->back();
    }

    public function suspendalloverdue()
    {
        if ($this->admin_details->role != 'SuperAdmin') {
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }

        $invoiceModel = model(InvoiceModel::class);
        $companyModel = model(CompanyModel::class);
        $companySubscriptionModel = model(CompanySubscriptionModel::class);

        $this->queryOverdueInvoice($invoiceModel);

        $overdueInvoices = $invoiceModel
            ->select('tb_autopay_invoice.company_id')
            ->groupBy('tb_autopay_invoice.company_id')
            ->findAll();

        $suspendedCompanies = [];

        foreach ($overdueInvoices as $invoice) {
            $companyId = $invoice->company_id;
            $company = $companyModel->find($companyId);

            if (! $company || $company->status === 'Suspended') {
                continue;
            }

            $companyOverdueInvoices = $invoiceModel
                ->getOverdueInvoices(false, true)
                ->where('tb_autopay_invoice.company_id', $companyId)
                ->findAll();

            if (empty($companyOverdueInvoices)) {
                continue;
            }

            $companyModel->update($companyId, ['status' => 'Suspended']);

            $companySubscriptionModel
                ->set('status', 'Suspended')
                ->where('company_id', $companyId)
                ->where('status', 'Active')
                ->update();

            $companyModel->sendStatusChangeNotification($companyId, 'Suspended', $companyOverdueInvoices);

            add_admin_log([
                'data_id' => $companyId,
                'data_type' => 'company_suspend_bulk',
                'description' => 'Tạm ngưng dịch vụ của công ty (qua chức năng khóa hàng loạt)',
                'admin_id' => $this->admin_details->id,
                'ip' => $this->request->getIPAddress(),
                'user_agent' => $this->request->getUserAgent()->getAgentString(),
                'status' => 'Success',
            ]);

            $suspendedCompanies[] = $companyId;
        }

        session()->setFlashdata('alert-success', 'Đã tạm ngưng dịch vụ của ' . count($suspendedCompanies) . ' công ty');

        return redirect()->to(base_url('invoice/overdue'));
    }

    public function overdue_stats()
    {
        if (! has_permission('Invoice', 'can_view_all')) {
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền xem']);
        }

        $invoiceModel = model(InvoiceModel::class);
        $this->queryOverdueInvoice($invoiceModel, $this->request->getGet());

        $stats = $invoiceModel
            ->select('COUNT(*) as total, SUM(total) as total_overdue')
            ->first();

        return $this->response->setJSON([
            'status' => true,
            'data' => [
                'total' => number_format($stats->total),
                'total_overdue' => number_format($stats->total_overdue) . 'đ',
            ],
        ]);   
    }

    public function ajax_delete($id = '')
    {
        if (! has_permission('Invoice', 'can_delete')) {
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }

        if (empty($id)) {
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy hoá đơn này']);
        }

        $invoiceModel = model(InvoiceModel::class);
        $invoice = $invoiceModel->find($id);

        if (! $invoice) {
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy hoá đơn này']);
        }

        $invoiceModel->delete($id);
        model(InvoiceItemModel::class)->where('invoice_id', $id)->delete();

        add_admin_log([
            'data_id' => $id,
            'data_type' => 'invoice_delete',
            'description' => 'Xoá hoá đơn',
            'admin_id' => $this->admin_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);

        set_alert('success', 'Đã xoá hoá đơn');

        return $this->response->setJSON(['status' => true]);
    }

    protected function queryOverdueInvoice(&$model, $data = [])
    {
        $baseQuery = $model->getOverdueInvoices(false, ! ($data['with_inactivated'] ?? false));

        if (isset($data['company_id']) && $data['company_id']) {
            $baseQuery->where('tb_autopay_invoice.company_id', $data['company_id']);
        }

        if (isset($data['product_id']) && $data['product_id']) {
            $baseQuery->where('tb_autopay_company_subscription.plan_id', $data['product_id']);
        }

        $data['sort'] = $data['sort'] ?? 'most_overdue';

        switch ($data['sort']) {
            case 'least_overdue':
                $baseQuery->orderBy('tb_autopay_invoice.paybefore', 'DESC');
                break;
            case 'oldest':
                $baseQuery->orderBy('tb_autopay_invoice.created_at', 'ASC');
                break;
            case 'newest':
                $baseQuery->orderBy('tb_autopay_invoice.created_at', 'DESC');
                break;
            case 'amount_asc':
                $baseQuery->orderBy('tb_autopay_invoice.total', 'ASC');
                break;
            case 'amount_desc':
                $baseQuery->orderBy('tb_autopay_invoice.total', 'DESC');
                break;
            default:
                $baseQuery->orderBy('tb_autopay_invoice.paybefore', 'ASC');
                break;
            }
    }
}
