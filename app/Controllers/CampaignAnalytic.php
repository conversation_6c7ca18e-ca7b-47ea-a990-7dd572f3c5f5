<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\TrackingModel;

class CampaignAnalytic extends BaseController
{
    public function index()
    {
        if (! has_permission('CampaignAnalytic', 'can_view_all')) {
            show_404();
        }

        $model = model(TrackingModel::class);

        $data = [
            'page_title' => 'Campaign Analytics',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
            'sources' => $model->getDistinct('utm_source'),
            'mediums' => $model->getDistinct('utm_medium'),
            'campaigns' => $model->getDistinct('utm_campaign'),
            'contents' => $model->getDistinct('utm_content'),
            'terms' => $model->getDistinct('utm_term'),
        ];

        echo view('templates/sepay/header', $data);
        echo view('campaign-analytics/index', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajaxList()
    {
        if (! has_permission('CampaignAnalytic', 'can_view_all')) {
            show_404();
        }

        helper(['url', 'text']);

        $trackingModel = model(TrackingModel::class);

        $data = [];

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($trackingModel->getDatatables() as $row) {
            $no++;

            $referer = esc($row->referer);

            $data[] = [
                $no,
                anchor(base_url("company/details/{$row->company_id}"), $row->company_name),
                esc($row->utm_source),
                esc($row->utm_medium),
                esc($row->utm_campaign),
                esc($row->utm_content),
                esc($row->utm_term),
                $referer ? anchor($referer, mb_strimwidth($referer, 0, 50, '...'), ['target' => '_blank', 'rel' => 'noopener noreferrer', 'title' => $referer]) : '-',
                date('d/m/Y H:i:s', strtotime($row->created_at)),
            ];
        }

        $output = [
            'draw' => $draw,
            'recordsTotal' => $trackingModel->countAll(),
            'recordsFiltered' => $trackingModel->countFiltered(),
            'data' => $data,
        ];

        return $this->response->setJSON($output);
    }

    public function ajaxSourceStats()
    {
        if (! has_permission('CampaignAnalytic', 'can_view_all')) {
            show_404();
        }

        return $this->response->setJSON(
            model(TrackingModel::class)->getGroupedStatsBySource(
                $this->request->getGet('start_date'),
                $this->request->getGet('end_date')
            )
        );
    }
}
