<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\UnsubscribeModel;
use CodeIgniter\Database\RawSql;
use App\Models\NotificationModel;
use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;
use App\Models\FcmDeviceTokenModel;
use App\Models\NotificationUserModel;
use App\Models\PopupNotificationModel;
use App\Actions\Notification\PushNotificationFcmByTokenQueueAction;

class NotificationCenter extends BaseController
{
    use ResponseTrait;

    private function getNotificableTypeOptions()
    {
        return [['label' => 'Không phân loại', 'value' => ''], ['label' => 'Ưu đãi', 'value' => 'promotion']];
    }

    private function getUserFilterList($notificationId)
    {
        $db = db_connect();
        $apiBanks = model(BankModel::class)->where('active', 1)->whereIn('code', ['MB', 'KLB', 'OCB', 'MSB', 'BIDV', 'ACB', 'TPB'])->get()->getResult();

        $notificationId = $db->escape($notificationId);

        $internalTestingEmailList = config(\Config\Notification::class)->testingEmailWhitelist ?? [];

        return array_filter([
            count($internalTestingEmailList) ? 
            [
                'label' => 'Tất cả người dùng trong danh sách kiểm thử nội bộ',
                'name' => 'internal-testing-users',
                'raw_sql' => 'select tb_autopay_company_user.user_id as id, tb_autopay_user.email from tb_autopay_company_user 
                    join tb_autopay_company on tb_autopay_company.id = tb_autopay_company_user.company_id
                    join tb_autopay_user on tb_autopay_user.id = tb_autopay_company_user.user_id  
                    where tb_autopay_user.deleted_at is null 
                        and tb_autopay_user.active = 1 
                        and tb_autopay_company.active = 1 
                        and tb_autopay_company.deleted_at is null
                        and tb_autopay_user.email IN (' . implode(', ', array_map(function($email) use ($db) { return $db->escape($email); }, $internalTestingEmailList)) . ')
                        and not exists (select id from tb_autopay_notification_user where tb_autopay_notification_user.user_id = tb_autopay_user.id and tb_autopay_notification_user.notification_id = ' . $notificationId . ')
                '
            ] : false,
            [
                'label' => 'Tất cả người dùng đang hoạt động',
                'name' => 'active-users',
                'raw_sql' => 'select tb_autopay_company_user.user_id as id, tb_autopay_user.email from tb_autopay_company_user 
                    join tb_autopay_company on tb_autopay_company.id = tb_autopay_company_user.company_id
                    join tb_autopay_user on tb_autopay_user.id = tb_autopay_company_user.user_id  
                    where tb_autopay_user.deleted_at is null 
                        and tb_autopay_user.active = 1 
                        and tb_autopay_company.active = 1 
                        and tb_autopay_company.deleted_at is null
                        and not exists (select id from tb_autopay_notification_user where tb_autopay_notification_user.user_id = tb_autopay_user.id and tb_autopay_notification_user.notification_id = ' . $notificationId . ')
                '
            ],
            [
                'label' => 'Người dùng thuộc công ty đang sử dụng gói FREE',
                'name' => 'free-plan-users',
                'raw_sql' => 'select tb_autopay_company_user.user_id as id, tb_autopay_user.email from tb_autopay_company_user 
                    join tb_autopay_company on tb_autopay_company.id = tb_autopay_company_user.company_id
                    join tb_autopay_user on tb_autopay_user.id = tb_autopay_company_user.user_id  
                    join tb_autopay_company_subscription on tb_autopay_company_subscription.company_id = tb_autopay_company.id
                    join tb_autopay_product on tb_autopay_product.id = tb_autopay_company_subscription.plan_id
                    where tb_autopay_company_subscription.billing_cycle = "free"
                        and tb_autopay_product.billing_type = "Free"
                        and tb_autopay_user.deleted_at is null 
                        and tb_autopay_user.active = 1 
                        and tb_autopay_company.active = 1 
                        and tb_autopay_company.deleted_at is null
                        and not exists (select id from tb_autopay_notification_user where tb_autopay_notification_user.user_id = tb_autopay_user.id and tb_autopay_notification_user.notification_id = ' . $notificationId . ')
                '
            ],
            [
                'label' => 'Người dùng thuộc công ty đang sử dụng gói có hỗ trợ SMS',
                'name' => 'sms-plan-users',
                'raw_sql' => 'select tb_autopay_company_user.user_id as id, tb_autopay_user.email from tb_autopay_company_user 
                    join tb_autopay_company on tb_autopay_company.id = tb_autopay_company_user.company_id
                    join tb_autopay_user on tb_autopay_user.id = tb_autopay_company_user.user_id  
                    join tb_autopay_company_subscription on tb_autopay_company_subscription.company_id = tb_autopay_company.id
                    join tb_autopay_product on tb_autopay_product.id = tb_autopay_company_subscription.plan_id
                    where tb_autopay_product.sms_allow = 1
                        and tb_autopay_user.deleted_at is null 
                        and tb_autopay_user.active = 1 
                        and tb_autopay_company.active = 1 
                        and tb_autopay_company.deleted_at is null
                        and not exists (select id from tb_autopay_notification_user where tb_autopay_notification_user.user_id = tb_autopay_user.id and tb_autopay_notification_user.notification_id = ' . $notificationId . ')
                '
            ],
            ...array_map(function($bank) use ($db, $notificationId) {
                $bank->id = $db->escape($bank->id);

                return [
                    'label' => 'Người dùng thuộc công ty có liên kết ngân hàng ' . $bank->short_name . ' qua API',
                    'name' => 'api-bank-users-' . $bank->id,
                    'raw_sql' => 'select tb_autopay_company_user.user_id as id, tb_autopay_user.email from tb_autopay_company_user 
                        join tb_autopay_company on tb_autopay_company.id = tb_autopay_company_user.company_id
                        join tb_autopay_user on tb_autopay_user.id = tb_autopay_company_user.user_id
                        where exists (select id from tb_autopay_bank_account where tb_autopay_bank_account.company_id = tb_autopay_company_user.company_id and tb_autopay_bank_account.bank_id = ' . $bank->id . ')
                            and tb_autopay_user.deleted_at is null 
                            and tb_autopay_user.active = 1 
                            and tb_autopay_company.active = 1 
                            and tb_autopay_company.deleted_at is null
                            and not exists (select id from tb_autopay_notification_user where tb_autopay_notification_user.user_id = tb_autopay_user.id and tb_autopay_notification_user.notification_id = ' . $notificationId . ')
                    '
                ];
            }, $apiBanks)
        ]);
    }

    public function index()
    {
        $data = [
            'page_title' => 'Thông báo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if (!has_permission('Notification', 'can_view_all'))
            show_404();

        if (has_permission('Notification', 'can_edit')) {
            $data['popup_notification_list'] = model(PopupNotificationModel::class)
                ->join('tb_autopay_notification', 'tb_autopay_notification.id = tb_autopay_popup_notification.notification_id')
                ->where(['tb_autopay_notification.hidden' => 0])
                ->where(['tb_autopay_notification.sent_at !=' => 'null'])
                ->groupStart()
                    ->where(['tb_autopay_popup_notification.hidden_at >' => date('Y-m-d H:i:s')])
                    ->orWhere(['tb_autopay_popup_notification.hidden_at' => null])
                ->groupEnd()
                ->orderBy('tb_autopay_popup_notification.pos', 'asc')
                ->orderBy('tb_autopay_notification.sent_at', 'desc')
                ->get()->getResult();
        }

        echo view('templates/sepay/header',$data);
        echo view('notification-center/index',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function ajax_sort_popup()
    {
        if (!has_permission('Notification', 'can_add'))
            return $this->failNotFound();

        $posList = $this->request->getVar('pos');

        if (!is_array($posList) || count($posList) < 1) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.']);
        }

        $popupNotificationModel = model(PopupNotificationModel::class);

        $availabelPopupNotificationList = $popupNotificationModel->join('tb_autopay_notification', 'tb_autopay_notification.id = tb_autopay_popup_notification.notification_id')
            ->where(['tb_autopay_notification.hidden' => 0])
            ->where(['tb_autopay_notification.sent_at !=' => 'null'])
            ->groupStart()
                ->where(['tb_autopay_popup_notification.hidden_at >' => date('Y-m-d H:i:s')])
                ->orWhere(['tb_autopay_popup_notification.hidden_at' => null])
            ->groupEnd()
            ->orderBy('tb_autopay_popup_notification.pos', 'asc')
            ->get()->getResult();

        $incomingPopupNotificationCount = $popupNotificationModel->whereIn('notification_id', $posList)->countAllResults();

        if (count($availabelPopupNotificationList) != $incomingPopupNotificationCount) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.']);
        }

        foreach ($posList as $pos => $notificationId) {
            $popupNotificationModel->where('notification_id', $notificationId)->set(['pos' => $pos + 1])->update();
        }

        return $this->respond(['status' => true]);
    }

    public function ajax_list()
    {
        if (!has_permission('Notification', 'can_view_all'))
            show_404();

        $notificationModel = model(NotificationModel::class);
        $emailQueueModel = model(EmailQueueModel::class);

        $rows = $notificationModel->getDatatablesForAdmin();

        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
 
        foreach ($rows as $result) {

            $no++;
            $row = array();

            $row[] = $no;
            $row[] = esc($result->id);

            if ($result->channels) {
                if (strtotime($result->sent_at) > 0) {
                    $row[] = "<a href='". base_url('notificationCenter/details/'. $result->id)."'>". strip_tags($result->title) . "</a>";
                    $row[] = '<span class="badge bg-primary">Đã gửi</span>';
                } else {
                    $row[] = "<a href='". base_url('notificationCenter/step2/'. $result->id)."'>". strip_tags($result->title) . "</a>";
                    $row[] = '<span class="badge bg-secondary">Chưa gửi</span>';
                }
            } else {
                $row[] = "<a href='#' class='link-secondary d-flex align-items-center' style='text-decoration: none;'><span class='badge bg-light text-dark me-1'>Legacy</span>". strip_tags($result->title) . "</a>";
                $row[] = ''; 
            }

            $row[] = $result->hidden ? '<span class="badge bg-secondary">Đã ẩn</span>' : '<span class="badge bg-primary">Hiển thị</span>';

            $row[] = esc($result->created_at);
 

            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $notificationModel->countAllForAdmin(),
            "recordsFiltered" => $notificationModel->countFilteredForAdmin(),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }

    public function step1($id = '')
    {
        if (!has_permission('Notification', 'can_add'))
            show_404();

        $notificationDetails = null;

        if (is_numeric($id)) {
            $notificationModel = model(NotificationModel::class);

            $notificationDetails = $notificationModel
                ->select(['tb_autopay_notification.*', 'tb_autopay_popup_notification.thumbnail_url', 'tb_autopay_popup_notification.cta_text', 'tb_autopay_popup_notification.cta_url', 'tb_autopay_popup_notification.hidden_at', 'tb_autopay_popup_notification.pos'])
                ->join('tb_autopay_popup_notification', 'tb_autopay_popup_notification.notification_id = tb_autopay_notification.id', 'left')
                ->where(['tb_autopay_notification.id' => $id, 'tb_autopay_notification.sent_at' => null])->first();
        }


        $data = [
            'page_title' => 'Tạo thông báo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
            'notification_details' => $notificationDetails,
            'notificable_type_options' => $this->getNotificableTypeOptions()
        ];

        echo view('templates/sepay/header', $data);
        echo view('notification-center/step1', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajax_step1()
    {
        if (! has_permission('Notification', 'can_add'))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền tạo thông báo']);

        $data = [
            'id' => trim($this->request->getVar('id')),
            'title' => trim($this->request->getVar('title')),
            'description' => trim($this->request->getVar('description')),
            'body' => trim($this->request->getVar('body')),
            'notificable_type' => trim(htmlspecialchars($this->request->getVar('notificable_type')))   
        ];

        $rules = [
            'title' => [
                'rules' => ['required', 'max_length[100]']
            ],
            'description' => [
                'rules' => ['permit_empty', 'max_length[255]']
            ],
            'body' => [
                'rules' => ['required']
            ],
            'notificable_type' => [
                'rules' => ['permit_empty', 'in_list[promotion]']
            ]
        ];

        if (! $this->validateData($data, $rules)) return $this->fail($this->validator->getErrors());

        $notificationModel = model(NotificationModel::class);
        
        if (! is_numeric($data['id'])) {
            $data['channels'] = '[]';
            $notificationId = $notificationModel->insert($data);
    
            if (!$notificationId) return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại.']);

            add_admin_log(array('data_id'=> $notificationId, 'data_type'=>'notification_add','description'=>'Tạo thông báo','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
        } else {
            $notificationDetails = $notificationModel->where('id', $data['id'])->first();

            if (! $notificationDetails) return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.']);

            $safeNotificationData = [
                'title' => $data['title'],
                'description' => $data['description'],
                'body' => $data['body'],
                'notificable_type' => $data['notificable_type']
            ];

            $updated = $notificationModel->where('id', $data['id'])->set($safeNotificationData)->update();

            if (! $updated) return $this->respond(['status' => true, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại.']);
           
            $notificationId = $notificationDetails->id;

            add_admin_log(array('data_id'=> $notificationId, 'data_type'=>'notification_edit','description'=>'Sửa thông báo','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
        }

        return $this->respond(['status' => true, 'id' => $notificationId]); 
    }

    public function step2($id = '')
    {
        $id = trim($id);

        if (! is_numeric($id)) 
            return show_404();

        if (!has_permission('Notification', 'can_add'))
            return show_404();

        $config = config(\Config\Notification::class);
        $notificationModel = model(NotificationModel::class);
        $fcmDeviceTokenModel = model(FcmDeviceTokenModel::class);
        $userModel = model(UserModel::class);

        $notificationDetails = $notificationModel
            ->select(['tb_autopay_notification.*', 'tb_autopay_popup_notification.thumbnail_url', 'tb_autopay_popup_notification.cta_text', 'tb_autopay_popup_notification.cta_url', 'tb_autopay_popup_notification.hidden_at', 'tb_autopay_popup_notification.pos'])
            ->join('tb_autopay_popup_notification', 'tb_autopay_popup_notification.notification_id = tb_autopay_notification.id', 'left')
            ->where(['tb_autopay_notification.id' => $id, 'tb_autopay_notification.sent_at' => null])->first();

        if (! $notificationDetails) 
            return show_404();

        $notificationDetails->channels = json_decode($notificationDetails->channels, true) ?? [];

        $userByAdminDetails = $userModel->where(['email' => $this->admin_details->email])->first();

        $mobileDeviceTokens = [];

        if ($userByAdminDetails) {
            $mobileDeviceTokens = $fcmDeviceTokenModel->where(['user_id' => $userByAdminDetails->id, 'is_browser' => 0, 'active' => 1])->get()->getResult();
        }

        $data = [
            'page_title' => 'Tạo thông báo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
            'notification_details' => $notificationDetails,
            'email_in_testing_whitelist' => in_array($this->admin_details->email, $config->testingEmailWhitelist ?? []),
            'mobile_device_tokens' => $mobileDeviceTokens
        ];

        echo view('templates/sepay/header', $data);
        echo view('notification-center/step2', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajax_step2($id = '')
    {
        $id = trim($id);

        if (! is_numeric($id)) 
            return $this->failNotFound();

        if (!has_permission('Notification', 'can_add'))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền tạo thông báo']);

        $notificationModel = model(NotificationModel::class);

        $notificationDetails = $notificationModel->where(['id' => $id, 'sent_at' => null])->first();

        if (! $notificationDetails) 
            return $this->failNotFound();

        $data = [
            'channels' => $this->request->getVar('channels'),
            'popup_cta_url' => trim($this->request->getVar('popup_cta_url')),
            'popup_cta_text' => trim($this->request->getVar('popup_cta_text')),
            'popup_thumbnail_url' => trim($this->request->getVar('popup_thumbnail_url')),
            'popup_hidden_at' => trim($this->request->getVar('popup_hidden_at'))
        ];

        $rules = [
            'channels.*' => ['rules' => ['in_list[Mail,Mobile,Desktop,Popup]']],
            'popup_thumbnail_url' => ['rules' => ['permit_empty', 'valid_url', 'max_length[255]']],
            'popup_cta_url' => ['rules' => ['permit_empty', 'valid_url', 'max_length[255]']],
            'popup_cta_text' => ['rules' => ['permit_empty', 'min_length[1]', 'max_length[255]']],
            'popup_hidden_at' => ['rules' => ['permit_empty', 'valid_date']]
        ];

        if (!is_array($data['channels']) || count($data['channels']) == 0) {
            return $this->respond(['status' => false, 'message' => 'Vui lòng chọn một kênh gửi thông báo']);
        }

        $hasPopupChannel = in_array('Popup', $data['channels']);

        if (in_array('Popup', $data['channels'])) {
            if ($data['popup_cta_url'] != '' && $data['popup_cta_text'] == '') {
                return $this->fail(['popup_cta_text' => 'Trường nhãn nút hành động là bắt buộc']);            
            }

            if ($data['popup_cta_url'] == '' && $data['popup_cta_text'] != '') {
                return $this->fail(['popup_cta_url' => 'Trường URL nút hành động là bắt buộc']);            
            }

            if ($data['popup_hidden_at'] && strtotime($data['popup_hidden_at']) < strtotime(date('Y-m-d'))) {
                return $this->fail(['popup_hidden_at' => 'Trường ngày gỡ xuống không được ở quá khứ']);            
            }
        }

        if (! $this->validateData($data, $rules)) return $this->fail($this->validator->getErrors());

        if ($hasPopupChannel) {
            $popupNotificationModel = model(PopupNotificationModel::class);
            $popupNotificationDetails = $popupNotificationModel->where('notification_id', $notificationDetails->id)->first();
            
            $safePopupNotificationData = [
                'notification_id' => $notificationDetails->id,
                'thumbnail_url' => $data['popup_thumbnail_url'],
                'cta_url' => $data['popup_cta_url'], 
                'cta_text' => $data['popup_cta_text'],
                'hidden_at' => $data['popup_hidden_at'] ? $data['popup_hidden_at'] . ' 23:59:59' : null, 
            ];

            if (! $popupNotificationDetails) {
                $popupNotificationModel->insert($safePopupNotificationData);
            } else {
                $popupNotificationModel->where('id', $popupNotificationDetails->id)->set($safePopupNotificationData)->update();
            }
        }

        $notificationModel = model(NotificationModel::class);
        $notificationModel->where('id', $notificationDetails->id)->set(['channels' => json_encode($data['channels'])])->update();

        return $this->respond(['status' => true]);
    }

    public function previewMail($id = '')
    {
        $id = trim($id);

        if (! is_numeric($id)) return show_404();

        if (!has_permission('Notification', 'can_add'))
            return show_404();

        $notificationModel = model(NotificationModel::class);

        $notificationDetails = $notificationModel->where(['id' => $id, 'sent_at' => null])->first();

        if (! $notificationDetails) return show_404();

        echo view('notification-center/mail-template', ['body' => $notificationDetails->body]);
    }

    public function ajax_test_mail($id = '')
    {
        $id = trim($id);

        if (! is_numeric($id)) return show_404();

        if (!has_permission('Notification', 'can_add'))
            return show_404();

        $notificationModel = model(NotificationModel::class);
        $emailQueueModel = model(EmailQueueModel::class);
        
        $notificationDetails = $notificationModel->where(['id' => $id, 'sent_at' => null])->first();

        if (! $notificationDetails) return show_404();

        $inserted = $emailQueueModel->insert([
            'uuid' => md5($this->admin_details->id . $notificationDetails->id . uniqid()),
            'data_id' => $notificationDetails->id,
            'data_type' => 'notification',
            'mail_to' => $this->admin_details->email,
            'title' => $notificationDetails->title,
            'body' => $notificationDetails->body,
        ]);

        if (!$inserted) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại.']);
        }

        return $this->respond(['status' => true]);
    }

    public function ajax_test_mobile($id = '')
    {
        $id = trim($id);

        if (! is_numeric($id)) return show_404();

        if (!has_permission('Notification', 'can_add'))
            return show_404();

        $deviceTokenIds = $this->request->getVar('device_token_ids');

        $notificationModel = model(NotificationModel::class);
        $fcmDeviceTokenModel = model(FcmDeviceTokenModel::class);
        $userModel = model(UserModel::class);

        $notificationDetails = $notificationModel->where(['id' => $id, 'sent_at' => null])->first();

        if (! $notificationDetails) return show_404();

        if (! count($deviceTokenIds)) {
            return $this->respond(['status' => false, 'message' => 'Vui lòng chọn ít nhất một thiết bị di động.']);
        }

        $userByAdminDetails = $userModel->where(['email' => $this->admin_details->email])->first();

        if (! $userByAdminDetails) {
            return $this->respond(['status' => false, 'message' => 'Không có tài khoản người dùng tại my.sepay.vn với địa chỉ email' . $this->admin_details->email]);
        }

        $deviceTokens = $fcmDeviceTokenModel->whereIn('id', $deviceTokenIds)->where('user_id', $userByAdminDetails->id)->get()->getResult();

        if (! count($deviceTokens)) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.']);
        }

        foreach ($deviceTokens as $deviceToken) {
            $notification = [];
            $notification['title'] = $notificationDetails->title;
            $notification['body'] = $notificationDetails->description;
            $apnAlert = [
                'title' => $notification['title'],
                'body' => $notification['body']
            ];

            $fcmData = [
                'notification_id' => $notificationDetails->id,
                'notification_url' => rtrim(env('CLIENT_BASE_URL'), '/') . '/notification/details/' . $notificationDetails->id
            ];

            $apns = [
                'headers' => [
                    'apns-priority' => '10'
                ],
                'payload' => [
                    'aps' => [
                        'alert' => $apnAlert,
                        'category' => 'NEW_MESSAGE_CATEGORY',
                        'mutable-content' => 1,
                        'content-available' => 1,
                    ]
                ]
            ];

            $webpush = []; 

            try {
                PushNotificationFcmByTokenQueueAction::run($deviceToken->token, $notification, $fcmData, $apns, $webpush, $deviceToken->id, $notificationDetails->id, null, 0);
            } catch (Exception $e) {
                log_message('error', 'PushNotificationFcmByTokenQueueAction failed: ' . $deviceToken->token . ' - ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            }
        }

        return $this->respond(['status' => true]);
    }

    public function step3($id = '')
    {
        $id = trim($id);

        if (! is_numeric($id)) return show_404();

        if (!has_permission('Notification', 'can_add'))
            return show_404();

        $notificationModel = model(NotificationModel::class);

        $notificationDetails = $notificationModel->where(['id' => $id, 'sent_at' => null])->first();

        if (! $notificationDetails) return show_404();

        $notificationDetails->channels = json_decode($notificationDetails->channels, true) ?? [];

        if (!count($notificationDetails->channels)) {
            set_alert('error', 'Vui lòng chọn một kênh gửi thông báo');
            return redirect()->to(base_url('notificationCenter/step2/' . $id));
        }

        $data = [
            'page_title' => 'Tạo thông báo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
            'notification_details' => $notificationDetails,
            'user_filters' => $this->getUserFilterList($notificationDetails->id), 
        ];

        echo view('templates/sepay/header', $data);
        echo view('notification-center/step3', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function details($id = '')
    {
        $id = trim($id);

        if (! is_numeric($id)) return show_404();

        if (!has_permission('Notification', 'can_view_all'))
            return show_404();

        $notificationModel = model(NotificationModel::class);
        $notificationUserModel = model(NotificationUserModel::class);
        $emailQueueModel = model(EmailQueueModel::class);
        $notificationFcmLogModel = model(NotificationFcmLogModel::class);
        $popupNotificationModel = model(PopupNotificationModel::class);
        $fcmDeviceTokenModel = model(FcmDeviceTokenModel::class);

        $notificationDetails = $notificationModel->where(['id' => $id, 'sent_at != ' => null])->first();
        
        if (! $notificationDetails) return show_404();

        $notificationDetails->channels = json_decode($notificationDetails->channels, true) ?? [];
        $popupNotificationDetails = $popupNotificationModel->where(['notification_id' => $id])->first();

        $userIds = array_map(function($row) {
            return $row->user_id;
        }, $notificationUserModel->select(['user_id'])->where('notification_id', $notificationDetails->id)->get()->getResult());

        $data = [
            'page_title' => 'Thông báo',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
            'notification_details' => $notificationDetails,
            'notificable_type_options' => $this->getNotificableTypeOptions(),
            'user_filters' => $this->getUserFilterList($notificationDetails->id),
            'user_count' => $notificationUserModel->where(['notification_id' => $notificationDetails->id])->countAllResults(),
            'seen_user_count' => $notificationUserModel->where(['notification_id' => $notificationDetails->id, 'seen_at !=' => null])->countAllResults(),
            'mail_count' => $emailQueueModel->where(['data_type' => 'notification', 'data_id' => $notificationDetails->id, 'user_id !=' => 0])->countAllResults(),
            'pending_mail_count' => $emailQueueModel->where(['data_type' => 'notification', 'data_id' => $notificationDetails->id, 'status' => 'Pending', 'user_id !=' => 0])->countAllResults(),
            'failed_mail_count' => $emailQueueModel->where(['data_type' => 'notification', 'data_id' => $notificationDetails->id, 'status' => 'Failed', 'user_id !=' => 0])->countAllResults(),
            'sent_mail_count' => $emailQueueModel->where(['data_type' => 'notification', 'data_id' => $notificationDetails->id, 'status' => 'Sent', 'user_id !=' => 0, 'is_read' => 0])->countAllResults(),
            'seen_mail_count' => $emailQueueModel->where(['data_type' => 'notification', 'data_id' => $notificationDetails->id, 'status' => 'Sent', 'user_id !=' => 0, 'is_read' => 1])->countAllResults(),
            'mobile_count' => count($userIds) ? $fcmDeviceTokenModel->whereIn('user_id', $userIds)->where(['is_browser' => 0, 'user_id != ' => 0])->countAllResults() : 0,
            'sent_mobile_count' => $notificationFcmLogModel->where(['notification_id' => $notificationDetails->id, 'is_browser' => 0, 'status' => 'success'])->where(['user_id !=' => 0])->countAllResults(),
            'invalid_mobile_count' => $notificationFcmLogModel->where(['notification_id' => $notificationDetails->id, 'is_browser' => 0, 'status' => 'invalid'])->where(['user_id !=' => 0])->countAllResults(),
            'desktop_count' => count($userIds) ? $fcmDeviceTokenModel->whereIn('user_id', $userIds)->where(['is_browser' => 1])->countAllResults() : 0,
            'sent_desktop_count' => $notificationFcmLogModel->where(['notification_id' => $notificationDetails->id, 'is_browser' => 1, 'status' => 'success'])->countAllResults(),
            'invalid_desktop_count' => $notificationFcmLogModel->where(['notification_id' => $notificationDetails->id, 'is_browser' => 1, 'status' => 'invalid'])->countAllResults(),
            'popup_visible_days' => $popupNotificationDetails && strtotime($popupNotificationDetails->hidden_at) > time()
                ? ($popupNotificationDetails->hidden_at ? round((strtotime($popupNotificationDetails->hidden_at) - time()) / (60 * 60 * 24), 2) : 0) 
                : null
        ];

        $data['pending_mobile_count'] = $data['mobile_count'] - $data['sent_mobile_count'] - $data['invalid_mobile_count'];
        $data['pending_desktop_count'] = $data['desktop_count'] - $data['sent_desktop_count'] - $data['invalid_desktop_count'];
        $data['popup_notification_details'] = $popupNotificationDetails;

        echo view('templates/sepay/header', $data);
        echo view('notification-center/details', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajax_edit($id = '')
    {
        $id = trim($id);

        if (! is_numeric($id)) return $this->failNotFound();

        if (!has_permission('Notification', 'can_edit'))
            return $this->failNotFound();

        $notificationModel = model(NotificationModel::class);
        $popupNotificationModel = model(PopupNotificationModel::class);

        $notificationDetails = $notificationModel->where(['id' => $id, 'sent_at != ' => null])->first();
        
        if (! $notificationDetails) return $this->failNotFound();

        $notificationDetails->channels = json_decode($notificationDetails->channels, true) ?? [];
        $popupNotificationDetails = $popupNotificationModel->where(['notification_id' => $id])->first();

        $data = [
            'hidden' =>  $this->request->getVar('hidden'),
            'title' => trim($this->request->getVar('title')),
            'description' => trim($this->request->getVar('description')),
            'body' => trim($this->request->getVar('body')),
            'notificable_type' => trim(htmlspecialchars($this->request->getVar('notificable_type'))),
            'popup_cta_url' => trim($this->request->getVar('popup_cta_url')),
            'popup_cta_text' => trim($this->request->getVar('popup_cta_text')),
            'popup_thumbnail_url' => trim($this->request->getVar('popup_thumbnail_url')),
            'popup_hidden_at' => trim($this->request->getVar('popup_hidden_at'))
        ];

        $rules = [
            'title' => ['rules' => ['required', 'max_length[100]']],
            'description' => ['rules' => ['permit_empty', 'max_length[255]']],
            'body' => ['rules' => ['required']],
            'notificable_type' => ['rules' => ['permit_empty', 'in_list[promotion]']],
            'popup_thumbnail_url' => ['rules' => ['permit_empty', 'valid_url', 'max_length[255]']],
            'popup_cta_url' => ['rules' => ['permit_empty', 'valid_url', 'max_length[255]']],
            'popup_cta_text' => ['rules' => ['permit_empty', 'min_length[1]', 'max_length[255]']],
            'popup_hidden_at' => ['rules' => ['permit_empty', 'valid_date']]
        ];

        if (! $this->validateData($data, $rules)) return $this->fail($this->validator->getErrors());

        if ($popupNotificationDetails) {
            if ($data['popup_cta_url'] != '' && $data['popup_cta_text'] == '') {
                return $this->fail(['popup_cta_text' => 'Trường nhãn nút hành động là bắt buộc']);            
            }

            if ($data['popup_cta_url'] == '' && $data['popup_cta_text'] != '') {
                return $this->fail(['popup_cta_url' => 'Trường URL nút hành động là bắt buộc']);            
            }

            if ($data['popup_hidden_at'] && $data['popup_hidden_at'] != $popupNotificationDetails->hidden_at && strtotime($data['popup_hidden_at']) < strtotime(date('Y-m-d'))) {
                return $this->fail(['popup_hidden_at' => 'Trường ngày gỡ xuống không được ở quá khứ']);            
            }

            $safePopupNotificationData = [
                'notification_id' => $notificationDetails->id,
                'thumbnail_url' => $data['popup_thumbnail_url'],
                'cta_url' => $data['popup_cta_url'], 
                'cta_text' => $data['popup_cta_text'],
                'hidden_at' => $data['popup_hidden_at'] ? $data['popup_hidden_at'] . ' 23:59:59' : null, 
            ];
          
            $popupNotificationModel->where('id', $popupNotificationDetails->id)->set($safePopupNotificationData)->update();
        }

        $safeNotificationData = [
            'hidden' => $data['hidden'] == 'on' ? 1 : 0,
            'title' => $data['title'],
            'description' => $data['description'],
            'body' => $data['body'],
            'notificable_type' => $data['notificable_type']
        ];

        set_alert('success', 'Lưu thay đổi thành công');

        $notificationModel->where('id', $notificationDetails->id)->set($safeNotificationData)->update();

        return $this->respond(['status' => true]);
    }

    public function ajax_step3($id = '')
    {
        $id = trim($id);

        if (! is_numeric($id)) return show_404();

        if (!has_permission('Notification', 'can_add'))
            return show_404();

        $data = [
            'user_filter' => trim($this->request->getVar('user_filter'))
        ];

        $rules = [
            'user_filter' => ['rules' => ['required', 'in_list[' . implode(',', array_map(function($item) { return $item['name']; }, $this->getUserFilterList($id)))  . ']']] 
        ];

        if (! $this->validateData($data, $rules)) return $this->fail($this->validator->getErrors());

        $notificationModel = model(NotificationModel::class);
        $notificationUserModel = model(NotificationUserModel::class);
        $emailQueueModel = model(EmailQueueModel::class);
        $fcmDeviceTokenModel = model(FcmDeviceTokenModel::class);
        $userModel = model(UserModel::class);
        $unsubscribeModel = model(UnsubscribeModel::class);

        $notificationDetails = $notificationModel->where(['id' => $id, 'sent_at' => null])->first();

        if (! $notificationDetails) return show_404();
        
        $notificationDetails->channels = json_decode($notificationDetails->channels, true) ?? [];

        $users = $this->getFilteredUserList($data['user_filter'], $notificationDetails->id);

        if (! count($users)) {
            return $this->respond(['status' => false, 'message' => 'Không có đối tượng nào để tiến hành gửi thông báo.']);
        }

        $failedUsers = [];

        do {
            $sendingUsers = count($failedUsers) > 0 ? $failedUsers : $users;
            
            foreach ($sendingUsers as $user) {
                $notificationUserId = $notificationUserModel->insert(['notification_id' => $notificationDetails->id, 'user_id' => $user->id]);

                if (!$notificationUserId) {
                    $failedUsers[] = $user;
                    continue;
                }
                
                if (in_array('Mail', $notificationDetails->channels)) {
                    $unsubscribed = $unsubscribeModel->where(['email' => $user->email])->first();

                    // Skip send mail if user unsubscribed and notification is not promotion type
                    if ($unsubscribed && $notificationDetails->notificable_type != 'promotion') continue;

                    $emailQueueModel->insert([
                        'uuid' => md5($user->id . $notificationDetails->id . uniqid()),
                        'user_id' => $user->id,
                        'data_id' => $notificationDetails->id,
                        'data_type' => 'notification',
                        'mail_to' => $user->email,
                        'title' => $notificationDetails->title,
                        'body' => $notificationDetails->body,
                    ]);
                }

                $deviceTokens = [];

                if (in_array('Mobile', $notificationDetails->channels)) {
                    $deviceTokens = array_merge($deviceTokens, $fcmDeviceTokenModel->where(['user_id' => $user->id, 'is_browser' => 0])->get()->getResult());
                }

                if (in_array('Desktop', $notificationDetails->channels)) {
                    $deviceTokens = array_merge($deviceTokens, $fcmDeviceTokenModel->where(['user_id' => $user->id, 'is_browser' => 1])->get()->getResult());
                }

                foreach ($deviceTokens as $deviceToken) {
                    $notification = [];
                    $notification['title'] = $notificationDetails->title;
                    $notification['body'] = $notificationDetails->description;
                    $apnAlert = [
                        'title' => $notification['title'],
                        'body' => $notification['body']
                    ];

                    $fcmData = [
                        'notification_id' => $notificationDetails->id,
                        'notification_url' => rtrim(env('CLIENT_BASE_URL'), '/') . '/notification/details/' . $notificationDetails->id
                    ];

                    $apns = [
                        'headers' => [
                            'apns-priority' => '10'
                        ],
                        'payload' => [
                            'aps' => [
                                'alert' => $apnAlert,
                                'category' => 'NEW_MESSAGE_CATEGORY',
                                'mutable-content' => 1,
                                'content-available' => 1,
                            ]
                        ]
                    ];

                    $webpush = [
                    ]; 

                    try {
                        PushNotificationFcmByTokenQueueAction::run($deviceToken->token, $notification, $fcmData, $apns, $webpush, $deviceToken->id, $notificationDetails->id, $deviceToken->user_id, $deviceToken->is_browser);
                    } catch (Exception $e) {
                        $failedDeviceTokens[] = $deviceToken->token;
                        log_message('error', 'PushNotificationFcmByTokenQueueAction failed: ' . $deviceToken->token . ' - ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
                    }
                }
            }
        } while (count($failedUsers) > 0);

        $notificationModel->where('id', $notificationDetails->id)->set(['user_filter' => $data['user_filter'], 'sent_at' => date('Y-m-d H:i:s')])->update();

        set_alert('success', 'Đã gửi thông báo đến người dùng');
        
        return $this->respond(['status' => true]); 
    }

    public function ajax_statistic($id = '', $name = '')
    {
        $id = trim($id);
        $name = trim($name);

        if (! is_numeric($id)) return $this->failNotFound();
        if (! is_string($name) || $name == '') return $this->failNotFound();

        if (! has_permission('Notification', 'can_add')) return $this->failNotFound();

        $notificationModel = model(NotificationModel::class);
        $notificationDetails = $notificationModel->where(['id' => $id, 'sent_at' => null])->first();

        if (! $notificationDetails) return $this->failNotFound();
        
        $notificationDetails->channels = json_decode($notificationDetails->channels, true) ?? [];
        
        $users = $this->getFilteredUserList($name, $id); 

        $db = db_connect();
        $rawSql = (string) $db->getLastQuery();
        
        if (!count($users)) {
            return $this->respond([
                'user' => 0,
                'mail' => 0,
                'mobile' => 0,
                'desktop' => 0,
                'sendable' => false,
                'raw_sql' => $this->admin_details->role === 'Admin' ? trim($rawSql) . ';' : null
            ]);
        }

        $userIds = array_map(function($row) {
            return $row->id;
        }, $users);

        $fcmDeviceTokenModel = model(FcmDeviceTokenModel::class);

        $mobileCount = $fcmDeviceTokenModel->whereIn('user_id', $userIds)->where(['is_browser' => 0, 'active' => 1])->countAllResults();
        $desktopCount = $fcmDeviceTokenModel->whereIn('user_id', $userIds)->where(['is_browser' => 1, 'active' => 1])->countAllResults();

        return $this->respond([
            'user' => count($users),
            'mail' => count($users),
            'mobile' => $mobileCount,
            'desktop' => $desktopCount,
            'sendable' => count($users) > 0,
            'raw_sql' => $this->admin_details->role === 'Admin' ? trim($rawSql) . ';' : null
        ]);
    }

    protected function getUserFilter($filterName, $notificationId)
    {
        $filter = array_values(array_filter($this->getUserFilterList($notificationId), function($item) use ($filterName) {
            return $item['name'] == $filterName;
        }));

        if (count($filter) == 0) return null;

        return $filter[0];
    }

    protected function getFilteredUserList($filterName, $notificationId)
    {
        $filter = $this->getUserFilter($filterName, $notificationId);

        if (!$filter) return null;

        $db = db_connect();

        return $db->query(new RawSql($filter['raw_sql']))->getResult();
    }
}
