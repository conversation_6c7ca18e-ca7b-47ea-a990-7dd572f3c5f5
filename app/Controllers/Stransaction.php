<?php

namespace App\Controllers;
use App\Models\TransactionsModel;
use App\Models\StransactionModel;
use App\Models\AdminModel;
use App\Models\UserModel;
use App\Models\CompanyModel;
use App\Models\InvoiceModel;
use App\Models\InvoiceItemModel;

use CodeIgniter\Controller;

class Stransaction extends BaseController
{
    public function index()
    {
        $data = [
            'page_title' => 'Giao dịch',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        if(!has_permission('Stransaction', 'can_view_all')) {
            show_404();
        }
        echo view('templates/sepay/header', $data);
        echo view('stransaction/index', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajax_add_by_invoice() {

        if(!has_permission('Stransaction', 'can_add'))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn chưa có quyền"));

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'invoice_id' => ['label' => 'Invoice ID', "rules" => "required|is_natural"],
            'date' => ['label' => 'Invoice date', "rules" => "required|valid_date[Y-m-d\TH:i:s]"],
            'in' => ['label' => 'Amount', "rules" => "required|is_natural"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $invoice_id = $this->request->getVar('invoice_id');

        $companyModel = model(CompanyModel::class);
        $merchantDetailsModel = model(MerchantDetailsModel::class);
        $invoiceModel = model(InvoiceModel::class);

        $invoice_details = $invoiceModel->find($invoice_id);

        $data = [
            'invoice_id' => $this->request->getVar('invoice_id'),
            'date' => $this->request->getVar('date'),
            'in' => $this->request->getVar('in'),
            'type' => 'invoice',
            'description' => trim($this->request->getVar('description')),

        ];

        if(!is_object($invoice_details))
            return $this->response->setJSON(["status"=>FALSE, "message" => "Không tìm thấy hoá đơn này"]);
        

        if($invoice_details->company_id > 0) {
            $company_details = $companyModel->find($invoice_details->company_id);
         
            if(!is_object($company_details))
                return $this->response->setJSON(["status"=>FALSE, "message" => "Không tìm thấy công ty liên quan đến hoá đơn này"]);
            $data['company_id']  = $invoice_details->company_id;
    
        } else {
            $merchant_details = $merchantDetailsModel->find($invoice_details->merchant_id);
         
            if(!is_object($merchant_details))
                return $this->response->setJSON(["status"=>FALSE, "message" => "Không tìm thấy merchant liên quan đến hoá đơn này"]);

            $data['merchant_id']  = $invoice_details->merchant_id;
        }
       

    

        $stransactionModel = model(StransactionModel::class);

        $strans_id = $stransactionModel->insert($data);

        if(is_numeric($strans_id)) {
            add_admin_log(array('data_id'=> $strans_id, 'data_type'=>'stransaction_add','description'=>'Thêm giao dịch','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
 
            $strans_in = $stransactionModel->select("sum(`in`) as `total_in`")->where(['invoice_id' => $invoice_id])->get()->getRow();

            if( $invoice_details->status !="Paid" && is_object($strans_in) && $strans_in->total_in == $invoice_details->total)
                $invoiceModel->set(['status' => 'Paid', 'datepaid' => date("Y-m-d H:i:s")])->where(['id' => $invoice_id])->update();
        
            return $this->response->setJSON(["status"=>TRUE]);
    
        } else {
            return $this->response->setJSON(["status"=>FALSE, "Không thể thêm giao dịch."]);

        }
 
     
    }


    public function all_ajax_list() {

        if(!has_permission('Stransaction', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $stransactionModel = model(StransactionModel::class);
        $companyModel = model(CompanyModel::class);

        $stransactions = $stransactionModel->getDatatables();
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($stransactions as $strans) {

            $company_details = $companyModel->find($strans->company_id);

         
            $no++;
            $row = array();
            
            $row[] = $no;
            //$row[] = "<a href='" . base_url('stransaction/edit/' . $strans->id) . "'>". esc($strans->id) . "</a>";
            $row[] = esc($strans->id);

            if(is_object($company_details))
                $row[] = "<a href='" . base_url("company/details/" . $company_details->id) . "'>" . esc($company_details->short_name) . "</a>";
            else
                $row[] = "";
             
            $row[] = esc(date("d/m/Y H:i:s", strtotime($strans->date)));
            
            
            if($strans->invoice_id)
                $row[] = "<a href='" . base_url("invoice/edit/" . $strans->invoice_id) . "'>" . esc($strans->invoice_id) . "</a>";
            else
                $row[] = "";

            $row[] = "<span class='text-success'>" . number_format($strans->in) . " đ" . "</span>";
            $row[] = number_format($strans->out) . " đ";

            
            $row[] = "<a href='" . base_url('stransaction/edit/' . $strans->id) . "'>Edit</a>";
           
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $stransactionModel->countAll(),
            "recordsFiltered" => $stransactionModel->countFiltered(),
            "data" => $data,
        );
        return $this->response->setJSON($output); 
    }

    public function ajax_get($id)
    {
        if(! has_permission('Stransaction', 'can_view_all')) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn chưa có quyền"));
        }

        $stransactionModel = model(StransactionModel::class);
        $companyModel = model(CompanyModel::class);

        $strans = $stransactionModel->find($id);

        if(! $strans) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy giao dịch này"));
        }

        $data = [
            'id' => $strans->id,
            'date' => $strans->date,
            'in' => $strans->in,
            'description' => $strans->description,
        ];

        return $this->response->setJSON(array('status'=>TRUE,'data'=>$data));   
    }

    public function ajax_update_by_invoice()
    {
        if (! has_permission('Stransaction', 'can_edit')) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn chưa có quyền"));
        }

        $data = $this->request->getPost();

        if (! $this->validateData($data, [
            'id' => ['label' => 'ID', "rules" => "required|is_natural"],
            'date' => ['label' => 'Invoice date', "rules" => "required|valid_date[Y-m-d\TH:i]"],
            'in' => ['label' => 'Amount', "rules" => "required|is_natural"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $this->validator->getErrors())));
        }

        $stransactionModel = model(StransactionModel::class);
        $strans = $stransactionModel->find($data['id']);

        if(! $strans) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy giao dịch này"));
        }

        $stransactionModel->update($data['id'], $data);

        return $this->response->setJSON(array('status'=>TRUE,'message'=>"Cập nhật thành công"));
    }

    public function ajax_delete($id)
    {
        if (! has_permission('Stransaction', 'can_delete')) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn chưa có quyền"));
        }

        $stransactionModel = model(StransactionModel::class);
        $strans = $stransactionModel->find($id);

        if(! $strans) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy giao dịch này"));
        }

        $stransactionModel->delete($id);

        set_alert('success', 'Xoá thành công');

        return $this->response->setJSON(array('status'=>TRUE));
    }
}