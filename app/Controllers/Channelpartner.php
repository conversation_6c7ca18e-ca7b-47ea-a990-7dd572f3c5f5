<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use Config\ChannelPartner as ConfigChannelPartner;

class Channelpartner extends BaseController
{
    protected $configCPA;
    protected $listConfigKeys;
    protected $channelPartner;
    public function __construct()
    {
       // Tải cấu hình từ tệp cấu hình
       $configCPA = config(ConfigChannelPartner::class);

       $this->channelPartner = $configCPA->channelPartner;

       $this->listConfigKeys = array_keys($this->channelPartner);

        
    }
    public function partnerList($partner) {

        if(!in_array($partner,$this->listConfigKeys)){
             show_404();
        }

        if(!has_permission('ChannelPartner', 'can_view_all'))
            show_404();

        // info congif channel partner
        $token_cpa = $this->channelPartner[$partner]['cpaToken']??"";
        $channel_partner_id = $this->channelPartner[$partner]['channelPartnerId']??"";
        $base_url = $this->channelPartner[$partner]['urlMySepay']??"";

        $url_commission = $base_url."/internalapi/v1/cpacommission/all?channel_partner_id=".$channel_partner_id;
        $url_partner_list = $base_url."/internalapi/v1/cpaofficer/all?channel_partner_id=".$channel_partner_id;

        $data_api_commission = $this->callApi('GET',$url_commission,$token_cpa);
        $data_api_partner_list = $this->callApi('GET',$url_partner_list,$token_cpa);
        $data_commission = [];
        $data_partner_list = [];
        if($data_api_commission['code']==200){

            $data_commission = $data_api_commission['data'];
        }
        $data_total_amount_officer = 0;
        if($data_api_partner_list['code']==200){
            
            if(!empty($data_api_partner_list['data'])){
                $data_total_amount_officer = array_sum(array_column($data_api_partner_list['data'],'balance_amount'));
            }

            $data_partner_list = $data_api_partner_list['data'];
        }
        $total_commission = 0;
        $total_commission_pending = 0;
        $total_commission_real = 0;

        if(!empty($data_commission)){
            $total_commission = array_sum(array_column(array_filter($data_commission,function($item){
                return $item['status'] !="Rejected";
            }),'commission_amount'));

            $total_commission_pending = array_sum(array_column(array_filter($data_commission,function($item){
                return $item['status'] =="Pending";
            }),'commission_amount'));

            $total_commission_real = array_sum(array_column(array_filter($data_commission,function($item){
                return $item['status'] =="Approved";
            }),'commission_amount'));
        }
        

        $url_withdraw = $base_url."/internalapi/v1/cpawithdraw/all?channel_partner_id=".$channel_partner_id;
        $data_api_withdraw = $this->callApi('GET',$url_withdraw,$token_cpa);
        $data_withdraw = [];
        if($data_api_withdraw['code']==200){

            $data_withdraw = $data_api_withdraw['data'];
        }
        
        $total_withdraw = 0;
        $total_withdraw_pending = 0;
        $total_withdraw_real = 0;
        
        if(!empty($data_withdraw)){
            $total_withdraw = array_sum(array_column(array_filter($data_withdraw,function($item){
                return $item['status'] !="Rejected" ||  $item['status'] !="Closed";
            }),'withdraw_amount'));

            $total_withdraw_pending = array_sum(array_column(array_filter($data_withdraw,function($item){
                return $item['status'] =="Pending" || $item['status'] =="InProgress";
            }),'withdraw_amount'));

           

            $total_withdraw_real = array_sum(array_column(array_filter($data_withdraw,function($item){
                return $item['status'] =="Approved";
            }),'withdraw_amount'));
        }        

        if(!empty($data_partner_list)){
            foreach($data_partner_list as &$val){
                $array_color = array(
                    "0" => "badge bg-warning",
                    "1" => "badge bg-success",
                );
                $array_color_text = array(
                    "0" => "Chưa kích hoạt",
                    "1" => "hoạt động",
                );
                $val['rate'] = number_format($val['commission_rate'])." %";
                $val['balance_amount'] = number_format($val['balance_amount']??0)." đ";
                $val['color_status'] = $array_color[$val['active']] ?? "badge bg-info";
                $val['color_text'] = $array_color_text[$val['active']] ?? "NA/N";

                // tính hoa hồng đã nhận từng officer
                $channel_partner_officer_id = $val['channel_partner_officer_id'];
                $val['totalCommission'] = 0;
                log_message("debug",print_r($data_commission,true));
                if(!empty($data_commission)){
                    // Tính tổng hoa hồng từ B
                    $totalCommission = array_reduce($data_commission, function ($carry, $item) use ($channel_partner_officer_id) {
                        return $carry + (
                            $item['channel_partner_officer_id'] == $channel_partner_officer_id 
                            && isset($item['status']) 
                            && $item['status'] == 'Approved' 
                            ? intval($item['commission_amount']) 
                            : 0
                        );
                    }, 0);
                    $val['totalCommission'] = number_format($totalCommission). " đ";
                }

                $val['totalWithdraw'] = 0;
                if(!empty($data_withdraw)){
                    // Tính tổng hoa hồng từ B
                    $totalWithdraw = array_reduce($data_withdraw, function ($carry, $item) use ($channel_partner_officer_id) {
                        return $carry + (
                            $item['channel_partner_officer_id'] == $channel_partner_officer_id 
                            && isset($item['status']) 
                            && $item['status'] == 'Approved' 
                            ? intval($item['withdraw_amount']) 
                            : 0
                        );
                    }, 0);
                    $val['totalWithdraw'] = number_format($totalWithdraw). " đ";
                }
            }
            unset($val);
        }

        $data = [
            'partner'              => $partner ??"",
            'page_title'           => $this->channelPartner[$partner]['page_title'] ?? "",
            'admin_details'        => $this->admin_details,
            'admin_session'        => $this->admin_session,
            'data_commission'      => $data_commission ?? [],
            'data_partner_list'      => $data_partner_list ?? [],
            'total_commission'     => $total_commission ?? 0,
            'data_total_amount_officer' => $data_total_amount_officer ?? 0,
            'total_commission_pending' => $total_commission_pending ?? 0,
            'total_withdraw'           => $total_withdraw ?? 0,
            'total_withdraw_pending'   => $total_withdraw_pending ?? 0,
            'total_withdraw_real'      => $total_withdraw_real ?? 0,
            'total_commission_real'    => $total_commission_real ?? 0
        ];
        
        echo view('templates/sepay/header',$data);
        echo view('channelpartner/partner_list',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function loginAsChannelPartner($partner,$channel_partner_officer_id){
       
       $token_cpa = $this->channelPartner[$partner]['cpaToken']??"";
       $base_url = $this->channelPartner[$partner]['urlMySepay']??"";
       $channel_partner_id = $this->channelPartner[$partner]['channelPartnerId']??"";
       $url_partner_list = $base_url."/internalapi/v1/cpaofficer/all?channel_partner_id=".$channel_partner_id."&channel_partner_officer_id=".$channel_partner_officer_id;

       $data_api_partner_list = $this->callApi('GET',$url_partner_list,$token_cpa);
       $data_partner_list = [];
       if($data_api_partner_list['code']==200){
           $data_partner_list = $data_api_partner_list['data'];
       }
       if(empty($data_api_partner_list)){
           return false;
       }
       if($data_partner_list[0]['active']==0){
            return false;
       };

       $site_login = "https://".$partner.".sepay.vn";
       $serverIP = "**************";
       if(env('CI_ENVIRONMENT') === 'development'){
            $site_login = "http://".$partner.".local.com";
            $serverIP = "127.0.0.1";
       }

       $email_officer = isset($data_partner_list[0]['email']) ? $data_partner_list[0]['email'] : '';
       $name_officer = isset($data_partner_list[0]['name']) ? $data_partner_list[0]['name'] : '';
       $role_officer = isset($data_partner_list[0]['role']) ? $data_partner_list[0]['role'] : '';
       
       
       // Tạo chuỗi value_token
       $value_token = "role=$role_officer&name=$name_officer&channel_partner_id=$channel_partner_id&channel_partner_officer_id=$channel_partner_officer_id&email_officer=$email_officer&clientIP=$serverIP";
       // Mã hóa chuỗi bằng MD5
       $md5_value_token = md5($value_token);
       $url_site_login = $site_login."/login_token";

       $ret_url_site_login = $url_site_login . "?token=" . urlencode($md5_value_token) . 
       "&p1=" . urlencode($role_officer) . 
       "&p2=" . urlencode($name_officer) . 
       "&p3=" . urlencode($channel_partner_id) . 
       "&p4=" . urlencode($channel_partner_officer_id) . 
       "&p5=" . urlencode($email_officer);

        return $ret_url_site_login;

    }

    public function commission($partner) {

        if(!in_array($partner,$this->listConfigKeys)){
             show_404();
        }

        if(!has_permission('ChannelPartner', 'can_view_all'))
            show_404();

        // info congif channel partner
        $token_cpa = $this->channelPartner[$partner]['cpaToken']??"";
        $channel_partner_id = $this->channelPartner[$partner]['channelPartnerId']??"";
        $base_url = $this->channelPartner[$partner]['urlMySepay']??"";

        $url_commission = $base_url."/internalapi/v1/cpacommission/all?channel_partner_id=".$channel_partner_id;
        $data_api_commission = $this->callApi('GET',$url_commission,$token_cpa);
        $data_commission = [];
        if($data_api_commission['code']==200){

            $data_commission = $data_api_commission['data'];
        }
        $total_commission = 0;
        $total_commission_pending = 0;
        $total_commission_real = 0;

        if(!empty($data_commission)){
            $total_commission = array_sum(array_column(array_filter($data_commission,function($item){
                return $item['status'] !="Rejected";
            }),'commission_amount'));

            $total_commission_pending = array_sum(array_column(array_filter($data_commission,function($item){
                return $item['status'] =="Pending";
            }),'commission_amount'));

            $total_commission_real = array_sum(array_column(array_filter($data_commission,function($item){
                return $item['status'] =="Approved";
            }),'commission_amount'));
        }
        

       
       

        if(!empty($data_commission)){
            foreach($data_commission as &$val){
                $array_color = array(
                    "Pending" => "badge bg-warning",
                    "Approved" => "badge bg-success",
                    "Rejected" => "badge bg-danger"
                );
                $array_color_price = array(
                    "Pending" => "badge bg-warning-subtle text-warning",
                    "Approved" => "badge bg-success-subtle text-success",
                    "Rejected" => "badge bg-danger-subtle text-danger"
                );
                $val['rate'] = number_format($val['rate'])." %";
                $val['commission_amount'] = number_format($val['commission_amount']??0)." đ";
                $val['color'] = $array_color[$val['status']] ?? "badge bg-info";
                $val['color_price'] = $array_color_price[$val['status']] ?? "badge bg-secondary-subtle text-secondary";
            }
        }

        $data = [
            'partner'              => $partner ??"",
            'page_title'           => $this->channelPartner[$partner]['page_title'] ?? "",
            'admin_details'        => $this->admin_details,
            'admin_session'        => $this->admin_session,
            'data_commission'      => $data_commission ?? [],
            'total_commission'     => $total_commission ?? 0,
            'total_commission_pending' => $total_commission_pending ?? 0,
            'total_commission_real'    => $total_commission_real ?? 0
        ];
        
        echo view('templates/sepay/header',$data);
        echo view('channelpartner/commission',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function commission_detail($partner,$id=null) {
        if(empty($id)){
            return show_404("Không tìm thấy trang");
        }
        if(!in_array($partner,$this->listConfigKeys)){
            show_404();
        }

        if(!has_permission('ChannelPartner', 'can_view_all'))
            show_404();
        // info congif channel partner
        $token_cpa = $this->channelPartner[$partner]['cpaToken']??"";
        $channel_partner_id = $this->channelPartner[$partner]['channelPartnerId']??"";
        $base_url = $this->channelPartner[$partner]['urlMySepay']??"";

        $url = $base_url."/internalapi/v1/cpacommission/detail/$id?channel_partner_id=$channel_partner_id";
        $method = "GET";
        $dataResponse = $this->callApi($method,$url,$token_cpa);

        $data_detail = [];
        if($dataResponse['code']==200){
            $data_detail = $dataResponse['data'];
        }else{
            show_404();
        }



      
        $color_status = array(
            "Pending" =>"text-danger",
            "Active" =>"text-success"
        );

        $color_text = $color_status[$data_detail['company']['status']??"NA\N"]??"text-info";
        $data = [
            'data_detail' => $data_detail,
            'page_title' => 'Chi tiết hoa hồng',
            'color_text' =>$color_text,
            'admin_details' => $this->admin_details,
            'company_id' =>$data_detail['company']['id']??"NA\N",
            'admin_session' => $this->admin_session,
            'path_url_logo' => $base_url,
            'partner' => $partner,
        ];

        if(!has_permission('Partner', 'can_view_all') || !has_permission('LoginAsPartner', 'can_view_all'))
            show_404();


        echo view('templates/sepay/header',$data);
        echo view('channelpartner/commission_detail',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function commissionUpdate($partner){

        if(!in_array($partner,$this->listConfigKeys)){
            show_404();
       }
        $input_data = $this->request->getPost();
        
       // info congif channel partner
       $token_cpa = $this->channelPartner[$partner]['cpaToken']??"";
       $channel_partner_id = $this->channelPartner[$partner]['channelPartnerId']??"";
       $base_url = $this->channelPartner[$partner]['urlMySepay']??"";
        $validationRules = [
            'commission_id'    => 'required|max_length[11]|is_not_unique[tb_autopay_channel_partner_commission.id]',
            'status'     => 'required|in_list[Approved,Rejected]',
            'channel_partner_id'=>'required|max_length[11]|is_not_unique[tb_autopay_channel_partner.id]',
        ];
    
        $validation = service('validation');
        $input_data['channel_partner_id']=$channel_partner_id;
        if (!$validation->setRules($validationRules)->run($input_data)) {
            return $this->responseResult(422, $validation->getErrors());
        }
        if($input_data['status']=="Rejected"){
            if(!$validation->setRules(['rejection_reasons'=>'required'])->run(['rejection_reasons'=>$input_data['rejection_reasons']??""])){
                return $this->responseResult(422, $validation->getErrors());
            }
        }
       
        $url = $base_url."/internalapi/v1/cpacommission/update_status";
        $method = "POST";
        $dataResponse = $this->callApi($method,$url,$token_cpa,$input_data);

        if($dataResponse['code']==200){
            return $this->responseResult(200,"Cập nhật thành công!");
        }else{
            log_message("debug","response_data: ".print_r($dataResponse,true));
            return $this->responseResult(500,"Lỗi api");
        }
        
    }

    public function withdraw($partner) {

        if(!in_array($partner,$this->listConfigKeys)){
            show_404();
       }

        if(!has_permission('ChannelPartner', 'can_view_all'))
            show_404();

         // info congif channel partner
         $token_cpa = $this->channelPartner[$partner]['cpaToken']??"";
         $channel_partner_id = $this->channelPartner[$partner]['channelPartnerId']??"";
         $base_url = $this->channelPartner[$partner]['urlMySepay']??"";

        $url_withdraw = $base_url."/internalapi/v1/cpawithdraw/all?channel_partner_id=".$channel_partner_id;
        $data_api_withdraw = $this->callApi('GET',$url_withdraw,$token_cpa);
        $data_withdraw = [];
        if($data_api_withdraw['code']==200){

            $data_withdraw = $data_api_withdraw['data'];
        }
        
        $total_withdraw = 0;
        $total_withdraw_pending = 0;
        $total_withdraw_real = 0;
        
        if(!empty($data_withdraw)){
            $total_withdraw = array_sum(array_column(array_filter($data_withdraw,function($item){
                return $item['status'] !="Rejected" ||  $item['status'] !="Closed";
            }),'withdraw_amount'));

            $total_withdraw_pending = array_sum(array_column(array_filter($data_withdraw,function($item){
                return $item['status'] =="Pending" || $item['status'] =="InProgress";
            }),'withdraw_amount'));

           

            $total_withdraw_real = array_sum(array_column(array_filter($data_withdraw,function($item){
                return $item['status'] =="Approved";
            }),'withdraw_amount'));
        }        

        if(!empty($data_withdraw)){
            foreach($data_withdraw as &$val){
                list($bank,$num_bank,$name_bank) = explode('|',$val['notes']);
                $array_color = array(
                    "Pending" => "badge bg-warning",
                    "Approved" => "badge bg-success",
                    "InProgress" => "badge bg-primary",
                    "Rejected" => "badge bg-danger",
                    "Closed" => "badge bg-dark"
                );
                $val['withdraw_amount'] = number_format($val['withdraw_amount']?? 0)." đ";
                $val['bank'] = $bank ??"";
                $val['num_bank'] = $num_bank ??"";
                $val['name_bank'] = $name_bank ??"";
                $val['color'] = $array_color[$val['status']] ?? "badge bg-info";
            }
        }

         //  info account officer
         $url_officer =$base_url."/internalapi/v1/cpaofficer/all?channel_partner_id=".$channel_partner_id;
         $data_api_cofficer = $this->callApi('GET',$url_officer,$token_cpa);
         $data_officer = [];
         if($data_api_cofficer['code']==200){
             $data_officer = $data_api_cofficer['data'];
         }
         $data_total_officer = count($data_officer)??0;
         $data_total_amount_officer = 0;
         if(!empty($data_officer)){
             $data_total_amount_officer = array_sum(array_column($data_officer,'balance_amount'));
         }
 
         $data = [
            'page_title'               => $this->channelPartner[$partner]['page_title']??"",
            'admin_details'            => $this->admin_details,
            'admin_session'            => $this->admin_session,
            'data_total_amount_officer'=> $data_total_amount_officer ?? 0,
            'total_officer'            => $data_total_officer ?? 0,
            'data_withdraw'            => $data_withdraw ?? [],
            'partner'                  => $partner,
            'total_withdraw'           => $total_withdraw ?? 0,
            'total_withdraw_pending'   => $total_withdraw_pending ?? 0,
            'total_withdraw_real'      => $total_withdraw_real ?? 0
        ];
        
        
        echo view('templates/sepay/header',$data);
        echo view('channelpartner/withdraw',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function withdraw_detail($partner,$id=null) {

        if(!in_array($partner,$this->listConfigKeys)){
            show_404();
       }

        if(empty($id)){
            return show_404("Không tìm thấy trang");
        }

        if(!has_permission('ChannelPartner', 'can_view_all'))
            show_404();
        // info congif channel partner
        $token_cpa = $this->channelPartner[$partner]['cpaToken']??"";
        $channel_partner_id = $this->channelPartner[$partner]['channelPartnerId']??"";
        $base_url = $this->channelPartner[$partner]['urlMySepay']??"";

        $url = $base_url."/internalapi/v1/cpawithdraw/detail/$id?channel_partner_id=$channel_partner_id";
        $method = "GET";

        $dataResponse = $this->callApi($method,$url,$token_cpa);

        if($dataResponse['code']!==200){
            show_404(); 
        }

       $data_detail = $dataResponse['data'];

        $val = array();
        if (!empty($data_detail['withdraw'])) {
            $val = $data_detail['withdraw']; // Giả sử đây là một phần tử đơn lẻ
        
            // Kiểm tra và phân tách giá trị trong 'notes'
            $notes_parts = explode('|', $val['notes'] ?? '');
            $bank = $notes_parts[0] ?? '';
            $num_bank = $notes_parts[1] ?? '';
            $name_bank = $notes_parts[2] ?? '';
        
            // Định nghĩa các màu sắc trạng thái
            $array_color = array(
                "Pending" => "badge bg-warning",
                "Approved" => "badge bg-success",
                "InProgress" => "badge bg-primary",
                "Rejected" => "badge bg-danger",
                "Closed" => "badge bg-dark"
            );
        
            // Định dạng số tiền rút tiền
            $val['withdraw_amount'] = number_format($val['withdraw_amount'] ?? 0) . " đ";
        
            // Gán giá trị cho các trường
            $val['bank'] = $bank;
            $val['num_bank'] = $num_bank;
            $val['name_bank'] = $name_bank;
        
            // Đặt màu sắc trạng thái
            $val['color'] = $array_color[$val['status']] ?? "badge bg-info";
            $color_text = $val['color']??"";
        }
        $data = [
            'data_detail' => $data_detail,
            'value' => $val,
            'page_title' => 'Chi tiết rút tiền',
            'color_text' =>$color_text??"",
            'admin_details' => $this->admin_details,
            'company_id' =>$data_detail['company']['id']??"NA\N",
            'admin_session' => $this->admin_session,
            'partner' => $partner,
        ];

        if(!has_permission('Partner', 'can_view_all') || !has_permission('LoginAsPartner', 'can_view_all'))
            show_404();

       
        echo view('templates/sepay/header',$data);
        echo view('channelpartner/withdraw_detail',$data);
        echo view('templates/sepay/footer',$data);
    }


    public function withdrawUpdate($partner){

        if(!in_array($partner,$this->listConfigKeys)){
            show_404();
       }

        $input_data = $this->request->getPost();

         // info congif channel partner
         $token_cpa = $this->channelPartner[$partner]['cpaToken']??"";
         $channel_partner_id = $this->channelPartner[$partner]['channelPartnerId']??"";
         $base_url = $this->channelPartner[$partner]['urlMySepay']??"";

         
        $validationRules = [
            'channel_partner_withdraw_id'    => 'required|max_length[11]|is_not_unique[tb_autopay_channel_partner_withdraw.id]',
            'status'     => 'required|in_list[Approved,Rejected,InProgress]',
            'channel_partner_id' =>'required|max_length[11]|is_not_unique[tb_autopay_channel_partner.id]',
        ];
    
        $validation = service('validation');
        $input_data['channel_partner_id'] = $channel_partner_id;
        if (!$validation->setRules($validationRules)->run($input_data)) {
            return $this->responseResult(422, $validation->getErrors());
        }
        if($input_data['status']=="Rejected"){
            if(!$validation->setRules(['rejection_reasons'=>'required'])->run(['rejection_reasons'=>$input_data['rejection_reasons']??""])){
                return $this->responseResult(422, $validation->getErrors());
            }
        }
       
       
        $url = $base_url."/internalapi/v1/cpawithdraw/update_status";
        $method = "POST";
        $dataResponse = $this->callApi($method,$url,$token_cpa,$input_data);

        if($dataResponse['code']==200){
            return $this->responseResult(200,"Cập nhật thành công!");
        }else{
            log_message("debug","response_data: ".print_r($dataResponse,true));
            return $this->responseResult(500,"Lỗi api");
        }
        
    }

    public function invoiceDetail($partner)
    {
        $invoice_id = $this->request->getGet('invoice_id')??"";
        $channel_partner_officer_id = $this->request->getGet('channel_partner_officer_id')??"";
        if(!in_array($partner,$this->listConfigKeys)){
            show_404();
       }

        if (empty($invoice_id)) {
            return $this->responseResult(404, "Không tìm thấy ID hóa đơn");
        }
        if (empty($channel_partner_officer_id)) {
            return $this->responseResult(404, "Không tìm officer");
        }
    
        // info congif channel partner
        $token_cpa = $this->channelPartner[$partner]['cpaToken']??"";
        $channel_partner_id = $this->channelPartner[$partner]['channelPartnerId']??"";
        $base_url = $this->channelPartner[$partner]['urlMySepay']??"";
        $method = "GET";
        
        $url_invoice_item = $base_url."/internalapi/v1/invoice/invoice_item_view_all?invoice_id=$invoice_id&channel_partner_officer_id=$channel_partner_officer_id&channel_partner_id=$channel_partner_id";
        $dataResponseItem = $this->callApi($method, $url_invoice_item, $token_cpa);
        
        if ($dataResponseItem['code'] !== 200) {
            return $this->responseResult(404, "Không tìm thấy thông tin hóa đơn");
        }
        log_message("debug", print_r($url_invoice_item,true));
        log_message("debug", print_r($dataResponseItem,true));
        // Loại hóa đơn
        $type_invoice_item = [
            'Product' => 'Sản phẩm',
            'Discount' => 'Giảm giá',
            'Addon' => 'Thêm gói',
            'Upgrade' => 'Nâng cấp',
            'Orther' => 'Khác',
        ];
    
        // Mảng để chứa kết quả cuối cùng
        $data_merge = [];
    
        foreach ($dataResponseItem['data'] as &$item) {
            $prefix = $item['type'];
            if($prefix !="Product"){
                $prefix = "Adddes";
            }
            $item['type_invoice_item'] = $type_invoice_item[$prefix] ?? "Không xác định";
            $item['amount_invoice_item'] = number_format($item['amount'] ?? 0) . " đ";
            $item['tax_invoice_item'] = number_format($item['tax'] ?? 0) . " đ";
    
            // Thêm các giá trị với tiền tố
            foreach ($item as $key => $value) {
                $data_merge[$prefix . '_' . $key] = $value;
            }
    
            // Nếu là sản phẩm, thực hiện API call để lấy tên sản phẩm
            if ($prefix == "Product") {
                $product_id = $item['item_id'];
                $url_product_item = $base_url."/internalapi/v1/package/all?product_id=$product_id&channel_partner_id=$channel_partner_id";
                $productResponse = $this->callApi($method, $url_product_item, $token_cpa);
                
                // Gắn tên sản phẩm vào mảng
                $data_merge['Product_name'] = $productResponse['code'] == 200 ? ($productResponse['data'][0]['name'] ?? "") : "";
            }
        }
    
        return $this->responseResult(200, "OK", $data_merge);
    }
}