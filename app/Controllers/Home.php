<?php

namespace App\Controllers;

use App\Models\TransactionsModel;
use App\Models\SimModel;
use App\Models\TicketModel;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\InvoiceModel;
use App\Models\WebhooksModel;
use App\Models\BankAccountModel;
use App\Models\WebhooksLogModel;
use App\Models\StransactionModel;
use App\Models\CompanySubscriptionModel;
use App\Models\NotificationTelegramModel;
use App\Models\NotificationTelegramQueueModel;
use App\Models\PhysicalInvoiceModel;
use App\Models\PhysicalOrderModel;
use Config\SInvoice;

class Home extends BaseController
{
    public function index()
    {
        $data = [
            'page_title' => 'Tổng quan',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        $companySubscriptionModel = slavable_model(CompanySubscriptionModel::class, 'Home');
        $transactionsModel = slavable_model(TransactionsModel::class, 'Home');
        $stransactionModel = slavable_model(StransactionModel::class, 'Home');
        $invoiceModel = slavable_model(InvoiceModel::class, 'Home');
        $simModel = slavable_model(SimModel::class, 'Home');
        $notificationTelegramModel = slavable_model(NotificationTelegramModel::class, 'Home');
        $notificationTelegramQueueModel = slavable_model(NotificationTelegramQueueModel::class, 'Home');
        $webhooksModel = slavable_model(WebhooksModel::class, 'Home');
        $webhooksLogModel = slavable_model(WebhooksLogModel::class, 'Home');
        $bankAccountModel = slavable_model(BankAccountModel::class, 'Home');
        $companyModel = slavable_model(CompanyModel::class, 'Home');
        $counterModel = slavable_model(CounterModel::class, 'Home');
        $ticketModel = slavable_model(TicketModel::class, 'Home');

        if(in_array($this->admin_details->role,['Admin','SuperAdmin'])):

            $results = $companySubscriptionModel->select("tb_autopay_company_subscription.recurring_payment,tb_autopay_company_subscription.billing_cycle")->join("tb_autopay_order","tb_autopay_order.id=tb_autopay_company_subscription.order_id")->join("tb_autopay_invoice","tb_autopay_invoice.id=tb_autopay_order.invoice_id")->where(['tb_autopay_company_subscription.status' => 'Active', 'tb_autopay_invoice.status' => 'Paid','tb_autopay_company_subscription.billing_cycle!=' => 'free','tb_autopay_company_subscription.recurring_payment>' => 0])->get()->getResult();
            
            $data['total_mrr'] = 0;
            foreach($results as $result) {
                if($result->billing_cycle == 'annually')
                    $data['total_mrr'] =  $data['total_mrr'] + $result->recurring_payment/12;
                else
                    $data['total_mrr'] =  $data['total_mrr'] + $result->recurring_payment;

            }

    
            $data['count_active_paid_companies'] = $companySubscriptionModel->join("tb_autopay_order","tb_autopay_order.id=tb_autopay_company_subscription.order_id")->join("tb_autopay_invoice","tb_autopay_invoice.id=tb_autopay_order.invoice_id")->where(['tb_autopay_company_subscription.status' => 'Active', 'tb_autopay_invoice.status' => 'Paid'])->countAllResults();

            //$data['total_bank_transactions'] = $transactionsModel->where(['parser_status' => 'Success', 'account_number!=' => '*************','account_number!=' => '*************'])->countAllResults();

            //$data['total_telegram_sent'] = $notificationTelegramQueueModel->where(['status' => 'Success'])->countAllResults();
            $result = $counterModel->select("sum(transaction) as `sum_transaction`")->get()->getRow();

            if($result)
                $data['total_bank_transactions'] = $result->sum_transaction;
            else 
                $data['total_bank_transactions'] = 0;
 
            $result = $counterModel->select("sum(chat) as `sum_chat`")->get()->getRow();

            if($result)
                $data['total_chat_sent'] = $result->sum_chat;
            else 
                $data['total_chat_sent'] = 0;

            //$data['total_webhooks_sent'] = $webhooksLogModel->where(['connect_success' => '1'])->countAllResults();

            $result = $counterModel->select("sum(webhook) as `sum_webhooks`")->get()->getRow();

            if($result)
                $data['total_webhooks_sent'] = $result->sum_webhooks;
            else 
                $data['total_webhooks_sent'] = 0;
                

            $result = $bankAccountModel->select("count(id) as `count_bank_account`")->where(['last_transaction!=' => '0000-00-00 00:00:00'])->get()->getRow();
         
            $data['total_bank_account_connected'] = $result->count_bank_account;
            
           // $data['total_bank_account_connected'] = $bankAccountModel->where(['account_number!=' => NULL])->groupBy('account_number')->countAllResults();

            $result = $transactionsModel->select("sum(`amount_in`) as `sum_amount_in`")->where(['parser_status' => 'Success', 'account_number!=' => '*************','account_number!=' => '*************'])->get()->getRow();

            $data['total_amount_in'] = $result->sum_amount_in;

            $data['total_bank_account_connnected'] = $transactionsModel->groupBy("tb_autopay_sms_parsed.account_number")->countAllResults() - 9;

            $result = $invoiceModel->select("sum(total) as `sum_invoice`")->where(['status' => 'Paid'])->get()->getRow();

            if($result)
                $data['sum_invoice_total'] = $result->sum_invoice;
            else
                $data['sum_invoice_total'] = 0;


            $result = $invoiceModel->select("sum(total) as `sum_invoice`")->where(['status' => 'Paid','datepaid>=' => date("Y-m-d 00:00:00", strtotime("7 days ago"))])->get()->getRow();

            if($result)
                $data['sum_invoice_7_days_ago'] = $result->sum_invoice;
            else
                $data['sum_invoice_7_days_ago'] = 0;

            $result = $invoiceModel->select("sum(total) as `sum_invoice`")->where(['status' => 'Paid','datepaid>=' => date("Y-m-01 00:00:00")])->get()->getRow();

            if($result)
                $data['sum_invoice_this_month'] = $result->sum_invoice;
            else
                $data['sum_invoice_this_month'] = 0;
    

            $result = $invoiceModel->select("sum(total) as `sum_invoice`")->where(['status' => 'Paid','datepaid>=' => date("Y-01-01 00:00:00")])->get()->getRow();

            if($result)
                $data['sum_invoice_this_year'] = $result->sum_invoice;
            else
                $data['sum_invoice_this_year'] = 0;
    
    


           $result = $counterModel->select("sum(webhooks_pay_success) as `sum_webhooks_pay_success`")->get()->getRow();

           if($result)
               $data['total_webhooks_verify_payment_success'] = $result->sum_webhooks_pay_success;
           else 
               $data['total_webhooks_verify_payment_success'] = 0;

            $data['total_eligible_invoices'] = $invoiceModel->getTotalEligibleInvoices();
            $data['total_issued_invoices'] = $invoiceModel->getTotalIssuedInvoices();

            $physicalInvoiceModel = model(PhysicalInvoiceModel::class);
            $sInvoiceConfig = config(SInvoice::class);

            $data['total_eligible_physical_invoices'] = $physicalInvoiceModel->getTotalEligibleInvoices($sInvoiceConfig->issueInvoiceFrom);
            $data['total_issued_physical_invoices'] = $physicalInvoiceModel->getTotalIssuedInvoices($sInvoiceConfig->issueInvoiceFrom);

            $data['recent_stransactions'] = $stransactionModel->orderBy('id','DESC')->limit(8)->get()->getResult();

            $data['recent_bank_accounts'] = $bankAccountModel->select("tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.account_number,tb_autopay_bank.brand_name,tb_autopay_bank.icon_path,tb_autopay_bank_account.created_at")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_bank_account.bank_sms_connected' => 1])->orWhere(['tb_autopay_bank_account.bank_api_connected' => 1])->orderBy('tb_autopay_bank_account.id','DESC')->limit(8)->get()->getResult();

               

        endif;

        $data['recent_companies_register'] = $companyModel->orderBy('id','DESC')->limit(8)->get()->getResult();

        $data['recent_invoices'] = $invoiceModel->orderBy('id','DESC')->limit(8)->get()->getResult();

        $data['recent_tickets'] = $ticketModel->orderBy('id','DESC')->limit(8)->get()->getResult();

        echo view('templates/sepay/header',$data);
        if(in_array($this->admin_details->role,['Admin','SuperAdmin']))
            echo view('dashboard/index',$data);
        else
            echo view('dashboard/user',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function delay_report() {
        if(!in_array($this->admin_details->role,['Admin','SuperAdmin']) && !has_permission('LoginAsClient', 'can_view_all'))
            show_404();

        $data = [
            'page_title' => 'Delay Report',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        $transactionsModel = model(TransactionsModel::class);

        $data['results'] = $transactionsModel->select("tb_autopay_sms_parsed.id,tb_autopay_sms_parsed.gateway,tb_autopay_sms_parsed.account_number,tb_autopay_bank_account.company_id,tb_autopay_company.short_name,tb_autopay_sms_parsed.datecreated,TIMESTAMPDIFF(SECOND,tb_autopay_sms_parsed.transaction_date,tb_autopay_sms_parsed.datecreated) as `diff_seconds`")->join("tb_autopay_bank_account", "tb_autopay_bank_account.account_number=tb_autopay_sms_parsed.account_number")->join("tb_autopay_company", "tb_autopay_company.id=tb_autopay_bank_account.company_id")->where(['tb_autopay_sms_parsed.parser_status' => 'Success', 'TIMESTAMPDIFF(SECOND,tb_autopay_sms_parsed.transaction_date,tb_autopay_sms_parsed.datecreated)>=' => 100,'tb_autopay_sms_parsed.amount_in>' =>0])->orderBy('tb_autopay_sms_parsed.id', 'DESC')->limit(500)->get()->getResult();


        echo view('templates/sepay/header',$data);
        echo view('dashboard/delayreport',$data);
        echo view('templates/sepay/footer',$data);
    }
}


