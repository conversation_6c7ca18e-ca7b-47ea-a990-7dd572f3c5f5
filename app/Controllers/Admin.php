<?php

namespace App\Controllers;
use App\Models\AdminModel;
use App\Models\AdminPermissionFeatureModel;
use CodeIgniter\Controller;

class Admin extends BaseController
{
    
    public function index() {

        $data = [
            'page_title' => 'Admin User',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!in_array($this->admin_details->role,['Admin','SuperAdmin']))
            show_404();

        $adminModel = model(AdminModel::class);
      
        echo view('templates/sepay/header',$data);
        echo view('admin/index',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function update_theme() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $adminModel = model(AdminModel::class);
        $user_info = $adminModel->select('firstname, lastname')->where(['id' => $this->admin_details->id, 'active' => 1])->get()->getRow();

        if(!is_object($user_info))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tim thấy thông tin người dùng"));
        
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'theme' => "required|in_list[dark,light,auto]",
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $new_theme = $this->request->getVar('theme');

        $adminModel->set(['theme' => $new_theme])->where('id', $this->admin_details->id)->update();

        return $this->response->setJSON(["status"=>TRUE]);

    }

    public function sidebar_toggle() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $adminModel = model(AdminModel::class);
        $user_info = $adminModel->select('firstname, lastname, sidebar_toggle')->where(['id' => $this->admin_details->id, 'active' => 1])->get()->getRow();

        if(!is_object($user_info))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tim thấy thông tin người dùng"));
        
        if($user_info->sidebar_toggle == 1)
            $sidebar_toggle = 0;
        else
            $sidebar_toggle = 1;

        $adminModel->set(['sidebar_toggle' => $sidebar_toggle])->where('id', $this->admin_details->id)->update();

        return $this->response->setJSON(["status"=>TRUE]);

    }

    public function sidebar_behavior() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $adminModel = model(AdminModel::class);
        $user_info = $adminModel->select('firstname, lastname')->where(['id' => $this->admin_details->id, 'active' => 1])->get()->getRow();

        if(!is_object($user_info))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tim thấy thông tin người dùng"));
        
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'sidebar_behavior' => "required|in_list[compact,fixed,sticky]",
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $sidebar_behavior = $this->request->getVar('sidebar_behavior');

        $adminModel->set(['sidebar_behavior' => $sidebar_behavior])->where('id', $this->admin_details->id)->update();

        return $this->response->setJSON(["status"=>TRUE]);

    }

    
    function admin_add() {
        

        $model = model(AdminModel::class);

        $user_data = [
            'firstname' => "",
            'lastname' => "",
            'role' => "Staff",
            'email' => "@sepay.vn",
            'password' => password_hash("", PASSWORD_DEFAULT),
            'active' => 1,
        ];

        $new_admin_id = $model->insert($user_data);

        
        if(!is_numeric($new_admin_id))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không thể thêm mới người dùng do lỗi hệ thống"));

        if($user_data['role'] != 'Admin' && $user_data['role'] != 'SuperAdmin') {
            $adminPermissionFeatureModel = model(AdminPermissionFeatureModel::class);
            $adminPermissionFeatureModel->initialize_permission($new_admin_id);
        }

       
         
        return $this->response->setJSON(array("status"=>true,"user_id" => $new_admin_id));
        
    }

    /*
    function admin_change_p($id='') {
        

        $model = model(AdminModel::class);

        $new_pass = "Hien@2023@SePay";
        $model->set(['password' => password_hash($new_pass, PASSWORD_DEFAULT)])->where(['id' => $id])->update();

         
        return $this->response->setJSON(array("status"=>true,"user_id" => $id));
        
    } */ 
    
}