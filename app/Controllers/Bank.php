<?php

namespace App\Controllers;

use App\Models\BankModel;

class Bank extends BaseController
{
    public function index()
    {
        if (! in_array($this->admin_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $data = [
            'page_title' => 'Bank',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        echo view('templates/sepay/header', $data);
        echo view('bank/index', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajax_list()
    {
        if (! in_array($this->admin_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $bankModel = model(BankModel::class);

        $banks = $bankModel->getDatatables();
        $data = [];

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($banks as $bank) {
            $no++;
            $row = [
                $no,
                sprintf(
                    '<a href="%s"><img src="%s" alt="%s" class="img-fluid" style="max-width: 30px;"> <span class="fw-medium ms-2">%s</span></a>',
                    base_url('bank/edit/' . $bank->id),
                    'https://my.sepay.vn/assets/images/banklogo/' . $bank->icon_path,
                    $bank->brand_name,
                    $bank->brand_name
                ),
                sprintf('<span class="badge bg-%s">%s</span>', $bank->active == 1 ? 'success' : 'danger', $bank->active == 1 ? 'Hoạt động' : 'Đã tắt'),
            ];

            $data[] = $row;
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $bankModel->countAll(),
            'recordsFiltered' => $bankModel->countFiltered(),
            'data' => $data,
        ]);
    }

    public function edit($id)
    {
        if (! in_array($this->admin_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $bankModel = model(BankModel::class);

        $bank = $bankModel->find($id);

        if (! $bank) {
            return redirect()->to(base_url('bank'));
        }

        $bank->invididual_features = json_decode($bank->invididual_features ?: '[]');
        $bank->enterprise_features = json_decode($bank->enterprise_features ?: '[]');

        $data = [
            'page_title' => 'Bank',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
            'bank' => $bank,
        ];

        echo view('templates/sepay/header', $data);
        echo view('bank/edit', $data);
        echo view('templates/sepay/footer', $data); 
    }

    public function update($id)
    {
        if (! in_array($this->admin_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $bankModel = model(BankModel::class);

        $bank = $bankModel->find($id);
        
        if (! $bank) {
            show_404();
        }

        foreach (['invididual', 'enterprise'] as $type) {
            $rules = [
                $type . '_api_connection' => 'permit_empty|in_list[0,1]',
                $type . '_speed' => 'permit_empty',
                $type . '_connection_procedure' => 'permit_empty',
                $type . '_features' => 'permit_empty',
                $type . '_promotion' => 'permit_empty',
                $type . '_description' => 'permit_empty',
                $type . '_order' => 'required|integer',
                $type . '_promotion_short_description' => 'permit_empty',
                $type . '_promotion_button_label' => 'permit_empty',
                $type . '_promotion_description' => 'permit_empty',
            ];
        }

        if (! $this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => implode('<br>', $this->validator->getErrors()),
            ]);
        }

        $data = [];

        foreach (['invididual', 'enterprise'] as $type) {
            $data[$type . '_api_connection'] = $this->request->getPost($type . '_api_connection');
            $data[$type . '_speed'] = $this->request->getPost($type . '_speed');
            $data[$type . '_connection_procedure'] = $this->request->getPost($type . '_connection_procedure');
            $data[$type . '_features'] = json_encode($this->request->getPost($type . '_features') ?: []);
            $data[$type . '_promotion'] = $this->request->getPost($type . '_promotion');
            $data[$type . '_description'] = $this->request->getPost($type . '_description');
            $data[$type . '_order'] = $this->request->getPost($type . '_order');
            $data[$type . '_promotion_short_description'] = $this->request->getPost($type . '_promotion_short_description');
            $data[$type . '_promotion_button_label'] = $this->request->getPost($type . '_promotion_button_label');
            $data[$type . '_promotion_description'] = $this->request->getPost($type . '_promotion_description');
        }

        $bankModel->update($id, $data);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Cập nhật thành công',
        ]);
    }
}
