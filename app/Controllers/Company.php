<?php

namespace App\Controllers;

use App\Models\TransactionsModel;
use App\Models\StransactionsModel;
use App\Models\BankAccountModel;
use App\Models\AdminModel;
use App\Models\UserModel;
use App\Models\CompanyModel;
use App\Models\CompanySubscriptionModel;
use App\Models\CompanyUserModel;
use App\Models\StransactionModel;
use App\Models\LgTokenModel;
use App\Models\CounterModel;
use App\Models\CrmActivityModel;
use App\Models\EmailLogModel;
use App\Models\InvoiceModel;
use App\Models\SimModel;
use App\Models\SimCompanyModel;
use App\Models\PartnerModel;
use App\Models\PartnerCompanyModel;

use CodeIgniter\Controller;
use Config\Services;

class Company extends BaseController
{
    public function index()
    {
        $data = [
            'page_title' => 'Công ty',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if (!has_permission('Company', 'can_view_all'))
            show_404();

        echo view('templates/sepay/header', $data);
        echo view('company/index', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajax_list()
    {

        if (!has_permission('Company', 'can_view_all'))
            show_404();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        $companyModel = model(CompanyModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $stransactionModel = model(StransactionModel::class);

        $bankAccountModel = model(bankAccountModel::class);
        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        $companyUserModel = model(CompanyUserModel::class);
        $productModel = model(ProductModel::class);
        $counterModel = model(CounterModel::class);

        $companies = $companyModel->getDatatables();
        $crmActivityModel = model(CrmActivityModel::class);

        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($companies as $company) {

            $no++;
            $row = array();

            $subscription_details = $companySubscriptionModel->select("tb_autopay_company_subscription.plan_id, tb_autopay_product.name")->join("tb_autopay_product", "tb_autopay_product.id=tb_autopay_company_subscription.plan_id")->where(['tb_autopay_company_subscription.company_id' => $company->id])->get()->getRow();
            if (is_object($subscription_details))
                $subscription = $subscription_details->name;
            else
                $subscription = "";

            $result = $stransactionModel->select("sum(`in`) as `income`")->where(['company_id' => $company->id])->get()->getRow();

            if (is_object($result) && $result->income > 0)
                $income = "<span class='text-success'>" . number_format($result->income) . "</span>";
            else
                $income = 0;

            $result = $counterModel->select("sum(transaction) as `count_rows`")->where(['company_id' => $company->id])->get()->getRow();
            if ($result)
                $count_transactions = $result->count_rows;
            else
                $count_transactions = 0;

            $result = $crmActivityModel->select("count(id) as `num_rows`")->where(['company_id' => $company->id])->get()->getRow();

            $count_activity = $result->num_rows;
            $activity_badge = "";
            if ($count_activity > 0)
                $activity_badge = "<a href='" . base_url('company/activity/' . $company->id) . "'><span class='badge bg-info  rounded-pill ms-2'><i class='bi bi-activity'></i> " . $count_activity . "</span></a>";


            $row[] = $no;
            $row[] =  esc($company->id);

            $row[] = "<a href='" . base_url('company/details/' . $company->id) . "'>" . esc($company->short_name) . "</a>" . $activity_badge;

            $row[] = esc($company->email);
            $row[] = esc($company->lastname . ' ' . $company->firstname);

            $row[] = esc($company->phonenumber);

            $row[] = esc($subscription);
            $row[] = $income;



            if ($count_transactions > 0)
                $row[] = "<span class='text-info'>" . number_format($count_transactions) . "</span>";
            else
                $row[] = number_format($count_transactions);

            $row[] = $company->tr_gcid ? 'Affiliate' : esc($company->tracking_source);

            $row[] = esc($company->created_at);

            $data[] = $row;
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => $companyModel->countAll(),
            "recordsFiltered" => $companyModel->countFiltered(),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }

    public function details($id = '')
    {
        if (!is_numeric($id))
            show_404();


        $data = [
            'page_title' => 'Công ty',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if (!has_permission('Company', 'can_view_all'))
            show_404();

        $companyModel = model(CompanyModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $stransactionModel = model(StransactionModel::class);
        $invoiceModel = model(InvoiceModel::class);

        $bankAccountModel = model(bankAccountModel::class);
        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        $companyUserModel = model(CompanyUserModel::class);
        $userModel = model(UserModel::class);
        $simCompanyModel = model(SimCompanyModel::class);
        $partnerModel = model(PartnerModel::class);
        $partnerCompanyModel = model(PartnerCompanyModel::class);
        $counterModel = model(CounterModel::class);
        $webhooksModel = model(WebhooksModel::class);
        $notificationTelegramModel = model(NotificationTelegramModel::class);
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);

        $data['company_details'] = $companyModel
            ->select('tb_autopay_company.*, tb_autopay_tracking.utm_source as tracking_source, tb_autopay_tracking.utm_medium as tracking_medium, tb_autopay_tracking.utm_campaign as tracking_campaign, tb_autopay_tracking.utm_content as tracking_content, tb_autopay_tracking.utm_term as tracking_term, tb_autopay_tracking.referer as tracking_referer, tb_autopay_company_subscription.status as subscription_status')
            ->join('tb_autopay_tracking', 'tb_autopay_tracking.company_id = tb_autopay_company.id', 'left')
            ->join('tb_autopay_company_subscription', 'tb_autopay_company_subscription.company_id = tb_autopay_company.id', 'left')
            ->where('tb_autopay_company.id', $id)
            ->first();

        if (!is_object($data['company_details']))
            show_404();

        $data['user_details'] = $userModel->select("tb_autopay_user.id,tb_autopay_user.email,tb_autopay_user.firstname,tb_autopay_user.lastname,tb_autopay_user.phonenumber, tb_autopay_user.created_at")->join("tb_autopay_company_user", "tb_autopay_user.id=tb_autopay_company_user.user_id")->where(['tb_autopay_company_user.company_id' => $id, 'tb_autopay_company_user.role' => 'SuperAdmin'])->get()->getRow();


        if (!is_object($data['user_details']))
            show_404();


        $data['subscriptions'] = $companySubscriptionModel->select("tb_autopay_company_subscription.id, tb_autopay_company_subscription.order_id,
        tb_autopay_company_subscription.plan_id,tb_autopay_company_subscription.begin_date,tb_autopay_company_subscription.end_date,tb_autopay_company_subscription.first_payment,tb_autopay_company_subscription.recurring_payment,tb_autopay_company_subscription.billing_cycle,tb_autopay_company_subscription.auto_renew,tb_autopay_company_subscription.status,tb_autopay_company_subscription.monthly_transaction_limit,tb_autopay_company_subscription.created_at,tb_autopay_product.name")->join("tb_autopay_product", "tb_autopay_product.id=tb_autopay_company_subscription.plan_id", "left")->where(['tb_autopay_company_subscription.company_id' => $id])->get()->getResult();

        $data['paid_invoice_count'] = $invoiceModel->where(['company_id' => $id, 'status' => 'Paid'])->countAllResults();
        $data['unpaid_invoice_count'] = $invoiceModel->where(['company_id' => $id, 'status' => 'Unpaid'])->countAllResults();
        $data['stransaction_count'] = $stransactionModel->where(['company_id' => $id])->countAllResults();
        $data['user_count'] = $companyUserModel->where(['company_id' => $id])->countAllResults();

        $data['paid_invoice_sum'] = $invoiceModel->select("sum(total) as `total_amount`")->where(['company_id' => $id, 'status' => 'Paid'])->get()->getRow();

        $data['unpaid_invoice_sum'] = $invoiceModel->select("sum(total) as `total_amount`")->where(['company_id' => $id, 'status' => 'Unpaid'])->get()->getRow();

        $data['stransaction_sum'] = $stransactionModel->select("sum(`in`) as `total_in`")->where(['company_id' => $id])->get()->getRow();

        $data['bank_account_count'] = $bankAccountModel->where(['company_id' => $id])->countAllResults();

        $result = $counterModel->select("sum(transaction) as transaction_count")->where(['company_id' => $id])->get()->getRow();

        if (is_object($result))
            $data['transaction_count'] = $result->transaction_count;
        else
            $data['transaction_count'] = 0;
        $data['sim_count'] = $simCompanyModel->join("tb_autopay_sim", "tb_autopay_sim.id=tb_autopay_sim_company.sim_id")->where(['tb_autopay_sim_company.company_id' => $id])->countAllResults();

        $result = $bankAccountModel->select("last_transaction")->where(['company_id' => $id])->orderBy("last_transaction", "DESC")->get()->getRow();

        if (is_object($result) && $result->last_transaction)
            $data['last_transaction'] = $result;
        else
            $data['last_transaction'] = FALSE;


        $data['partner_details'] = $partnerCompanyModel->select('tb_autopay_partner.id, tb_autopay_partner.firstname, tb_autopay_partner.lastname,tb_autopay_partner_company.created_at')->join("tb_autopay_partner", "tb_autopay_partner.id=tb_autopay_partner_company.partner_id")->where(['tb_autopay_partner_company.company_id' => $id])->get()->getRow();

        $data['partners'] = $partnerModel->select('tb_autopay_partner.id, tb_autopay_partner.firstname, tb_autopay_partner.lastname')->orderBy("id", 'DESC')->get()->getResult();

        $data['webhook_count'] = $webhooksModel->where(['company_id' => $id])->countAllResults();
        $data['telegram_count'] = $notificationTelegramModel->where(['company_id' => $id])->countAllResults();
        $data['lark_count'] = $notificationLarkMessengerModel->where(['company_id' => $id])->countAllResults();
        $result = $counterModel->select("sum(chat) as chat_count")->where(['company_id' => $id])->get()->getRow();

        if (is_object($result))
            $data['chat_count'] = $result->chat_count;
        else
            $data['chat_count'] = 0;

        echo view('templates/sepay/header', $data);
        echo view('company/details', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function subscription($id = '')
    {
        if (!is_numeric($id))
            show_404();


        $data = [
            'page_title' => 'Subscription',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if (!has_permission('Subscription', 'can_view_all'))
            show_404();

        $companyModel = model(CompanyModel::class);



        $companySubscriptionModel = model(CompanySubscriptionModel::class);


        $data['company_details'] = $companyModel->find($id);

        if (!is_object($data['company_details']))
            show_404();




        $data['subscriptions'] = $companySubscriptionModel->select("tb_autopay_company_subscription.id, tb_autopay_company_subscription.order_id,
        tb_autopay_company_subscription.plan_id,tb_autopay_company_subscription.begin_date,tb_autopay_company_subscription.end_date,tb_autopay_company_subscription.first_payment,tb_autopay_company_subscription.recurring_payment,tb_autopay_company_subscription.billing_cycle,tb_autopay_company_subscription.auto_renew,tb_autopay_company_subscription.status,tb_autopay_company_subscription.monthly_transaction_limit,tb_autopay_company_subscription.created_at,tb_autopay_product.name")->join("tb_autopay_product", "tb_autopay_product.id=tb_autopay_company_subscription.plan_id", "left")->where(['tb_autopay_company_subscription.company_id' => $id])->get()->getResult();



        echo view('templates/sepay/header', $data);
        echo view('company/subscription', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function sim($id = '')
    {
        if (!is_numeric($id))
            show_404();


        $data = [
            'page_title' => 'SIM',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if (!has_permission('Sim', 'can_view_all'))
            show_404();

        $companyModel = model(CompanyModel::class);



        $simCompanyModel = model(SimCompanyModel::class);
        $simModel = model(SimModel::class);


        $data['company_details'] = $companyModel->find($id);

        if (!is_object($data['company_details']))
            show_404();


        $data['sims'] = $simCompanyModel->select("tb_autopay_sim.sim_phonenumber,tb_autopay_sim_company.id,tb_autopay_sim_company.sim_id,tb_autopay_sim.description,tb_autopay_sim.active,tb_autopay_sim.is_shared,tb_autopay_sim_company.created_at,tb_autopay_sim.device_type")->join("tb_autopay_sim", "tb_autopay_sim.id=tb_autopay_sim_company.sim_id")->where(['tb_autopay_sim_company.company_id' => $id])->orderBy("tb_autopay_sim_company.created_at", "DESC")->get()->getResult();

        //select sim_phonenumber from tb_autopay_sim left join tb_autopay_sim_company on tb_autopay_sim_company.sim_id=tb_autopay_sim.id where tb_autopay_sim_company.id is null and tb_autopay_sim.active=1

        $data['availability_sims'] = $simModel->select("tb_autopay_sim.sim_phonenumber,tb_autopay_sim.id, tb_autopay_sim.description")->join("tb_autopay_sim_company", "tb_autopay_sim_company.sim_id=tb_autopay_sim.id", "left")->where("tb_autopay_sim_company.id is null and tb_autopay_sim.active=1")->get()->getResult();



        echo view('templates/sepay/header', $data);
        echo view('company/sim', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function invoice($id = '')
    {
        if (!is_numeric($id))
            show_404();

        if (!has_permission('Invoice', 'can_view_all'))
            show_404();

        $data = [
            'page_title' => 'Hoá đơn',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        $companyModel = model(CompanyModel::class);

        $invoiceModel = model(InvoiceModel::class);


        $data['company_details'] = $companyModel->find($id);

        if (!is_object($data['company_details']))
            show_404();

        $data['unpaid_invoice_count'] = $invoiceModel->where(['company_id' => $id, 'status' => 'Unpaid'])->countAllResults();

        $data['paid_invoice_count'] = $invoiceModel->where(['company_id' => $id, 'status' => 'Paid'])->countAllResults();

        $data['paid_invoice_sum'] = $invoiceModel->select("sum(total) as `total_amount`")->where(['company_id' => $id, 'status' => 'Paid'])->get()->getRow();

        $data['unpaid_invoice_sum'] = $invoiceModel->select("sum(total) as `total_amount`")->where(['company_id' => $id, 'status' => 'Unpaid'])->get()->getRow();


        $data['cancelled_invoice_count'] = $invoiceModel->where(['company_id' => $id, 'status' => 'Cancelled'])->countAllResults();

        $data['cancelled_invoice_sum'] = $invoiceModel->select("sum(total) as `total_amount`")->where(['company_id' => $id, 'status' => 'Cancelled'])->get()->getRow();

        echo view('templates/sepay/header', $data);
        echo view('company/invoice', $data);
        echo view('templates/sepay/footer', $data);
    }


    public function ticket($id = '')
    {
        if (!is_numeric($id))
            show_404();

        if (!has_permission('Invoice', 'can_view_all'))
            show_404();

        $data = [
            'page_title' => 'Hỗ trợ',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        $companyModel = model(CompanyModel::class);

        $ticketModel = model(TicketModel::class);


        $data['company_details'] = $companyModel->find($id);

        if (!is_object($data['company_details']))
            show_404();


        echo view('templates/sepay/header', $data);
        echo view('company/ticket', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function invoice_ajax_list($id = '')
    {

        if (!has_permission('Invoice', 'can_view_all'))
            show_404();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        $invoiceModel = model(InvoiceModel::class);

        $invoices = $invoiceModel->getDatatables($id);

        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($invoices as $invoice) {


            $no++;
            $row = array();

            $row[] = $no;
            $row[] = "<a href='" . base_url('invoice/edit/' . $invoice->id) . "'>#" . esc($invoice->id) . "</a>";

            if ($invoice->type == "NewOrder")
                $row[] = "<span class='text-info'>Đăng ký mới</span>";
            else if ($invoice->type == "Recurring")
                $row[] = "<span class='text-warning'>Gia hạn</span>";
            else if ($invoice->type == "Credit")
                $row[] = "<span class=''>Thêm tiền</span>";
            else if ($invoice->type == "Excess")
                $row[] = "<span class='text-danger'>Vượt hạn mức</span>";
            else if ($invoice->type == "SubscriptionChange")
                $row[] = "<span class='text-success'>Đổi gói dịch vụ</span>";
            else
                $row[] = esc($invoice->type);

            if ($invoice->status == "Unpaid")
                $row[] = "<span class='badge rounded-pill bg-danger'>Chưa thanh toán</span>";
            else if ($invoice->status == "Paid")
                $row[] = "<span class='badge rounded-pill bg-success'>Đã thanh toán</span>";
            else if ($invoice->status == "Cancelled")
                $row[] = "<span class='badge rounded-pill bg-secondary'>Đã hủy</span>";
            else if ($invoice->status == "Refunded")
                $row[] = "<span class='badge rounded-pill bg-warning'>Đã hoàn tiền</span>";
            else
                $row[] = esc($invoice->status);

            $row[] = number_format($invoice->total) . " đ";

            if ($invoice->tax_issued == 1)
                $row[] = "<span class='badge rounded-pill bg-success'>Đã xuất</span><br><span class='text-info'>" . esc($invoice->tax_issued_id) . "</span>";
            else
                $row[] = "<span class='badge rounded-pill bg-danger'>Chưa xuất</span>";


            $row[] = esc($invoice->date);
            $row[] = "<a href='" . base_url('invoice/edit/' . $invoice->id) . "'>Xem <i class='bi bi-chevron-right'></i></a>";


            $data[] = $row;
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => $invoiceModel->countAll($id),
            "recordsFiltered" => $invoiceModel->countFiltered($id),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }


    public function ticket_ajax_list($id = '')
    {

        if (!has_permission('Ticket', 'can_view_all'))
            show_404();

        $ticketModel = model(TicketModel::class);

        $tickets = $ticketModel->getDatatables($id);

        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($tickets as $ticket) {

            $no++;
            $row = array();

            $row[] = $no;
            $row[] = "<a href='" . base_url('ticket/details/' . esc($ticket->id)) . "'>#" . esc($ticket->id) . ' - ' . ellipsize(esc($ticket->subject), 110, 1, '..') . "</a>";

            $row[] = get_ticket_status_badge($ticket->status);

            if (is_numeric($ticket->company_id) && $ticket->company_id > 0)
                $row[] = "<a href='" . base_url('company/details/' . esc($ticket->company_id)) . "'>#" . esc($ticket->company_id) . ' - ' . ellipsize(esc($ticket->name), 50, 1, '..') . "</a>";
            else
                $row[] = ellipsize(esc($ticket->name), 50, 1, '..');



            $row[] =  timespan(esc($ticket->created_at), 1);

            $data[] = $row;
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => $ticketModel->countAll($id),
            "recordsFiltered" => $ticketModel->countFiltered($id),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }

    public function stransaction($id = '')
    {
        if (!is_numeric($id))
            show_404();

        if (!has_permission('Stransaction', 'can_view_all'))
            show_404();

        $data = [
            'page_title' => 'Giao dịch',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        $companyModel = model(CompanyModel::class);

        $stransactionModel = model(StransactionModel::class);

        $data['company_details'] = $companyModel->find($id);

        if (!is_object($data['company_details']))
            show_404();

        $data['in_stransaction_count'] = $stransactionModel->where(['company_id' => $data['company_details']->id, 'in>' => 0])->countAllResults();

        $data['out_stransaction_count'] = $stransactionModel->where(['company_id' => $data['company_details']->id, 'out>' => 0])->countAllResults();

        $data['in_stransaction_sum'] = $stransactionModel->select("sum(`in`) as `total_amount`")->where(['company_id' => $data['company_details']->id])->get()->getRow();

        $data['out_stransaction_sum'] = $stransactionModel->select("sum(`out`) as `total_amount`")->where(['company_id' => $data['company_details']->id])->get()->getRow();

        echo view('templates/sepay/header', $data);
        echo view('company/stransaction', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function stransaction_ajax_list($id = '')
    {

        if (!has_permission('Stransaction', 'can_view_all'))
            show_404();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        $stransactionModel = model(StransactionModel::class);

        $stransactions = $stransactionModel->getDatatables($id);

        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($stransactions as $stransaction) {


            $no++;
            $row = array();

            $row[] = $no;
            //$row[] = "<a href='" . base_url('stransactions/details/' . $stransaction->id) . "'>#". esc($stransaction->id) . "</a>";
            $row[] = esc($stransaction->id);

            $row[] = esc($stransaction->date);


            $row[] = number_format($stransaction->in) . " đ";
            $row[] = number_format($stransaction->out) . " đ";

            $row[] = "<a href='" . base_url('invoice/edit/' . $stransaction->invoice_id) . "'>" . $stransaction->invoice_id . "</a>";
            $actions = '';

            if (has_permission('Stransaction', 'can_edit')) {
                $actions .= "<button onclick='edit_payment(" . $stransaction->id . ")' class='btn btn-sm btn-warning me-3  mt-2'><i class='bi bi-pencil'> Sửa</i></button>";
            }
            if (has_permission('Stransaction', 'can_delete')) {
                $actions .= "<button onclick='delete_payment(" . $stransaction->id . ")' class='btn btn-sm btn-danger  mt-2'><i class='bi bi-trash'> Xóa</i></button>";
            }

            $row[] = $actions;

            $data[] = $row;
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => $stransactionModel->countAll($id),
            "recordsFiltered" => $stransactionModel->countFiltered($id),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }

    public function login_as_client($user_id = '')
    {

        $companyUserDetails = model(CompanyUserModel::class)->where(['user_id' => $user_id])->first();

        if (! $companyUserDetails) {
            set_alert('error', 'Đã có lỗi xảy ra, vui lòng báo kỹ thuật');
            return redirect()->to(base_url());
        }

        $companyDetails = model(CompanyModel::class)->where(['id' => $companyUserDetails->company_id])->first();

        if (! $companyDetails) {
            set_alert('error', 'Đã có lỗi xảy ra, vui lòng báo kỹ thuật');
            return redirect()->to(base_url());
        }

        if (in_array($companyDetails->id, [3, 4, 8]) && !in_array($this->admin_details->role, ['Admin', 'SuperAdmin'])) {
            set_alert('error', 'Bạn không đủ quyền thực hiện thao tác này');
            return redirect()->to(base_url());
        }

        $lgToken = model(LgTokenModel::class);

        $token = random_string('alnum', 36);

        $lgToken->insert(['user_id' => $user_id, 'admin_id' => $this->admin_details->id, 'token' => $token]);

        return redirect()->to(getenv('CLIENT_BASE_URL') . "login/token/" . $token);
    }


    public function activity($id = '')
    {
        if (!is_numeric($id))
            show_404();

        if (!has_permission('CrmActivity', 'can_view_all'))
            show_404();

        $data = [
            'page_title' => 'CSKH',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        $companyModel = model(CompanyModel::class);

        $crmActivityModel = model(CrmActivityModel::class);

        $data['company_details'] = $companyModel->find($id);

        if (!is_object($data['company_details']))
            show_404();


        echo view('templates/sepay/header', $data);
        echo view('company/activity', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function activity_ajax_list($id = '')
    {

        if (!has_permission('CrmActivity', 'can_view_all'))
            show_404();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        $crmActivityModel = model(CrmActivityModel::class);
        $adminModel = model(AdminModel::class);

        $activities = $crmActivityModel->getDatatables($id);

        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($activities as $activity) {
            $admin_details = $adminModel->select('firstname, lastname, email')->where(['id' => $activity->admin_id])->get()->getRow();
            if ($admin_details)
                $admin_full_name = esc($admin_details->lastname .  " " . $admin_details->firstname);
            else
                $admin_full_name = "";

            $no++;
            $row = array();

            $row[] = $no;


            $row[] = date("H:i:s d/m/Y", strtotime($activity->activity_at));
            $row[] = nl2br(esc($activity->content));
            $row[] = esc($activity->type);
            $row[] = esc($admin_full_name);
            $row[] = "<button onclick='edit_activity(" . $activity->id . ")' class='btn btn-sm btn-warning me-3  mt-2'><i class='bi bi-pencil'> Sửa</i></button> <button onclick='delete_activity(" . $activity->id . ")' class='btn btn-sm btn-danger  mt-2'><i class='bi bi-trash'> Xóa</i></button>";

            $data[] = $row;
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => $crmActivityModel->countAll($id),
            "recordsFiltered" => $crmActivityModel->countFiltered($id),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }


    public function ajax_status_update()
    {

        if (!has_permission('Company', 'can_edit'))
            return $this->response->setJSON(array('status' => FALSE, 'message' => "Bạn không có quyền thực hiện thao tác này"));

        $validation =  \Config\Services::validation();

        if (! $this->validate([
            'id' => ['label' => 'ID', "rules" => "required|is_natural"],
            'status' => ['label' => 'Status', "rules" => "required|in_list[Pending,Active,Suspended,Terminated,Cancelled,Fraud]"],
        ])) {
            return $this->response->setJSON(array('status' => FALSE, 'message' => implode(". ", $validation->getErrors())));
        }

        $company_id = $this->request->getVar('id');

        $data_update = [
            'status' => $this->request->getVar('status')
        ];

        $companyModel = model(CompanyModel::class);

        $companyModel->set($data_update)->where(['id' => $company_id])->update();

        add_admin_log(array('data_id' => $company_id, 'data_type' => 'company_edit', 'description' => 'Sửa status sang ' . $data_update['status'], 'admin_id' => $this->admin_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success'));


        return $this->response->setJSON(["status" => TRUE]);
    }

    public function ajax_login_update()
    {

        if (!has_permission('Company', 'can_edit'))
            return $this->response->setJSON(array('status' => FALSE, 'message' => "Bạn không có quyền thực hiện thao tác này"));

        $validation =  \Config\Services::validation();

        if (! $this->validate([
            'id' => ['label' => 'ID', "rules" => "required|is_natural"],
            'active' => ['label' => 'Active', "rules" => "required|in_list[0,1]"],
        ])) {
            return $this->response->setJSON(array('status' => FALSE, 'message' => implode(". ", $validation->getErrors())));
        }

        $company_id = $this->request->getVar('id');

        $data_update = [
            'active' => $this->request->getVar('active')
        ];

        $companyModel = model(CompanyModel::class);

        $companyModel->set($data_update)->where(['id' => $company_id])->update();

        add_admin_log(array('data_id' => $company_id, 'data_type' => 'company_edit', 'description' => 'Sửa active sang ' . $data_update['active'], 'admin_id' => $this->admin_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success'));


        return $this->response->setJSON(["status" => TRUE]);
    }


    public function ajax_update_partner()
    {
        if (!has_permission('Company', 'can_edit'))
            return $this->response->setJSON(array('status' => FALSE, 'message' => "Bạn không có quyền thực hiện thao tác này"));

        $validation =  \Config\Services::validation();

        if (! $this->validate([
            'company_id' => ['label' => 'Company ID', "rules" => "required|is_natural"],
            'partner_id' => ['label' => 'Partner ID', "rules" => "required|is_natural"],
        ])) {
            return $this->response->setJSON(array('status' => FALSE, 'message' => implode(". ", $validation->getErrors())));
        }

        $company_id = $this->request->getVar('company_id');
        $partner_id = $this->request->getVar('partner_id');

        $partnerCompanyModel = model(PartnerCompanyModel::class);

        $result = $partnerCompanyModel->where(['company_id' => $company_id])->get()->getRow();

        if (is_object($result)) {
            $partnerCompanyModel->set(['partner_id' => $partner_id])->where(['id' => $result->id])->update();
        } else {
            $partnerCompanyModel->insert(['partner_id' => $partner_id, 'company_id' => $company_id, 'type' => 'Manual']);
        }

        return $this->response->setJSON(["status" => TRUE]);
    }


    public function consulting_scenario($id = '')
    {
        if (!is_numeric($id))
            show_404();

        if (!has_permission('CrmActivity', 'can_view_all'))
            show_404();

        $data = [
            'page_title' => 'Tư vấn',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        $companyModel = model(CompanyModel::class);

        $crmActivityModel = model(CrmActivityModel::class);

        $stransactionModel = model(StransactionModel::class);

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        $invoiceModel = model(InvoiceModel::class);

        $counterModel = model(CounterModel::class);
        $notificationTelegramModel = model(NotificationTelegramModel::class);
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $webhooksModel = model(WebhooksModel::class);

        $data['company_details'] = $companyModel->find($id);

        if (!is_object($data['company_details']))
            show_404();

        $data['subscription_details'] = $companySubscriptionModel->where(['company_id' => $id])->get()->getRow();

        $data['invoice_new_order'] = $invoiceModel->where(['company_id' => $id, 'type' => 'NewOrder'])->orderBy('id', 'desc')->get()->getRow();
        $data['invoice_recurring'] = $invoiceModel->where(['company_id' => $id, 'type' => 'Recurring'])->orderBy('id', 'desc')->get()->getRow();
        $data['invoice_excess'] = $invoiceModel->where(['company_id' => $id, 'type' => 'Excess'])->orderBy('id', 'desc')->get()->getRow();

        $data['count_bank_account'] = $bankAccountModel->where(['company_id' => $id, 'last_transaction!=' => NULL])->countAllResults();

        $result = $counterModel->select("sum(transaction) as transaction_count")->where(['company_id' => $id])->get()->getRow();

        if (is_object($result))
            $data['transaction_count'] = $result->transaction_count;
        else
            $data['transaction_count'] = 0;

        $data['count_telegram'] = $notificationTelegramModel->where(['company_id' => $id])->countAllResults();
        $data['count_lark'] = $notificationLarkMessengerModel->where(['company_id' => $id])->countAllResults();
        $data['count_webhook'] = $webhooksModel->where(['company_id' => $id])->countAllResults();

        $data['limit_trans_this_period'] = 0;
        $data['count_trans_this_period'] = 0;

        if (is_object($data['subscription_details'])) {
            $result = $counterModel->select("sum(transaction) as `sum_transaction`", FALSE)->where(['company_id' => $id, 'date>=' => $data['subscription_details']->begin_date, 'date<=' => $data['subscription_details']->end_date])->get()->getRow();
            if (is_object($result) && is_numeric($result->sum_transaction))
                $data['count_trans_this_period'] = $result->sum_transaction;

            $data['limit_trans_this_period'] = $data['subscription_details']->monthly_transaction_limit * get_month_by_billing_cycle($data['subscription_details']->billing_cycle);
        }


        echo view('templates/sepay/header', $data);
        echo view('company/consulting_scenario', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function bankaccount($id = '')
    {
        if (!is_numeric($id))
            show_404();

        if (!has_permission('BankAccount', 'can_view_all'))
            show_404();

        $data = [
            'page_title' => 'Bank Account',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        $companyModel = model(CompanyModel::class);

        $bankAccountModel = model(BankAccountModel::class);

        $data['company_details'] = $companyModel->find($id);

        if (!is_object($data['company_details']))
            show_404();


        echo view('templates/sepay/header', $data);
        echo view('company/bankaccount', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajax_user_update()
    {
        if (!in_array($this->admin_details->role, ['Admin', 'SuperAdmin']))
            return $this->response->setJSON(array('status' => FALSE, 'message' => "Bạn không có quyền thực hiện thao tác này"));

        $validation =  \Config\Services::validation();

        if (! $this->validate([
            'user_id' => ['label' => 'Company ID', "rules" => "required|is_natural"],
            'email' => ['label' => 'Email', 'rules' => "required|valid_email"],
            'phonenumber' => ['label' => 'Số điện thoại', 'rules' => "required|max_length[20]"],
        ])) {
            return $this->response->setJSON(array('status' => FALSE, 'message' => implode(". ", $validation->getErrors())));
        }

        $user_id = $this->request->getVar('user_id');
        $email = $this->request->getVar('email');
        $userModel = model(UserModel::class);


        $user_details = $userModel->find($user_id);

        if (!is_object($user_details))
            return $this->response->setJSON(array('status' => FALSE, 'message' => "Không tìm thấy người dùng này"));


        $data_update = [
            'phonenumber' => $this->request->getVar('phonenumber'),
        ];

        if ($user_details->email != $email) {
            $result = $userModel->where(['email' => $email])->get()->getRow();
            if (is_object($result))
                return $this->response->setJSON(array('status' => FALSE, 'message' => "Email trên đã tồn tại"));
            else
                $data_update['email'] = $email;
        }

        $result = $userModel->set($data_update)->where(['id' => $user_id])->update();

        add_admin_log(array('data_id' => $user_id, 'data_type' => 'user_edit', 'description' => 'Sửa Super Admin', 'admin_id' => $this->admin_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success'));


        set_alert("success", "Sửa thông tin thành công");

        return $this->response->setJSON(["status" => TRUE]);
    }

    public function tax_info($id = '')
    {
        if (!is_numeric($id))
            show_404();


        $data = [
            'page_title' => 'Tax Info',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if (!has_permission('TaxInfo', 'can_view_all'))
            show_404();

        $companyModel = model(CompanyModel::class);

        $taxInfoModel = model(TaxInfoModel::class);


        $data['company_details'] = $companyModel->find($id);

        if (!is_object($data['company_details']))
            show_404();


        $data['tax_infos'] = $taxInfoModel->select(
            "tb_autopay_tax_info.id,
            tb_autopay_tax_info.type,
            tb_autopay_tax_info.company_id,
            tb_autopay_tax_info.merchant_id,
            tb_autopay_tax_info.name,
            tb_autopay_tax_info.email,
            tb_autopay_tax_info.tax_code,
            tb_autopay_tax_info.address1,
            tb_autopay_tax_info.note,
            tb_autopay_tax_info.province,
            tb_autopay_tax_info.district,
            tb_autopay_tax_info.ward,
            tb_autopay_tax_info.created_at,
            tb_autopay_provinces.name as `province_name`,
            tb_autopay_districts.name as `district_name`,
            tb_autopay_wards.name as `ward_name`
        "
        )
            ->join("tb_autopay_provinces", "tb_autopay_provinces.code=tb_autopay_tax_info.province", "left")
            ->join("tb_autopay_districts", "tb_autopay_districts.code=tb_autopay_tax_info.district", "left")
            ->join("tb_autopay_wards", "tb_autopay_wards.code=tb_autopay_tax_info.ward", "left")
            ->where(['tb_autopay_tax_info.company_id' => $id])->orderBy('id', 'DESC')->get()->getResult();

        echo view('templates/sepay/header', $data);
        echo view('company/taxinfo', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function update_status_bulk($id = '')
    {
        if ($this->admin_details->role != 'SuperAdmin') {
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }

        if (! $this->validate([
            'status' => ['label' => 'Trạng thái', 'rules' => 'required|in_list[Active,Suspended,Cancelled]'],
        ])) {
            return $this->response->setJSON(['status' => false, 'message' => implode('. ', $this->validator->getErrors())]);
        }

        $companyModel = model(CompanyModel::class);
        $company = $companyModel->find($id);

        if (! $company) {
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy công ty']);
        }

        if (in_array($id, [1, 3, 8])) {
            return $this->response->setJSON(['status' => false, 'message' => 'Không thể suspend công ty này']);
        }

        $status = $this->request->getPost('status');
        $companyStatus = $status;

        model(CompanySubscriptionModel::class)
            ->set('status', $companyStatus)
            ->where('company_id', $id)
            ->update();

        if ($status === 'Cancelled') {
            model(InvoiceModel::class)
                ->where('company_id', $id)
                ->where('status', 'Unpaid')
                ->set('status', 'Cancelled')
                ->update();

            $companyStatus = 'Suspended';
        }

        $companyModel->update($id, ['status' => $companyStatus]);

        add_admin_log([
            'data_id' => $id,
            'data_type' => 'company_subscription_status_change',
            'description' => 'Thay đổi trạng thái dịch vụ và công ty sang ' . $companyStatus,
            'admin_id' => $this->admin_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);

        set_alert('success', 'Thay đổi trạng thái thành công');

        return $this->response->setJSON(['status' => true]);
    }

    public function email_sent($id)
    {
        if (! has_permission('EmailSent', 'can_view_all')) {
            show_404();
        }

        $company = model(CompanyModel::class)->find($id);

        if (! $company) {
            show_404();
        }

        $data = [
            'page_title' => 'Email đã gửi',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
            'company_details' => $company,
        ];

        echo view('templates/sepay/header', $data);
        echo view('company/email-sent', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajax_email_sent($companyId)
    {
        if (! $this->request->isAJAX() || ! has_permission('EmailSent', 'can_view_all')) {
            show_404();
        }

        $company = model(CompanyModel::class)->find($companyId);

        if (! $company) {
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy công ty']);
        }

        $emailLogModel = model(EmailLogModel::class);
        $emails = $emailLogModel->getDatatables($this->request, $companyId);

        $data = [];

        foreach ($emails as $email) {
            $row = [];

            $row[] = sprintf('<a href="#" class="email-subject-link" data-id="%s">%s</a>', $email->id, $email->subject);
            $row[] = $email->email_type;
            $row[] = "#{$email->data_id}";
            $row[] = $email->email_to;
            $row[] = $email->status === 'Sent' ? '<span class="badge bg-success">Đã gửi</span>' : '<span class="badge bg-danger">Lỗi</span>';
            $row[] = $email->created_at;

            $data[] = $row;
        }

        return $this->response->setJSON([
            'draw' => $this->request->getPost('draw'),
            'recordsTotal' => $emailLogModel->countAll($companyId),
            'recordsFiltered' => $emailLogModel->countFiltered($this->request, $companyId),
            'data' => $data,
        ]);
    }

    public function ajax_get_email_details($emailId)
    {
        if (! $this->request->isAJAX() || ! has_permission('EmailSent', 'can_view_all')) {
            show_404();
        }

        $email = model(EmailLogModel::class)->find($emailId);

        return $this->response->setJSON([
            'status' => true,
            'data' => $email,
        ]);
    }
}
