<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use <PERSON>Igniter\HTTP\CLIRequest;
use <PERSON>Igniter\HTTP\IncomingRequest;
use <PERSON>Igniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

use CodeIgniter\Config\Services;

/**
 * Class BaseController
 *
 * BaseController provides a convenient place for loading components
 * and performing functions that are needed by all your controllers.
 * Extend this class in any new controllers:
 *     class Home extends BaseController
 *
 * For security be sure to declare any new methods as protected or private.
 */
abstract class BaseController extends Controller
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    /**
     * An array of helpers to be loaded automatically upon
     * class instantiation. These helpers will be available
     * to all other controllers that extend BaseController.
     *
     * @var array
     */
    protected $helpers = ['general','form','url','text'];

    protected $admin_details;

    protected $admin_session;

    /**
     * Be sure to declare properties for any property fetch you initialized.
     * The creation of dynamic property is deprecated in PHP 8.2.
     */
    // protected $session;

    /**
     * Constructor.
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);

        // Preload any models, libraries, etc, here.

        // E.g.: $this->session = \Config\Services::session();

        $this->uri = new \CodeIgniter\HTTP\URI();

        $session = session();
 
        if(isset($session->get('admin_logged_in')['admin_id'])) {
            $admin_id = $session->get('admin_logged_in')['admin_id'];

            $adminModel = new \App\Models\AdminModel();
    
            $admin_result = $adminModel->select('id,email,firstname,lastname,active,theme,sidebar_toggle,sidebar_behavior,role')->where(['active'=>1,'id'=>$admin_id])->get()->getRow();
    
            if(is_object($admin_result)) {
                $this->admin_details = $admin_result;
            }

        } 

        $this->admin_session = $session->get('admin_logged_in');

    }

    protected function responseResult($code, $message, $data = 0): ResponseInterface
    {
        $response = service('response');
        $data_res = [
            'code' => $code,
            'message' => $message,
        ];
        if ($data != 0 || $data == []) {
            $data_res['data'] = $data;
        }
        $csrfToken = csrf_hash(); 
        $response->setStatusCode($code);
        $response->setJSON($data_res);
        $response->setHeader('X-CSRF-TOKEN', $csrfToken);
        return $response;
    }
    
    public function callApi(string $method, string $url, string $token = '', array $data = []): array
    {
        $client = Services::curlrequest();
        $options = [
            'headers' => [
                'Accept' => 'application/json',
            ],
            'http_errors' => false,
        ];
        // Nếu có token, thêm vào header
        if ($token) {
            $options['headers']['Authorization'] = "Bearer $token";
        }
        // Chọn cách gửi dữ liệu dựa trên phương thức
        if (strtoupper($method) === 'POST') {
            $options['json'] = $data; // Dữ liệu gửi qua POST
        } 
        // Thực hiện yêu cầu cURL
        $response = $client->request($method, $url, $options);
        $responseData = $response->getBody();
        $responseData = json_decode($responseData, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            
            log_message("debug",print_r(json_last_error(),true));
            return array(
                'code'=>500,
                'message'=> "Lỗi api",
            );
        }
        // Trả về dữ liệu phản hồi
        return  $responseData;
                   
    }

}
