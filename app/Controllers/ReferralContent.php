<?php

namespace App\Controllers;

use App\Models\ReferralContentModel;

class ReferralContent extends BaseController
{
    public function index()
    {
        if (! has_permission('Referral', 'can_view_all')) {
            show_404();
        }

        $model = model(ReferralContentModel::class);

        $data = [
            'page_title' => 'Tinh chỉnh nội dung',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
            'manuals' => $model
                ->where('type', 'manual')
                ->orderBy('order', 'asc')
                ->findAll(),
            'faqs' => $model
                ->where('type', 'faq')
                ->orderBy('order', 'asc')
                ->findAll(),
        ];

        echo view('templates/sepay/header', $data);
        echo view('referral/contents/index', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function create()
    {
        if (! has_permission('Referral', 'can_add')) {
            show_404();
        }

        $type = $this->request->getGet('type');

        if ($type === 'manual') {
            $title = 'Hướng dẫn giới thiệu';
        } elseif ($type === 'faq') {
            $title = 'Câu hỏi thường gặp';
        } else {
            return redirect()->to('/referral/contents');
        }

        $data = [
            'page_title' => $title,
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
            'type' => $type,
        ];

        echo view('templates/sepay/header', $data);
        echo view('referral/contents/create', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function store()
    {
        if (! has_permission('Referral', 'can_add')) {
            show_404();
        }

        if (! $this->validate([
            'type' => ['label' => 'Loại', 'rules' => 'required|in_list[manual,faq]'],
            'title' => ['label' => 'Tiêu đề', 'rules' => 'required'],
            'description' => ['label' => 'Mô tả', 'rules' => 'required'],
            'content' => ['label' => 'Nội dung', 'rules' => 'permit_empty'],
            'active' => ['label' => 'Kích hoạt', 'rules' => 'permit_empty|in_list[on]'],
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'message' => implode('. ', $this->validator->getErrors()),
            ]);
        }

        $type = $this->request->getPost('type');

        $title = $type === 'manual' ? 'Hướng dẫn giới thiệu' : 'Câu hỏi thường gặp';

        $result = model(ReferralContentModel::class)->insert([
            'type' => $type,
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'content' => $this->request->getPost('content'),
            'active' => $this->request->getPost('active') == 'on' ? true : false,
        ]);

        add_admin_log([
            'data_id' => $result,
            'data_type' => "referral_{$type}_add",
            'description' => 'Tạo ' . $title,
            'admin_id' => $this->admin_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);

        set_alert('success', $title . ' đã được tạo thành công.');

        return $this->response->setJSON([
            'status' => $result,
            'message' => $result ? $title .' đã được tạo thành công.' : 'Không thể tạo ' . $title . '.',
            'data' => [
                'id' => $result,
            ],
        ]);
    }

    public function edit(int $id)
    {
        if (! has_permission('Referral', 'can_edit')) {
            show_404();
        }

        $referralManual = model(ReferralContentModel::class)->find($id);

        if (! $referralManual) {
            show_404();
        }

        $title = $referralManual->type === 'manual' ? 'Hướng dẫn giới thiệu' : 'Câu hỏi thường gặp';

        $data = [
            'page_title' => 'Chỉnh sửa ' . $title,
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
            'referralManual' => $referralManual,
        ];

        echo view('templates/sepay/header', $data);
        echo view('referral/contents/edit', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function update(int $id)
    {
        if (! has_permission('Referral', 'can_edit')) {
            show_404();
        }

        $referralContentModel = model(ReferralContentModel::class);

        $referralManual = $referralContentModel->find($id);

        if (! $referralManual) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy hướng dẫn giới thiệu.',
            ]);
        }

        if (! $this->validate([
            'title' => ['label' => 'Tiêu đề', 'rules' => 'required'],
            'description' => ['label' => 'Mô tả', 'rules' => 'required'],
            'content' => ['label' => 'Nội dung', 'rules' => 'permit_empty'],
            'active' => ['label' => 'Kích hoạt', 'rules' => 'permit_empty|in_list[on]'],
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'message' => implode('. ', $this->validator->getErrors()),
            ]);
        }

        $result = $referralContentModel->update($id, [
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'content' => $this->request->getPost('content'),
            'active' => $this->request->getPost('active') == 'on' ? true : false,
        ]);

        $title = $referralManual->type === 'manual' ? 'Hướng dẫn giới thiệu' : 'Câu hỏi thường gặp';

        add_admin_log([
            'data_id' => $id,
            'data_type' => "referral_{$referralManual->type}_edit",
            'description' => 'Chỉnh sửa ' . $title,
            'admin_id' => $this->admin_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);

        return $this->response->setJSON([
            'status' => $result,
            'message' => $result ? $title . ' đã được cập nhật thành công.' : 'Không thể cập nhật ' . $title . '.',
        ]);
    }

    public function delete(int $id)
    {
        if (! has_permission('Referral', 'can_delete')) {
            show_404();
        }

        $referralContentModel = model(ReferralContentModel::class);

        $referralManual = $referralContentModel->find($id);

        if (! $referralManual) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy dữ liệu.',
            ]);
        }

        $referralContentModel->delete($id);

        $title = $referralManual->type === 'manual' ? 'Hướng dẫn giới thiệu' : 'Câu hỏi thường gặp';

        add_admin_log([
            'data_id' => $id,
            'data_type' => "referral_{$referralManual->type}_delete",
            'description' => 'Xóa ' . $title,
            'admin_id' => $this->admin_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);

        set_alert('success', $title . ' đã được xóa thành công.');

        return $this->response->setJSON([
            'status' => true,
            'message' => $title . ' đã được xóa thành công.',
        ]);
    }

    public function updateOrder()
    {
        if (! has_permission('Referral', 'can_edit')) {
            show_404();
        }

        $orders = $this->request->getPost('order');

        if (! $orders) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không có dữ liệu sắp xếp.',
            ]);
        }

        $referralContentModel = model(ReferralContentModel::class);

        foreach ($orders as $index => $id) {
            $referralContentModel->update($id, ['order' => $index + 1]);
        }

        return $this->response->setJSON([
            'status' => true,
            'message' => 'Sắp xếp danh sách thành công.',
        ]);
    }
}
