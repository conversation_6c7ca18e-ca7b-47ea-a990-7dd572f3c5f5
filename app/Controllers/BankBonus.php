<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\BankAccountModel;
use App\Models\BankBonusModel;
use App\Models\BankModel;

class BankBonus extends BaseController
{
    
    public function index()
    {
        if (!has_permission('BankBonus', 'can_view_all')) {
            show_404();
        }

        $model = model(BankBonusModel::class);
        
        $data = [
            'page_title' => 'Danh sách',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        
        $data += $model->getSummaryData(
            date('Y-m-01'),
            date('Y-m-t')
        );

        echo view('templates/sepay/header', $data);
        echo view('bankbonus/index', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajax_summary()
    {
        if(!has_permission('BankBonus', 'can_view_all')) {
            show_404();
        }

        $dates = $this->request->getGet(['start_date', 'end_date']);
        $bankBonusModel = model(BankBonusModel::class);
        
        return $this->response->setJSON([
            'success' => true,
            'data' => $bankBonusModel->getSummaryData(
                $dates['start_date'] ?? null, 
                $dates['end_date'] ?? null
            ),
            'message' => 'Thành công'
        ]);
    }
    
    public function ajax_bank_bonus()
    {
        if(!has_permission('BankBonus', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $bankBonusModel = model(BankBonusModel::class);

        $bank_bonus = $bankBonusModel->getDatatables();

        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
 
        foreach ($bank_bonus as $bonus) {
            $no++;
            $row = array();

            $row[] = $no;
            $row[] = "<a href=".base_url('company/details/').$bonus->id.">".$bonus->short_name."</a>";
            $row[] = $bonus->email;
            $row[] = $bonus->bank_bonus_info;

            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $bankBonusModel->countAll(),
            "recordsFiltered" => $bankBonusModel->countFiltered(),
            "data" => $data,
        );
        return $this->response->setJSON($output);

    }

    public function index_bank (){
        if(!has_permission('BankBonus', 'can_view_all'))
        show_404();

        $data = [
            'page_title' => 'Bank',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        echo view('templates/sepay/header',$data);
        echo view('bankbonus/bank',$data);
        echo view('templates/sepay/footer',$data);
    }
    public function ajax_list_bank()
    {
        if(!has_permission('BankBonus', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
            
        $bankModel = model(BankModel::class);

        $banks = $bankModel->getDatatablesBankBonus();
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
 
        foreach ($banks as $bank) {
            $no++;
            $row = array();

            $row[] = esc($bank->id);
            $row[] = "<b>".esc($bank->brand_name)."</b>";
            $row[] = esc($bank->short_name);
            $row[] = '<img src="https://my.sepay.vn/assets/images/banklogo/'.esc($bank->icon_path).'" width="30px" height="30px" alt="icon_path"/>';

            if($bank->active == 1)
                $row[] = "<span class='text-success'>Kích hoạt</span>";
            else
                $row[] = "<span class='text-danger'>Tạm khóa</span>";

            $row[] = number_format($bank->bank_bonus);
            $row[] = esc($bank->bank_path);

            $row[] = "<button onclick='edit_bank(". $bank->id.")' class='btn btn-sm btn-warning'><i class='bi bi-pencil'>Cập nhật</button>";


            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $bankModel->countAll(),
            "recordsFiltered" => $bankModel->countFiltered(),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }

    public function ajax_bank_add()
    {

        if(!has_permission('BankBonus', 'can_add'))
            show_404();
    
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'brand_name' => ['label' => 'Tên thương hiệu', 'rules' => 'required'],
            'short_name' => ['label' => 'Tên viết tắt', 'rules' => "required"],
            'full_name' => ['label' => 'Tên đầy đủ', 'rules' => "required"],
            'bin' => ['label' => 'Mã bin', 'rules' => "required|integer|is_natural"],
            'code' => ['label' => 'Mã code', 'rules' => 'required'],
            'logo_path' => ['label' => 'Đường dẫn logo', 'rules' => 'required'],
            'icon_path' => ['label' => 'Đường dẫn icon', 'rules' => 'required'],
            'active' => ['label' => 'Trạng thái', 'rules' => "required|in_list[0,1]"],
            'bank_bonus' => ['label' => 'Giao dịch', 'rules' => "required|integer|is_natural"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } else {

            $bankModel = model(BankModel::class);
            
            $data = array(
                'brand_name' => $this->request->getVar('brand_name'),
                'short_name' => $this->request->getVar('short_name'),
                'full_name' => $this->request->getVar('full_name'),
                'bin' => $this->request->getVar('bin'),
                'code' => $this->request->getVar('code'),
                'logo_path' => $this->request->getVar('logo_path'),
                'icon_path' => $this->request->getVar('icon_path'),
                'active' => $this->request->getVar('active'),
                'bank_bonus' => $this->request->getVar('bank_bonus'),
                'bank_path' => $this->request->getVar('bank_path'),
                'referral_code' => $this->request->getVar('referral_code'),
                'guide' => $this->request->getVar('guide'),
            );

            $result = $bankModel->insert($data);
            
            add_admin_log(array('data_id'=> $result, 'data_type'=>'bank_add','description'=>'Thêm ngân hàng','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
            
        }
    }

    public function ajax_bank_update(){
        if ($this->request->getMethod(true) != 'POST')
        return '';

        if(!has_permission('BankBonus', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền"));

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
            'brand_name' => ['label' => 'Tên thương hiệu', 'rules' => 'required'],
            'short_name' => ['label' => 'Tên viết tắt', 'rules' => "required"],
            'full_name' => ['label' => 'Tên đầy đủ', 'rules' => "required"],
            'bin' => ['label' => 'Mã bin', 'rules' => "required|integer|is_natural"],
            'code' => ['label' => 'Mã code', 'rules' => 'required'],
            'logo_path' => ['label' => 'Đường dẫn logo', 'rules' => 'required'],
            'icon_path' => ['label' => 'Đường dẫn icon', 'rules' => 'required'],
            'active' => ['label' => 'Trạng thái', 'rules' => "required|in_list[0,1]"],
            'bank_bonus' => ['label' => 'Giao dịch', 'rules' => "required|integer|is_natural"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }  
        
        $bankModel = model(BankModel::class);
        $id = $this->request->getPost('id');

        $result = $bankModel->where(['id'=>$id])->get()->getRow();
        if(!is_object($result))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu"));

        $data = array(
            'brand_name' => $this->request->getVar('brand_name'),
            'short_name' => $this->request->getVar('short_name'),
            'full_name' => $this->request->getVar('full_name'),
            'bin' => $this->request->getVar('bin'),
            'code' => $this->request->getVar('code'),
            'logo_path' => $this->request->getVar('logo_path'),
            'icon_path' => $this->request->getVar('icon_path'),
            'active' => $this->request->getVar('active'),
            'bank_bonus' => $this->request->getVar('bank_bonus'),
            'bank_path' => $this->request->getVar('bank_path'),
            'referral_code' => $this->request->getVar('referral_code'),
            'guide' => $this->request->getVar('guide'),
        );
        
        $result = $bankModel->set($data)->where("id", $id)->update();
        
        add_admin_log(array('data_id'=> $result, 'data_type'=>'bank_edit','description'=>'Cập nhật ngân hàng','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
    }

    public function ajax_get_bank($id = '')
    {
        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy ngân hàng này"));

        if(!has_permission('BankBonus', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền"));
 
        
        $bankModel = model(BankModel::class);
        
        $result = $bankModel->find($id);
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu"));
    }

}
