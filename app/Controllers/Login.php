<?php

namespace App\Controllers;

use App\Models\AdminModel;
use CodeIgniter\Controller;

class Login extends Controller
{
    public function index()
    {
        helper(['form','general','url']);

        if(check_logged_admin()) {
            return redirect()->to(base_url());
        }

        return view('login/index');
    }

    public function do_login() {
        
        helper(['form','general','url']);
        
     
        $request = \Config\Services::request();

        if ($request->getMethod(true) != 'POST')
            return redirect()->to(base_url());
 
        if(check_logged_admin()) {
            return redirect()->to(base_url());
        }

        $validation =  \Config\Services::validation();


        if(! $this->validate([
            'email' => ["label" => "Email", "rules" => "required|valid_email"],
            'password' =>  ["label" => "Mật khẩu", "rules" => "required|max_length[1000]"],
            // 'g-recaptcha-response' =>  ["label" => "Recaptcha", "rules" => "required|valid_recaptcha"]
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode("<br>", $validation->getErrors())));
        } else {
            $model = model(AdminModel::class);

            $email = $request->getVar('email', FILTER_SANITIZE_EMAIL);
            $password = $request->getVar('password');

            $authen = $model->login($email, $password, $remember=FALSE);

            if($authen) {
                
                return $this->response->setJSON(array("status"=>true));
            } else {
                return $this->response->setJSON(array("status"=>false,"message"=>"Thông tin đăng nhập không chính xác. Vui lòng thử lại."));
            }
                

        }
    }

    public function logout() {
        helper(['form','general','url']);

        $session = session();

        $session->remove("admin_logged_in");

        return redirect()->to(base_url('login'));
    }

    /*
    public function add() {

        $model = model(AdminModel::class);
        $admin_data = [
            'firstname' => "Việt",
            'lastname' => "Bùi Tấn",
            'email' => "<EMAIL>",
            'password' => password_hash(trim("Vietvina@123@SePay@Admin"), PASSWORD_DEFAULT),
            'active' => 1,
        ];

        $new_admin_id = $model->insert($admin_data);
    } */
     
    
     
    

}