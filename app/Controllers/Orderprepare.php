<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\OutputDeviceDecalModel;
use App\Models\OutputDeviceModel;
use App\Models\PhysicalOrderModel;
use Exception;
use CodeIgniter\API\ResponseTrait;
use Config\InternalApi;
use Config\Services;

class Orderprepare extends BaseController
{
    use ResponseTrait;

    public function index($orderId)
    {
        if (! has_permission('Order', 'can_edit')) {
            show_404();
        }

        $orderModel = model(PhysicalOrderModel::class);
        $order = $orderModel->find($orderId);

        if (! $order) {
            show_404();
        }

        $outputDeviceDecalModel = model(OutputDeviceDecalModel::class);
        $items = $outputDeviceDecalModel
            ->select([
                'tb_autopay_output_device_decal.*',
                'tb_autopay_physical_orders.notes',
                'tb_autopay_physical_order_items.name as product_name',
                'tb_autopay_physical_order_items.quantity as quantity',
                'tb_autopay_physical_order_items.image',
                'tb_autopay_output_device.serial_number',
                'tb_autopay_bank.icon_path',
            ])
            ->join('tb_autopay_physical_orders', 'tb_autopay_physical_orders.id = tb_autopay_output_device_decal.physical_order_id')
            ->join('tb_autopay_physical_order_items', 'tb_autopay_physical_order_items.id = tb_autopay_output_device_decal.physical_order_item_id')
            ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_output_device_decal.output_device_id', 'left')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_output_device_decal.bank_id')
            ->where('tb_autopay_output_device_decal.physical_order_id', $orderId)
            ->findAll();

        $outputDeviceModel = model(OutputDeviceModel::class);
        $outputDevices = $outputDeviceModel->findAll();
        $progress = $outputDeviceDecalModel->getPreparationProgress($orderId);

        $data = [
            'page_title' => 'Chuẩn bị đơn hàng #' . $order->order_code,
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
            'order' => $order,
            'items' => $items,
            'outputDevices' => $outputDevices,
            'progress' => $progress,
        ];

        echo view('templates/sepay/header', $data);
        echo view('orders/prepare', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function assignSpeaker($orderId, $itemId)
    {
        if (! has_permission('Order', 'can_edit')) {
            show_404();
        }

        $outputDeviceDecalModel = model(OutputDeviceDecalModel::class);
        $outputDeviceModel = model(OutputDeviceModel::class);
        $orderModel = model(PhysicalOrderModel::class);
        $order = $orderModel->find($orderId);

        if (! $order) {
            show_404();
        }

        $decal = $outputDeviceDecalModel->find($itemId);

        if (! $decal) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Sản phẩm không tồn tại.',
            ]);
        }

        $serialNumber = $this->request->getPost('serial_number');

        if (! empty($serialNumber)) {
            $outputDevice = $outputDeviceModel
                ->where('serial_number', trim($serialNumber))
                ->where('active', false)
                ->first();

            if ($outputDevice) {
                $existingAssignment = $outputDeviceDecalModel->where('output_device_id', $outputDevice['id'])->first();
                if ($existingAssignment && $existingAssignment->id != $itemId) {
                    return $this->response->setJSON([
                        'status' => false,
                        'message' => 'Loa này đã được gán cho sản phẩm khác.',
                    ]);
                }

                if ($decal->bank_id == 19) {
                    $outputDeviceModel->update($outputDevice['id'], ['bank_id' => $decal->bank_id]);
                }
            }
        }

        $outputDeviceDecalModel->update($itemId, ['output_device_id' => $outputDevice['id'] ?? null]);

        if ($decal->bank_id == 19 && empty($serialNumber)) {
            $outputDeviceModel->update($decal->output_device_id, ['bank_id' => null]);
        }

        session()->setFlashdata('success', 'Gán loa thành công.');

        return $this->response->setJSON(['status' => true]);
    }

    public function setAccountNumber($orderId, $itemId)
    {
        if (! has_permission('Order', 'can_edit')) {
            show_404();
        }

        $orderModel = model(PhysicalOrderModel::class);
        $order = $orderModel->find($orderId);

        if (! $order) {
            show_404();
        }

        if (!$this->validateData($this->request->getPost(), [
            'account_number' => 'required|max_length[50]',
        ])) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $accountNumber = $this->request->getPost('account_number');
        $outputDeviceDecalModel = model(OutputDeviceDecalModel::class);

        $decal = $outputDeviceDecalModel->find($itemId);

        if (! $decal) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy sản phẩm.',
            ]);
        }

        if ($decal->account_number != $accountNumber) {
            $outputDeviceDecalModel->update($itemId, ['account_number' => $accountNumber]);
        }

        if ($outputDeviceDecalModel->update($itemId, ['account_number' => $accountNumber])) {
            return $this->response->setJSON([
                'status' => true,
                'message' => 'Cập nhật số tài khoản thành công.',
            ]);
        }

        return $this->response->setJSON([
            'status' => false,
            'message' => 'Có lỗi xảy ra khi cập nhật số tài khoản.',
        ]);
    }

    public function generateQR($orderId, $itemId)
    {
        if (! has_permission('Order', 'can_edit')) {
            show_404();
        }

        $orderModel = model(PhysicalOrderModel::class);
        $order = $orderModel->find($orderId);

        if (! $order) {
            show_404();
        }

        $decal = model(OutputDeviceDecalModel::class)
            ->where('physical_order_id', $order->id)
            ->where('output_device_id IS NOT NULL')
            ->where('virtual_account_number', null)
            ->find($itemId);

        if (! $decal) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy mã QR.',
            ]);
        }

        $config = config(InternalApi::class);

        try {
            $response = Services::curlrequest([
                'headers' => [
                    'Authorization' => 'Bearer ' . $config->internalToken,
                ],
            ])->post($config->internalApiUrl . '/outputdevicedecal/create_va', [
                'json' => [
                    'decal_id' => $decal->id,
                    'bank_id' => $decal->bank_id,
                    'account_number' => $decal->account_number,
                ],
                'http_errors' => false,
            ]);

            $data = json_decode($response->getBody(), true);

            if ($data['status'] === false) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => $data['message'],
                ]);
            }

            $decal = model(OutputDeviceDecalModel::class)
                ->select('tb_autopay_output_device_decal.*, tb_autopay_bank.brand_name, tb_autopay_bank.icon_path as icon_path, tb_autopay_output_device.serial_number')
                ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_output_device_decal.bank_id', 'left')
                ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_output_device_decal.output_device_id', 'left')
                ->find($decal->id);

            $remark = '';

            if ($decal->brand_name === 'ABBANK') {
                $remark = 'SEVQR TKP' . $decal->virtual_account_number;
            } elseif ($decal->brand_name === 'VietinBank') {
                $remark = 'SEVQR ';
                if (in_array($decal->brand_name, ['VPBank', 'TPBank', 'VietinBank', 'ACB'])) {
                    $remark .= 'TKP' . $decal->virtual_account_number;
                }
            } elseif (in_array($decal->brand_name, ['VPBank', 'TPBank', 'ACB'])) {
                $remark = 'TKP' . $decal->virtual_account_number;
            }

            model(OutputDeviceDecalModel::class)->update($decal->id, ['qrcode_printed' => true]);

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đã tạo mã QR thành công',
                'data' => [
                    'qr_code' => sprintf(
                        'https://qr.sepay.vn/img?bank=%s&acc=%s&template=qronly&des=%s',
                        $decal->brand_name,
                        in_array($decal->brand_name, ['MBBank', 'OCB', 'BIDV']) ? $decal->virtual_account_number : $decal->account_number,
                        $remark,
                    ),
                    'virtual_account_number' => $decal->virtual_account_number,
                    'bank_name' => $decal->brand_name,
                    'bank_logo' => 'https://my.sepay.vn/assets/images/banklogo/' . $decal->icon_path,
                    'description' => $remark,
                    'serial_number' => $decal->serial_number,
                    'account_number' => $decal->account_number,
                ],
            ]);
        } catch (Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }
}
