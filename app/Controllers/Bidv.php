<?php

namespace App\Controllers;

use App\Models\CompanyModel;
use App\Models\BankAccountModel;
use App\Controllers\BaseController;
use App\Models\BidvEnterpriseAccountModel;
use App\Models\MerchantDetailsModel;
use CodeIgniter\API\ResponseTrait;

class Bidv extends BaseController
{
    use ResponseTrait;

    const BANK_ID = 9;

    public function details($id = '')
    {
        $data = [
            'page_title' => 'Bank Account',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if (! in_array($this->admin_details->role, ['Admin','SuperAdmin'])) {
            show_404();
        }

        if (!is_numeric($id)) {
            show_404();
        }

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $bankAccountModel->where(['id' => $id, 'bank_id' => Bidv::BANK_ID])->first();

        if (! $bankAccountDetails) {
            show_404();
        }

        $data['bank_account_details'] = $bankAccountDetails;

        $data['enterprise_account_details'] = model(BidvEnterpriseAccountModel::class)->where(['bank_account_id' => $bankAccountDetails->id])->first();
        $data['company_details'] = model(CompanyModel::class)->where(['id' => $bankAccountDetails->company_id])->first();

        echo view('templates/sepay/header', $data);
        echo view('bidv/details', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajax_edit_bank_account()
    {
        if (! in_array($this->admin_details->role, ['Admin','SuperAdmin'])) {
            return $this->failNotFound();
        }

        $bankAccountId = trim($this->request->getVar('bank_account_id'));

        if (!$bankAccountId || !is_numeric($bankAccountId)) {
            return $this->failNotFound();
        }

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $bankAccountModel->where(['id' => $bankAccountId, 'bank_id' => Bidv::BANK_ID])->first();

        if (!$bankAccountDetails) {
            return $this->failNotFound();
        }

        $data = [
            'account_holder_name' => trim(remove_accents($this->request->getVar('account_holder_name'), true)),
        ];

        $rules = [
            'account_holder_name' => ['required', 'string', 'max_length[70]']
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        $updated = $bankAccountModel->where(['id' => $bankAccountId])->set(['account_holder_name' => $data['account_holder_name']])->update();

        if (!$updated) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại.']);
        }

        set_alert('success', 'Lưu thay đổi thành công');

        return $this->respond(['status' => true]);
    }

    public function ajax_edit_enterprise_bank_account()
    {
        if (! in_array($this->admin_details->role, ['Admin','SuperAdmin'])) {
            return $this->failNotFound();
        }

        $bankAccountId = trim($this->request->getVar('bank_account_id'));

        if (!$bankAccountId || !is_numeric($bankAccountId)) {
            return $this->failNotFound();
        }

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $bankAccountModel->where(['id' => $bankAccountId, 'bank_id' => Bidv::BANK_ID])->first();

        if (!$bankAccountDetails) {
            return $this->failNotFound();
        }

        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);
        $bidvEnterpriseAccountDetails = $bidvEnterpriseAccountModel->where(['bank_account_id' => $bankAccountId])->first();

        if (!$bidvEnterpriseAccountDetails) {
            return $this->failNotFound();
        }

        $data = [
            'custom_va_name' => $this->request->getVar('custom_va_name') ? 1 : 0
        ];
 
        $updated = $bidvEnterpriseAccountModel->where(['id' => $bidvEnterpriseAccountDetails->id])->set($data)->update();

        if (!$updated) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại.']);
        }
        
        return $this->respond(['status' => true]);
    }
}
