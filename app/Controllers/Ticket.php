<?php

namespace App\Controllers;

use CodeIgniter\Controller;

class Ticket extends BaseController
{
    public function index()
    {
 
        $data = [
            'page_title' => 'Hỗ trợ',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
 
        if(!has_permission('Ticket', 'can_view_all'))
            show_404();
      
        $data['status'] = $this->request->getGet('status');

        if(!in_array($data['status'], ['Open','Answered','InProgress','ClientReply','AwaitingReply','OnHold','Closed']))
            $data['status'] = NULL;

        $ticketModel = model(TicketModel::class);

        $result = $ticketModel->select("count(*) as `numrows`")->get()->getRow();
        $data['count_all'] = $result->numrows;


        $result = $ticketModel->select("count(*) as `numrows`")->where(['status'=>'Open'])->get()->getRow();
        $data['count_open'] = $result->numrows;

        $result = $ticketModel->select("count(*) as `numrows`")->where(['status'=>'Answered'])->get()->getRow();
        $data['count_answered'] = $result->numrows;

        $result = $ticketModel->select("count(*) as `numrows`")->where(['status'=>'InProgress'])->get()->getRow();
        $data['count_inprogress'] = $result->numrows;

        $result = $ticketModel->select("count(*) as `numrows`")->where(['status'=>'ClientReply'])->get()->getRow();
        $data['count_clientreply'] = $result->numrows;

        $result = $ticketModel->select("count(*) as `numrows`")->where(['status'=>'AwaitingReply'])->get()->getRow();
        $data['count_awaitingreply'] = $result->numrows;
 
        $result = $ticketModel->select("count(*) as `numrows`")->where(['status'=>'OnHold'])->get()->getRow();
        $data['count_onhold'] = $result->numrows;

        $result = $ticketModel->select("count(*) as `numrows`")->where(['status'=>'Closed'])->get()->getRow();
        $data['count_closed'] = $result->numrows;
 

        echo view('templates/sepay/header',$data);
        echo view('ticket/index',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function create($company_id)
    {
 
        $data = [
            'page_title' => 'Hỗ trợ',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('Ticket', 'can_add'))
            show_404();

        $companyModel = model(CompanyModel::class);

        $data['company_details'] = $companyModel->find($company_id);

        if(!is_object($data['company_details']))
            show_404();
        
        echo view('templates/sepay/header',$data);
        echo view('ticket/create', $data);
        echo view('templates/sepay/footer',$data);
    }

    public function details($id)
    {
 
        $data = [
            'page_title' => 'Hỗ trợ',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        if(!has_permission('Ticket', 'can_add'))
            show_404();


        $ticketModel = model(TicketModel::class);
        $ticketReplyModel = model(TicketReplyModel::class);

        $data['ticket_details'] = $ticketModel->find($id);

        if(!is_object($data['ticket_details']))
            show_404();
        
        $data['ticket_replies'] = $ticketReplyModel->where(['ticket_id' => $id])->orderBy('id','DESC')->get()->getResult();

        $companyModel = model(CompanyModel::class);
        $userModel = model(UserModel::class);

        $data['company_details'] = $companyModel->find($data['ticket_details']->company_id);

        if($data['ticket_details']->owner_type == "Admin") 
            $data['user_details'] = $userModel->select("tb_autopay_user.id,tb_autopay_user.email,tb_autopay_user.firstname,tb_autopay_user.lastname,tb_autopay_user.phonenumber, tb_autopay_user.created_at")->join("tb_autopay_company_user","tb_autopay_user.id=tb_autopay_company_user.user_id")->where(['tb_autopay_company_user.company_id' => $data['ticket_details']->company_id, 'tb_autopay_company_user.role' => 'SuperAdmin'])->get()->getRow();
        else 
            $data['user_details'] = $userModel->select("tb_autopay_user.id,tb_autopay_user.email,tb_autopay_user.firstname,tb_autopay_user.lastname,tb_autopay_user.phonenumber, tb_autopay_user.created_at")->where(['tb_autopay_user.id' => $data['ticket_details']->owner_id])->get()->getRow();


        $result = $ticketModel->select("count(*) as `numrows`")->get()->getRow();
        $data['count_all'] = $result->numrows;


        $result = $ticketModel->select("count(*) as `numrows`")->where(['status'=>'Open'])->get()->getRow();
        $data['count_open'] = $result->numrows;

        $result = $ticketModel->select("count(*) as `numrows`")->where(['status'=>'Answered'])->get()->getRow();
        $data['count_answered'] = $result->numrows;

        $result = $ticketModel->select("count(*) as `numrows`")->where(['status'=>'InProgress'])->get()->getRow();
        $data['count_inprogress'] = $result->numrows;

        $result = $ticketModel->select("count(*) as `numrows`")->where(['status'=>'ClientReply'])->get()->getRow();
        $data['count_clientreply'] = $result->numrows;

        $result = $ticketModel->select("count(*) as `numrows`")->where(['status'=>'AwaitingReply'])->get()->getRow();
        $data['count_awaitingreply'] = $result->numrows;
 
        $result = $ticketModel->select("count(*) as `numrows`")->where(['status'=>'OnHold'])->get()->getRow();
        $data['count_onhold'] = $result->numrows;

        $result = $ticketModel->select("count(*) as `numrows`")->where(['status'=>'Closed'])->get()->getRow();
        $data['count_closed'] = $result->numrows;
 
        $data['status'] = $data['ticket_details']->status;


        echo view('templates/sepay/header',$data);
        echo view('ticket/details',$data);
        echo view('templates/sepay/footer',$data);
    }

     

    public function ajax_list() {

      
        if(!has_permission('Ticket', 'can_view_all'))
            show_404();


        $status = $this->request->getGet('status');

        if(!in_array($status, ['Open','Answered','InProgress','ClientReply','AwaitingReply','OnHold','Closed']))
            $status = FALSE;
            

        $ticketModel = model(TicketModel::class);

        $tickets = $ticketModel->getDatatables(FALSE, $status);

      
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
        

        foreach ($tickets as $ticket) {
         
            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = "<a href='" . base_url('ticket/details/' . esc($ticket->id)) . "'>#" . esc($ticket->id) . ' - ' . ellipsize(esc($ticket->subject),110,1,'..') . "</a>";

            $row[] = get_ticket_status_badge($ticket->status);  

            if(is_numeric($ticket->company_id) && $ticket->company_id >0)
                $row[] = "<a href='".base_url('company/details/' . esc($ticket->company_id))."'>#" . esc($ticket->company_id) . ' - ' . ellipsize(esc($ticket->name),50,1,'..') . "</a>";
            else
                $row[] = ellipsize(esc($ticket->name),50,1,'..');

           

            $row[] =  timespan(esc($ticket->created_at),1);
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $ticketModel->countAll(FALSE, $status),
            "recordsFiltered" => $ticketModel->countFiltered(FALSE, $status),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }


    public function ajax_create() {


        if(!has_permission('Ticket', 'can_add'))
            show_404();

        $validation =  \Config\Services::validation();


        if(! $this->validate([
            'subject' => ['label' => 'Tiêu đề', "rules" => "required|max_length[2000]"],
            'company_id' => ['label' => 'Công ty', "rules" => "required|integer"],
            'body' => ['label' => 'Nội dung yêu cầu', "rules" => "required"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $subject = $this->request->getVar('subject');
        $body = $this->request->getVar('body');
        $company_id = $this->request->getVar('company_id');

        $ticketModel = model(TicketModel::class);

        $ticket_id = $ticketModel->insert(['company_id' => $company_id, 'subject' => $subject, 'body' => $body, 'status' => 'Open', 'name' => $this->admin_details->lastname . ' ' . $this->admin_details->firstname, 'email' => $this->admin_details->email, 'owner_type' => 'Admin', 'owner_id' => $this->admin_details->id, 'lastreply' => date("Y-m-d H:i:s"), 'user_read' => 0]);

        set_alert("success", "Đã gửi yêu cầu hỗ trợ");

        $send_mail = $ticketModel->sendEmailTicket($ticket_id);


        return $this->response->setJSON(array('status'=>TRUE,'id'=>$ticket_id));
    
        
    }

    private function handleImageUploads($ticketId, $replyId = null) {
        
        $images = $this->request->getFiles();
        
        if (empty($images) || !isset($images['images'])) {
            return;
        }
        
        $s3Config = config('AwsS3');
        $s3Service = new \App\Libraries\AwsS3Service();
        $ticketAttachmentModel = model(\App\Models\TicketAttachmentModel::class);
        
        $imageFiles = $images['images'];

        if (count($imageFiles) > $s3Config->maxFiles) {
            return ;
        }

        foreach ($imageFiles as $file) {
            $fileName = basename($file->getClientName());

            if ($file->getError() > 0 || $file->getSize() > $s3Config->maxFileSize) {
                continue;
            }
            
            $fileInfo = new \finfo(FILEINFO_MIME_TYPE);
            $actualMime = $fileInfo->file($file->getTempName());

            if (!in_array($actualMime, $s3Config->allowedTypes)) {
                continue;
            }

            if (in_array($actualMime, $s3Config->allowedTypes)) {

                list($width, $height) = getimagesize($file->getTempName());
                if ($width <= 0 || $height <= 0) {
                    continue;
                }

                try {
                    $image = imagecreatefromstring(file_get_contents($file->getTempName()));
                    if ($image === false) {
                        continue;
                    }
                    imagedestroy($image);
                } catch (\Exception $e) {
                    continue;
                }
            }
            
            $newName = bin2hex(random_bytes(16));
            
            $s3Key = $s3Config->ticketAttachmentPrefix . '/' . 
                     date('Y-m-d') . '/' . 
                     $this->admin_details->id . '/' . 
                     $ticketId . '/' . 
                     $newName;
            
            try {
                $fileUrl = $s3Service->upload(
                    $file->getTempName(),
                    $s3Key,
                    false,
                    $actualMime
                );

                $safeFileName = $this->sanitizeFileName($fileName);

                $ticketAttachmentModel->insert([
                    'ticket_id' => $ticketId,
                    'reply_id' => $replyId,
                    'file_name'   => $safeFileName,
                    'file_path' => $fileUrl,
                    'file_type' => $actualMime,
                    'file_size' => $file->getSize(),
                    'company_id' => null,
                    'created_by' => $this->admin_details->id,
                    's3_key' => $s3Key
                ]);

                if (!unlink($file->getTempName())) {
                    log_message('error', 'Failed to delete temp file: ' . $file->getTempName());
                }

            } catch (\Exception $e) {
                log_message('error', 'Failed to upload image for ticket #' . $ticketId . ': ' . $e->getMessage());
            }
        }
    }
    
    private function sanitizeFileName($fileName) {
        return preg_replace('/[^a-zA-Z0-9-_\.]/', '', basename($fileName));
    }

    public function ajax_reply() {


        if(!has_permission('Ticket', 'can_add'))
            show_404();

        $validation =  \Config\Services::validation();


        if(! $this->validate([
            'ticket_id' => ['label' => 'Ticket ID', "rules" => "required|integer|is_natural"],
            'body' => ['label' => 'Nội dung phản hồi', "rules" => "required"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $companyModel = model(CompanyModel::class);
        $ticketModel = model(TicketModel::class);
        $ticketReplyModel = model(TicketReplyModel::class);

        $ticket_id = $this->request->getVar('ticket_id');
        $company_id = $this->request->getVar('company_id');

        $company_details = $companyModel->where(['id' => $company_id])->get()->getRow();
        if(!is_object($company_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy Công ty này'));

        $ticket_details = $ticketModel->where(['id' => $ticket_id])->get()->getRow();

        if(!is_object($ticket_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy ticket này'));

        $body = $this->request->getVar('body');


        $reply_id = $ticketReplyModel->insert(['ticket_id' => $ticket_id,'company_id' => $company_id, 'body' => $body, 'name' => $this->admin_details->lastname . ' ' . $this->admin_details->firstname, 'email' => $this->admin_details->email,  'owner_type' => 'Admin', 'owner_id' => $this->admin_details->id]);
        
        $this->handleImageUploads($ticket_id, $reply_id);

        $ticketModel->set(['status' => 'Answered', 'lastreply' => date("Y-m-d H:i:s"),'user_read' => 0])->where(['id' => $ticket_id])->update();

        set_alert("success", "Đã gửi trả lời");

        $send_mail = $ticketReplyModel->sendEmailReplyTicket($ticket_id);

        return $this->response->setJSON(array('status'=>TRUE,'id'=>$ticket_id));

        
    }


    public function ajax_set_status() {

        if(!has_permission('Ticket', 'can_add'))
            show_404();

        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'ticket_id' => ['label' => 'Ticket ID', "rules" => "required|integer|is_natural"],
            'status' => ['label' => 'Trạng thái', "rules" => "required|in_list[Open,Answered,InProgress,ClientReply,AwaitingReply,OnHold,Closed]"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $ticketModel = model(TicketModel::class);

        $ticket_id = $this->request->getVar('ticket_id');
        $ticket_status = $this->request->getVar('status');

        $ticket_details = $ticketModel->where(['id' => $ticket_id])->get()->getRow();

        if(!is_object($ticket_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy ticket này'));

            
        $ticketModel->set(['status' => $ticket_status])->where(['id' => $ticket_id])->update();

        set_alert("success", "Đã thay đổi trạng thái yêu cầu hỗ trợ");

        return $this->response->setJSON(array('status'=>TRUE));

    }
 
 
    
}