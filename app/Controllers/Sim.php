<?php

namespace App\Controllers;
use App\Models\SimModel;
use App\Models\SimCompanyModel;
use App\Models\SmsModel;

use CodeIgniter\Controller;

class Sim extends BaseController
{
    public function index() {

        if(!has_permission('Sim', 'can_view_all'))
            show_404();

        $data = [
            'page_title' => 'SIM',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        echo view('templates/sepay/header',$data);
        echo view('sim/index',$data);
        echo view('templates/sepay/footer',$data);
    }

    public function ajax_list() {

        if(!has_permission('Sim', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
            
        $simModel = model(SimModel::class);
        $simCompanyModel = model(SimCompanyModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $invoiceModel = model(InvoiceModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $smsModel = model(SmsModel::class);

        $sims = $simModel->getDatatables();
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
 
        foreach ($sims as $sim) {

            $result = $bankAccountModel->select("count(id) as `numrows`")->where(['sim_id' => $sim->id])->get()->getRow();
            if($result)
                $count_bank_account = $result->numrows;
            else
                $count_bank_account = 0;
            $count_transaction = 0;
           
            /* $result = $transactionsModel->select("count(tb_autopay_sms_parsed.id) as `numrows`")->join("tb_autopay_bank_account","tb_autopay_bank_account.account_number=tb_autopay_sms_parsed.account_number")->where(['tb_autopay_bank_account.sim_id' => $sim->id, 'tb_autopay_sms_parsed.parser_status' => 'Success'])->get()->getRow();
            if($result)
                $count_transactions = $result->numrows;
        else */
            //$count_transaction = 0;

            $companies = $simCompanyModel->select("tb_autopay_company.full_name,tb_autopay_company.short_name,tb_autopay_company.id")->join("tb_autopay_company","tb_autopay_company.id=tb_autopay_sim_company.company_id")->where(['sim_id' => $sim->id])->get()->getResult();

            $last_sms = $smsModel->where(['to' => $sim->sim_phonenumber])->orderBy('id', 'DESC')->get()->getRow();


            $company_text = '';
            foreach($companies as $company) {
                $result = $invoiceModel->select("sum(`total`) as `sum_paid`")->where(['status' => 'Paid', 'company_id' => $company->id])->get()->getRow();
                if($result->sum_paid > 0)
                    $invoice_text = "<span class='badge bg-success'>Paid</span>";
                else
                    $invoice_text = "<span class='badge bg-danger'>Unpaid</span>";

                $company_text = $company_text . "<div><a href='" .base_url('company/details/' . $company->id) . "'>" . esc($company->short_name). "</a> " . $invoice_text ."</div>";

            }
        

            $no++;
            $row = array();

  
            $row[] = $no;
            $row[] = esc($sim->id);
            $row[] = esc($sim->sim_phonenumber);
            $row[] = esc($sim->device_type);
            $row[] = esc($sim->description);
            //$row[] = number_format($count_company);
            $row[] = $company_text;

            $row[] = number_format($count_bank_account);
            //$row[] = number_format($count_transactions);
            if($last_sms)
                $row[] = $last_sms->created_at;
            else
                $row[] = '';

            if($sim->active == 1)
                $row[] = "<span class='text-success'>Kích hoạt</span>";
            else
                $row[] = "<span class='text-danger'>Tạm khóa</span>";

            $row[] = esc($sim->created_at);

            $row[] = "<button onclick='edit_sim(". $sim->id.")' class='btn btn-sm btn-warning'><i class='bi bi-pencil'> Sửa</button>";


            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $simModel->countAll(),
            "recordsFiltered" => $simModel->countFiltered(),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }


    public function ajax_sim_add() {

        if(!has_permission('Sim', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'sim_phonenumber' => ['label' => 'Số điện thoại', 'rules' => 'required|integer|min_length[9]|max_length[12]|is_natural|is_unique[tb_autopay_sim.sim_phonenumber]'],
            'description' => ['label' => 'Description', 'rules' => "max_length[500]"],
            'is_shared' => ['label' => 'Is Shared', 'rules' => "required|in_list[0,1]"],
            'is_primary' => ['label' => 'Is Primary', 'rules' => "required|in_list[0,1]"],
            'active' => ['label' => 'Trại thái', 'rules' => "required|in_list[0,1]"],
            'device_type' => ['label' => 'Device', 'rules' => "required|in_list[USB,Dinstar,None]"],
            'dinstar_imsi' => ['label' => 'Dinstar imsi', 'rules' => "max_length[500]"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } else {

            $simModel = model(SimModel::class);
            
            $data = array(
                'sim_phonenumber' => $this->request->getVar('sim_phonenumber'),
                'description' => $this->request->getVar('description'),
                'is_shared' => $this->request->getVar('is_shared'),
                'is_primary' => $this->request->getVar('is_primary'),
                'active' => $this->request->getVar('active'),
                'sms_source' => $this->request->getVar('sms_source'),
                'dinstar_imsi' => $this->request->getVar('dinstar_imsi'),

            );
 
            $result = $simModel->insert($data);
            
            add_admin_log(array('data_id'=> $result, 'data_type'=>'sim_add','description'=>'Thêm SIM','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);

                

        }
    }

    public function ajax_get_sim($id='') {
        
        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy SIM này"));

        if(!has_permission('Sim', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không chưa được cấp quyền"));
 
        
        $simModel = model(SimModel::class);
        
        $result = $simModel->find($id);
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu"));
    }

    public function ajax_sim_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('Sim', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
            //'sim_phonenumber' => ['label' => 'Số điện thoại', 'rules' => 'required|integer|min_length[9]|max_length[12]|is_natural|is_unique[tb_autopay_sim.sim_phonenumber]'],
            'seri' => ['label' => 'Seri', 'rules' => "max_length[100]"],
            'location' => ['label' => 'Location', 'rules' => "max_length[100]"],
            'description' => ['label' => 'Description', 'rules' => "max_length[500]"],
            'is_shared' => ['label' => 'Is shared', 'rules' => "required|in_list[0,1]"],
            'is_primary' => ['label' => 'Is Primary', 'rules' => "required|in_list[0,1]"],
            'active' => ['label' => 'Trại thái', 'rules' => "required|in_list[0,1]"],
            'device_type' => ['label' => 'Device', 'rules' => "required|in_list[USB,Dinstar,None]"],
            'dinstar_imsi' => ['label' => 'Dinstar imsi', 'rules' => "max_length[500]"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }  
        $simModel = model(SimModel::class);
        $sim_id = $this->request->getPost('id');

        $result = $simModel->where(['id'=>$sim_id])->get()->getRow();
        
        if(!is_object($result))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu"));

        $data = array(
            //'sim_phonenumber' => $this->request->getVar('sim_phonenumber'),
            'seri' => $this->request->getVar('seri'),
            'location' => trim($this->request->getVar('location')),
            'description' => $this->request->getVar('description'),
            'is_shared' => $this->request->getVar('is_shared'),
            'is_primary' => $this->request->getVar('is_primary'),
            'active' => $this->request->getVar('active'),
            'device_type' => $this->request->getVar('device_type'),
            'dinstar_imsi' => $this->request->getVar('dinstar_imsi'),

        );

 
           
        $result = $simModel->set($data)->where("id",$sim_id)->update();
        
        add_admin_log(array('data_id'=> $result, 'data_type'=>'sim_edit','description'=>'Sửa SIM','admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);


    
    }


    public function ajax_sim_delete() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Sim', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền"));
 
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $sim_id = $this->request->getPost('id');

        $simModel = model(SimModel::class);

        $simCompanyModel = model(SimCompanyModel::class);

        $company_count = $simCompanyModel->where(['sim_id' => $sim_id])->countAllResults();

        if($company_count > 0)
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Sim này đang được cấp phát cho khách hàng, vui lòng thu hồi sim trước."));

        $simModel->delete($sim_id);

        add_admin_log(array('data_id'=> $result, 'data_type'=>'sim_delete','description'=>'Xóa SIM ID ' .$sim_id,'admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    
        return $this->response->setJSON(array("status"=>true));
         
            

    
    }


    public function ajax_assign_sim() {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('Sim', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'sim_id' => ['rules' => 'required|integer|is_natural', 'label' => 'Chọn Sim'] ,
            'company_id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }  
        $simCompanyModel = model(SimCompanyModel::class);

        $data = array(
            'company_id' => $this->request->getVar('company_id'),
            'sim_id' => $this->request->getVar('sim_id'),
        );

           
        $result = $simCompanyModel->insert($data);
        
        add_admin_log(array('data_id'=> $result, 'data_type'=>'sim_assign','description'=>'Gán SIM ID ' . $data['sim_id'] . ' cho công ty ' . $data['company_id'],'admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);


    
    }

    public function ajax_revoke_sim() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Company', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        
        $assign_id = $this->request->getPost('id');


        $simCompanyModel = model(SimCompanyModel::class);

        
        $simCompanyModel->delete($assign_id);

        add_admin_log(array('data_id'=> $assign_id, 'data_type'=>'sim_revoke','description'=>'Xóa tại sim_company với ID ' . $assign_id, 'admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    
        return $this->response->setJSON(array("status"=>true));
         
            

    
    }


    
 
}
