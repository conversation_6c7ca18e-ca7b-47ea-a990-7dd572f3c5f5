<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\MQTTClientModel;
use App\Models\MQTTServerModel;
class MQTTClient extends BaseController
{
    protected function responseCustom($code, $message, $data = 0)
    {
        $response = service('response');

        $data_res = [
            'code' => $code,
            'message' => $message,
        ];

        if ($data != 0 || $data == []) {
            $data_res['data'] = $data;
        }

        $csrfToken = csrf_hash(); 

        $response->setStatusCode($code);
        $response->setJSON($data_res);

        $response->setHeader('X-CSRF-TOKEN', $csrfToken);

        return $response;
    }

    public function index()
    {
        if(!has_permission('Outputdevice', 'can_view_all'))
        show_404();

        $MQTTServerModel = model(MQTTServerModel::class);
        $MQTTClientModel = model(MQTTClientModel::class);

        $data_server = $MQTTServerModel->select("id,hostname,port,username username_server")->get()->getResultArray();

        $data = [
            'data_server' => $data_server,
            'page_title' => 'MQTT Client',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        //
        echo view('templates/sepay/header',$data);
        echo view('mqttclient/index',$data);
        echo view('templates/sepay/footer',$data);
    }
  
    
    public function allData() {
        $MQTTClientModel = model(MQTTClientModel::class);

        $data = $MQTTClientModel->select("
        tb_autopay_mqtt_client.id,
        tb_autopay_mqtt_client.username,
        tb_autopay_mqtt_client.password,
        tb_autopay_mqtt_client.mqtt_server_id,
        
        tb_autopay_mqtt_server.hostname,
        tb_autopay_mqtt_server.port,
        tb_autopay_mqtt_server.username username_server,
        tb_autopay_mqtt_server.ssl_enabled,

        tb_autopay_output_device.serial_number,
        tb_autopay_output_device.vendor,
        tb_autopay_output_device.model,


        ")
        ->join("tb_autopay_mqtt_server","tb_autopay_mqtt_server.id = tb_autopay_mqtt_client.mqtt_server_id","left")
        ->join("tb_autopay_output_device","tb_autopay_output_device.mqtt_client_id = tb_autopay_mqtt_client.id","left")
        ->get()
        ->getResultArray();

        

        if(!empty($data)){
            foreach($data as &$val){
                $val['type'] = !empty($val['serial_number'])?"Đã liên kết":"Chưa liên kết";
              
            }
            unset($val);
        }

       
    
        return json_encode(['data' => $data]);
    }

    public function addData() {
        $data_input = $this->request->getPost();
        $MQTTClientModel = model(MQTTClientModel::class);
        $validationRules = [
            "mqtt_server_id" => [
                "label" => "MQTT server",
                "rules" => "required|is_not_unique[tb_autopay_mqtt_server.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống.",
                ],
            ],
            "username" => [
                "label" => "Username",
                "rules" => "required|is_unique[tb_autopay_mqtt_client.username]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_unique" => "{field} đã tồn tại trong hệ thống.",
                ],
            ],
            "password" => [
                "label" => "Password",
                // Bỏ qua kiểm tra is_unique nếu `serial_number` đã thuộc về `id` hiện tại
                "rules" => "required|is_unique[tb_autopay_mqtt_client.password]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_unique" => "{field} đã tồn tại trong hệ thống.",
                ],
            ],
            
           
            
        ];
    
        // Load validation service
        $validation = service("validation");
    
        // Set validation rules
        $validation->setRules($validationRules);
    
        // Check data
        if (!$validation->run($data_input)) {
            $errors = $validation->getErrors();
    
            // Lấy thông báo lỗi đầu tiên
            $firstErrorKey = array_key_first($errors); // Lấy key của lỗi đầu tiên
            $firstError = $errors[$firstErrorKey]; // Lấy thông báo lỗi đầu tiên
            log_message("debug",$firstError);
            // Thay thế {field} bằng tên trường tương ứng
            $errorMessage = str_replace('{field}', $validationRules[$firstErrorKey]['label'], $firstError);
    
            return $this->responseCustom(
                423,
                $errorMessage, // Trả về thông báo lỗi đầu tiên
                $errors// Chỉ trả về lỗi đầu tiên trong mảng lỗi
            );
        }

       

        $result = $MQTTClientModel->insert($data_input);

        if (!$result) {
            $error = $MQTTClientModel->errors(); // Nếu mô hình có phương thức để lấy lỗi
            return $this->responseCustom(500, "Lỗi hệ thống insert: " . implode(", ", $error));
        }
        
        return $this->responseCustom(200, "Thêm thành công!");
        
    }
    public function updateData() {
        $data_input = $this->request->getPost();
        $MQTTClientModel = model(MQTTClientModel::class);
        
        // Lấy id của thiết bị đang được chỉnh sửa
        $id = $data_input['id'];
    
        $validationRules = [
            "id" => [
                "label" => "ID Thiết bị",
                "rules" => "required|is_not_unique[tb_autopay_mqtt_client.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống.",
                ],
            ],
            "mqtt_server_id" => [
                "label" => "MQTT server",
                "rules" => "required|is_not_unique[tb_autopay_mqtt_server.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống.",
                ],
            ],
           
            
            "username" => [
                "label" => "Username",
                "rules" => "required|is_unique[tb_autopay_mqtt_client.username,id,{id}]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_unique" => "{field} đã tồn tại trong hệ thống.",
                ],
            ],
            "password" => [
                "label" => "Password",
                // Bỏ qua kiểm tra is_unique nếu `serial_number` đã thuộc về `id` hiện tại
                "rules" => "required|is_unique[tb_autopay_mqtt_client.password,id,{id}]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_unique" => "{field} đã tồn tại trong hệ thống.",
                ],
            ],
        ];
    
        // Load validation service
        $validation = service("validation");
    
        // Set validation rules
        $validation->setRules($validationRules);
    
        // Check data
        if (!$validation->run($data_input)) {
            $errors = $validation->getErrors();
        
            // Lấy thông báo lỗi đầu tiên
            $firstErrorKey = array_key_first($errors); // Lấy key của lỗi đầu tiên
            $firstError = $errors[$firstErrorKey]; // Lấy thông báo lỗi đầu tiên
            log_message("debug", $firstError);
            
            // Thay thế {field} bằng tên trường tương ứng
            $errorMessage = str_replace('{field}', $validationRules[$firstErrorKey]['label'], $firstError);
        
            return $this->responseCustom(
                423,
                $errorMessage, // Trả về thông báo lỗi đầu tiên
                $errors // Chỉ trả về lỗi đầu tiên trong mảng lỗi
            );
        }
    
        $result = $MQTTClientModel->set($data_input)->where(['id' => $id])->update();
    
        if (!$result) {
            $error = $MQTTClientModel->errors(); // Nếu mô hình có phương thức để lấy lỗi
            return $this->responseCustom(500, "Lỗi hệ thống update: " . implode(", ", $error));
        }
        
        return $this->responseCustom(200, "Cập nhật thiết bị thành công!");
    }


    public function edit($id){
        $MQTTClientModel = model(MQTTClientModel::class);
        $data = $MQTTClientModel->where(['id'=>$id])->get()->getRowArray();
        return $this->responseCustom(200,"OK",$data);
    }

    public function delete($id){
        $MQTTClientModel = model(MQTTClientModel::class);

        $validationRules = [
            "id" => [
                "label" => "ID User",
                "rules" => "required|is_unique[tb_autopay_output_device.mqtt_client_id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_unique" => "{field} Đã liên kết, không thể xóa.",
                ],
            ],
            
        ];
         // Load validation service
         $validation = service("validation");
    
         // Set validation rules
         $validation->setRules($validationRules);
     
         // Check data
         if (!$validation->run(['id'=>$id])) {
             $errors = $validation->getErrors();
     
             // Lấy thông báo lỗi đầu tiên
             
             return $this->responseCustom(
                 422,
                 "Lỗi dữ liệu ID",
                 implode(", ", $errors)
             );
         }
         $result  = $MQTTClientModel->delete($id);
         
         if (!$result) {
            $error = $MQTTClientModel->errors(); // Nếu mô hình có phương thức để lấy lỗi
            return $this->responseCustom(500, "Lỗi hệ thống xóa: " . implode(", ", $error));
        }
        return $this->responseCustom(200, "Xóa thành công!");
    }
}

