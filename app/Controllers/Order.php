<?php

namespace App\Controllers;

use App\Enums\OrderStatus;
use App\Libraries\GhtkClient;
use App\Libraries\SInvoice;
use App\Models\OutputDeviceDecalModel;
use App\Models\OutputDeviceModel;
use App\Models\InvoiceCustomerInfoModel;
use App\Models\InvoiceModel;
use App\Models\OrderModel;
use App\Models\PartnerModel;
use App\Models\PhysicalInvoiceModel;
use App\Models\PhysicalOrderHistoryModel;
use App\Models\PhysicalOrderItemModel;
use App\Models\PhysicalOrderModel;
use App\Models\PhysicalOrderTrackingModel;
use Config\Services;
use Config\InternalApi;
use App\Models\StransactionModel;
use Config\Ghtk;
use Config\SInvoice as SInvoiceConfig;
use DateInterval;
use DatePeriod;
use DateTime;
use Exception;
use App\Libraries\Printer;

class Order extends BaseController
{
    public function index()
    {
        $data = [
            'page_title' => 'Quản lý đơn hàng',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
        ];

        if (! has_permission('Order', 'can_view_all')) {
            show_404();
        }

        $data['sources'] = model(PhysicalOrderModel::class)->getSourceList();

        echo view('templates/sepay/header', $data);
        echo view('orders/index', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function details($id)
    {
        if (! has_permission('Order', 'can_view_all')) {
            show_404();
        }

        $orderModel = model(PhysicalOrderModel::class);
        $orderItemModel = model(PhysicalOrderItemModel::class);
        $order = $orderModel->find($id);

        if (! $order) {
            show_404();
        }

        $items = $orderItemModel->where('order_id', $id)->findAll();

        $histories = model(PhysicalOrderHistoryModel::class)
            ->select([
                'tb_autopay_physical_order_history.id',
                'tb_autopay_physical_order_history.order_id',
                'tb_autopay_physical_order_history.status',
                'tb_autopay_physical_order_history.payment_status',
                'tb_autopay_physical_order_history.note',
                'tb_autopay_physical_order_history.created_at',
                'CONCAT(tb_autopay_admin.lastname, " ", tb_autopay_admin.firstname) as admin_name',
            ])
            ->join('tb_autopay_admin', 'tb_autopay_admin.id = tb_autopay_physical_order_history.admin_id', 'left')
            ->where('order_id', $id)
            ->orderBy('created_at', 'DESC')
            ->findAll();

        $tracking = model(PhysicalOrderTrackingModel::class)
            ->where('order_id', $id)
            ->first();

        $invoice = model(InvoiceModel::class)
            ->select([
                'tb_autopay_invoice.id',
                'tb_autopay_physical_invoices.invoice_id',
                'tb_autopay_invoice.total',
                'tb_autopay_invoice.tax',
                'tb_autopay_invoice.subtotal',
                'tb_autopay_invoice.public_note',
                'tb_autopay_invoice.physical_invoice_id',
                'tb_autopay_physical_invoices.status',
                'tb_autopay_physical_invoices.tax_rate',
                'tb_autopay_invoice_customer_info.name as customer_name',
                'tb_autopay_invoice_customer_info.email as customer_email',
                'tb_autopay_invoice_customer_info.phone as customer_phone',
                'tb_autopay_invoice_customer_info.address as customer_address',
                'tb_autopay_invoice_customer_info.company_name',
                'tb_autopay_invoice_customer_info.tax_code as tax_code',
            ])
            ->join('tb_autopay_physical_invoices', 'tb_autopay_physical_invoices.id = tb_autopay_invoice.physical_invoice_id')
            ->join('tb_autopay_invoice_customer_info', 'tb_autopay_invoice_customer_info.invoice_id = tb_autopay_invoice.id')
            ->where('tb_autopay_physical_invoices.order_id', $id)
            ->first();

        if ($invoice) {
            $transactions = model(StransactionModel::class)
                ->select([
                    'tb_autopay_stransaction.id',
                    'tb_autopay_stransaction.payment_method',
                    'tb_autopay_stransaction.trans_id as transaction_id',
                    'tb_autopay_stransaction.date as transaction_date',
                    'tb_autopay_stransaction.created_at',
                    'tb_autopay_stransaction.in as amount',
                    'tb_autopay_stransaction.description',
                ])
                ->join('tb_autopay_invoice', 'tb_autopay_invoice.id = tb_autopay_stransaction.invoice_id')
                ->where('tb_autopay_invoice.physical_invoice_id', $invoice->physical_invoice_id)
                ->orderBy('tb_autopay_stransaction.created_at', 'DESC')
                ->findAll();
        } else {
            $transactions = [];
        }

        $partnerModel = model(PartnerModel::class);
        $partner = null;

        if ($order->partner_id) {
            $partner = $partnerModel
                ->select('tb_autopay_partner.id, CONCAT(tb_autopay_partner.lastname, " ", tb_autopay_partner.firstname) as name, tb_autopay_partner.email')
                ->find($order->partner_id);
        }

        $decals = model(OutputDeviceDecalModel::class)
            ->select([
                'tb_autopay_output_device_decal.id',
                'tb_autopay_output_device_decal.physical_order_id',
                'tb_autopay_output_device_decal.output_device_id',
                'tb_autopay_output_device_decal.account_number',
                'tb_autopay_output_device_decal.virtual_account_number',
                'tb_autopay_output_device_decal.bank_id',
                'tb_autopay_bank.brand_name as bank_name',
                'tb_autopay_bank.icon_path as bank_icon',
                'tb_autopay_output_device.company_id as output_device_company_id',
                'tb_autopay_output_device.vendor as output_device_vendor',
                'tb_autopay_output_device.serial_number as output_device_serial_number',
                'tb_autopay_physical_order_items.name as item_name',
            ])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_output_device_decal.bank_id', 'left')
            ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_output_device_decal.output_device_id', 'left')
            ->join('tb_autopay_physical_order_items', 'tb_autopay_physical_order_items.id = tb_autopay_output_device_decal.physical_order_item_id', 'left')
            ->where('physical_order_id', $id)
            ->findAll();

        $data = [
            'page_title' => 'Chi tiết đơn hàng #' . $order->order_code,
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
            'order' => $order,
            'tracking' => $tracking,
            'items' => $items,
            'histories' => $histories,
            'transactions' => $transactions,
            'partner' => $partner,
            'invoice' => $invoice,
            'shippingCompanies' => $this->getShippingCompanies(),
            'mapUrl' => sprintf('https://www.google.com/maps/search/?api=1&query=%s', urlencode("$order->address, $order->ward, $order->district, $order->province")),
            'decals' => $decals,
        ];

        echo view('templates/sepay/header', $data);
        echo view('orders/details', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function report()
    {
        if (! has_permission('Order', 'can_view_all')) {
            show_404();
        }

        $data = [
            'page_title' => 'Báo cáo đơn hàng',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
        ];

        echo view('templates/sepay/header', $data);
        echo view('orders/report', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajax_list()
    {
        $this->verifyAjaxRequest('can_view_all');

        $orderModel = model(PhysicalOrderModel::class);
        $orders = $orderModel->getDatatables($this->request);
        $data = [];

        foreach ($orders as $order) {
            $row = [];
            $row[] = sprintf('<a href="%s">%s</a>', base_url('orders/' . $order->id), esc($order->order_code));
            $row[] = esc($order->customer_name);
            $trackingInfo = [];
            if ($order->bank_referral_code) {
                $trackingInfo[] = '<span class="badge bg-info">Cán bộ: ' . esc($order->bank_referral_code) . '</span>';
            }
            if ($order->ip_address) {
                if (in_array($order->ip_address, ['**************', '***************'])) {
                    $trackingInfo[] = '<span class="badge bg-info">Sale order</span>';
                } else {
                    if ($order->utm_source) $trackingInfo[] = '<span class="badge bg-light text-dark">Source: ' . esc($order->utm_source) . '</span>';
                    if ($order->utm_medium) $trackingInfo[] = '<span class="badge bg-light text-dark">Medium: ' . esc($order->utm_medium) . '</span>';
                    if ($order->utm_campaign) $trackingInfo[] = '<span class="badge bg-light text-dark">Campaign: ' . esc($order->utm_campaign) . '</span>';
                }
            }
            $row[] = ! empty($trackingInfo) ? implode(' ', $trackingInfo) : '-';
            $row[] = (int) $order->total_quantity;
            $row[] = format_currency($order->total_amount);
            $row[] = OrderStatus::toHtml($order->status);
            $row[] = $order->payment_status === 'Paid' ? '<span class="badge bg-success">Đã thanh toán</span>' : ($order->payment_status === 'Unpaid' ? '<span class="badge bg-danger">Chưa thanh toán</span>' :
                '<span class="badge bg-warning">Đã hoàn tiền</span>');

            $row[] = date('d/m/Y H:i:s', strtotime($order->created_at));
            if (has_permission('Order', 'can_view_all')) {
                $row[] = sprintf('<a href="%s" class="btn btn-primary">%s</a>', base_url("orders/$order->id"), 'Xem');
            }
            $data[] = $row;
        }

        return $this->response->setJSON([
            'draw' => $this->request->getPost('draw'),
            'recordsTotal' => $orderModel->countAll(),
            'recordsFiltered' => $orderModel->countFiltered($this->request),
            'data' => $data,
        ]);
    }

    public function ajax_chart()
    {
        $this->verifyAjaxRequest('can_view_all');

        $type = $this->request->getPost('type') ?? 'count';

        $endDate = date('Y-m-d');
        $startDate = date('Y-m-d', strtotime('-30 days'));

        $orderModel = model(PhysicalOrderModel::class);

        if ($type === 'quantity') {
            $timelineData = $orderModel
                ->select([
                    'DATE(tb_autopay_physical_orders.created_at) as date',
                    'SUM(tb_autopay_physical_order_items.quantity) as value'
                ])
                ->join('tb_autopay_physical_order_items', 'tb_autopay_physical_order_items.order_id = tb_autopay_physical_orders.id', 'left')
                ->where('tb_autopay_physical_orders.created_at >=', $startDate)
                ->where('tb_autopay_physical_orders.created_at <=', $endDate . ' 23:59:59')
                ->where('tb_autopay_physical_orders.status !=', 'Cancelled')
                ->groupBy('DATE(tb_autopay_physical_orders.created_at)')
                ->orderBy('DATE(tb_autopay_physical_orders.created_at)', 'ASC')
                ->findAll();
        } else {
            $timelineData = $orderModel
                ->select([
                    'DATE(created_at) as date',
                    $type === 'count' ? 'COUNT(*) as value' : 'SUM(total_amount) as value'
                ])
                ->where('created_at >=', $startDate)
                ->where('created_at <=', $endDate . ' 23:59:59')
                ->where('status !=', 'Cancelled')
                ->groupBy('DATE(created_at)')
                ->orderBy('DATE(created_at)', 'ASC')
                ->findAll();
        }

        $labels = [];
        $values = [];

        $currentDate = new DateTime($startDate);
        $lastDate = new DateTime($endDate);
        $interval = new DateInterval('P1D');
        $dateRange = new DatePeriod($currentDate, $interval, $lastDate->modify('+1 day'));

        $dateValues = [];
        foreach ($timelineData as $item) {
            $dateValues[$item->date] = (float) $item->value;
        }

        foreach ($dateRange as $date) {
            $dateStr = $date->format('Y-m-d');
            $labels[] = $dateStr;
            $values[] = $dateValues[$dateStr] ?? 0;
        }

        $trackingModel = model(PhysicalOrderTrackingModel::class);

        if ($type === 'quantity') {
            $sourceData = $trackingModel
                ->join('tb_autopay_physical_orders', 'tb_autopay_physical_orders.id = tb_autopay_physical_order_tracking.order_id', 'left')
                ->join('tb_autopay_physical_order_items', 'tb_autopay_physical_order_items.order_id = tb_autopay_physical_orders.id', 'left')
                ->select([
                    'tb_autopay_physical_order_tracking.utm_source',
                    'SUM(tb_autopay_physical_order_items.quantity) as quantity'
                ])
                ->groupBy('utm_source')
                ->orderBy('quantity', 'DESC')
                ->where('tb_autopay_physical_orders.status !=', 'Cancelled')
                ->findAll();
        } else {
            $sourceData = $trackingModel
                ->join('tb_autopay_physical_orders', 'tb_autopay_physical_orders.id = tb_autopay_physical_order_tracking.order_id', 'left')
                ->select([
                    'tb_autopay_physical_order_tracking.utm_source',
                    $type === 'count' ? 'COUNT(*) as count' : 'SUM(tb_autopay_physical_orders.total_amount) as amount',
                ])
                ->groupBy('utm_source')
                ->orderBy($type === 'count' ? 'count' : 'amount', 'DESC')
                ->where('tb_autopay_physical_orders.status !=', 'Cancelled')
                ->findAll();
        }

        $sources = [];

        foreach ($sourceData as $item) {
            $sourceName = empty($item->utm_source) ? 'Unknown' : $item->utm_source;
            if ($type === 'quantity') {
                $sources[] = [
                    'name' => $sourceName,
                    'count' => (int) $item->quantity
                ];
            } else {
                $sources[] = [
                    'name' => $sourceName,
                    'count' => $type === 'count' ? (int) $item->count : (float) $item->amount
                ];
            }
        }

        return $this->response->setJSON([
            'status' => true,
            'data' => [
                'labels' => $labels,
                'values' => $values,
                'sources' => $sources,
            ]
        ]);
    }

    public function ajax_extended_stats()
    {
        $this->verifyAjaxRequest('can_view_all');

        $orderModel = model(PhysicalOrderModel::class);
        $orderItemModel = model(PhysicalOrderItemModel::class);

        $regions = $orderModel
            ->select('province as name, COUNT(*) as count')
            ->where('status !=', 'Cancelled')
            ->groupBy('province')
            ->orderBy('count', 'DESC')
            ->limit(5)
            ->findAll();

        $topProducts = $orderItemModel
            ->select('name, SUM(quantity) as total_quantity, SUM(total_price) as revenue')
            ->join('tb_autopay_physical_orders', 'tb_autopay_physical_orders.id = tb_autopay_physical_order_items.order_id')
            ->where('tb_autopay_physical_orders.status !=', 'Cancelled')
            ->groupBy('name')
            ->orderBy('total_quantity', 'DESC')
            ->limit(5)
            ->findAll();

        return $this->response->setJSON([
            'status' => true,
            'data' => [
                'regions' => $regions,
                'top_products' => $topProducts,
            ]
        ]);
    }

    public function ajax_stats()
    {
        $this->verifyAjaxRequest('can_view_all');

        $orderModel = model(PhysicalOrderModel::class);

        $totalOrders = $orderModel->countAllResults();
        $completedOrders = $orderModel->where('status', 'Completed')->countAllResults();
        $pendingOrders = $orderModel->whereNotIn('status', ['Pending', 'Completed', 'Cancelled'])->countAllResults();
        $revenue = $orderModel->where('status', 'Completed')->selectSum('total_amount')->first()->total_amount ?? 0;

        $pendingOrdersCount = $orderModel->where('status', 'Pending')->countAllResults();
        $pendingOrdersQuantity = $orderModel
            ->select('SUM(tb_autopay_physical_order_items.quantity) as total_quantity')
            ->join('tb_autopay_physical_order_items', 'tb_autopay_physical_order_items.order_id = tb_autopay_physical_orders.id', 'left')
            ->where('tb_autopay_physical_orders.status', 'Pending')
            ->first()
            ->total_quantity ?? 0;

        return $this->response->setJSON([
            'status' => true,
            'data' => [
                'total_orders' => $totalOrders,
                'completed_orders' => $completedOrders,
                'pending_orders' => $pendingOrders,
                'revenue' => format_currency($revenue),
                'pending_orders_count' => $pendingOrdersCount,
                'pending_orders_quantity' => (int) $pendingOrdersQuantity,
            ],
        ]);
    }

    public function ajax_filter_counts()
    {
        $this->verifyAjaxRequest('can_view_all');

        $filters = [
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'status' => $this->request->getPost('status[]') ?: $this->request->getPost('status'),
            'payment_status' => $this->request->getPost('payment_status'),
            'customer_type' => $this->request->getPost('customer_type')
        ];

        $orderModel = model(PhysicalOrderModel::class);

        return $this->response->setJSON([
            'status' => true,
            'data' => [
                'paymentStatusCounts' => $orderModel->getPaymentStatusCounts($filters),
                'customerTypeCounts' => $orderModel->getCustomerTypeCounts($filters),
                'total' => $orderModel->countAllResults()
            ]
        ]);
    }

    public function search_partners()
    {
        $this->verifyAjaxRequest('can_edit');

        $search = $this->request->getPost('search');

        $partnerModel = model(PartnerModel::class);

        $partners = $partnerModel
            ->select(['id', 'CONCAT(firstname, " ", lastname) as name', 'email'])
            ->groupStart()
            ->like('firstname', $search)
            ->orLike('lastname', $search)
            ->orLike('email', $search)
            ->groupEnd()
            ->limit(15)
            ->findAll();

        return $this->response->setJSON([
            'status' => true,
            'data' => $partners,
        ]);
    }

    public function update_partner($id)
    {
        $order = $this->getOrderWithPermission($id, 'can_edit');

        $partnerId = $this->request->getPost('partner_id') ?: null;

        if ($order->partner_id == $partnerId) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không có thay đổi nào được thực hiện.',
            ]);
        }

        model(PhysicalOrderModel::class)->update($id, ['partner_id' => $partnerId]);

        $partnerName = 'Không có';

        if ($partnerId) {
            $partner = model(PartnerModel::class)->find($partnerId);
            if ($partner) {
                $partnerName = $partner->firstname . ' ' . $partner->lastname;
            }
        }

        $this->recordHistory($order, 'Cập nhật người giới thiệu: ' . $partnerName);

        set_alert('success', 'Cập nhật người giới thiệu thành công.');

        return $this->response->setJSON(['status' => true]);
    }

    public function update_status($id)
    {
        $order = $this->getOrderWithPermission($id, 'can_edit');
        $canUpdatePaymentStatus = in_array($this->admin_session['role'], ['Admin', 'SuperAdmin']);

        $data = [
            'status' => $this->request->getPost('status'),
            'status_note' => xss_clean($this->request->getPost('status_note')),
            'shipping_company' => $this->request->getPost('shipping_company'),
            'tracking_code' => $this->request->getPost('tracking_code'),
        ];

        if ($canUpdatePaymentStatus) {
            $data['payment_status'] = $this->request->getPost('payment_status');
        }

        $rules = [
            'status' => 'required|in_list[' . implode(',', array_keys(OrderStatus::getLabels())) . ']',
            'status_note' => 'permit_empty|max_length[255]',
            'shipping_company' => 'permit_empty|in_list[' . implode(',', array_keys($this->getShippingCompanies())) . ']',
            'tracking_code' => 'permit_empty|max_length[255]',
        ];

        if ($canUpdatePaymentStatus) {
            $rules['payment_status'] = 'required|in_list[Paid,Unpaid,Refunded]';
        }

        if (! $this->validateData($data, $rules)) {
            return $this->validationErrorResponse();
        }

        if ($data['status'] === 'Shipping') {
            $unassignedDecals = model(OutputDeviceDecalModel::class)
                ->where('physical_order_id', $id)
                ->where('output_device_id IS NULL')
                ->countAllResults();

            if ($unassignedDecals > 0) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => "Không thể chuyển sang trạng thái 'Đang giao hàng' khi còn {$unassignedDecals} loa chưa được gán. Vui lòng gán loa cho tất cả sản phẩm trước khi giao hàng.",
                ]);
            }
        }

        if (empty($data['status_note'])) {
            $changes = [];

            if ($order->status !== $data['status']) {
                $changes[] = "Trạng thái từ $order->status sang {$data['status']}";
            }

            if ($canUpdatePaymentStatus && $order->payment_status !== $data['payment_status']) {
                $changes[] = "Trạng thái thanh toán từ $order->payment_status sang {$data['payment_status']}";
            }

            if ($data['status'] === 'Shipping') {
                if ($order->shipping_company !== $data['shipping_company'] && ! empty($data['shipping_company'])) {
                    $shippingCompanies = $this->getShippingCompanies();
                    $companyName = $shippingCompanies[$data['shipping_company']] ?? $data['shipping_company'];
                    $changes[] = "Đơn vị vận chuyển: $companyName";
                }

                if ($order->tracking_code !== $data['tracking_code'] && ! empty($data['tracking_code'])) {
                    $changes[] = "Mã vận đơn: {$data['tracking_code']}";
                }
            }

            if (! empty($changes)) {
                $data['status_note'] = 'Thay đổi: ' . implode(', ', $changes);
            } else {
                $data['status_note'] = 'Cập nhật trạng thái';
            }
        }

        $orderData = [
            'status' => $data['status'],
            'shipping_company' => $data['shipping_company'],
            'tracking_code' => $data['tracking_code'],
        ];

        if ($canUpdatePaymentStatus) {
            $orderData['payment_status'] = $data['payment_status'];
        }

        if ($order->status !== $data['status']) {
            $orderData['last_update_status'] = date('Y-m-d H:i:s');
        }

        if ($data['status'] === 'Completed' && $order->payment_method === 'cod' && $order->payment_status === 'Unpaid') {
            $invoice = model(InvoiceModel::class)
                ->select('tb_autopay_invoice.id, tb_autopay_invoice.total, tb_autopay_invoice.physical_invoice_id')
                ->join('tb_autopay_physical_invoices', 'tb_autopay_physical_invoices.id = tb_autopay_invoice.physical_invoice_id')
                ->where('tb_autopay_physical_invoices.order_id', $order->id)
                ->first();

            if ($invoice) {
                $stransactionModel = model(StransactionModel::class);

                $datePaid = date('Y-m-d H:i:s');

                $stransactionModel->insert([
                    'invoice_id' => $invoice->id,
                    'in' => $order->total_amount,
                    'payment_method' => 'cod',
                    'description' => 'Thanh toán qua COD',
                    'date' => $datePaid,
                ]);

                $totalPaid = $stransactionModel
                    ->where('invoice_id', $invoice->id)
                    ->selectSum('in')
                    ->first()
                    ->in ?? 0;

                if ($totalPaid >= $invoice->total) {
                    $orderData['payment_status'] = 'Paid';
                    $order->payment_status = 'Paid';

                    model(InvoiceModel::class)->update($invoice->id, [
                        'status' => 'Paid',
                        'datepaid' => $datePaid,
                    ]);
                }
            }
        }

        model(PhysicalOrderModel::class)->update($id, $orderData);

        $invoice = model(InvoiceModel::class)
            ->select('tb_autopay_invoice.id')
            ->join('tb_autopay_physical_invoices', 'tb_autopay_physical_invoices.id = tb_autopay_invoice.physical_invoice_id')
            ->where('tb_autopay_physical_invoices.order_id', $order->id)
            ->first();

        if ($invoice) {
            model(InvoiceModel::class)->update($invoice->id, [
                'status' => $orderData['payment_status'],
                'datepaid' => $orderData['payment_status'] === 'Paid' ? date('Y-m-d H:i:s') : null,
            ]);
        }

        $order->status = $data['status'];
        if ($canUpdatePaymentStatus) {
            $order->payment_status = $data['payment_status'];
        }

        model(PhysicalOrderHistoryModel::class)->recordHistory(
            $order,
            $data['status_note'],
            $this->admin_session['admin_id']
        );

        set_alert('success', 'Cập nhật trạng thái đơn hàng thành công.');

        return $this->response->setJSON(['status' => true]);
    }

    public function update_shipping($id)
    {
        $order = $this->getOrderWithPermission($id, 'can_edit');

        $data = [
            'shipping_company' => $this->request->getPost('shipping_company'),
            'tracking_code' => xss_clean($this->request->getPost('tracking_code')),
        ];

        $rules = [
            'shipping_company' => 'permit_empty|in_list[' . implode(',', array_keys($this->getShippingCompanies())) . ']',
            'tracking_code' => 'permit_empty|max_length[50]',
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->validationErrorResponse();
        }

        if (
            $order->shipping_company === $data['shipping_company']
            && $order->tracking_code === $data['tracking_code']
        ) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không có thay đổi nào được thực hiện.',
            ]);
        }

        model(PhysicalOrderModel::class)->update($id, $data);

        $this->recordHistory($order, 'Cập nhật thông tin vận chuyển: ' .
            (! empty($data['shipping_company']) ? 'Đơn vị: ' . $data['shipping_company'] : '') .
            (! empty($data['tracking_code']) ? ', Mã vận đơn: ' . $data['tracking_code'] : ''));

        set_alert('success', 'Cập nhật thông tin vận chuyển thành công.');

        return $this->response->setJSON(['status' => true]);
    }

    public function update_customer($id)
    {
        $order = $this->getOrderWithPermission($id, 'can_edit');

        $data = [
            'customer_type' => xss_clean($this->request->getPost('customer_type')),
            'customer_name' => xss_clean($this->request->getPost('customer_name')),
            'customer_phone' => xss_clean($this->request->getPost('customer_phone')),
            'customer_email' => $this->request->getPost('customer_email'),
            'address' => xss_clean($this->request->getPost('address')),
            'ward' => xss_clean($this->request->getPost('ward')),
            'district' => xss_clean($this->request->getPost('district')),
            'province' => xss_clean($this->request->getPost('province')),
        ];

        $rules = [
            'customer_type' => 'required|in_list[retail,wholesale]',
            'customer_name' => 'required|max_length[100]',
            'customer_phone' => 'required|regex_match[/(?:\+84|0084|0)[235789][0-9]{1,2}[0-9]{7}(?:[^\d]+|$)/]',
            'customer_email' => 'permit_empty|valid_email|max_length[100]',
            'address' => 'permit_empty|max_length[255]',
            'ward' => 'permit_empty|max_length[100]',
            'district' => 'permit_empty|max_length[100]',
            'province' => 'permit_empty|max_length[100]',
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->validationErrorResponse();
        }

        if (
            $order->customer_type === $data['customer_type'] &&
            $order->customer_name === $data['customer_name'] &&
            $order->customer_phone === $data['customer_phone'] &&
            $order->customer_email === $data['customer_email'] &&
            $order->address === $data['address'] &&
            $order->ward === $data['ward'] &&
            $order->district === $data['district'] &&
            $order->province === $data['province']
        ) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không có thay đổi nào được thực hiện.',
            ]);
        }

        model(PhysicalOrderModel::class)->update($id, $data);

        $this->recordHistory($order, 'Cập nhật thông tin khách hàng: ' .
            $data['customer_type'] . ', ' .
            $data['customer_name'] . ', ' .
            $data['customer_phone'] . ', ' .
            $data['customer_email'] . ', ' .
            $data['address'] . ', ' .
            $data['ward'] . ', ' .
            $data['district'] . ', ' .
            $data['province']);

        set_alert('success', 'Cập nhật thông tin khách hàng thành công.');

        return $this->response->setJSON(['status' => true]);
    }

    public function update_shipping_fee($id)
    {
        $order = $this->getOrderWithPermission($id, 'can_edit');

        $data = [
            'shipping_fee' => (int) $this->request->getPost('shipping_fee'),
        ];

        $rules = [
            'shipping_fee' => 'required|is_natural|greater_than_equal_to[0]',
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->validationErrorResponse();
        }

        if ($order->shipping_fee === $data['shipping_fee']) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không có thay đổi nào được thực hiện.',
            ]);
        }

        $invoice = $this->getInvoiceForOrder($order->id);

        if ($order->payment_status === 'Paid') {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không thể cập nhật phí vận chuyển cho đơn hàng đã được thanh toán.',
            ]);
        }


        $newTotalAmount = $order->subtotal - $order->discount_amount + $data['shipping_fee'];
        $data['total_amount'] = $newTotalAmount;

        $order->total_amount = $newTotalAmount;
        $order->shipping_fee = $data['shipping_fee'];

        model(PhysicalOrderModel::class)->update($id, $data);
        $this->recalculateAmount($order, $invoice);

        $this->recordHistory($order, 'Cập nhật phí vận chuyển: ' .
            ($data['shipping_fee'] > 0 ? format_currency($data['shipping_fee']) : 'Miễn phí'));

        set_alert('success', 'Cập nhật phí vận chuyển thành công.');

        return $this->response->setJSON(['status' => true]);
    }

    public function update_discount($id)
    {
        $order = $this->getOrderWithPermission($id, 'can_edit');

        $data = [
            'discount_amount' => (int) $this->request->getPost('discount_amount'),
        ];

        $rules = [
            'discount_amount' => 'required|is_natural|greater_than_equal_to[0]',
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->validationErrorResponse();
        }

        if ($order->discount_amount === $data['discount_amount']) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không có thay đổi nào được thực hiện.',
            ]);
        }

        $invoice = $this->getInvoiceForOrder($order->id);

        if ($invoice->status === 'issued') {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không thể cập nhật giảm giá cho đơn hàng đã được thanh toán.',
            ]);
        }

        $newTotalAmount = $order->subtotal - $data['discount_amount'] + $order->shipping_fee;
        $data['total_amount'] = $newTotalAmount;

        $db = db_connect();
        $db->transBegin();

        try {
            model(PhysicalOrderModel::class)->update($id, $data);

            $order->total_amount = $newTotalAmount;
            $order->discount_amount = $data['discount_amount'];

            $this->recalculateAmount($order, $invoice);

            $db->transCommit();
        } catch (Exception $e) {
            $db->transRollback();

            return $this->response->setJSON([
                'status' => false,
                'message' => 'Đã xảy ra lỗi khi cập nhật dữ liệu.',
            ]);
        }

        $this->recordHistory($order, 'Cập nhật giảm giá: ' .
            ($data['discount_amount'] > 0 ? format_currency($data['discount_amount']) : '0đ'));

        set_alert('success', 'Cập nhật giảm giá thành công.');

        return $this->response->setJSON(['status' => true]);
    }

    public function add_payment($id)
    {
        $order = $this->getOrderWithPermission($id, 'can_edit');

        $rules = [
            'payment_method' => 'required|in_list[cod,bank_transfer]',
            'amount' => 'required|numeric|greater_than[0]',
            'description' => 'permit_empty|string',
            'transaction_id' => 'permit_empty|string',
            'transaction_date' => 'required|valid_date[Y-m-d H:i:s]',
        ];

        $data = [
            'payment_method' => $this->request->getPost('payment_method'),
            'amount' => $this->request->getPost('amount'),
            'description' => $this->request->getPost('description'),
            'transaction_id' => $this->request->getPost('transaction_id'),
            'transaction_date' => date('Y-m-d H:i:s', strtotime($this->request->getPost('transaction_date') ?: time())),
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->response->setJSON([
                'status' => false,
                'messages' => $this->validator->getErrors()
            ])->setStatusCode(422);
        }

        $invoice = model(InvoiceModel::class)
            ->select('tb_autopay_invoice.id, tb_autopay_invoice.physical_invoice_id, tb_autopay_invoice.total')
            ->join('tb_autopay_physical_invoices', 'tb_autopay_physical_invoices.id = tb_autopay_invoice.physical_invoice_id')
            ->where('tb_autopay_physical_invoices.order_id', $order->id)
            ->first();

        $db = db_connect();
        $db->transBegin();

        try {
            $transactionData = [
                'invoice_id' => $invoice->id,
                'payment_method' => $this->request->getPost('payment_method'),
                'in' => $this->request->getPost('amount'),
                'description' => $this->request->getPost('description'),
                'date' => $this->request->getPost('transaction_date'),
                'trans_id' => $this->request->getPost('transaction_id'),
                'type' => null,
            ];

            model(StransactionModel::class)->insert($transactionData);

            $totalPaid = model(StransactionModel::class)
                ->where('invoice_id', $invoice->id)
                ->selectSum('in')
                ->first()
                ->in ?? 0;

            if ($totalPaid >= $invoice->total) {
                $datePaid = date('Y-m-d H:i:s');

                model(PhysicalOrderModel::class)->update($order->id, [
                    'payment_status' => 'Paid',
                    'last_update_status' => $datePaid,
                ]);

                model(InvoiceModel::class)->update($invoice->id, [
                    'status' => 'Paid',
                    'datepaid' => $datePaid,
                ]);

                $order->payment_status = 'Paid';
                $order->last_update_status = $datePaid;

                model(PhysicalOrderHistoryModel::class)->insert([
                    'order_id' => $order->id,
                    'status' => $order->status,
                    'payment_status' => 'Paid',
                    'note' => sprintf('Đã thanh toán đủ tiền qua %s', $this->request->getPost('payment_method') === 'bank_transfer' ? 'chuyển khoản ngân hàng' : 'tiền mặt'),
                    'admin_id' => $this->admin_details->id,
                ]);
            }

            $db->transCommit();

            set_alert('success', 'Thêm thanh toán thành công.');

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $db->transRollback();

            log_message('error', 'Error adding payment: ' . $e->getMessage());

            return $this->response->setJSON([
                'status' => false,
                'message' => 'Có lỗi xảy ra khi thêm thanh toán. Vui lòng thử lại.'
            ]);
        }
    }

    public function generate_decals($orderId)
    {
        $this->verifyAjaxRequest('can_edit');

        $order = $this->getOrderWithPermission($orderId, 'can_edit');

        $orderItems = model(PhysicalOrderItemModel::class)
            ->select([
                'tb_autopay_physical_order_items.id',
                'tb_autopay_physical_order_items.quantity',
                'tb_autopay_physical_products.bank_id'
            ])
            ->join(
                'tb_autopay_physical_products',
                'tb_autopay_physical_products.id = tb_autopay_physical_order_items.product_id'
            )
            ->where('tb_autopay_physical_order_items.order_id', $order->id)
            ->findAll();

        $decalModel = model(OutputDeviceDecalModel::class);
        foreach ($orderItems as $item) {
            for ($i = 0; $i < $item->quantity; $i++) {
                $decalModel->insert([
                    'physical_order_id' => $orderId,
                    'physical_order_item_id' => $item->id,
                    'bank_id' => $item->bank_id,
                ]);
            }
        }

        return $this->response->setJSON([
            'status' => true,
            'message' => 'Đã tạo thủ công toàn bộ mã QR thành công',
        ]);
    }

    public function create_va($orderId)
    {
        $this->verifyAjaxRequest('can_edit');

        $order = $this->getOrderWithPermission($orderId, 'can_edit');

        $decal = model(OutputDeviceDecalModel::class)
            ->where('physical_order_id', $order->id)
            ->where('output_device_id IS NOT NULL')
            ->where('virtual_account_number', null)
            ->find($this->request->getPost('decal_id'));

        if (! $decal) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy mã QR.',
            ]);
        }

        $config = config(InternalApi::class);

        try {
            $response = Services::curlrequest([
                'headers' => [
                    'Authorization' => 'Bearer ' . $config->internalToken,
                ],
            ])->post($config->internalApiUrl . '/outputdevicedecal/create_va', [
                'json' => [
                    'decal_id' => $decal->id,
                    'bank_id' => $decal->bank_id,
                    'account_number' => $decal->account_number,
                ],
                'http_errors' => false,
                'verify' => false
            ]);

            $data = json_decode($response->getBody(), true);

            if ($data['status'] === false) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => $data['message'],
                ]);
            }

            set_alert('success', 'Đã tạo mã QR thành công');

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function get_decal($decalId)
    {
        $this->verifyAjaxRequest('can_edit');

        $decal = model(OutputDeviceDecalModel::class)
            ->select('tb_autopay_output_device_decal.*, tb_autopay_physical_order_items.name as item_name, tb_autopay_output_device.serial_number as output_device_serial_number, tb_autopay_output_device.vendor as output_device_vendor, tb_autopay_bank.brand_name as bank_name')
            ->join('tb_autopay_physical_order_items', 'tb_autopay_physical_order_items.id = tb_autopay_output_device_decal.physical_order_item_id', 'left')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_output_device_decal.bank_id', 'left')
            ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_output_device_decal.output_device_id', 'left')
            ->find($decalId);

        if (! $decal) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy mã QR.'
            ]);
        }

        $outputDevices = model(OutputDeviceModel::class)
            ->select('id, serial_number, vendor')
            ->where('active', false)
            ->where('company_id', null);

        $existingDecals = model(OutputDeviceDecalModel::class)
            ->where('output_device_id IS NOT NULL')
            ->where('id !=', $decalId)
            ->findAll();

        $existingDeviceIds = array_column($existingDecals, 'output_device_id');
        $existingDeviceIds = array_filter($existingDeviceIds);

        if (! empty($existingDeviceIds)) {
            $outputDevices->whereNotIn('id', $existingDeviceIds);
        }

        $outputDevices = $outputDevices->findAll();

        return $this->response->setJSON([
            'status' => true,
            'data' => [
                'output_device_id' => $decal->output_device_id,
                'physical_order_id' => $decal->physical_order_id,
                'physical_order_item_id' => $decal->physical_order_item_id,
                'account_number' => $decal->account_number,
                'bank_id' => $decal->bank_id,
                'virtual_account_number' => $decal->virtual_account_number
            ],
            'devices' => array_map(function ($device) {
                return [
                    'id' => $device['id'],
                    'name' => $device['serial_number'] . ' - ' . $device['vendor'],
                    'serial_number' => $device['serial_number'],
                    'vendor' => $device['vendor']
                ];
            }, $outputDevices)
        ]);
    }

    public function update_decal($orderId)
    {
        $this->verifyAjaxRequest('can_edit');

        $order = $this->getOrderWithPermission($orderId, 'can_edit');

        if (! $this->validateData($this->request->getPost(), [
            'decal_id' => 'required|is_natural_no_zero',
            'device_id' => 'permit_empty|is_natural_no_zero',
            'account_number' => 'permit_empty|string',
        ])) {
            return $this->validationErrorResponse();
        }

        $decal = model(OutputDeviceDecalModel::class)
            ->select('tb_autopay_output_device_decal.*, tb_autopay_physical_order_items.name as item_name, tb_autopay_output_device.serial_number as output_device_serial_number, tb_autopay_output_device.vendor as output_device_vendor, tb_autopay_bank.brand_name as bank_name')
            ->join('tb_autopay_physical_order_items', 'tb_autopay_physical_order_items.id = tb_autopay_output_device_decal.physical_order_item_id', 'left')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_output_device_decal.bank_id', 'left')
            ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_output_device_decal.output_device_id', 'left')
            ->find($this->request->getPost('decal_id'));

        if ($decal->physical_order_id !== $orderId) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy đơn hàng.',
            ]);
        }

        $deviceId = $this->request->getPost('device_id');
        $accountNumber = $this->request->getPost('account_number');

        if (! empty($deviceId)) {
            $outputDevice = model(OutputDeviceModel::class)
                ->where('active', false)
                ->find($deviceId);

            if (! $outputDevice) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Không tìm thấy loa.',
                ]);
            }

            $existingDecal = model(OutputDeviceDecalModel::class)
                ->where('output_device_id', $outputDevice['id'])
                ->where('id !=', $decal->id)
                ->first();

            if ($existingDecal) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Loa này đã được gán cho mặt hàng khác.',
                ]);
            }

            if ($decal->bank_id == 19) {
                model(OutputDeviceModel::class)->update($deviceId, [
                    'bank_id' => $decal->bank_id,
                ]);
            }
        }

        if ($decal->bank_id == 19 && !$deviceId) {
            model(OutputDeviceModel::class)
                ->where('serial_number', $decal->output_device_serial_number)
                ->set(['bank_id' => null])
                ->update();
        }

        $updateData = [
            'output_device_id' => $deviceId ?: null,
        ];

        if ($decal->bank_id != 8 || ($decal->bank_id == 8 && ! $decal->virtual_account_number)) {
            $updateData['account_number'] = $accountNumber;
        }

        model(OutputDeviceDecalModel::class)->update($decal->id, $updateData);

        return $this->response->setJSON([
            'status' => true,
            'message' => 'Đã cập nhật thông tin thành công.',
        ]);
    }

    public function delete_va($orderId)
    {
        $this->verifyAjaxRequest('can_edit');

        $order = $this->getOrderWithPermission($orderId, 'can_edit');

        $outputDeviceDecalModel = model(OutputDeviceDecalModel::class);

        $decal = $outputDeviceDecalModel
            ->where('physical_order_id', $order->id)
            ->find($this->request->getPost('decal_id'));

        if (! $decal) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy mã QR.',
            ]);
        }

        $config = config(InternalApi::class);

        try {
            $response = Services::curlrequest([
                'headers' => [
                    'Authorization' => 'Bearer ' . $config->internalToken,
                ],
            ])->post($config->internalApiUrl . '/outputdevicedecal/delete_va/' . $decal->id, [
                'http_errors' => false,
                'verify' => false
            ]);

            $data = json_decode($response->getBody(), true);

            if ($data['status'] === false) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => $data['message'],
                ]);
            }

            set_alert('success', 'Đã xóa mã QR thành công');

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function update_invoice($id)
    {

        $data = $this->request->getPost();

        $rules = [
            'name' => 'required|max_length[100]',
            'email' => 'permit_empty',
            'email.*' => 'permit_empty|string|valid_email',
            'address' => 'required|string',
            'company_name' => 'permit_empty|max_length[100]',
            'tax_code' => 'permit_empty|max_length[20]',
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->validationErrorResponse();
        }

        $invoiceCustomerInfoModel = model(InvoiceCustomerInfoModel::class);

        $invoice = $invoiceCustomerInfoModel
            ->select('tb_autopay_invoice_customer_info.id')
            ->where('tb_autopay_invoice_customer_info.invoice_id', $id)
            ->join('tb_autopay_invoice', 'tb_autopay_invoice.id = tb_autopay_invoice_customer_info.invoice_id')
            ->first();

        if (! $invoice) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy hóa đơn cho đơn hàng này.',
            ]);
        }

        $data['email'] = isset($data['email']) ? json_encode($data['email']) : null;

        $invoiceCustomerInfoModel->update($invoice->id, $data);

        set_alert('success', 'Cập nhật thông tin hóa đơn thành công.');

        return $this->response->setJSON(['status' => true]);
    }

    public function get_payment($id)
    {
        $transaction = model(StransactionModel::class)
            ->select([
                'id',
                'payment_method',
                'in as amount',
                'date as transaction_date',
                'trans_id as transaction_id',
                'description',
            ])
            ->find($id);

        return $this->response->setJSON(['status' => true, 'data' => $transaction]);
    }

    public function update_payment($id)
    {
        $this->verifyAjaxRequest('can_edit');

        $rules = [
            'payment_method' => 'required|in_list[cod,bank_transfer]',
            'amount' => 'required|numeric|greater_than[0]',
            'description' => 'permit_empty|string',
            'transaction_id' => 'permit_empty|string',
            'transaction_date' => 'required|valid_date[Y-m-d H:i:s]',
        ];

        $data = [
            'id' => $this->request->getPost('id'),
            'payment_method' => $this->request->getPost('payment_method'),
            'amount' => $this->request->getPost('amount'),
            'description' => $this->request->getPost('description'),
            'transaction_id' => $this->request->getPost('transaction_id'),
            'transaction_date' => date('Y-m-d H:i:s', strtotime($this->request->getPost('transaction_date') ?: time())),
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->response->setJSON([
                'status' => false,
                'messages' => $this->validator->getErrors()
            ])->setStatusCode(422);
        }

        $transaction = model(StransactionModel::class)->find($id);

        if (! $transaction) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy thông tin thanh toán.'
            ]);
        }

        $db = db_connect();
        $db->transBegin();

        try {
            $transactionData = [
                'payment_method' => $this->request->getPost('payment_method'),
                'in' => $this->request->getPost('amount'),
                'description' => $this->request->getPost('description'),
                'trans_id' => $this->request->getPost('transaction_id'),
                'date' => $this->request->getPost('transaction_date'),
            ];

            model(StransactionModel::class)->update($id, $transactionData);

            $db->transCommit();

            set_alert('success', 'Cập nhật thông tin thanh toán thành công.');

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $db->transRollback();

            log_message('error', 'Error updating payment: ' . $e->getMessage());

            return $this->response->setJSON([
                'status' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật thanh toán. Vui lòng thử lại.'
            ]);
        }
    }

    public function get_qr($decalId)
    {
        $this->verifyAjaxRequest('can_edit');

        $decal = model(OutputDeviceDecalModel::class)
            ->select('tb_autopay_output_device_decal.*, tb_autopay_bank.brand_name as bank_name, tb_autopay_bank.icon_path as icon_path, tb_autopay_output_device.serial_number')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_output_device_decal.bank_id', 'left')
            ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_output_device_decal.output_device_id', 'left')
            ->find($decalId);

        if (! $decal) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy mã QR.'
            ]);
        }

        if (empty($decal->virtual_account_number)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Chưa có tài khoản ảo.'
            ]);
        }

        model(OutputDeviceDecalModel::class)->update($decal->id, ['qrcode_printed' => true]);

        $virtualAccountNumber = $decal->virtual_account_number;
        $bankName = $decal->bank_name;
        $iconPath = $decal->icon_path;

        $remark = '';

        if ($bankName === 'ABBANK') {
            $remark = 'LocVang TKP' . $virtualAccountNumber;
        } elseif ($bankName === 'VietinBank') {
            $remark = 'SEVQR ';
            if (in_array($bankName, ['VPBank', 'TPBank', 'VietinBank', 'ACB'])) {
                $remark .= 'TKP' . $virtualAccountNumber;
            }
        } elseif (in_array($bankName, ['VPBank', 'TPBank', 'ACB'])) {
            $remark = 'TKP' . $virtualAccountNumber;
        }

        return $this->response->setJSON([
            'status' => true,
            'data' => [
                'qr_code' => sprintf(
                    'https://qr.sepay.vn/img?bank=%s&acc=%s&template=qronly&des=%s',
                    $bankName,
                    in_array($bankName, ['MBBank', 'OCB', 'BIDV']) ? $virtualAccountNumber : $decal->account_number,
                    $remark,
                ),
                'virtual_account_number' => $virtualAccountNumber,
                'bank_name' => $bankName,
                'bank_logo' => 'https://my.sepay.vn/assets/images/banklogo/' . $iconPath,
                'description' => $remark,
                'serial_number' => $decal->serial_number,
                'account_number' => $decal->account_number,
            ],
        ]);
    }

    public function delete_payment($id)
    {
        $this->verifyAjaxRequest('can_edit');

        $transaction = model(StransactionModel::class)
            ->select([
                'tb_autopay_stransaction.id',
                'tb_autopay_stransaction.invoice_id',
                'tb_autopay_invoice.physical_invoice_id',
                'tb_autopay_physical_invoices.order_id'
            ])
            ->join('tb_autopay_invoice', 'tb_autopay_invoice.id = tb_autopay_stransaction.invoice_id')
            ->join('tb_autopay_physical_invoices', 'tb_autopay_physical_invoices.id = tb_autopay_invoice.physical_invoice_id')
            ->find($id);

        if (! $transaction) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy thông tin thanh toán.'
            ]);
        }

        $this->getOrderWithPermission($transaction->order_id, 'can_edit');

        model(StransactionModel::class)->delete($id);

        set_alert('success', 'Xóa thanh toán thành công.');

        return $this->response->setJSON(['status' => true]);
    }

    protected function getShippingCompanies(): array
    {
        return [
            'GHTK' => 'Giao Hàng Tiết Kiệm (GHTK)',
            'Self Delivery' => 'Nhân viên tự giao hàng',
            'GHN' => 'Giao Hàng Nhanh (GHN)',
            'J&T' => 'J&T Express',
            'Viettel Post' => 'Viettel Post',
        ];
    }

    protected function getOrderWithPermission(int $id, string $permission = 'can_view_all')
    {
        if (!has_permission('Order', $permission)) {
            show_404();
        }

        $orderModel = model(PhysicalOrderModel::class);
        $order = $orderModel->find($id);

        if (! $order) {
            show_404();
        }

        return $order;
    }

    protected function verifyAjaxRequest(string $permission = 'can_view_all')
    {
        if (! $this->request->isAJAX() || ! has_permission('Order', $permission)) {
            show_404();
        }
    }

    protected function recordHistory(object $order, string $note)
    {
        model(PhysicalOrderHistoryModel::class)->recordHistory($order, $note, $this->admin_session['admin_id']);
    }

    protected function validationErrorResponse()
    {
        return $this->response
            ->setStatusCode(422)
            ->setJSON([
                'status' => false,
                'messages' => $this->validator->getErrors(),
            ]);
    }

    protected function recalculateAmount(object $order, object $invoice)
    {
        $db = db_connect();
        $db->transBegin();

        try {
            $invoiceData = [
                'total_amount' => $order->total_amount - ($order->total_amount * $invoice->tax_rate / (100 + $invoice->tax_rate)),
                'tax_amount' => $order->total_amount * $invoice->tax_rate / (100 + $invoice->tax_rate),
                'shipping_fee' => $order->shipping_fee,
                'discount_amount' => $order->discount_amount,
                'final_amount' => $order->total_amount,
            ];

            model(PhysicalInvoiceModel::class)->update($invoice->physical_invoice_id, $invoiceData);

            $originalInvoiceData = [
                'subtotal' => $order->total_amount,
                'total' => $invoiceData['total_amount'],
                'tax' => $invoiceData['tax_amount'],
            ];

            model(InvoiceModel::class)->update($invoice->id, $originalInvoiceData);

            model(OrderModel::class)
                ->where('physical_order_id', $order->id)
                ->set(['total' => $order->total_amount,])
                ->update();

            $db->transCommit();
        } catch (Exception $e) {
            $db->transRollback();
            log_message('error', 'Error recalculating amount: ' . $e->getMessage());
            throw $e;
        }
    }

    public function invoice($id)
    {
        if (!has_permission('Order', 'can_view_all')) {
            show_404();
        }

        $action = $this->request->getGet('action');

        $invoice = model(InvoiceModel::class)
            ->select([
                'tb_autopay_invoice.id',
                'tb_autopay_physical_invoices.invoice_id',
                'tb_autopay_physical_invoices.status',
                'tb_autopay_invoice.status as invoice_status',
            ])
            ->join('tb_autopay_physical_invoices', 'tb_autopay_physical_invoices.id = tb_autopay_invoice.physical_invoice_id')
            ->where('tb_autopay_physical_invoices.order_id', $id)
            ->first();

        if (! $invoice || $invoice->status !== 'issued') {
            set_alert('error', 'Hóa đơn chưa được phát hành hoặc không tồn tại.');
            return redirect()->back();
        }

        try {
            $cache = Services::cache();
            $cacheKey = "vat_invoice_{$invoice->invoice_id}";
            $cacheTime = 60 * 60 * 24;

            $result = $cache->get($cacheKey);

            if ($result === null) {
                $sInvoice = (new SInvoice())->setConfig(config(SInvoiceConfig::class));

                $result = $sInvoice->getInvoiceFile(
                    $invoice->invoice_id,
                    null,
                    'PDF',
                    $invoice->invoice_status === 'Paid'
                );

                log_message('error', "Download Vat Invoice: " . json_encode($result));

                if (empty($result['fileName']) || empty($result['fileToBytes'])) {
                    throw new Exception('Không thể tải file hóa đơn.');
                }

                $cache->save($cacheKey, $result, $cacheTime);
            }

            if ($action === 'preview') {
                return $this->response->setJSON($result);
            }

            return $this->response->download($result['fileName'], base64_decode($result['fileToBytes']));
        } catch (Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Có lỗi xảy ra khi tải hóa đơn: ' . $e->getMessage(),
            ]);
        }
    }

    public function update_quantity($id)
    {
        $order = $this->getOrderWithPermission($id, 'can_edit');

        $data = [
            'item_id' => $this->request->getPost('item_id'),
            'new_quantity' => (int) $this->request->getPost('new_quantity'),
            'quantity_note' => xss_clean($this->request->getPost('quantity_note')),
        ];

        $rules = [
            'item_id' => 'required|is_natural_no_zero',
            'new_quantity' => 'required|is_natural_no_zero|greater_than[0]',
            'quantity_note' => 'permit_empty|max_length[255]',
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->validationErrorResponse();
        }

        $orderItemModel = model(PhysicalOrderItemModel::class);
        $item = $orderItemModel->where(['id' => $data['item_id'], 'order_id' => $id])->first();

        if (! $item) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy sản phẩm trong đơn hàng này.',
            ]);
        }

        if ($order->payment_status === 'Paid') {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không thể thay đổi số lượng cho đơn hàng đã được thanh toán.',
            ]);
        }

        if ($item->quantity == $data['new_quantity']) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Số lượng mới phải khác số lượng hiện tại.',
            ]);
        }

        $db = db_connect();
        $db->transBegin();

        try {
            $oldQuantity = $item->quantity;
            $newTotalPrice = $item->price * $data['new_quantity'];
            $oldTotalPrice = $item->total_price;

            $orderItemModel->update($item->id, [
                'quantity' => $data['new_quantity'],
                'total_price' => $newTotalPrice,
            ]);

            $orderModel = model(PhysicalOrderModel::class);
            $newSubtotal = $order->subtotal - $oldTotalPrice + $newTotalPrice;
            $newTotalAmount = $newSubtotal - $order->discount_amount + $order->shipping_fee;

            $orderModel->update($id, [
                'subtotal' => $newSubtotal,
                'total_amount' => $newTotalAmount,
            ]);

            $invoice = $this->getInvoiceForOrder($id);
            if ($invoice) {
                $order->subtotal = $newSubtotal;
                $order->total_amount = $newTotalAmount;
                $this->recalculateAmount($order, $invoice);
            }

            $this->updateDecalsForQuantityChange($id, $item->id, $oldQuantity, $data['new_quantity']);

            $note = empty($data['quantity_note'])
                ? "Thay đổi số lượng sản phẩm '{$item->name}' từ {$oldQuantity} thành {$data['new_quantity']}"
                : $data['quantity_note'];

            $this->recordHistory($order, $note);

            $db->transCommit();

            set_alert('success', 'Cập nhật số lượng sản phẩm thành công.');

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $db->transRollback();
            log_message('error', 'Error updating quantity: ' . $e->getMessage());

            return $this->response->setJSON([
                'status' => false,
                'message' => 'Đã xảy ra lỗi khi cập nhật số lượng. Vui lòng thử lại.',
            ]);
        }
    }

    public function create_shipping_order($orderId)
    {
        $order = $this->getOrderWithPermission($orderId, 'can_edit');

        if (! $order) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy đơn hàng.',
            ]);
        }

        $client = new GhtkClient();
        $config = config(Ghtk::class);
        $orderModel = model(PhysicalOrderModel::class);
        $pickupInformation = $config->pickupInformation;

        $orderItems = model(PhysicalOrderItemModel::class)
            ->select([
                'tb_autopay_physical_order_items.id',
                'tb_autopay_physical_order_items.name',
                'tb_autopay_physical_order_items.price',
                'tb_autopay_physical_order_items.quantity',
                'tb_autopay_physical_order_items.product_id',
                'tb_autopay_physical_products.weight',
            ])
            ->join('tb_autopay_physical_products', 'tb_autopay_physical_products.id = tb_autopay_physical_order_items.product_id')
            ->where('order_id', $orderId)
            ->findAll();

        $products = [];
        $totalWeight = 0;
        $totalQuantity = 0;
        foreach ($orderItems as $item) {
            if ($item->product_id == 10) {
                $name = $item->name . ' ABBank';
            } else {
                $name = $item->name;
            }

            $products[] = [
                'name' => $name,
                'price' => (int) $item->price,
                'quantity' => (int) $item->quantity,
                'weight' => (double) ($item->weight) / 1000,
            ];

            $totalWeight += $item->weight * $item->quantity;
            $totalQuantity += $item->quantity;
        }

        $money = $order->subtotal - $order->discount_amount;

        list($height, $width, $length) = $this->calculateOptimalDimensions($totalQuantity, $totalWeight / 1000);

        $orderData = [
            'id' => $order->order_code,
            'pick_name' => $pickupInformation['name'],
            'pick_money' => $order->payment_status === 'Paid' ? 0 : $money,
            'pick_address' => $pickupInformation['address'],
            'pick_province' => $pickupInformation['province'],
            'pick_district' => $pickupInformation['district'],
            'pick_ward' => $pickupInformation['ward'],
            'pick_tel' => $pickupInformation['phone'],
            'name' => $order->customer_name,
            'address' => $order->address,
            'province' => $order->province,
            'district' => $order->district,
            'ward' => $order->ward,
            'street' => $order->ward,
            'hamlet' => 'Khác',
            'tel' => $order->customer_phone,
            'note' => 'không cho xem hàng, không cho thử hàng/đồng kiểm',
            'email' => $order->customer_email,
            'is_freeship' => $order->payment_status === 'Paid' ? 1 : 0,
            'total_weight' => (float) $totalWeight / 1000,
            'value' => $money >= 1_000_000 ? 920_000 : $money,
            'pick_option' => 'cod',
            'actual_transfer_method' => 'road',
            'transport' => 'road',
        ];

        if ($height > 0 && $width > 0 && $length > 0) {
            $orderData['height'] = $height;
            $orderData['width'] = $width;
            $orderData['length'] = $length;
        }

        $data = $client->createOrder([
            'order' => $orderData,
            'products' => $products,
        ]);

        if ($data['success']) {
            $orderModel->update($orderId, [
                'tracking_code' => $data['order']['label'],
                'tracking_id' => $data['order']['tracking_id'] ?? null,
                'shipping_company' => 'GHTK',
                'status' => OrderStatus::WAITING_SHIPPER,
                'create_shipping_api_response' => json_encode($data),
            ]);

            try {
                $order = $orderModel->where('id', $orderId)->first();
    
                $base64 = $this->makeTrackingOrderLabelAsBase64($order, 'landscape');
    
                $printer = new Printer('ghtk');
                $printer->printBase64AsPng($base64);
            } catch (\Exception $e) {
                log_message('error', 'Error printing GHTK order label: ' . $e->getMessage());
            }

            $this->recordHistory($order, 'Tạo đơn hàng GHTK ' . $data['order']['label'] . ' thành công');

            set_alert('success', 'Tạo đơn hàng GHTK ' . $data['order']['label'] . ' thành công');
        } else {
            log_message('error', 'GHTK Create Order: ' . json_encode($data));
        }

        return $this->response->setJSON(['status' => $data['success'], 'message' => $data['message']]);
    }

    protected function updateDecalsForQuantityChange($orderId, $itemId, $oldQuantity, $newQuantity)
    {
        $decalModel = model(OutputDeviceDecalModel::class);

        if ($newQuantity > $oldQuantity) {
            $item = model(PhysicalOrderItemModel::class)
                ->select(['tb_autopay_physical_order_items.*', 'tb_autopay_physical_products.bank_id'])
                ->join('tb_autopay_physical_products', 'tb_autopay_physical_products.id = tb_autopay_physical_order_items.product_id')
                ->where('tb_autopay_physical_order_items.id', $itemId)
                ->first();

            if ($item) {
                $addCount = $newQuantity - $oldQuantity;
                for ($i = 0; $i < $addCount; $i++) {
                    $decalModel->insert([
                        'physical_order_id' => $orderId,
                        'physical_order_item_id' => $itemId,
                        'bank_id' => $item->bank_id,
                    ]);
                }
            }
        } elseif ($newQuantity < $oldQuantity) {
            $decalModel->where('physical_order_id', $orderId)
                ->where('physical_order_item_id', $itemId)
                ->where('virtual_account_number', null)
                ->where('output_device_id', null)
                ->limit($oldQuantity - $newQuantity)
                ->delete();
        }
    }
    
    public function print_tracking_order(string $orderId = '')
    {
        $order = $this->getOrderWithPermission($orderId, 'can_edit');

        if (! $order) {
            show_404();
        }
        
        if (!$order->tracking_code) {
            show_404();
        }

        $base64 = $this->makeTrackingOrderLabelAsBase64($order);

        return view('orders/printer', ['order' => $order, 'base64' => $base64]);
    }
    
    public function ajax_print_tracking_order(string $orderId = '')
    {
        $order = $this->getOrderWithPermission($orderId, 'can_edit');

        if (! $order) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy đơn hàng.',
            ]);
        }
        
        if (!$order->tracking_code) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Đơn hàng chưa có mã vận đơn.',
            ]);
        }

        $client = new GhtkClient();
        $config = config(Ghtk::class);
        
        $response = $client->printOrder($order->tracking_code);
        
        header('Content-Type: application/pdf');
        header('Content-Length: ' . strlen($response->getBody()));
        echo $response->getBody();
        exit;
    }

    protected function getInvoiceForOrder($orderId)
    {
        return  model(InvoiceModel::class)
            ->select('tb_autopay_invoice.id, tb_autopay_invoice.physical_invoice_id, tb_autopay_physical_invoices.status, tb_autopay_physical_invoices.tax_rate')
            ->join('tb_autopay_physical_invoices', 'tb_autopay_physical_invoices.id = tb_autopay_invoice.physical_invoice_id')
            ->where('tb_autopay_physical_invoices.order_id', $orderId)
            ->first();
    }

    /**
     * @param string $original "portrait" | "landscape"
     */
    protected function makeTrackingOrderLabelAsBase64(object $order, string $original = 'portrait'): string
    {
        $orderItems = model(PhysicalOrderItemModel::class)
            ->select([
                'tb_autopay_physical_order_items.id',
                'tb_autopay_physical_order_items.name',
                'tb_autopay_physical_order_items.price',
                'tb_autopay_physical_order_items.quantity',
                'tb_autopay_physical_order_items.product_id',
                'tb_autopay_physical_products.weight',
            ])
            ->join('tb_autopay_physical_products', 'tb_autopay_physical_products.id = tb_autopay_physical_order_items.product_id')
            ->where('order_id', $order->id)
            ->findAll();

        $orderItemCount = array_sum(array_map(function($item) { return $item->quantity; }, $orderItems));

        $d1 = new \Milon\Barcode\DNS1D();
        $d2 = new \Milon\Barcode\DNS2D();
        $d1->setStorPath(WRITEPATH. '/cache/');
        $d2->setStorPath(WRITEPATH. '/cache/');
        
        $createShippingApiResponse = property_exists($order, 'create_shipping_api_response') && $order->create_shipping_api_response 
            ? json_decode($order->create_shipping_api_response, true) 
            : [];
            
        $trackingId = $order->tracking_id ?? $createShippingApiResponse['order']['tracking_id'] ?? null;
        
        if (!$trackingId) {
            show_404();
        }
        
        $width = 600;
        $height = 1000;

        $canvas = imagecreatetruecolor($width, $height);
        $white = imagecolorallocate($canvas, 255, 255, 255);
        imagefill($canvas, 0, 0, $white);

        $barcodePath = $d1->getBarcodePNG($trackingId, 'C128', 7, 290, array(0,0,0));
        $qrcodePath = $d2->getBarcodePNG($trackingId, 'QRCODE', 14, 14, array(0,0,0));

        $logoImg = imagecreatefrompng(ROOTPATH . 'public/assets/images/ghtk.png');
        $barcodeImg = imagecreatefromstring(base64_decode((string) $barcodePath));
        $barcodeImg = imagerotate($barcodeImg, 89.99, 0);
        $qrcodeImg = imagecreatefromstring(base64_decode($qrcodePath));

        $logoWidth = imagesx($logoImg);
        $logoHeight = imagesy($logoImg);
        $barcodeWidth = imagesx($barcodeImg);
        $barcodeHeight = imagesy($barcodeImg);
        $qrcodeWidth = imagesx($qrcodeImg);
        $qrcodeHeight = imagesy($qrcodeImg);

        $totalContentHeight = $barcodeHeight + $qrcodeHeight + 50;
        $startY = ($height - $totalContentHeight) / 2;

        $fontSize = 40;
        $smallFontSize = 30;
        $fontFamily = ROOTPATH . 'public/assets/font/arial.ttf';

        $infoLines = [
            mb_strlen($order->customer_name) > 30 ? mb_substr($order->customer_name, 0, 27) . '...' : $order->customer_name,
            sprintf('%s đ', number_format($order->payment_status == 'Paid' ? 0 : $order->total_amount, 0, '.', '.')),
            sprintf('%s loa', $orderItemCount),
        ];

        foreach ($infoLines as $index => $line) {
            $infoLineBbox = imagettfbbox($smallFontSize, 90, $fontFamily, $line);
            $customerNameWidth = abs($infoLineBbox[1] - $infoLineBbox[7]);
            $customerNameHeight = abs($infoLineBbox[0] - $infoLineBbox[6]);
            $infoLineX = 60 + ($index * ($smallFontSize + 20));
            $infoLineY = ($height - 20);
            imagettftext($canvas, $smallFontSize, 90, $infoLineX, $infoLineY, imagecolorallocate($canvas, 0, 0, 0), $fontFamily, $line);
        }

        $trackingIdBbox = imagettfbbox($fontSize, 90, $fontFamily, $trackingId);
        $trackingIdWidth = abs($trackingIdBbox[1] - $trackingIdBbox[7]);
        $trackingIdHeight = abs($trackingIdBbox[0] - $trackingIdBbox[6]);
        $trackingIdX = ($width - $trackingIdWidth - 20);
        $trackingIdY = ($height - $trackingIdHeight) / 2;

        imagecopy($canvas, $logoImg, 20, 0, 0, 0, $logoWidth, $logoHeight);
        imagecopy($canvas, $barcodeImg, ($width - $barcodeWidth - $trackingIdHeight - 40), $startY, 0, 0, $barcodeWidth, $barcodeHeight);
        imagecopy($canvas, $qrcodeImg, ($width - $qrcodeWidth - $trackingIdHeight - 40), $startY + $barcodeHeight + 50, 0, 0, $qrcodeWidth, $qrcodeHeight);
        
        imagettftext($canvas, $fontSize, 90, $trackingIdX, $trackingIdY, imagecolorallocate($canvas, 0, 0, 0), $fontFamily, $trackingId);

        if ($original === 'landscape') {
            $rotatedCanvas = imagerotate($canvas, -90, 0);
            imagedestroy($canvas);
            $canvas = $rotatedCanvas;

            $origWidth = imagesx($canvas);
            $origHeight = imagesy($canvas);
            $scaledCanvas = imagecreatetruecolor($origWidth * 3, $origHeight * 3);
            imagecopyresampled($scaledCanvas, $canvas, 0, 0, 0, 0, $origWidth * 3, $origHeight * 3, $origWidth, $origHeight);
            imagedestroy($canvas);
            $canvas = $scaledCanvas;
        }
            
        ob_start();
        imagepng($canvas);
        $imageData = ob_get_contents();
        ob_end_clean();

        $base64 = base64_encode($imageData);

        imagedestroy($canvas);
        imagedestroy($barcodeImg);
        imagedestroy($qrcodeImg);

        return $base64;
    }

    protected function calculateOptimalDimensions($quantity, $totalWeight)
    {
        if ($totalWeight <= 1) {
            return [0, 0, 0];
        }

        $baseHeight = 7;
        $baseWidth = 18;
        $baseLength = 13;

        // Tính số lượng sản phẩm có thể xếp theo chiều cao (tối đa 5 tầng)
        $heightLayers = min(5, ceil($quantity / 2));

        // Tính số lượng sản phẩm có thể xếp theo chiều rộng
        // Nếu số lượng > 10 thì xếp 2 hàng ngang, ngược lại xếp 1 hàng
        $widthLayers = $quantity > 10 ? 2 : 1;

        $height = $baseHeight * $heightLayers;
        $width = $baseWidth * $widthLayers;
        $length = $baseLength;

        return [$height, $width, $length];
    }
}
