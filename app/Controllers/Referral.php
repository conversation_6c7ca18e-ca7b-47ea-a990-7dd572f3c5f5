<?php

namespace App\Controllers;

use App\Models\ReferralCodeModel;
use App\Models\ReferralUseModel;

class Referral extends BaseController
{
    public function index()
    {
        if (! has_permission('Referral', 'can_view_all')) {
            show_404();
        }

        $referralCodeModel = model(ReferralCodeModel::class);

        $data = [
            'page_title' => 'Tổng quan Chương trình giới thiệu',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session,
            'stats' => $referralCodeModel->getReferralStatistics(),
            'topReferralsThisMonth' => $referralCodeModel->getTopReferrals(15, date('Y-m-01 00:00:00')),
            'topReferralsLastMonth' => $referralCodeModel->getTopReferrals(15, date('Y-m-01 00:00:00', strtotime('first day of last month')), date('Y-m-t 23:59:59', strtotime('last day of last month'))),
        ];

        echo view('templates/sepay/header', $data);
        echo view('referral/index', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function ajax_referral_uses_list()
    {
        if (! has_permission('Referral', 'can_view_all')) {
            show_404();
        }

        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        $referralUsesModel = model(ReferralUseModel::class);
        $referralUses = $referralUsesModel->getDatatables();

        $data = [];
        $no = $this->request->getPost('start');
        $draw = $this->request->getPost('draw');

        foreach ($referralUses as $referralUse) {
            $no++;

            switch ($referralUse->action) {
                case 'first_connect_bank':
                    $action = 'Đăng ký mới';
                    break;
                case 'connect_bank':
                    $action = 'Kết nối ngân hàng';
                    break;
                case 'paid':
                    $action = 'Trả phí';
                    break;
            }

            $data[] = [
                $no,
                sprintf('<a href="%s" target="_blank"><img src="%s" class="img-fluid" style="max-width: 24px; max-height: 24px; margin-right: 4px; border-radius: 50%%;">%s</a>', base_url("company/details/$referralUse->referrer_id"), get_gravatar($referralUse->referrer_email), $referralUse->referrer_name),
                sprintf('<a href="%s" target="_blank"><img src="%s" class="img-fluid" style="max-width: 24px; max-height: 24px; margin-right: 4px; border-radius: 50%%;">%s</a>', base_url("company/details/$referralUse->referred_id"), get_gravatar($referralUse->referred_email), $referralUse->referred_name),
                $referralUse->value,
                $action,
                $referralUse->is_valid ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-warning"></i>',
                $referralUse->created_at,
            ];
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $referralUsesModel->countTotal(),
            'recordsFiltered' => $referralUsesModel->countFiltered(),
            'data' => $data,
        ]);
    }
}
