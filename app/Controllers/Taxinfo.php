<?php

namespace App\Controllers;
use App\Models\AdminModel;
use App\Models\CompanyModel;
use App\Models\InvoiceModel;
use App\Models\InvoiceItemModel;

use CodeIgniter\Controller;

class Taxinfo extends BaseController
{
    public function index()
    {
        $data = [
            'page_title' => 'Tax Info',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];
        if(!has_permission('TaxInfo', 'can_view_all')) {
            show_404();
        }
        echo view('templates/sepay/header', $data);
        echo view('taxinfo/index', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function create_for_company($company_id='')
    {
        $data = [
            'page_title' => 'Tax Info',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('TaxInfo', 'can_view_all')) {
            show_404();
        }

        if(!is_numeric($company_id)) {
            show_404();
        }

        $companyModel = model(CompanyModel::class);
        $taxInfoModel = model(TaxInfoModel::class);

        $data['company_details'] = $companyModel->find($company_id);

        if(!is_object($data['company_details'])) {
            show_404();
        }

        $provinceModel = model(ProvinceModel::class);
      
        $data['provinces'] = $provinceModel->orderBy('code','ASC')->get()->getResult();
       

        echo view('templates/sepay/header', $data);
        echo view('taxinfo/create', $data);
        echo view('templates/sepay/footer', $data);
    }

    public function create_for_merchant($merchant_id='')
    {
        $data = [
            'page_title' => 'Tax Info',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('TaxInfo', 'can_view_all')) {
            show_404();
        }

        if(!is_numeric($merchant_id)) {
            show_404();
        }

        $merchantDetailsModel = model(MerchantDetailsModel::class);
        $taxInfoModel = model(TaxInfoModel::class);

        $data['merchant_details'] = $merchantDetailsModel->find($merchant_id);

        if(!is_object($data['merchant_details'])) {
            show_404();
        }

        $provinceModel = model(ProvinceModel::class);
      
        $data['provinces'] = $provinceModel->orderBy('code','ASC')->get()->getResult();
       

        echo view('templates/sepay/header', $data);
        echo view('taxinfo/merchant_create', $data);
        echo view('templates/sepay/footer', $data);
    }


    public function ajax_get_districts($province_code) {
       
        $districtModel = model(DistrictModel::class);
        $districts = $districtModel->where(['province_code' => $province_code])->orderBy('code','ASC')->get()->getResult();

        return $this->response->setJSON(array("status"=>true,"data" => $districts));

    }

    public function ajax_get_wards($district_code) {
       
        $wardModel = model(WardModel::class);
        $wards = $wardModel->where(['district_code' => $district_code])->orderBy('code','ASC')->get()->getResult();

        return $this->response->setJSON(array("status"=>true,"data" => $wards));

    }


    public function ajax_add() {

        if(!has_permission('TaxInfo', 'can_add'))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không có quyền thao tác"));

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'company_id' => ['label' => 'Company ID', "rules" => "required|is_natural"],
            'merchant_id' => ['label' => 'Merchant ID', "rules" => "required|is_natural"],
            'type' => ['label' => 'Type', "rules" => "required|in_list[Individual,Organization]"],
            'name' => ['label' => 'Tên', "rules" => "required"],
            'email' => ['label' => 'Email nhận hoá đơn', "rules" => "required|valid_email"],
            'province' => ['label' => 'Tỉnh/ Thành', "rules" => "required|is_natural"],
            'district' => ['label' => 'Quận/ Huyện', "rules" => "required|is_natural"],
            'ward' => ['label' => 'Phường/ Xã', "rules" => "required|is_natural"],
            'address1' => ['label' => 'Số nhà, đường/ thôn, ấp', "rules" => "required"],

        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $data = [
            'company_id' => $this->request->getVar('company_id'),
            'merchant_id' => $this->request->getVar('merchant_id'),
            'type' => $this->request->getVar('type'),
            'name' => $this->request->getVar('name'),
            'email' => $this->request->getVar('email'),
            'province' => $this->request->getVar('province'),
            'district' => $this->request->getVar('district'),
            'ward' => $this->request->getVar('ward'),
            'address1' => trim($this->request->getVar('address1')),
            'tax_code' => $this->request->getVar('tax_code'),
            'note' => trim($this->request->getVar('note')),
        ];

        if($data['type'] == "Organization" && $data['tax_code'] == "")
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Thông tin công ty cần cung cấp thêm mã số thuế"));


        $taxInfoModel = model(TaxInfoModel::class);

        $taxInfoModel->insert($data);

        return $this->response->setJSON(["status"=>TRUE]);

    }


    
    public function edit($id='')
    {
        $data = [
            'page_title' => 'Tax Info',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        if(!has_permission('TaxInfo', 'can_edit')) {
            show_404();
        }

        if(!is_numeric($id)) {
            show_404();
        }

        $companyModel = model(CompanyModel::class);
        $merchantDetailsModel = model(MerchantDetailsModel::class);
        $taxInfoModel = model(TaxInfoModel::class);
        $provinceModel = model(ProvinceModel::class);
        $districtModel = model(DistrictModel::class);
        $wardModel = model(WardModel::class);

        $data['tax_details'] = $taxInfoModel->find($id);

        if(!is_object($data['tax_details'])) {
            show_404();
        }


        $data['provinces'] = $provinceModel->orderBy('code','ASC')->get()->getResult();
        $data['districts'] = $districtModel->orderBy('code','ASC')->get()->getResult();
        $data['wards'] = $wardModel->orderBy('code','ASC')->get()->getResult();



        if($data['tax_details']->company_id > 0) {
            $data['company_details'] = $companyModel->find($data['tax_details']->company_id);

            if(!is_object($data['company_details'])) {
                show_404();
            }
     
            echo view('templates/sepay/header', $data);
            echo view('taxinfo/edit', $data);
            echo view('templates/sepay/footer', $data);
            
        } else if($data['tax_details']->merchant_id > 0) {

            $data['merchant_details'] = $merchantDetailsModel->find($data['tax_details']->merchant_id);

            if(!is_object($data['merchant_details'])) {
                show_404();
            }
     
            echo view('templates/sepay/header', $data);
            echo view('taxinfo/merchant_edit', $data);
            echo view('templates/sepay/footer', $data);
        } else {
            show_404();
        }
        
        
    }



    public function ajax_update() {

        if(!has_permission('TaxInfo', 'can_edit'))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không có quyền thao tác"));

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'id' => ['label' => 'ID', "rules" => "required|is_natural"],
            'type' => ['label' => 'Type', "rules" => "required|in_list[Individual,Organization]"],
            'name' => ['label' => 'Tên', "rules" => "required"],
            'email' => ['label' => 'Email nhận hoá đơn', "rules" => "required|valid_email"],
            'province' => ['label' => 'Tỉnh/ Thành', "rules" => "required|is_natural"],
            'district' => ['label' => 'Quận/ Huyện', "rules" => "required|is_natural"],
            'ward' => ['label' => 'Phường/ Xã', "rules" => "required|is_natural"],
            'address1' => ['label' => 'Số nhà, đường/ thôn, ấp', "rules" => "required"],

        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $id =  $this->request->getVar('id');

        $data_update = [
            'type' => $this->request->getVar('type'),
            'name' => $this->request->getVar('name'),
            'email' => $this->request->getVar('email'),
            'province' => $this->request->getVar('province'),
            'district' => $this->request->getVar('district'),
            'ward' => $this->request->getVar('ward'),
            'address1' => trim($this->request->getVar('address1')),
            'tax_code' => $this->request->getVar('tax_code'),
            'note' => trim($this->request->getVar('note')),
        ];

        if($data_update['type'] == "Organization" && $data_update['tax_code'] == "")
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Thông tin công ty cần cung cấp thêm mã số thuế"));


        $taxInfoModel = model(TaxInfoModel::class);

        $taxInfoModel->set($data_update)->where(['id' => $id])->update();
        set_alert("success", "Sửa thành công");

        return $this->response->setJSON(["status"=>TRUE]);

    }

    public function ajax_delete() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('TaxInfo', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        
        $id = $this->request->getPost('id');


        $taxInfoModel = model(TaxInfoModel::class);

        $taxInfoModel->delete($id);

        add_admin_log(array('data_id'=> $id, 'data_type'=>'delete_tax_info','description'=>'Xóa Tax Info ID ' . $id, 'admin_id'=> $this->admin_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    
        set_alert("success", "Xoá thành công");


        return $this->response->setJSON(array("status"=>true));
         
            

    
    }
 
}