<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\BankIntegrationOutputDeviceModel;
use App\Models\BankModel;
use App\Models\CompanyModel;
use App\Models\CompanyUserModel;
use App\Models\FlametechvnTokenModel;
use App\Models\MQTTClientModel;
use App\Models\OutputDeviceModel;
use App\Models\MQTTServerModel;
use App\Models\OutputDeviceReplayMessageQueueModel;
use Config\Flametechvn;

class Outputdevice extends BaseController
{
    protected function responseCustom($code, $message, $data = 0)
    {
        $response = service('response');

        $data_res = [
            'code' => $code,
            'message' => $message,
        ];

        if ($data != 0 || $data == []) {
            $data_res['data'] = $data;
        }

        $csrfToken = csrf_hash(); 

        $response->setStatusCode($code);
        $response->setJSON($data_res);

        $response->setHeader('X-CSRF-TOKEN', $csrfToken);

        return $response;
    }

    public function index()
    {
        if(!has_permission('Outputdevice', 'can_view_all'))
        show_404();
        $MQTTServerModel = model(MQTTServerModel::class);
        $MQTTClientModel = model(MQTTClientModel::class);
        $CompanyModel = model(CompanyModel::class);

        $data_server = $MQTTServerModel->select("id,hostname,port,username username_server")->get()->getResultArray();
        $data_client = $MQTTClientModel->select("id,username,mqtt_server_id")->get()->getResultArray();
        
        if (!empty($data_server)) {
            foreach ($data_server as &$val) { 
                $val['user_mqtt'] = array_values(array_filter($data_client, function ($client) use ($val) {
                    return $client['mqtt_server_id'] == $val['id'];
                }));
            }
            unset($val);
        }
      
        
        $data_company = $CompanyModel->select("
        tb_autopay_company.*,
        tb_autopay_user.email
        ")
        ->join("tb_autopay_company_user","tb_autopay_company_user.company_id=tb_autopay_company.id")
        ->join("tb_autopay_user","tb_autopay_company_user.user_id=tb_autopay_user.id")
        ->where("tb_autopay_company_user.role",'SuperAdmin')
        ->orderBy("tb_autopay_company.id", "desc") 
        ->get()
        ->getResultArray();

        $list_model = model(OutputDeviceModel::class)->select("model")->groupBy("model")->get()->getResultArray();
        $list_bank= model(BankModel::class)->get()->getResultArray();
        $data = [
            'list_bank' => $list_bank,
            'list_model' => $list_model,
            'data_server' => $data_server,
            'data_company' => $data_company,
            'page_title' => 'Thiết bị Loa',
            'admin_details' => $this->admin_details,
            'admin_session' => $this->admin_session
        ];

        //
        echo view('templates/sepay/header',$data);
        echo view('outputdevice/index',$data);
        echo view('templates/sepay/footer',$data);
    }
  
    
    public function allData() {
        $OutputDeviceModel = model(OutputDeviceModel::class);

        $data = $OutputDeviceModel->select("
        tb_autopay_output_device.id,
        tb_autopay_output_device.name,
        tb_autopay_output_device.mqtt_server_id,
        tb_autopay_output_device.imei,
        tb_autopay_output_device.serial_number,
        tb_autopay_output_device.model,
        tb_autopay_output_device.vendor,
        tb_autopay_output_device.serial_sim,
        tb_autopay_output_device.active,
        tb_autopay_output_device.company_id,
        tb_autopay_output_device.active_date,
        tb_autopay_output_device.required_content,
        tb_autopay_output_device.max_amount,
        tb_autopay_output_device.min_amount,
        tb_autopay_output_device.created_at,
        tb_autopay_output_device.phone_serial_sim,
        tb_autopay_output_device.created_at,

        tb_autopay_mqtt_server.hostname,
        tb_autopay_mqtt_server.port,
        tb_autopay_mqtt_server.username username_server,
        tb_autopay_mqtt_server.ssl_enabled,

        tb_autopay_bank.brand_name,
        tb_autopay_bank.id bank_id,

        tb_autopay_mqtt_client.username username_client,

        tb_autopay_company.full_name,
        tb_autopay_company.short_name,

        

        ")
        ->join("tb_autopay_bank_integration_output_device","tb_autopay_bank_integration_output_device.output_device_id = tb_autopay_output_device.id","left")
        ->join("tb_autopay_mqtt_server","tb_autopay_mqtt_server.id = tb_autopay_output_device.mqtt_server_id","left")
        ->join("tb_autopay_mqtt_client","tb_autopay_mqtt_client.id = tb_autopay_output_device.mqtt_client_id","left")
        ->join("tb_autopay_company","tb_autopay_company.id = tb_autopay_output_device.company_id","left")
        ->join("tb_autopay_bank","tb_autopay_bank.id = tb_autopay_output_device.bank_id","left")
        ->groupBy('tb_autopay_output_device.id')
        ->get()
        ->getResultArray();

       

        if(!empty($data)){
            foreach($data as &$val){
                $val['integration_status_text'] = !empty($val['company_id'])? "Đã liên kết":"Chưa liên kết";
                $val['hostname'] = esc($val['hostname'])??"";
                $val['port'] = $val['port']??"";
                $val['full_name'] = esc($val['full_name'])??"";
                $val['short_name'] = esc($val['short_name'])??"";
                $val['min_amount'] = $val['min_amount'] ? number_format($val['min_amount']) . " đ" : "";
                $val['max_amount'] = $val['max_amount'] ? number_format($val['max_amount']) . " đ" : "";                
                $val['required_content'] = esc($val['required_content'])??"";
            }
            unset($val);
        }
    
        return json_encode(['data' => $data]);
    }

    public function addData() {
        $data_input = $this->request->getPost();
        $OutputDeviceModel = model(OutputDeviceModel::class);
        if(empty($data_input['bank_id'])){
            $data_input['bank_id'] = null;
        }
        $validationRules = [
            
            "serial_number" => [
                "label" => "Serial Number",
                "rules" => "required|is_unique[tb_autopay_output_device.serial_number]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_unique" => "{field} đã tồn tại trong hệ thống.",
                ],
            ],
            "model" => [
                "label" => "Model",
                "rules" => "required|in_list[S003,S16,Q181,Q190]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],
           
            "vendor" => [
                "label" => "Vendor",
                "rules" => "required|in_list[Aisino,Flametechvn]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "valid_vendor" => "{field} phải là 'Aisino' hoặc 'Flametechvn'.",
                ],
            ],

        ];


        // Kiểm tra vendor
        if (isset($data_input['vendor']) && $data_input['vendor'] === "Aisino") {

            $check_exit = $OutputDeviceModel->where(['mqtt_client_id'=>$data_input['mqtt_client_id'],'mqtt_server_id'=>$data_input['mqtt_server_id']])->get()->getRowArray();
            if(!empty($check_exit)){
                return $this->responseCustom(423, "User MQTT Đã liên kết với thiết bị S/N {$check_exit['serial_number']}");
            }

            $validationRules['imei'] = [
                "label" => "IMEI",
                "rules" => "required|is_unique[tb_autopay_output_device.imei]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_unique" => "{field} đã tồn tại trong hệ thống.",
                ],
            ];

            $validationRules["mqtt_server_id"] = [
                "label" => "MQTT server",
                "rules" => "required|is_not_unique[tb_autopay_mqtt_server.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống.",
                ],
            ];
            $validationRules["mqtt_client_id"] = [
                "label" => "MQTT user",
                "rules" => "required|is_not_unique[tb_autopay_mqtt_client.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống.",
                ],
            ];
        }


        if (isset($data_input['vendor']) && $data_input['vendor'] === "Flametechvn"){
            $status_devices = $this->checkDeviceInfo($data_input['serial_number']);

            if($status_devices['status_code'] == 200 ){

                // check model
                if($status_devices['model'] != $data_input['model'] ){

                    return $this->responseCustom(422,"Model thiết bị không khớp, hãy kiểm tra lại");
                }

               $data_input['serial_sim'] = $status_devices['serial_sim'];
               $validationRules["serial_sim"] = [
                    "label" => "serial sim",
                    "rules" => "required",
                    "errors" => [
                        "required" => "Vui lòng truyền {field}.",
                    ],
                ];
            
               

            }else{
                return $this->responseCustom(423,"Lỗi check trạng thái Flametechvn",['error' => $status_devices]);
            }
        }
    
        // Load validation service
        $validation = service("validation");
    
        // Set validation rules
        $validation->setRules($validationRules);
    
        // Check data
        if (!$validation->run($data_input)) {
            $errors = $validation->getErrors();
    
            // Lấy thông báo lỗi đầu tiên
            $firstErrorKey = array_key_first($errors); // Lấy key của lỗi đầu tiên
            $firstError = $errors[$firstErrorKey]; // Lấy thông báo lỗi đầu tiên
            // Thay thế {field} bằng tên trường tương ứng
            $errorMessage = str_replace('{field}', $validationRules[$firstErrorKey]['label'], $firstError);
    
            return $this->responseCustom(
                423,
                $errorMessage, // Trả về thông báo lỗi đầu tiên
                $errors// Chỉ trả về lỗi đầu tiên trong mảng lỗi
            );
        }

        


        $result = $OutputDeviceModel->insert($data_input);

        if (!$result) {
            $error = $OutputDeviceModel->errors(); // Nếu mô hình có phương thức để lấy lỗi
            return $this->responseCustom(500, "Lỗi hệ thống insert: " . implode(", ", $error));
        }
        
        return $this->responseCustom(200, "Thêm thiết bị thành công!");
        
    }
    public function updateData() {
        $data_input = $this->request->getPost();
        $OutputDeviceModel = model(OutputDeviceModel::class);
        
        if(empty($data_input['bank_id'])){
            $data_input['bank_id'] = null;
        }
        $validationRules = [
            "id" => [
                "label" => "ID Thiết bị",
                "rules" => "required|is_not_unique[tb_autopay_output_device.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống.",
                ],
            ],
            
            "model" => [
                "label" => "Model",
                "rules" => "required|in_list[S003,S16,Q181,Q190]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "valid_vendor" => "{field} phải là 'Aisino' hoặc 'Flametechvn'.",
                ],
            ],
            "serial_number" => [
                "label" => "Serial Number",
                // Bỏ qua kiểm tra is_unique nếu `serial_number` đã thuộc về `id` hiện tại
                "rules" => "required|is_unique[tb_autopay_output_device.serial_number,id,{id}]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_unique" => "{field} đã tồn tại trong hệ thống.",
                ],
            ],
            "vendor" => [
                "label" => "Vendor",
                "rules" => "required|in_list[Aisino,Flametechvn]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "{field} phải là 'Aisino' hoặc 'Flametechvn'.",
                ],
            ],
            "active" => [
                "label" => "Trạng thái",
                "rules" => "required|in_list[0,1]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "{field} phải là 'Aisino' hoặc 'Flametechvn'.",
                ],
            ],
        ];
        
        if (isset($data_input['vendor']) && $data_input['vendor'] === "Aisino") {

            $validationRules['imei'] = [
                "label" => "IMEI",
                // Bỏ qua kiểm tra is_unique nếu `imei` đã thuộc về `id` hiện tại
                "rules" => "required|is_unique[tb_autopay_output_device.imei,id,{id}]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_unique" => "{field} đã tồn tại trong hệ thống.",
                ],
            ];

            $validationRules['mqtt_server_id']= [
                "label" => "MQTT server",
                "rules" => "required|is_not_unique[tb_autopay_mqtt_server.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống.",
                ],
            ];
            
            $validationRules['mqtt_client_id'] = [
                "label" => "MQTT server",
                "rules" => "required|is_unique[tb_autopay_output_device.mqtt_client_id,id,{id}]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_unique" => "{field} đã được tích hợp trong hệ thống.",
                ],
            ];
        }

        // Load validation service
        $validation = service("validation");
    
        // Set validation rules
        $validation->setRules($validationRules);
    
        // Check data
        if (!$validation->run($data_input)) {
            $errors = $validation->getErrors();
        
            // Lấy thông báo lỗi đầu tiên
            $firstErrorKey = array_key_first($errors); // Lấy key của lỗi đầu tiên
            $firstError = $errors[$firstErrorKey]; // Lấy thông báo lỗi đầu tiên
            
            // Thay thế {field} bằng tên trường tương ứng
            $errorMessage = str_replace('{field}', $validationRules[$firstErrorKey]['label'], $firstError);
        
            return $this->responseCustom(
                423,
                $errorMessage, // Trả về thông báo lỗi đầu tiên
                $errors // Chỉ trả về lỗi đầu tiên trong mảng lỗi
            );
        }
    
        $result = $OutputDeviceModel->set($data_input)->where(['id' => $data_input['id']])->update();
    
        if (!$result) {
            $error = $OutputDeviceModel->errors(); // Nếu mô hình có phương thức để lấy lỗi
            return $this->responseCustom(500, "Lỗi hệ thống update: " . implode(", ", $error));
        }
        
        return $this->responseCustom(200, "Cập nhật thiết bị thành công!");
    }
    

    public function addIntegrationData() {
        $data_input = $this->request->getPost();
        $BankIntegrationOutputDeviceModel = model(BankIntegrationOutputDeviceModel::class);
        $validationRules = [
            
            "output_device_id" => [
                "label" => "ID thiết bị",
                "rules" => "required|is_not_unique[tb_autopay_output_device.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_unique" => "{field} không tồn tại trong hệ thống.",
                ],
            ],

            "company_id" => [
                "label" => "ID Công ty",
                "rules" => "required|is_not_unique[tb_autopay_company.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_unique" => "{field} không tồn tại trong hệ thống.",
                ],
            ],
            "vendor" => [
                "label" => "Vendor",
                "rules" => "required|in_list[Aisino]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "{field} phải là 'Aisino'.",
                ],
            ],
        ];
    
        // Load validation service
        $validation = service("validation");
    
        // Set validation rules
        $validation->setRules($validationRules);
    
        // Check data
       
        if (!$validation->run($data_input)) {
            $errors = $validation->getErrors();
    
            // Lấy thông báo lỗi đầu tiên
            $firstErrorKey = array_key_first($errors); // Lấy key của lỗi đầu tiên
            $firstError = $errors[$firstErrorKey]; // Lấy thông báo lỗi đầu tiên
            // Thay thế {field} bằng tên trường tương ứng
            $errorMessage = str_replace('{field}', $validationRules[$firstErrorKey]['label'], $firstError);
    
            return $this->responseCustom(
                423,
                $errorMessage, // Trả về thông báo lỗi đầu tiên
                $errors// Chỉ trả về lỗi đầu tiên trong mảng lỗi
            );
        }
        $model = model(OutputDeviceModel::class);
        $result = $model->set(['company_id'=>$data_input['company_id'],'id'=>$data_input['output_device_id']])->update($data_input['output_device_id']);

        if (!$result) {
            $error = $model->errors(); // Nếu mô hình có phương thức để lấy lỗi
            return $this->responseCustom(500, "Lỗi hệ thống insert: " . implode(", ", $error));
        }
        
        return $this->responseCustom(200, "Tích hợp thành công!");
        
    }

    public function delete($id){
        $OutputDeviceModel = model(OutputDeviceModel::class);
        $BankIntegrationOutputDeviceModel = model(BankIntegrationOutputDeviceModel::class);

        $validationRules = [
            "id" => [
                "label" => "ID thiết bị",
                "rules" => "required|is_not_unique[tb_autopay_output_device.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống.",
                ],
            ],
            
        ];
         // Load validation service
         $validation = service("validation");
    
         // Set validation rules
         $validation->setRules($validationRules);
     
         // Check data
         if (!$validation->run(['id'=>$id])) {
             $errors = $validation->getErrors();
     
             // Lấy thông báo lỗi đầu tiên
             
             return $this->responseCustom(
                 500,
                 "Lỗi dữ liệu ID",
                 $errors
             );
         }
         // Gửi yêu cầu xóa qua API Flametechvn
        $check_exits = $OutputDeviceModel->where(['id' => $id, 'vendor' => 'Flametechvn'])->get()->getRowArray();
        if(!empty($check_exits) && !empty($check_exits['external_device_token'])){

            $status_delete = $this->removeDevice($check_exits['external_device_token']);
            log_message("debug","dùng xóa api ".json_encode($status_delete));
            // Xử lý lỗi từ API
        }

         $result  = $OutputDeviceModel->delete($id);
         $result_del_integration  = $BankIntegrationOutputDeviceModel->where(['output_device_id'=>$id])->delete();
         model(OutputDeviceReplayMessageQueueModel::class)->where(['output_device_id' => $id])->delete();
         if (!$result || !$result_del_integration) {
            $error = $OutputDeviceModel->errors(); // Nếu mô hình có phương thức để lấy lỗi
            return $this->responseCustom(500, "Lỗi hệ thống xóa: " . implode(", ", $error));
        }
        return $this->responseCustom(200, "Xóa  thiết bị!");
    }

    public function deleteIntegration($id){
        $OutputDeviceModel = model(OutputDeviceModel::class);
        $BankIntegrationOutputDeviceModel = model(BankIntegrationOutputDeviceModel::class);

        $validationRules = [
            "output_device_id" => [
                "label" => "ID thiết bị",
                "rules" => "required|is_not_unique[tb_autopay_output_device.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống tích hợp.",
                ],
            ],
            
        ];
         // Load validation service
         $validation = service("validation");
    
         // Set validation rules
         $validation->setRules($validationRules);
     
         // Check data
         if (!$validation->run(['output_device_id'=>$id])) {
             $errors = $validation->getErrors();
     
             // Lấy thông báo lỗi đầu tiên
            $firstErrorKey = array_key_first($errors); // Lấy key của lỗi đầu tiên
            $firstError = $errors[$firstErrorKey]; // Lấy thông báo lỗi đầu tiên
            // Thay thế {field} bằng tên trường tương ứng
            $errorMessage = str_replace('{field}', $validationRules[$firstErrorKey]['label'], $firstError);
    
            return $this->responseCustom(
                423,
                $errorMessage, // Trả về thông báo lỗi đầu tiên
                $errors// Chỉ trả về lỗi đầu tiên trong mảng lỗi
            );
         }

          // Gửi yêu cầu xóa qua API Flametechvn
        $check_exits = $OutputDeviceModel->where(['id' => $id, 'vendor' => 'Flametechvn'])->get()->getRowArray();
        if(!empty($check_exits) && !empty($check_exits['external_device_token'])){

            $status_delete = $this->removeDevice($check_exits['external_device_token']);
            log_message("debug","dùng xóa api ".json_encode($status_delete));
            // Xử lý lỗi từ API
        }

        $result_del_integration  = $BankIntegrationOutputDeviceModel->where(['output_device_id'=>$id])->delete();
        model(OutputDeviceReplayMessageQueueModel::class)->where(['output_device_id' => $id])->delete();

         if (!$result_del_integration) {
            $error = $BankIntegrationOutputDeviceModel->errors(); // Nếu mô hình có phương thức để lấy lỗi
            return $this->responseCustom(500, "Lỗi hệ thống xóa: " . implode(", ", $error));
        }
        $OutputDeviceModel->set(['company_id'=>null,'active' => 0,'external_device_id' =>null,'external_device_token'=>null,'min_amount'=> null,'max_amount'=>null,'required_content'=>null,'qr_content'=>null,'qr_bank_name'=>null,'qr_bank_account'=>null,'qr_amount'=>null,'qr_des'=>null])->where(['id'=>$id])->update();
        return $this->responseCustom(200, "Gỡ tích hợp thành công!");
    }
    
    public function edit($id){
        $OutputDeviceModel = model(OutputDeviceModel::class);
        $data = $OutputDeviceModel->select('id,mqtt_server_id,mqtt_client_id,imei,serial_number,model,vendor,online_status,name,serial_sim,active,company_id,bank_id,va_id,phone_serial_sim')->where(['id'=>$id])->get()->getRowArray();
        return $this->responseCustom(200,"OK",$data);
    }

    protected function removeDevice($device_token): array
    {
        $config = config(Flametechvn::class);
        $url = $config->host."/notibox/api/whitelabel/v1/devices/unpair";
        $method = "POST";    
        $options = [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->getAccessToken(),
                'deviceToken' => $device_token,
            ],
            
            'http_errors' => false, // Giúp tránh exception khi có lỗi HTTP
        ];    
    
        // Gửi yêu cầu và nhận phản hồi
        $curl = \Config\Services::curlrequest(); // Khởi tạo cURL service
        $response = $curl->request($method, $url, $options);
        $response_data = (string) $response->getBody(); // Chuyển dữ liệu về dạng chuỗi
        $status_code = $response->getStatusCode();          
        
        log_message("debug",'body '.json_encode($response_data));
        log_message("debug",'status_code '.json_encode($status_code));
        $array_res = [
            'status_code' => $status_code,
            'raw_response' => json_encode($response_data),
        ];

        
        if($status_code == 401){
            //reset token
            $this->getAccessToken();
            log_message("error", "API Login Error Token");
            return $array_res;
        }    
       
       
        // Giải mã dữ liệu JSON
        $data_convert = json_decode($response_data, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            log_message("error", "Remove Device API RES: " . json_encode($response_data));
            log_message("error", "Lỗi giải mã JSON: " . json_last_error_msg());
            return $array_res;
        }

        // Kiểm tra mã trạng thái HTTP
        if ($status_code == 400) {
            log_message("error", "Remove Device ERROR: " . json_encode($response_data));
            return $array_res;
        }    

        if($status_code == 200 && $data_convert['code'] == "COM0000"){
            $array_res['code'] = $data_convert['code'];
            
        }

        return $array_res;
        
    } 
    
    protected function getAccessToken(): string
    {
        $FlametechvnTokenModel = model(FlametechvnTokenModel::class);
        $config = config(Flametechvn::class);
        // Lấy thông tin token hiện tại
        $token_data = $FlametechvnTokenModel->get()->getRowArray();
        
        // Kiểm tra nếu token hết hạn
        if (empty($token_data) || time() >= strtotime($token_data['expiresIn_time'])) {

            $url = $config->host . "/notibox/api/auth/v1/login/client";
            $method = "POST";

            $options = [
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'clientId' => $config->client_id,
                    'secretKey' => $config->secret_key,
                ],
                'http_errors' => false,
            ];

            
            $curl = \Config\Services::curlrequest(); // Khởi tạo cURL service
            $response = $curl->request($method, $url, $options);
            $response_data = (string) $response->getBody();
            $status_code = $response->getStatusCode();
            // Kiểm tra dữ liệu phản hồi rỗng
            if (empty($response_data)) {
                log_message("error", "API Login Fleamtechvn trả về dữ liệu rỗng");
                return "";
            }
            // Giải mã dữ liệu JSON
            $data_convert = json_decode($response_data, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                log_message("error", "Lỗi giải mã JSON từ API Login Fleamtechvn: " . json_last_error_msg());
                return "";
            }
            // Kiểm tra mã trạng thái HTTP
            if ($status_code >= 400) {
                log_message("error", "API Login Fleamtechvn trả về lỗi: " . json_encode($response_data));
                return "";
            }
            // Kiểm tra mã thành công
            if ($status_code == 200 && $data_convert['code'] === "COM0000") {
                $expiresIn = (int) ($data_convert['data']['expiresIn'] ?? 0);
                $expiresIn_time = date('Y-m-d H:i:s', time() + $expiresIn);
                $data_update = [
                    'token' => $data_convert['data']['token'],
                    'expiresIn' => $expiresIn,
                    'expiresIn_time' => $expiresIn_time,
                    'token_type' => $data_convert['data']['tokenType'] ?? '',
                    'scope' => $data_convert['data']['scope'] ?? '',
                ];
                // Cập nhật token vào cơ sở dữ liệu
                if(empty($token_data)){
                    $check = $FlametechvnTokenModel->set($data_update)->insert();
                }else{
                    $check = $FlametechvnTokenModel->set($data_update)->where(['id' => $token_data['id'] ?? 1])->update();
                }
                if ($check) {
                    return $data_update['token'];
                } else {
                    log_message("error", "Lỗi cập nhật token mới vào cơ sở dữ liệu");
                    return "";
                }
            }
            log_message("error", "API Login Fleamtechvn trả về mã không hợp lệ: " . json_encode($data_convert));
            return "";

            

        } else {
            return $token_data['token'] ?? "";
        }
    }


    protected function checkDeviceInfo($serial_number): array
    {
        $config = config(Flametechvn::class);
        $url = $config->host."/notibox/api/whitelabel/v1/devices/pair/check?hardwareId=" . $serial_number;
        $method = "GET";    
        $options = [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->getAccessToken(),
            ],
            'http_errors' => false, // Giúp tránh exception khi có lỗi HTTP
        ];    
    
        // Gửi yêu cầu và nhận phản hồi
        $curl = \Config\Services::curlrequest();
        $response = $curl->request($method, $url, $options);
        $response_data = (string) $response->getBody(); // Chuyển dữ liệu về dạng chuỗi
        $status_code = $response->getStatusCode();           
        $array_res = [
            'status_code' => $status_code,
            'raw_response' =>json_encode($response_data),
        ];

        if($status_code == 401){
            //reset token
            $this->getAccessToken();
            log_message("error", "API Check Device Error Token ".json_encode($response_data));
            return $array_res;
        }    
       
        // Kiểm tra mã trạng thái HTTP
        if ($status_code >= 400) {
            log_message("error", "Error API Check Device: " .json_encode($response_data));
            return $array_res;
        }    
        // Giải mã dữ liệu JSON
        $data_convert = json_decode($response_data, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            log_message("error", "Lỗi giải mã JSON: " . json_last_error_msg());
            log_message("error", "API Check Device Error Token ".json_encode($response_data));
            return $array_res;
        }    

        if($status_code == 200 && $data_convert['code'] == "COM0000"){
            $array_res['model'] = $data_convert['data']['model'];
            $array_res['online'] = $data_convert['data']['online'];
            $array_res['serial_sim'] = $data_convert['data']['simSerialNumber'];
        }
        return $array_res;
        
    }

    public function importData()
    {
        // Lấy dữ liệu từ request (JSON)
        $jsonData = $this->request->getJSON(true);

        // Kiểm tra dữ liệu hợp lệ
        if (empty($jsonData['excelData'])) {
            return $this->responseCustom(422, "Dữ liệu không hợp lệ!", []);
        }

        // Duyệt từng dòng dữ liệu và trim giá trị từng cột
        $jsonData['excelData'] = array_map(function ($item) {
            foreach ($item as $key => $value) {
                $item[$key] = trim((string)$value);
            }
            return $item;
        }, $jsonData['excelData']);

        // **Lọc danh sách thiết bị không có BANKID**
        $dataNoBankID = [];
        $jsonData['excelData'] = array_filter($jsonData['excelData'], function ($item) use (&$dataNoBankID) {
            if (empty($item['BANKID'])) {
                $dataNoBankID[] = $item; // Lưu lại các item có BANKID rỗng
                return true; // giữ lại không có BANKID
            }
            return true; // Giữ lại item có BANKID
        });
        
        // Reset key mảng (nếu cần)
        $jsonData['excelData'] = array_values($jsonData['excelData']);
        

        // **Đếm số lần xuất hiện của HWID**
        $hwidCounts = array_count_values(array_column($jsonData['excelData'], 'HWID'));

        // **Lọc danh sách HWID bị trùng (chỉ lấy các HWID có số lần xuất hiện > 1)**
        $duplicateHWIDs = array_filter($hwidCounts, function ($count) {
            return $count > 1;
        });

        // **Lọc danh sách HWID duy nhất**
        $uniqueDevices = [];
        $seenHWIDs = [];
        foreach ($jsonData['excelData'] as $device) {
            if (isset($device['HWID']) && !in_array($device['HWID'], $seenHWIDs)) {
                $seenHWIDs[] = $device['HWID'];
                $uniqueDevices[] = $device;
            }
        }

        // Lấy danh sách HWID sau khi lọc trùng
        $list_serial_number = array_column($uniqueDevices, 'HWID');

        // Số lượng HWID duy nhất
        $countUniqueHWIDs = count($list_serial_number);
        if($countUniqueHWIDs >= 110){
            return $this->responseCustom(422, "Không thể xử lý quá 110 thiết bị!");
        }

        // Truy vấn danh sách serial_number đã tồn tại trong DB
        $model = model(OutputDeviceModel::class);
        $existingSerials = $model->select("serial_number")
            ->whereIn("serial_number", $list_serial_number)
            ->get()
            ->getResultArray();

        // Chuyển danh sách serial_number đã tồn tại thành mảng 1 chiều
        $existingSerialNumbers = array_column($existingSerials, 'serial_number');

        // Phân loại danh sách HWID thành hai nhóm: đã tồn tại và chưa tồn tại
        $existingDevices = [];
        $newDevices = [];
        $newDevicesActive = [];
        $newDevicesDisable = [];
        $dataChangeSNSim = [];

        foreach ($uniqueDevices as $device) {
            if (in_array($device['HWID'], $existingSerialNumbers)) {
                $existingDevices[] = $device; // Đã tồn tại
            } else {
                $newDevices[] = $device; // Chưa tồn tại
            }
        }

        // Check active thiết bị
        if (!empty($newDevices)) {
            foreach ($newDevices as $device) {
                $status_devices = $this->checkDeviceInfo($device['HWID']);
                if ($status_devices['status_code'] == 200) {
                    $device['serial_sim'] = $status_devices['serial_sim'];
                    // check seri api và excel
                    if(!empty($device['serial_sim'] && $device['SIM SN'] != $status_devices['serial_sim'])){
                        $dataChangeSNSim[] = [
                            'serial_number' => $device['HWID'],
                            'serial_sim_api' => $device['serial_sim'],
                            'serial_sim_excel' => $device['SIM SN'],
                        ];
                    }else{

                        $newDevicesActive[] = $device;
                    }
                    
                } else {
                    $newDevicesDisable[] = $device;
                }
            }
        }


        // insert DB using insertBatch
        if (!empty($newDevicesActive)) {
            $data_insert = [];
            foreach ($newDevicesActive as $device) {
               
                $data_insert[] = [
                    'name' => "SEP",
                    'serial_number' => $device['HWID'],
                    'model' => "S003",
                    'vendor' => 'Flametechvn',
                    'serial_sim' => $device['serial_sim'] ?? $device['SIM SN'],
                    'phone_serial_sim' => $device['SĐT'],
                    'bank_id' => $device['BANKID']?? null,

                ];
            }
           
            $model->insertBatch($data_insert);
        }

        return $this->responseCustom(200, "Danh sách phân loại!", [
            'countNew' => count($newDevices), // Số lượng thiết bị mới
            'countExisting' => count($existingDevices), // Số lượng thiết bị đã tồn tại
            'countNewActive' => count($newDevicesActive),   // Số lượng thiết bị mới active
            'countNewDisable' => count($newDevicesDisable), // Số lượng thiết bị mới không active
            'countDuplicates' => count($duplicateHWIDs), // Số lượng HWID trùng
            'countChangeSNSim' => count($dataChangeSNSim), // Danh sách thiết bị không khớp SN SIM API và Excel
            'countNoBankID' => count($dataNoBankID), // Danh sách thiết bị không có BANKID

            'existingData' => $existingDevices, // Danh sách thiết bị đã tồn tại
            'newData' => $newDevices, // Danh sách thiết bị mới
            'newDataActive' => $newDevicesActive, // Danh sách thiết bị mới active
            'newDataDisable' => $newDevicesDisable, // Danh sách thiết bị mới không active
            'duplicateHWIDs' => $duplicateHWIDs, // Danh sách HWID bị trùng và số lần xuất hiện
            'duplicateHWIDs' => $duplicateHWIDs, // Danh sách HWID bị trùng và số lần xuất hiện
            'dataChangeSNSim' => $dataChangeSNSim, // Danh sách thiết bị không khớp SN SIM API và Excel
            'dataNoBankID' => $dataNoBankID, // Danh sách thiết bị không có BANKID
        ]);
    }

    public function checkConnection()
    {
        $vendor = $this->request->getPost('vendor');
        $type = $this->request->getPost('type');

        if (!in_array($type, ['0', '1', '2'])) {
            return $this->responseCustom(422, "Type không hỗ trợ");
        }

        if ($vendor != "Flametechvn") {
            return $this->responseCustom(422, "Vendor không hỗ trợ");
        }

        $condition = ['vendor' => $vendor];
        if ($type == '0') {
            $condition['company_id'] = null;
        } elseif ($type == '1') {
            $condition['company_id !='] = null;
        }

        $list_device = model(OutputDeviceModel::class)
            ->select('serial_number, model, serial_sim, phone_serial_sim')
            ->where($condition)
            ->get()
            ->getResultArray();

        if (empty($list_device)) {
            return $this->responseCustom(422, "Không có thiết bị nào trong hệ thống");
        }

        // Kiểm tra nếu có quá 100 thiết bị thì báo lỗi
        if (count($list_device) >= 110) {
            return $this->responseCustom(422, "Quá nhiều thiết bị, chỉ hỗ trợ kiểm tra tối đa 100 thiết bị");
        }

        $data_active = [];
        $data_disable = [];

        foreach ($list_device as $device) {
            $status_devices = $this->checkDeviceInfo($device['serial_number']);
            if ($status_devices['status_code'] == 200) {
                $data_active[] = $device;
            } else {
                $data_disable[] = $device;
            }
        }

        return $this->responseCustom(200, "OK", [
            'total_device' => count($list_device),
            'data_active' => $data_active,
            'data_disable' => $data_disable,
            'count_active' => count($data_active),
            'count_disable' => count($data_disable)
        ]);
    }
    
    public function checkOnlineDevice($serial_number)
    {
        $status_devices = $this->checkDeviceInfo($serial_number);
        if ($status_devices['status_code'] == 200) {
            if(!empty($status_devices['online'])){
                return $this->responseCustom(200, "Thiết bị đang online", ['data' => $status_devices]);
            }else{
                return $this->responseCustom(201, "Thiết bị đang offline", ['data' => $status_devices]);
            }
        } else {
            return $this->responseCustom(202, "Lỗi check trạng thái Flametechvn", ['error' => $status_devices]);
        }
    }
}
