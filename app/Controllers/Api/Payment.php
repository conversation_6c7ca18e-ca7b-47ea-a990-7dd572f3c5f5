<?php

namespace App\Controllers\Api;

use App\Actions\BankAccount\DisableAllBankAccountAction;
use App\Actions\BankAccount\EnableAllBankAccountAction;
use App\Models\ReferralCodeModel;
use App\Models\CompanyModel;
use App\Models\InvoiceModel;
use App\Models\PhysicalOrderHistoryModel;
use App\Models\PhysicalOrderModel;
use App\Models\StransactionModel;
use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;
use Exception;

class Payment extends ResourceController
{
    use ResponseTrait;
 
    public function add() {

        helper('general');

        $billingConfig = config(\App\Config\Billing::class);
        $request = \Config\Services::request();

        $client_ip = $request->getIPAddress();

        if(!in_array($client_ip, ['*************', '**************']))
            return $this->failNotFound();
        
        if ($this->request->getMethod(true) != 'POST')
            return $this->failNotFound();

        $stransactionModel = model(StransactionModel::class);
        $invoiceModel = model(InvoiceModel::class);
        $orderModel = model(OrderModel::class);
        $companySubscriptionModel = model(CompanySubscriptionModel::class);

        $validation =  \Config\Services::validation();
 
        if(! $this->validate(
            [
                'gateway'=>'required',
                'transactionDate'=>'required',
                'accountNumber'=>'required',
                'transferAmount' => 'required|integer',
                'code'=>'required',
                'content'=>'required',
                'transferType' =>'required|in_list[in,out]',
                'description'=>'required',
            ]
        ))
            return $this->failValidationError(implode(". ", $validation->getErrors()));

        $physicalOrderModel = model(PhysicalOrderModel::class);

        $physicalOrder = $physicalOrderModel
            ->where('payment_method', 'bank_transfer')
            ->where('payment_status', 'Unpaid')
            ->where('total_amount', $this->request->getVar('transferAmount'))
            ->where('order_code', $this->request->getVar('code'))
            ->first();

        if ($physicalOrder) {
            $invoice = model(InvoiceModel::class)
                ->select('tb_autopay_invoice.id, tb_autopay_invoice.physical_invoice_id')
                ->join('tb_autopay_physical_invoices', 'tb_autopay_physical_invoices.id = tb_autopay_invoice.physical_invoice_id')
                ->where('tb_autopay_physical_invoices.order_id', $physicalOrder->id)
                ->first();

            $db = db_connect();
            $db->transBegin();

            try {
                $physicalOrderModel->update($physicalOrder->id, ['payment_status' => 'Paid']);

                model(InvoiceModel::class)->update($invoice->id, ['status' => 'Paid']);

                model(PhysicalOrderHistoryModel::class)->insert([
                    'order_id' => $physicalOrder->id,
                    'status' => $physicalOrder->status,
                    'payment_status' => 'Paid',
                    'note' => 'Đã nhận thanh toán qua chuyển khoản ngân hàng',
                ]);

                model(StransactionModel::class)->insert([
                    'sms_parsed_id' => $this->request->getVar('id'),
                    'invoice_id' => $invoice->id,
                    'payment_method' => 'bank_transfer',
                    'date' => $this->request->getVar('transactionDate'),
                    'description' => $this->request->getVar('content'),
                    'in' => $this->request->getVar('transferAmount'),
                    'trans_id' => $this->request->getVar('referenceCode'),
                    'type' => null,
                ]);

                $db->transCommit();

                return $this->respondCreated([
                    'status' => 201,
                    'error' => null,
                    'success' => true,
                    'messages' => [
                        'success' => "Đã thanh toán hóa đơn #{$invoice->id} cho đơn hàng #{$physicalOrder->id}",
                    ]
                ]);
            } catch (Exception $e) {
                $db->transRollback();

                log_message('error', 'Order payment error: ' . $e->getMessage());
                
                return $this->failServerError();
            }
        }

        $gateway = $this->request->getVar('gateway');
        $isCredit = $gateway === 'Credit';

        // Kiểm tra có hóa đơn tương ứng với code không
        $data_invoice = [
            "code" => $this->request->getVar('code'),
            "total" => $this->request->getVar('transferAmount'),
            "type" => $this->request->getVar('transferType'),
            "description" => $this->request->getVar('description'),
        ];
 
        if($data_invoice['type'] != 'in')
            return $this->fail("Transfer type must be in", 400);


        $invoice_id = str_replace("SEP", "",$data_invoice['code']);
        $invoice_id = intval($invoice_id);
        

        if(!is_numeric($invoice_id) || $invoice_id == 0)
            return $this->fail("Cannot find invoice id with code " . $data_invoice['code'], 400);

        $invoice_details = $invoiceModel->where(['id' => $invoice_id])->get()->getRow();

        if(!is_object($invoice_details))
            return $this->fail("Cannot find invoice id with id " . $invoice_id, 400);

        $company_id = $invoice_details->company_id;

        if($invoice_details->status != "Unpaid")
            return $this->fail("Invoice status needs to be unpaid", 400);
        

        if($invoice_details->total != $data_invoice['total'] && !$isCredit)
            return $this->fail("Invoice amount is different from the transfer amount", 400);

        // Thêm transaction
        $data_transaction = [
            "invoice_id" => $invoice_id,
            "company_id" => $company_id,
            "date" => $this->request->getVar('transactionDate'),
            "description" => $this->request->getVar('description'),
            "in" => $this->request->getVar('transferAmount'),
            "type" => $this->request->getVar('type'),
        ];

        if (!$isCredit) {
            $transaction_id =  $stransactionModel->insert($data_transaction);

            if(!is_numeric($transaction_id)) {
                return $this->fail("Cannot insert stransaction", 400);
            }
        }
        // Cập nhật trạng thái hóa đơn

        $updateInvoiceData = [
            'datepaid' => $data_transaction['date']
        ];

        if ($isCredit) {
            $updateInvoiceData['credit'] = $invoice_details->credit + $data_transaction['in'];
            $updateInvoiceData['total'] = $invoice_details->total - $data_transaction['in'];
        }

        if ($invoice_details->total == $data_invoice['total']) {
            $updateInvoiceData['status'] = 'Paid';
            $updateInvoiceData['last_update_status_at'] = date('Y-m-d H:i:s');
        }

        $invoiceModel->set($updateInvoiceData)->where(['id' => $invoice_id])->update();

        if ($isCredit && !isset($updateInvoiceData['status'])) {
            return $this->respondCreated([
                'status' => 201,
                'error' => null,
                'success' => true,
                'messages' => [
                    'success' => 'Data Saved: ' . 'Apply credit to invoice #' . $invoice_id
                ]
            ]);
        }

        $message = "Invoice ID: " . $invoice_id;

        // Kiểm tra xem company có hóa đơn nào chưa thanh toán không
        $unpaidInvoices = $invoiceModel
            ->where('company_id', $company_id)
            ->where('status', 'Unpaid')
            ->countAllResults();

        $companyModel = model(CompanyModel::class);
        $companyDetails = $companyModel->where('id', $company_id)->first();

        // Nếu "company đang bị suspend và đã thanh toán hết các hóa đơn" HOẶC "company đang ở trạng thái pending" thì set Active
        if (
            ($companyDetails->status == 'Suspended' && $unpaidInvoices == 0)
            || $companyDetails->status === 'Pending'
        ) {
            $companyModel
                ->set('status', 'Active')
                ->where('id', $company_id)
                ->update();

            if ($companyDetails->status == 'Suspended') {
                $companyModel->sendStatusChangeNotification($company_id, 'Active');
            }

            add_admin_log([
                'data_id' => $company_id,
                'data_type' => 'automation',
                'description' => 'Sửa status company sang Active',
                'admin_id' => 0,
                'ip' => $this->request->getIPAddress(),
                'user_agent' => $this->request->getUserAgent()->getAgentString(),
                'status' => 'Success',
            ]);
        }

        $subscription_details = $companySubscriptionModel->where(["company_id" => $invoice_details->company_id])->get()->getRow();

        // Do đang là gói Free hoặc từ gói Free chuyển sang gói khác nên không có order
        if ($subscription_details->status == 'Suspended' && in_array($invoice_details->type, ['Recurring', 'Excess', 'SubscriptionChange'])) {
            $companySubscriptionModel
                ->set('status', 'Active')
                ->where('id', $subscription_details->id)
                ->update();
        }

        // Nếu hóa đơn liên quan đến order: cập nhật trạng thái order
        $order_details = $orderModel->where(['invoice_id' => $invoice_id, "company_id" => $invoice_details->company_id])->get()->getRow();
        log_message('error', 'billing order:' . json_encode($order_details));

        model(ReferralCodeModel::class)->applyPaidReferral($company_id, $data_invoice['total']);

        if(is_object($order_details)) {
            $message = $message . " Order ID: " . $order_details->id;

            // Nếu order liên quan đến subscription: cập nhật trạng thái subscription
            $companySubscriptionModel = model(CompanySubscriptionModel::class);
            $companySubscriptionChangeModel = model(CompanySubscriptionChangeModel::class);

            $subscription_details = $companySubscriptionModel->where(["company_id" => $invoice_details->company_id])->get()->getRow();
            $subscriptionChangeDetails = $companySubscriptionChangeModel
                ->select('tb_autopay_company_subscription_change.*, tb_autopay_product_promotions.id as promotion_id')
                ->join('tb_autopay_product_promotions', 'tb_autopay_product_promotions.product_id = tb_autopay_company_subscription_change.plan_id', 'left')
                ->where(['tb_autopay_company_subscription_change.order_id' => $order_details->id,"tb_autopay_company_subscription_change.company_id" => $invoice_details->company_id])
                ->first();

            log_message('error', 'billing subscription:' . json_encode($subscription_details));
            log_message('error', 'billing subscription change:' . json_encode($subscriptionChangeDetails));

            if (is_object($subscription_details) && is_object($subscriptionChangeDetails)) {
                log_message('error', 'billing subscript change flow');
                $companySubscriptionModel->update($subscription_details->id, [
                    'status' => 'Active',
                    'order_id' => $order_details->id,
                    'plan_id' => $subscriptionChangeDetails->plan_id,
                    'begin_date' => $subscriptionChangeDetails->begin_date,
                    'end_date' => $subscriptionChangeDetails->end_date,
                    'first_payment' => $subscriptionChangeDetails->first_payment,
                    'recurring_payment' => $subscriptionChangeDetails->recurring_payment,
                    'billing_cycle' => $subscriptionChangeDetails->billing_cycle,
                    'monthly_transaction_limit' => $subscriptionChangeDetails->monthly_transaction_limit,
                    'bank_account_limit' => $subscriptionChangeDetails->bank_account_limit,
                    'telegram_intergration_limit' => $subscriptionChangeDetails->telegram_intergration_limit,
                    'webhook_intergration_limit' => $subscriptionChangeDetails->webhook_intergration_limit,
                    'monthly_telegram_send_limit' => $subscriptionChangeDetails->monthly_telegram_send_limit,
                    'monthly_webhooks_send_limit' => $subscriptionChangeDetails->monthly_webhooks_send_limit,
                    'shop_limit' => $subscriptionChangeDetails->shop_limit,
                    'is_trial' => 0,
                    'auto_renew' => 1,
                    'allow_exceed_limit' => $subscriptionChangeDetails->allow_exceed_limit,
                ]);
                $companySubscriptionChangeModel->where(['id' => $subscriptionChangeDetails->id])->delete();
                $orderModel->set(['status' => 'Active'])->where(['id' => $order_details->id])->update();

                if ($subscriptionChangeDetails->promotion_id) {
                    $promotionHistory = db_connect()
                        ->table('tb_autopay_company_promotion_history')
                        ->where('company_id', $companyDetails->id)
                        ->where('promotion_id', $subscriptionChangeDetails->promotion_id)
                        ->get()
                        ->getRow();
                }

                if ($subscriptionChangeDetails->promotion_id && ($promotionHistory && $subscription_details->plan_id != $promotionHistory->product_id)) {
                    DisableAllBankAccountAction::run($companyDetails->id);
                } else {
                    EnableAllBankAccountAction::run($companyDetails->id);
                }

                $productModel = model(ProductModel::class);
                $productDetails = $productModel->where(['id' => $subscriptionChangeDetails->plan_id])->get()->getRow();

                $simModel = model(SimModel::class);
                $simCompanyModel = model(SimCompanyModel::class);

                $userModel = model(\App\Models\UserModel::class);
                $userDetails = $userModel->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id=tb_autopay_user.id')->where(['tb_autopay_company_user.company_id' => $companyDetails->id, 'role' => 'SuperAdmin'])->get()->getRow();

                if(is_object($productDetails) && $productDetails->dedicated_sim == 1) {
                    if ($billingConfig->enabledAutoSimConfigForSubscriptionChange) {
                        $sims = $simModel->select("tb_autopay_sim.id ,tb_autopay_sim.sim_phonenumber, tb_autopay_sim.description")->join("tb_autopay_sim_company","tb_autopay_sim_company.sim_id=tb_autopay_sim.id","left")->where("tb_autopay_sim_company.sim_id IS NULL AND tb_autopay_sim.is_shared=0 AND tb_autopay_sim.active=1")->get()->getResult();

                        $result = $simCompanyModel->where(['company_id' => $invoice_details->company_id])->get()->getRow();

                        if(!is_object($result) && isset($sims[0]) && is_object($sims[0])) {
                            $simCompanyModel->insert([
                                'company_id' => $company_id,
                                'sim_id' => $sims[0]->id
                            ]);

                            $message = $message . " Dedicated SIM ID: " . $sims[0]->id;
                        } else if (!is_object($result) && count($sims) == 0) {
                            notify_sim_config('assign', $userDetails, $companyDetails, $productDetails->name, $subscriptionChangeDetails->billing_cycle);
                        }
                    } else {
                        notify_sim_config('assign', $userDetails, $companyDetails, $productDetails->name, $subscriptionChangeDetails->billing_cycle);
                    }
                } else if (is_object($productDetails) && $productDetails->dedicated_sim != 1) {
                    if ($billingConfig->enabledAutoSimConfigForSubscriptionChange) {
                        $simCompanyModel->where(['company_id' => $invoice_details->company_id])->delete();
                    } else {
                        notify_sim_config('unassign', $userDetails, $companyDetails, $productDetails->name, $subscriptionChangeDetails->billing_cycle);
                    }
                }

                add_system_log(['company_id' => $companyDetails->id, 'data_type' => 'change_subscription_by_user', 'description' => "Change subscription by user from plan #{$subscription_details->plan_id} to #{$subscriptionChangeDetails->plan_id}",'level' => 'Info', 'by' => 'Billing Controller']);    
            } else if(is_object($subscription_details) && !is_object($subscriptionChangeDetails)) {
                log_message('error', 'billing subscript new register flow');
                $companySubscriptionModel->set(['status' => 'Active'])->where(['id' => $subscription_details->id])->update();
                $orderModel->set(['status' => 'Active'])->where(['id' => $order_details->id])->update();

                $message = $message . " Subscription ID: " . $subscription_details->id;

                // dedicated sim
                // chỉ cấp sim nếu subscription_details chưa active
                $simModel = model(SimModel::class);
                $simCompanyModel = model(SimCompanyModel::class);

                $productModel = model(ProductModel::class);
                $product_details = $productModel->where(['id' => $subscription_details->plan_id])->get()->getRow();

                if(is_object($product_details) && $product_details->dedicated_sim == 1 && $subscription_details->status == 'Pending') {
                    $sims = $simModel->select("tb_autopay_sim.id ,tb_autopay_sim.sim_phonenumber, tb_autopay_sim.description")->join("tb_autopay_sim_company","tb_autopay_sim_company.sim_id=tb_autopay_sim.id","left")->where("tb_autopay_sim_company.sim_id IS NULL AND tb_autopay_sim.is_shared=0 AND tb_autopay_sim.active=1")->get()->getResult();

                    // Nếu đã cấp sim rồi (ví dụ cấp dùng thử) thì không cấp nữa
                    $result = $simCompanyModel->where(['company_id' => $invoice_details->company_id])->get()->getRow();

                    if(!is_object($result) && isset($sims[0]) && is_object($sims[0])) {
                        $simCompanyModel->insert([
                            'company_id' => $company_id,
                            'sim_id' => $sims[0]->id
                        ]);

                        $message = $message . " Dedicated SIM ID: " . $sims[0]->id;

                    }
                }

            }
        }
        
        $response = [
            'status'   => 201,
            'error'    => null,
            'success' => TRUE,
            'messages' => [
                'success' => 'Data Saved ' . $message
            ]
        ];

        if (!$isCredit) {
            $stransactionModel->sendEmailPayment($transaction_id);
        }
         
        return $this->respondCreated($response);

       
    }

}