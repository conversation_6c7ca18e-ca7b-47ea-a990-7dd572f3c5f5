<?php

namespace App\Enums;

class OrderStatus
{
    const PENDING = 'Pending';

    const CONFIRMED = 'Confirmed';

    const PROCESSING = 'Processing';

    const WAITING_SHIPPER = 'WaitingShipper';

    const SHIPPING = 'Shipping';

    const COMPLETED = 'Completed';

    const ON_HOLD = 'OnHold';

    const RETURNED = 'Returned';

    const PARTNER = 'Partner';

    const CANCELLED = 'Cancelled';

    public static function toArray(): array
    {
        return [
            self::PENDING,
            self::CONFIRMED,
            self::PROCESSING,
            self::WAITING_SHIPPER,
            self::SHIPPING,
            self::COMPLETED,
            self::ON_HOLD,
            self::RETURNED,
            self::PARTNER,
            self::CANCELLED,
        ];
    }

    public static function getLabels(): array
    {
        return [
            self::PENDING => 'Chờ xác nhận',
            self::CONFIRMED => 'Đã xác nhận',
            self::PROCESSING => 'Đang xử lý',
            self::WAITING_SHIPPER => 'Chờ lấy hàng',
            self::SHIPPING => 'Đang giao hàng',
            self::COMPLETED => 'Hoàn thành',
            self::ON_HOLD => 'Tạm giữ',
            self::RETURNED => 'Khách trả hàng',
            self::PARTNER => 'Đơn đối tác',
            self::CANCELLED => 'Đã hủy',
        ];
    }

    public static function getLabel(string $status): string
    {
        return self::getLabels()[$status] ?? 'Không xác định';
    }

    public static function toHtml(string $status): string
    {
        switch ($status) {
            case self::PENDING:
                return '<span class="badge bg-warning me-2">Chờ xác nhận</span>';
            case self::CONFIRMED:
                return '<span class="badge bg-warning me-2">Đã xác nhận</span>';
            case self::PROCESSING:
                return '<span class="badge bg-success me-2">Đang xử lý</span>';
            case self::WAITING_SHIPPER:
                return '<span class="badge bg-success me-2">Chờ lấy hàng</span>';
            case self::SHIPPING:
                return '<span class="badge bg-info me-2">Đang giao hàng</span>';
            case self::COMPLETED:
                return '<span class="badge bg-success me-2">Hoàn thành</span>';
            case self::CANCELLED:
                return '<span class="badge bg-danger me-2">Đã hủy</span>';
            case self::ON_HOLD:
                return '<span class="badge bg-secondary me-2">Tạm giữ</span>';
            case self::RETURNED:
                return '<span class="badge bg-danger me-2">Khách trả hàng</span>';
            case self::PARTNER:
                return '<span class="badge bg-secondary me-2">Đơn đối tác</span>';
            default:
                return '<span class="badge bg-secondary me-2">Không xác định</span>';
        }
    }
}
