<?php
namespace App\Validation;
use App\Libraries\Recaptcha; // Import library

class CustomRules{

    public function valid_domain_name(string $domain,  string &$error = null) {
        
        $regex_string = "/^((?:(?:(?:\w[\.\-\+]?)*)\w)+)((?:(?:(?:\w[\.\-\+]?){0,62})\w)+)\.(\w{2,6})$/";
        if (!preg_match($regex_string, idn_to_ascii($domain)))
            return FALSE;
        else
            return TRUE;
    }
 

    public function valid_account_holder_name(string $account_holder_name,  string &$error = null) {
        $regex_string = "/^[0-9a-zA-Z\s_-àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]+$/";

        if (!preg_match($regex_string, $account_holder_name))
            return FALSE;
        else
            return TRUE;
    }

    public function valid_recaptcha(string $captcha,  string &$error = null) {
        
        $recaptcha = new Recaptcha();
 
        $r_response = $recaptcha->is_valid();
        
        if(isset($r_response['success']) && $r_response['success'] === true) {
            if($r_response['score'] >= 0.5)
                return TRUE;
            else {
                $error = 'Lỗi xác minh trình duyệt. Vui lòng thử lại sau vài giây.';
                return FALSE;
            }
        } else {
            $error = 'Lỗi xác minh trình duyệt. Vui lòng thử lại sau vài giây.';
            return FALSE;

        }
    }

    public function valid_password(string $password, string &$error = null)
	{
		$password = trim($password);
		$regex_lowercase = '/[a-z]/';
		$regex_uppercase = '/[A-Z]/';
		$regex_number = '/[0-9]/';
		$regex_special = '/[!@#$%^&*()\-_=+{};:,<.>§~]/';
		
		if (preg_match_all($regex_lowercase, $password) < 1)
		{
			$error =  'Mật khẩu mới phải có ít nhất 1 ký tự không in hoa.';
			return FALSE;
		}
		if (preg_match_all($regex_uppercase, $password) < 1)
		{
			$error =  'Mật khẩu mới phải có ít nhất 1 ký tự IN HOA.';
			return FALSE;
		}
		if (preg_match_all($regex_number, $password) < 1)
		{
			$error =   'Mật khẩu mới phải có ít nhất 1 số.';
			return FALSE;
		}
		if (preg_match_all($regex_special, $password) < 1)
		{
			$error =  'Mật khẩu mới phải có ít nhất 1 ký tự đặc biệt thuộc các ký tự sau:' . ' ' . htmlentities('!@#$%^&*()\-_=+{};:,<.>§~');
			return FALSE;
		}
		
		return TRUE;
	}

    public function valid_webhooks_url(string $url,  string &$error = null) {
        $blacklist_ip = ['*************', '**************', '*************'];

        $domain = parse_url($url);

        if(!$domain) {
            $error =   'URL phải chứa một tên miền hợp lệ';
            return FALSE;
        }
            
        $ip = gethostbyname($domain['host']);

        if(! filter_var(
            $ip, 
            FILTER_VALIDATE_IP, 
            FILTER_FLAG_IPV4 | FILTER_FLAG_NO_PRIV_RANGE |  FILTER_FLAG_NO_RES_RANGE
        )) {
            $error =   'Tên miền của URL phải phân giải được DNS. Và không được phân giải về IP Private';
            return FALSE;
        }

        if(in_array($ip, $blacklist_ip)) {
            $error =   'Tên miền không hợp lệ. Vui lòng kiểm tra lại cấu hình DNS của tên miền.';
            return FALSE;
        } 

        return TRUE;
    }

}