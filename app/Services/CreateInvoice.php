<?php

namespace App\Services;

use App\Libraries\SInvoice;
use App\Models\AddonModel;
use App\Models\InvoiceCustomerInfoModel;
use App\Models\InvoiceItemModel;
use App\Models\InvoiceModel;
use App\Models\ProductModel;
use Config\SubscriptionSInvoice;
use App\Config\Google;
use App\Models\StransactionModel;
use Google\Client;
use Google\Service\Sheets;
use Google\Service\Sheets\ValueRange;
use Exception;
use Google\Service\Sheets\BatchUpdateSpreadsheetRequest;
use NumberFormatter;

class CreateInvoice
{
    protected SubscriptionSInvoice $config;

    protected SInvoice $sInvoice;

    public function __construct()
    {
        $this->config = config(SubscriptionSInvoice::class);
        $this->sInvoice = (new SInvoice())->setConfig($this->config);
    }

    public function createInvoice($invoiceId)
    {
        $invoiceModel = model(InvoiceModel::class);

        $invoice = $invoiceModel
            ->where('customer_info_id IS NOT NULL')
            ->where('tax_issued_id', '')
            ->where('status', 'Paid')
            ->where('total >', 0)
            ->find($invoiceId);

        if (! $invoice) {
            throw new Exception('Không tìm thấy hóa đơn');
        }

        try {
            $data = $this->prepareData($invoice);
            $response = $this->sInvoice->createInvoice($data, $this->config->useDraftInvoice);

            if ($this->config->pushToGoogleSheet ?? false) {
                $this->insertToGoogleSheet($invoice, $data);
            }

            if ($this->config->useDraftInvoice) {
                if (! empty($response['result'])) {
                    log_message('error', 'Create draft invoice failed: ' . $invoice->id . ' ' . json_encode($response));
                    throw new Exception($response['data']);
                }

                $status = 'draft';
            } else {
                if ($response['errorCode'] !== null || ! isset($response['result'])) {
                    log_message('error', 'Create invoice failed: ' . $invoice->id . ' ' . json_encode($response));

                    throw new Exception($response['data']);
                }

                $status = 'issued';
                $invoiceCode = $response['result']['invoiceNo'];
            }

            if (! $this->config->dryRun) {
                $invoiceModel->update($invoice->id, [
                    'tax_issued_id' => $status === 'issued' ? $invoiceCode : $invoice->tax_issued_id,
                    'tax_issued' => $status === 'issued',
                ]);
            }

            return [
                'invoiceId' => $invoiceId,
                'invoiceCode' => $invoiceCode,
            ];
        } catch (Exception $e) {
            throw $e;
        }
    }

    protected function insertToGoogleSheet($invoice, $data)
    {
        try {
            $config = config(Google::class);

            $client = new Client();
            $client->setApplicationName('Google Sheets API');
            $client->setScopes(Sheets::SPREADSHEETS);
            $client->setAuthConfig(ROOTPATH . $config->serviceAccountFile);
            $client->setAccessType('offline');

            $service = new Sheets($client);

            $sheetName = 'T' . date('n');

            $this->ensureSheetExists($service, $config->spreadSheetId, $sheetName);

            $this->ensureHeaderExists($service, $config->spreadSheetId, $sheetName);

            $customerInfo = model(InvoiceCustomerInfoModel::class)->find($invoice->customer_info_id);

            $nextSTT = $this->getNextSTT($service, $config->spreadSheetId, $sheetName);

            $content = $data['itemInfo'][0]['itemName'];
            $transaction = model(StransactionModel::class)
                ->select('tb_autopay_stransaction.description, tb_autopay_sms_parsed.gateway, tb_autopay_sms_parsed.account_number')
                ->where('invoice_id', $invoice->id)
                ->join('tb_autopay_sms_parsed', 'tb_autopay_sms_parsed.id = tb_autopay_stransaction.sms_parsed_id', 'left')
                ->first();

            $range = $sheetName . '!A:P';

            $body = new ValueRange([
                'values' => [
                    [
                        $nextSTT,
                        date('d/m/Y', strtotime($invoice->created_at)),
                        $transaction->description,
                        number_format($invoice->total, 0, '.', ''),
                        '',
                        '=HYPERLINK("https://ad.sepay.vn/invoice/details/' . $invoice->id . '"; "#' . $invoice->id . '")',
                        $customerInfo->company_name ?: $customerInfo->name,
                        $customerInfo->tax_code ?: '',
                        $customerInfo->address ?: '',
                        $customerInfo->email ? implode(';', json_decode($customerInfo->email)) : '',
                        $content,
                        '',
                        '',
                        '',
                        $transaction->account_number ? "$transaction->account_number - $transaction->gateway" : '',
                        '',
                    ],
                ],
            ]);

            $params = [
                'valueInputOption' => 'USER_ENTERED',
            ];

            $service->spreadsheets_values->append($config->spreadSheetId, $range, $body, $params);

            model(InvoiceModel::class)->update($invoice->id, [
                'is_pushed_to_google_sheets' => true,
            ]);

            log_message('info', 'Invoice ' . $invoice->id . ' inserted to Google Sheet successfully');
        } catch (Exception $e) {
            log_message('error', 'Failed to insert invoice to Google Sheet: ' . $e->getMessage());
        }
    }

    protected function ensureSheetExists($service, $spreadSheetId, $sheetName)
    {
        try {
            $spreadsheet = $service->spreadsheets->get($spreadSheetId);
            $sheets = $spreadsheet->getSheets();

            $sheetExists = false;
            foreach ($sheets as $sheet) {
                if ($sheet->getProperties()->getTitle() === $sheetName) {
                    $sheetExists = true;
                    break;
                }
            }

            if (! $sheetExists) {
                $requests = [
                    [
                        'addSheet' => [
                            'properties' => [
                                'title' => $sheetName,
                            ],
                        ],
                    ],
                ];

                $batchUpdateRequest = new \Google\Service\Sheets\BatchUpdateSpreadsheetRequest([
                    'requests' => $requests,
                ]);

                $service->spreadsheets->batchUpdate($spreadSheetId, $batchUpdateRequest);
            }
        } catch (Exception $e) {
            throw new Exception('Error creating sheet: ' . $e->getMessage());
        }
    }

    protected function ensureHeaderExists($service, $spreadSheetId, $sheetName)
    {
        $headerRange = $sheetName . '!A1:P1';
        $expectedHeaders = [
            'STT',
            'Ngày Tháng',
            'Nội dung chuyển khoản',
            'Số tiền vào',
            'Số tiền ra',
            'Hóa đơn hệ thống',
            'Tên công ty/ KH',
            'MST',
            'Địa chỉ',
            'Email',
            'Nội dung',
            'Hình thức',
            'Số hóa đơn xuất VAT',
            'Ngày xuất',
            'TK thụ hưởng',
            'Note',
        ];

        try {
            $headerResponse = $service->spreadsheets_values->get($spreadSheetId, $headerRange);
            $existingHeaders = $headerResponse->getValues();

            $headerExists = false;
            if (! empty($existingHeaders) && ! empty($existingHeaders[0])) {
                $headerExists = (count($existingHeaders[0]) === count($expectedHeaders) &&
                    $existingHeaders[0] === $expectedHeaders);
            }
        } catch (Exception $e) {
            $headerExists = false;
        }

        if (! $headerExists) {
            $headerBody = new ValueRange([
                'values' => [$expectedHeaders],
            ]);

            $headerParams = [
                'valueInputOption' => 'RAW',
            ];

            $service->spreadsheets_values->update($spreadSheetId, $headerRange, $headerBody, $headerParams);

            $requests = [
                [
                    'repeatCell' => [
                        'range' => [
                            'sheetId' => $this->getSheetId($service, $spreadSheetId, $sheetName),
                            'startColumnIndex' => 7,
                            'endColumnIndex' => 8,
                        ],
                        'cell' => [
                            'userEnteredFormat' => [
                                'numberFormat' => [
                                    'type' => 'TEXT',
                                ]
                            ]
                        ],
                        'fields' => 'userEnteredFormat.numberFormat',
                    ],
                ],
            ];

            $batchUpdateRequest = new BatchUpdateSpreadsheetRequest([
                'requests' => $requests,
            ]);

            $service->spreadsheets->batchUpdate($spreadSheetId, $batchUpdateRequest);
        }
    }

    protected function getSheetId(Sheets $service, $spreadSheetId, $sheetName)
    {
        $spreadsheet = $service->spreadsheets->get($spreadSheetId);
        $sheets = $spreadsheet->getSheets();

        foreach ($sheets as $sheet) {
            if ($sheet->getProperties()->getTitle() === $sheetName) {
                return $sheet->getProperties()->getSheetId();
            }
        }

        throw new Exception('Sheet not found: ' . $sheetName);
    }

    protected function getNextSTT(Sheets $service, $spreadSheetId, $sheetName)
    {
        try {
            $dataRange = $sheetName . '!A:A';
            $dataResponse = $service->spreadsheets_values->get($spreadSheetId, $dataRange);
            $existingData = $dataResponse->getValues();

            if (! empty($existingData)) {
                $nextSTT = count($existingData);

                if (! empty($existingData[0]) && $existingData[0][0] === 'STT') {
                    $nextSTT = count($existingData);
                } else {
                    $nextSTT = count($existingData) + 1;
                }
            } else {
                $nextSTT = 1;
            }

            return $nextSTT;
        } catch (Exception $e) {
            return 1;
        }
    }

    protected function prepareData($invoice): array
    {
        $customerInfo = model(InvoiceCustomerInfoModel::class)->find($invoice->customer_info_id);
        $invoiceItems = model(InvoiceItemModel::class)->where('invoice_id', $invoice->id)->findAll();

        $itemInfo = [];
        $lineNumber = 1;
        $discounts = [];

        foreach ($invoiceItems as $item) {
            if (in_array($item->type, ['Discount', 'SubscriptionDiff'])) {
                $discounts[$item->item_id] = abs($item->amount);
                continue;
            }

            $fromDate = null;
            $endDate = null;

            preg_match('/(\d{4}-\d{2}-\d{2}(?: \d{2}:\d{2}:\d{2})?) - (\d{4}-\d{2}-\d{2}(?: \d{2}:\d{2}:\d{2})?)/', $item->description, $matches);
            if (count($matches) === 3) {
                [, $fromDate, $endDate] = $matches;
            }

            $fromDate = $fromDate ? date('d/m/Y', strtotime($fromDate)) : null;
            $endDate = $endDate ? date('d/m/Y', strtotime($endDate)) : null;
            $unitByPerSubscription = $invoice->type === 'Excess';

            switch ($item->type) {
                case 'Product':
                    if ($unitByPerSubscription) {
                        if (preg_match('/SePay - (.+?) \(\d{4}-\d{2}-\d{2}/', $item->description, $matches)) {
                            $itemName = 'Phần mềm SePay gói ' . $matches[1];
                        } else {
                            $itemName = 'Phần mềm SePay gói ' . str_replace('SePay - ', '', $item->description);
                        }
                    } else {
                        $product = model(ProductModel::class)->find($item->item_id);
                        $itemName = "Phần mềm SePay gói {$product->name}";
                    }

                    if ($fromDate && $endDate) {
                        $itemName .= " ({$fromDate} - {$endDate})";
                    }

                    break;
                case 'Addon':
                    $addon = model(AddonModel::class)->find($item->item_id);
                    $itemName = "Phần mềm SePay gói mở rộng {$addon->name}";

                    if ($fromDate && $endDate) {
                        $itemName .= " ({$fromDate} - {$endDate})";
                    }

                    break;
                default:
                    $itemName = $item->description;
                    break;
            }

            $quantity = 1;

            if ($fromDate && $endDate) {
                $fromDate = date_create_from_format('d/m/Y', $fromDate);
                $endDate = date_create_from_format('d/m/Y', $endDate);

                $months = date_diff($fromDate, $endDate)->m;
                $quantity = $months + 1;
            }

            $itemInfo[] = [
                'lineNumber' => $lineNumber++,
                'itemCode' => $item->item_id,
                'itemName' => $itemName,
                'unitName' => $unitByPerSubscription ? 'Gói' : 'Tháng',
                'unitPrice' => $unitByPerSubscription ? round($item->amount, 0) : round($item->amount / $quantity, 0),
                'quantity' => $unitByPerSubscription ? 1 : $quantity,
                'itemTotalAmountWithoutTax' => $item->amount,
                'discount' => 0.0,
            ];
        }

        foreach ($itemInfo as &$item) {
            if (isset($discounts[$item['itemCode']])) {
                $discount = $discounts[$item['itemCode']];
                $originalAmount = $item['itemTotalAmountWithoutTax'];

                $item['discount'] = ($originalAmount > 0) ? ($discount > 0 ? round($discount / $originalAmount * 100, 2) : 0.0) : 0.0;
            }
        }

        unset($item);

        return [
            'generalInvoiceInfo' => [
                'paymentStatus' => true,
                'invoiceIssuedDate' => strtotime(date('Y-m-d H:i:s')) * 1000,
            ],
            'buyerInfo' => [
                'buyerName' => $customerInfo->company_name ? null : $customerInfo->name,
                'buyerLegalName' => $customerInfo->company_name ?: $customerInfo->name,
                'buyerTaxCode' => $customerInfo->tax_code,
                'buyerAddressLine' => empty($customerInfo->address) ? $this->randomAddress() : $customerInfo->address,
                'buyerPhoneNumber' => $customerInfo->phone,
                'buyerEmail' => $customerInfo->email ? implode(';', json_decode($customerInfo->email)) : null,
            ],
            'itemInfo' => $itemInfo,
            'summarizeInfo' => [
                'sumOfTotalLineAmountWithoutTax' => $invoice->subtotal,
                'totalAmountWithoutTax' => $invoice->subtotal,
                'totalTaxAmount' => $invoice->tax,
                'totalAmountWithTax' => $invoice->total,
                'totalAmountWithTaxInWords' => $this->numberToWords($invoice->total),
                'taxPercentage' => $invoice->tax_rate,
            ],
            'taxBreakdowns' => [
                [
                    'taxPercentage' => -2,
                ],
            ],
        ];
    }

    protected function numberToWords($number)
    {
        return (new NumberFormatter('vi', NumberFormatter::SPELLOUT))->format($number);
    }

    protected function randomAddress()
    {
        $randomAddresses = [
            'Số 12 Nguyễn Huệ, Phường Bến Nghé, Quận 1, TP. Hồ Chí Minh',
            '45 Tràng Tiền, Phường Tràng Tiền, Quận Hoàn Kiếm, Hà Nội',
            '236 Lê Duẩn, Quận Thanh Khê, TP. Đà Nẵng',
            '10 Phạm Văn Đồng, Quận Sơn Trà, TP. Đà Nẵng',
            '79 Duy Tân, Quận Cầu Giấy, Hà Nội',
            '180 Lý Chính Thắng, Quận 3, TP. Hồ Chí Minh',
            '1 Nguyễn Tất Thành, TP. Quy Nhơn, Bình Định',
            '25 Nguyễn Văn Cừ, TP. Vinh, Nghệ An',
            '91 Trần Phú, TP. Nha Trang, Khánh Hòa',
            '100 Nguyễn Trãi, Quận 5, TP. Hồ Chí Minh',
        ];

        return $randomAddresses[array_rand($randomAddresses)];
    }
}
