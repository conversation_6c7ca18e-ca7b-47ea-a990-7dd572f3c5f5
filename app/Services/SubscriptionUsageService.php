<?php

namespace App\Services;

use App\Models\CompanySubscriptionModel;
use App\Models\CounterModel;
use App\Models\EmailLogModel;
use App\Models\ProductModel;
use App\Models\UserModel;
use CodeIgniter\CLI\CLI;
use Config\Services;
use Config\Subscription;

class SubscriptionUsageService
{
    protected $config;

    public function __construct()
    {
        $this->config = config(Subscription::class);

        helper('general');
    }

    public function checkSubscriptionUsage($subscription, $isExecute = false)
    {
        if (
            $subscription->last_notified_at
            && (time() - strtotime($subscription->last_notified_at) < $this->config->notificationInterval)
        ) {
            return [
                'status' => 'skipped',
                'message' => 'Đã thông báo trong ' . ($this->config->notificationInterval / 3600) . ' giờ qua, bỏ qua.',
            ];
        }

        $counterModel = model(CounterModel::class);
        $usageCount = $counterModel->count_transaction_by_interval(
            $subscription->company_id,
            $subscription->begin_date,
            $subscription->end_date
        ) ?: 0;

        if (! $subscription->monthly_transaction_limit || $subscription->monthly_transaction_limit <= 0) {
            return [
                'status' => 'skipped',
                'message' => 'Gói dịch vụ không có giới hạn giao dịch.',
            ];
        }

        $billingCycleMonths = billing_cycle_to_month($subscription->billing_cycle);
        $totalTransactionLimit = $subscription->monthly_transaction_limit * $billingCycleMonths;

        $usagePercentage = ($usageCount / $totalTransactionLimit) * 100;
        $formattedPercentage = number_format($usagePercentage, 1);

        $usageInfo = [
            'usage' => $usageCount,
            'limit' => $totalTransactionLimit,
            'percentage' => $usagePercentage,
        ];

        if ($usagePercentage >= 100) {
            $result = $this->sendNotification($subscription, 'exceeded_limit', $usageCount, $usagePercentage, $isExecute);
            return array_merge($usageInfo, [
                'status' => $result ? 'notified' : 'failed',
                'message' => $result ?
                    ($isExecute ? "Đã gửi thông báo vượt giới hạn ({$formattedPercentage}%)" : "[DRY RUN] Sẽ gửi thông báo vượt giới hạn ({$formattedPercentage}%)") :
                    "Không thể gửi thông báo vượt giới hạn ({$formattedPercentage}%)",
                'alert_type' => 'exceeded',
            ]);
        } elseif ($usagePercentage >= $this->config->nearLimitThreshold && $usagePercentage < 100) {
            $result = $this->sendNotification($subscription, 'near_limit', $usageCount, $usagePercentage, $isExecute);
            return array_merge($usageInfo, [
                'status' => $result ? 'notified' : 'failed',
                'message' => $result ?
                    ($isExecute ? "Đã gửi thông báo sắp đạt giới hạn ({$formattedPercentage}%)" : "[DRY RUN] Sẽ gửi thông báo sắp đạt giới hạn ({$formattedPercentage}%)") :
                    "Không thể gửi thông báo sắp đạt giới hạn ({$formattedPercentage}%)",
                'alert_type' => 'near',
            ]);
        }

        return array_merge($usageInfo, [
            'status' => 'skipped',
            'message' => "Sử dụng bình thường ({$formattedPercentage}%)",
        ]);
    }

    protected function sendNotification($subscription, string $type, int $usageCount, float $usagePercentage, bool $isExecute = false): bool
    {
        $user = model(UserModel::class)
            ->select('tb_autopay_user.email, tb_autopay_user.firstname, tb_autopay_user.lastname')
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id=tb_autopay_user.id')
            ->where('tb_autopay_company_user.company_id', $subscription->company_id)
            ->where('tb_autopay_company_user.role', 'SuperAdmin')
            ->first();

        if (! $user) {
            CLI::write("Không tìm thấy người dùng SuperAdmin cho company_id: {$subscription->company_id}", 'yellow');
            return false;
        }

        $product = model(ProductModel::class)->find($subscription->plan_id);

        if (! $product) {
            CLI::write("Không tìm thấy thông tin gói dịch vụ: {$subscription->plan_id}", 'yellow');
            return false;
        }

        $clientName = $user->lastname . ' ' . $user->firstname;
        $formattedPercentage = number_format($usagePercentage, 1);

        switch ($subscription->billing_cycle) {
            case 'monthly':
                $billingCycleText = 'tháng';
                break;
            case 'quarterly':
                $billingCycleText = 'quý';
                break;
            case 'semi-annually':
                $billingCycleText = '6 tháng';
                break;
            case 'annually':
                $billingCycleText = 'năm';
                break;
        }

        $totalTransactionLimit = $subscription->monthly_transaction_limit * get_month_by_billing_cycle($subscription->billing_cycle);
        $endDateDiff = date_diff(date_create($subscription->begin_date), date_create($subscription->end_date))->days;
        $nextSubscriptionStartDate = date('Y-m-d', strtotime($subscription->end_date . ' +1 day'));
        $pricePerTransaction = $subscription->recurring_payment / ($subscription->monthly_transaction_limit * billing_cycle_to_month($subscription->billing_cycle));
        $formattedPricePerTransaction = number_format($pricePerTransaction);
        $formattedBeginDate = date('d/m/Y', strtotime($subscription->begin_date));
        $formattedEndDate = date('d/m/Y', strtotime($subscription->end_date));

        if ($subscription->allow_exceed_limit) {
            if ($type === 'near_limit') {
                $subject = "Thông báo: Sắp vượt hạn mức giao dịch ({$formattedPercentage}%)";
                $content = <<<HTML
                    <h3>Thông báo: Sắp vượt hạn mức giao dịch</h3> 
                    <p style="font-size: 13px;">Xin chào Quý khách $clientName,</p>
                    <p style="font-size: 13px;">SePay xin thông báo Quý khách đã sử dụng <b>{$usageCount}/{$totalTransactionLimit}</b> giao dịch, đạt <b>{$formattedPercentage}%</b> hạn mức sử dụng.</p>
                    <ul>
                        <li style="font-size: 13px;">Gói đang sử dụng: <b>{$product->name}</b></li>
                        <li style="font-size: 13px;">Hạn mức: <b>{$totalTransactionLimit}</b> giao dịch (theo {$billingCycleText})</li>
                        <li style="font-size: 13px;">Đã sử dụng: <b>{$usageCount}</b> giao dịch</li>
                        <li style="font-size: 13px;">Ngày bắt đầu: <b>{$formattedBeginDate}</b></li>
                        <li style="font-size: 13px;">Ngày kết thúc: <b>{$formattedEndDate}</b> (còn <b>{$endDateDiff}</b> ngày)</li>
                    </ul>
                    <p style="font-size: 13px;">
                        Tài khoản của quý khách <b>có thể vượt hạn mức trong vài ngày tới</b>. Khi vượt hạn mức, tài khoản vẫn hoạt động bình thường.
                        SePay sẽ <b>tính phí vượt</b> tương ứng với gói {$product->name} với <b>chi phí {$formattedPricePerTransaction}đ/giao dịch</b>.
                        Hóa đơn vượt (nếu có) sẽ được tạo vào ngày đầu tiên của chu kỳ tiếp theo, là ngày {$nextSubscriptionStartDate}.
                    </p> 
                    <p style="font-size: 13px;">
                        Email này mang tính chất thông báo đến khách hàng. Quý khách có thể bỏ qua nếu việc vượt hạn mức này nằm trong kế hoạch của mình. Hoặc quý khách có thể <a href="https://my.sepay.vn/company/change_plan" target="_blank">nâng cấp gói dịch vụ</a> để có thêm giao dịch và tiết kiệm chi phí.
                    </p>
                HTML;
            } else {
                $excessTransaction = $usageCount - $totalTransactionLimit;

                $subject = "Thông báo: Đã vượt hạn mức giao dịch";
                $content = <<<HTML
                    <h3>Thông báo: Đã vượt hạn mức giao dịch</h3>
                    <p style="font-size: 13px;">Xin chào Quý khách $clientName,</p>
                    <p style="font-size: 13px;">SePay xin thông báo Quý khách <b>đã sử dụng vượt hạn mức giao dịch</b>. Tổng số giao dịch đã sử dụng: <span style="color: red;">{$usageCount}/{$totalTransactionLimit}</span> (vượt <span style="color: red;">{$excessTransaction}</span> giao dịch, tương đương <span style="color: red;">{$formattedPercentage}%</span>)</p>
                    <ul>
                        <li style="font-size: 13px;">Gói đang sử dụng: <b>{$product->name}</b></li>
                        <li style="font-size: 13px;">Hạn mức: <b>{$totalTransactionLimit}</b> giao dịch (theo {$billingCycleText})</li>
                        <li style="font-size: 13px;">Đã sử dụng: <b>{$usageCount}</b> giao dịch (vượt <b>{$excessTransaction}</b> giao dịch)</li>
                        <li style="font-size: 13px;">Ngày bắt đầu: <b>{$formattedBeginDate}</b></li>
                        <li style="font-size: 13px;">Ngày kết thúc: <b>{$formattedEndDate}</b> (còn <b>{$endDateDiff}</b> ngày)</li>
                    </ul>
                    <p style="font-size: 13px;">
                        Hiện tại, tài khoản của quý khách vẫn hoạt động bình thường. SePay sẽ tính phí vượt tương ứng với gói {$product->name} với chi phí <b>{$formattedPricePerTransaction}đ/giao dịch</b>.
                        Hóa đơn vượt (nếu có) sẽ được tạo vào ngày đầu tiên của chu kỳ tiếp theo, là ngày {$nextSubscriptionStartDate}.
                    </p>
                    <p style="font-size: 13px;">
                        Email này mang tính chất thông báo đến khách hàng. Quý khách có thể bỏ qua nếu việc vượt hạn mức này nằm trong kế hoạch của mình. Hoặc quý khách có thể <a href="https://my.sepay.vn/company/change_plan" target="_blank">nâng cấp gói dịch vụ</a> để có thêm giao dịch và tiết kiệm chi phí.
                    </p>
                HTML;
            }
        } else {
            if ($type === 'near_limit') {
                $subject = "Thông báo: Sắp tiệm cận hạn mức giao dịch ({$formattedPercentage}%)";
                $content = <<<HTML
                    <h3>Thông báo: Sắp tiệm cận hạn mức giao dịch ({$formattedPercentage}%)</h3>
                    <p style="font-size: 13px;">Xin chào Quý khách $clientName,</p>
                    <p style="font-size: 13px;">Tài khoản của quý khách tại SePay có thể sẽ ngưng đồng bộ trong thời gian tới, do đã sử dụng <b>{$usageCount}/{$totalTransactionLimit}</b> giao dịch, đạt <b>{$formattedPercentage}%</b> hạn mức sử dụng.</p>
                    <ul>
                        <li style="font-size: 13px;">Gói đang sử dụng: <b>{$product->name}</b></li>
                        <li style="font-size: 13px;">Hạn mức: <b>{$totalTransactionLimit}</b> giao dịch (theo {$billingCycleText})</li>
                        <li style="font-size: 13px;">Đã sử dụng: <b>{$usageCount}</b> giao dịch</li>
                        <li style="font-size: 13px;">Ngày bắt đầu: <b>{$formattedBeginDate}</b></li>
                        <li style="font-size: 13px;">Ngày kết thúc: <b>{$formattedEndDate}</b> (còn <b>{$endDateDiff}</b> ngày)</li>
                    </ul>
                    <p style="font-size: 13px;">
                        Khi đạt đến 100% hạn mức, tài khoản sẽ ngưng đồng bộ giao dịch. Quý khách cần nâng cấp gói cao hơn hệ không bị gián đoạn dịch vụ. Gói cao hơn sẽ có mức phí trên giao dịch thấp hơn.
                    </p>
                    <p style="font-size: 13px;">
                        Email này mang tính chất thông báo đến khách hàng. Quý khách có thể bỏ qua nếu việc này nằm trong kế hoạch của mình.
                    </p>
                HTML;
            } else {
                $subject = "Thông báo: Đã đạt đến hạn mức giao dịch";
                $content = <<<HTML
                    <h3>Thông báo: Đã đạt đến hạn mức giao dịch</h3>
                    <p style="font-size: 13px;">Xin chào Quý khách $clientName,</p>
                    <p style="font-size: 13px;">SePay xin thông báo Quý khách dùng hết hạn mức của gói, và tài khoản đã ngưng đồng bộ giao dịch. Tổng số giao dịch đã sử dụng: <span style="color: red;">{$usageCount}/{$totalTransactionLimit}</span> (<span style="color: red;">{$formattedPercentage}%</span>).</p>
                    <ul>
                        <li style="font-size: 13px;">Gói đang sử dụng: <b>{$product->name}</b></li>
                        <li style="font-size: 13px;">Hạn mức: <b>{$totalTransactionLimit}</b> giao dịch (theo {$billingCycleText})</li>
                        <li style="font-size: 13px;">Đã sử dụng: <b>{$usageCount}</b> giao dịch</li>
                        <li style="font-size: 13px;">Ngày bắt đầu: <b>{$formattedBeginDate}</b></li>
                        <li style="font-size: 13px;">Ngày kết thúc: <b>{$formattedEndDate}</b> (còn <b>{$endDateDiff}</b> ngày)</li>
                    </ul>
                    <p style="font-size: 13px;">
                        Hiện tại, tài khoản của quý khách đã ngưng đồng bộ giao dịch. Quý khách cần nâng cấp gói cao hơn hệ không bị gián đoạn dịch vụ. Gói cao hơn sẽ có mức phí trên giao dịch thấp hơn.
                    </p>
                HTML;
            }
        }

        $content .= <<<HTML
            <h5 style="margin-bottom: 10px;">Các liên kết hữu ích:</h5>
            <ul>
                <li style="font-size: 13px;"><a href="https://my.sepay.vn/company/plans" target="_blank" style="font-size: 13px;">Xem chi tiết gói đang sử dụng</a></li>
                <li style="font-size: 13px;"><a href="https://my.sepay.vn/invoices" target="_blank" style="font-size: 13px;">Xem danh sách hóa đơn</a></li>
                <li style="font-size: 13px;"><a href="https://my.sepay.vn/company/change_plan" target="_blank" style="font-size: 13px;">Nâng cấp gói</a></li>
                <li style="font-size: 13px;">Bật/ tắt đồng bộ giao dịch: <a href="https://my.sepay.vn/bankaccount" target="_blank" style="font-size: 13px;">Ngân hàng</a> -> Chọn tài khoản -> Cấu hình chung</li>
                <li style="font-size: 13px;"><a href="https://my.sepay.vn/ticket/create" target="_blank" style="font-size: 13px;">Gửi yêu cầu hỗ trợ</a></li>
            </ul>
            <p style="font-size: 13px;">
                Trân trọng.
            </p>
        HTML;

        $from = '<EMAIL>';
        $to = $user->email;
        $message = $this->getEmailLayout($content);

        if (!$isExecute) {
            CLI::write("[DRY RUN] Sẽ gửi thông báo {$type} đến {$to} cho công ty ID {$subscription->company_id}", 'yellow');
            return true;
        }

        $sent = Services::email()
            ->initialize([
                'wordWrap' => true,
                'mailType' => 'html',
            ])
            ->setFrom($from, 'SePay')
            ->setTo($to)
            ->setSubject($subject)
            ->setMessage($message)
            ->send();

        if ($isExecute) {
            model(EmailLogModel::class)->insert([
                'company_id' => $subscription->company_id,
                'email_type' => $type === 'near_limit' ? 'Subscription_Near_Limit' : 'Subscription_Exceeded_Limit',
                'data_id' => $subscription->id,
                'email_to' => $to,
                'email_from' => $from,
                'subject' => $subject,
                'message' => $message,
                'status' => $sent ? 'Sent' : 'Error',
            ]);

            if ($sent) {
                model(CompanySubscriptionModel::class)
                    ->where('id', $subscription->id)
                    ->set([
                        'last_notified_at' => date('Y-m-d H:i:s'),
                        'last_notification_type' => $type,
                    ])
                    ->update();

                CLI::write("Đã gửi thông báo {$type} đến {$to} cho công ty ID {$subscription->company_id}", 'green');
            } else {
                CLI::write("Không thể gửi thông báo {$type} đến {$to} cho công ty ID {$subscription->company_id}", 'red');
            }
        }

        return $sent;
    }

    protected function getEmailLayout(string $content): string
    {
        return <<<EOD
            <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                <html xmlns="http://www.w3.org/1999/xhtml">
                <head>
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                </head>
                <body style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; background-color: #f9f9fb; color: #303f4d; height: 100%; line-height: 1.4; margin: 0; width: 100% !important; -webkit-text-size-adjust: none;">
                    <style>
                        @media  only screen and (max-width: 600px) {
                            .inner-body {
                                width: 100% !important;
                            }
                
                            .footer {
                                width: 100% !important;
                            }
                        }
                
                        @media  only screen and (max-width: 500px) {
                            .button {
                                width: 100% !important;
                            }
                        }
                    </style>
                <table class="wrapper" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;"><tr>
                <td align="center" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">
                                <table class="content" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
                <tr>
                <td class="header" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 25px 0; text-align: center;">
                        <a href="https://sepay.vn" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #bbbfc3; font-size: 19px; font-weight: bold; text-decoration: none; text-shadow: 0 1px 0 white;">
                            <img width="100" src="https://my.sepay.vn/assets/images/logo/sepay-blue-359x116.png" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; max-width: 100%; border: none;"></a>
                    </td>
                </tr>
                <!-- Email Body --><tr>
                <td class="body" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; margin: 0; padding: 0; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%;">
                                            <table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; background-color: #FFFFFF; margin: 0 auto; padding: 0; width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px;">
                <!-- Body content --><tr>
                <td class="content-cell" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; padding: 25px;">
                $content
                <!-- Subcopy -->
                <table class="subcopy" width="100%" cellpadding="0" cellspacing="0" style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; border-top: 1px solid #EDEFF2; margin-top: 25px; padding-top: 25px;"><tr>
                <td style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box;">


                    <p style="font-family: Lato, lato, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; box-sizing: border-box; color: #303f4d; line-height: 1.5em; margin-top: 0; text-align: center; font-size: 12px;">Copyright © 2025 SePay - All rights reserved.</p>
                        </td>
                    </tr></table>
                </td>
                                                </tr>
                </table>
                </td>
                                    </tr>
                </table>
                </td>
                </tr>
                </table>
                </td>
                        </tr></table>
                </body>
                </html>
        EOD;
    }
}
