<?php

namespace App\Services;

use App\Libraries\SInvoice;
use App\Models\InvoiceCustomerInfoModel;
use App\Models\InvoiceModel;
use App\Models\PhysicalInvoiceModel;
use App\Models\PhysicalOrderItemModel;
use App\Models\PhysicalOrderModel;
use Config\SInvoice as Config;
use Exception;
use NumberFormatter;

class CreatePhysicalInvoice
{
    protected SInvoice $sInvoice;

    protected Config $config;

    public function __construct()
    {
        $this->config = config(Config::class);
        $this->sInvoice = (new SInvoice())->setConfig($this->config);
    }

    public function createInvoice($orderId, $data = [])
    {
        $order = model(PhysicalOrderModel::class)->find($orderId);

        if (! $order) {
            throw new Exception('Không tìm thấy đơn hàng');
        }

        $invoiceModel = model(PhysicalInvoiceModel::class);
        $invoice = $invoiceModel
            ->select('tb_autopay_physical_invoices.id, tb_autopay_invoice.id as invoice_id')
            ->join('tb_autopay_invoice', 'tb_autopay_invoice.physical_invoice_id = tb_autopay_physical_invoices.id', 'left')
            ->where('tb_autopay_physical_invoices.order_id', $orderId)
            ->first();

        if (isset($data['invoice_email'])) {
            $data['invoice_email'] = (array) $data['invoice_email'];
        } elseif ($order->customer_email) {
            $data['invoice_email'] = [$order->customer_email];
        } else {
            $data['invoice_email'] = [];
        }

        $invoiceCustomerData = [
            'name' => $data['name'] ?? $order->customer_name,
            'phone' => $order->customer_phone,
            'email' => json_encode($data['invoice_email']),
            'address' => $data['address'] ?? "{$order->address}, {$order->ward}, {$order->district}, {$order->province}",
            'company_name' => $data['company_name'] ?? null,
            'tax_code' => $data['tax_code'] ?? null,
        ];

        if ($invoice) {
            model(InvoiceCustomerInfoModel::class)
                ->where('invoice_id', $invoice->invoice_id)
                ->set($invoiceCustomerData)
                ->update();

            return $invoice->id;
        }

        $invoiceData = [
            'order_id' => $orderId,
            'total_amount' => $order->subtotal - ($order->subtotal * $this->config->taxPercentage / (100 + $this->config->taxPercentage)),
            'tax_rate' => $this->config->taxPercentage,
            'tax_amount' => $order->subtotal * $this->config->taxPercentage / (100 + $this->config->taxPercentage),
            'shipping_fee' => $order->shipping_fee,
            'discount_amount' => $order->discount_amount,
            'final_amount' => $order->total_amount,
            'payment_method' => $order->payment_method === 'bank_transfer' ? 'bank_transfer' : 'cash',
            'status' => 'pending',
        ];

        $invoiceId = $invoiceModel->insert($invoiceData);

        if (! $invoiceId) {
            throw new Exception('Không thể tạo hóa đơn');
        }

        $originalOrderId = model(InvoiceModel::class)->insert([
            'physical_invoice_id' => $invoiceId,
            'status' => $order->payment_status,
            'type' => 'PhysicalProduct',
            'subtotal' => $order->subtotal,
            'total' => $invoiceData['total_amount'],
            'tax' => $invoiceData['tax_amount'],
            'tax_rate' => $invoiceData['tax_rate'],
            'public_note' => $order->notes,
        ]);

        model(InvoiceCustomerInfoModel::class)->insert(array_merge($invoiceCustomerData, [
            'invoice_id' => $originalOrderId,
        ]));

        return $invoiceId;
    }

    public function issueInvoice($invoiceId)
    {
        $invoiceModel = model(PhysicalInvoiceModel::class);
        $invoice = $invoiceModel->find($invoiceId);

        if (! $invoice) {
            throw new Exception('Không tìm thấy hóa đơn');
        }

        $order = model(PhysicalOrderModel::class)->find($invoice->order_id);

        if (! $order) {
            throw new Exception('Không tìm thấy đơn hàng');
        }

        $orderItems = model(PhysicalOrderItemModel::class)->where('order_id', $invoice->order_id)->findAll();

        try {
            $invoiceData = $this->prepareInvoiceData($invoiceId, $orderItems);
            $response = $this->sInvoice->createInvoice($invoiceData, $this->config);

            $invoiceId = null;

            if ($this->config->useDraftInvoice) {
                if (
                    isset($response['code']) && $response['code'] === 400
                    || ($response['errorCode'] !== '' && $response['description'] !== '' && $response['result'] !== [])
                ) {
                    log_message('error', 'Create draft invoice failed: ' . $invoice->id . ' ' . json_encode($response));
                    throw new Exception($response['data']);
                }

                $status = 'draft';
            } else {
                if ($response['errorCode'] !== null || ! isset($response['result'])) {
                    log_message('error', 'Create invoice failed: ' . $invoice->id . ' ' . json_encode($response));

                    throw new Exception($response['data']);
                }

                $status = 'issued';
                $invoiceId = $response['result']['invoiceNo'];
            }

            $invoiceModel->update($invoice->id, [
                'status' => $status,
                'invoice_id' => $invoiceId,
                'invoice_date' => $status === 'issued' ? date('Y-m-d H:i:s', $invoiceData['generalInvoiceInfo']['invoiceIssuedDate'] / 1000) : null,
            ]);

            return $invoiceId;
        } catch (Exception $e) {
            throw $e;
        }
    }

    protected function prepareInvoiceData($invoiceId, $orderItems)
    {
        $itemInfo = [];
        $lineNumber = 1;
        $totalAmountWithoutTax = 0;

        foreach ($orderItems as $item) {
            $taxRate = 1 + ($this->config->taxPercentage / 100);
            $priceWithoutTax = $item->price / $taxRate;
            $unitPrice = round($priceWithoutTax, 2);
            $calculatedTotal = $unitPrice * $item->quantity;
            $itemTaxAmount = round($calculatedTotal * ($this->config->taxPercentage / 100), 2);

            $itemInfo[] = [
                'lineNumber' => $lineNumber++,
                'itemCode' => $item->product_id,
                'itemName' => $item->name,
                'unitName' => 'Cái',
                'unitPrice' => $unitPrice,
                'quantity' => $item->quantity,
                'itemTotalAmountWithoutTax' => $calculatedTotal,
                'taxPercentage' => $this->config->taxPercentage,
                'taxAmount' => $itemTaxAmount,
                'discount' => 0.0,
                'itemDiscount' => 0.0,
            ];
            $totalAmountWithoutTax += $calculatedTotal;
        }

        $invoice = model(InvoiceModel::class)
            ->select([
                'tb_autopay_invoice.id',
                'tb_autopay_invoice.status',
                'tb_autopay_invoice_customer_info.name',
                'tb_autopay_invoice_customer_info.company_name',
                'tb_autopay_invoice_customer_info.tax_code',
                'tb_autopay_invoice_customer_info.address',
                'tb_autopay_invoice_customer_info.phone',
                'tb_autopay_invoice_customer_info.email',
                'tb_autopay_invoice.subtotal',
                'tb_autopay_invoice.total',
                'tb_autopay_invoice.tax',
            ])
            ->join('tb_autopay_invoice_customer_info', 'tb_autopay_invoice_customer_info.invoice_id = tb_autopay_invoice.id')
            ->where('physical_invoice_id', $invoiceId)
            ->first();

        $invoiceDate = date('Y-m-d H:i:s');
        $email = json_decode($invoice->email);

        return [
            'generalInvoiceInfo' => [
                'paymentStatus' => $invoice->status === 'Paid',
                'invoiceIssuedDate' => strtotime($invoiceDate) * 1000,
            ],
            'buyerInfo' => [
                'buyerName' => $invoice->company_name ? null : $invoice->name,
                'buyerLegalName' => $invoice->company_name ?: $invoice->name,
                'buyerTaxCode' => $invoice->tax_code,
                'buyerAddressLine' => $invoice->address,
                'buyerPhoneNumber' => $invoice->phone,
                'buyerEmail' => is_array($email) ? implode(';', $email) : null,
            ],
            'itemInfo' => $itemInfo,
            'summarizeInfo' => [
                'sumOfTotalLineAmountWithoutTax' => $invoice->subtotal,
                'totalAmountWithoutTax' => $invoice->subtotal,
                'totalTaxAmount' => $invoice->tax,
                'totalAmountWithTax' => $invoice->total,
                'totalAmountWithTaxInWords' => $this->numberToWords($invoice->total),
                'taxPercentage' => $this->config->taxPercentage,
            ],
            'taxBreakdowns' => [
                [
                    'taxPercentage' => $this->config->taxPercentage,
                    'taxableAmount' => $invoice->total,
                    'taxAmount' => $invoice->tax,
                ],
            ],
        ];
    }

    protected function numberToWords($number)
    {
        return (new NumberFormatter('vi', NumberFormatter::SPELLOUT))->format($number);
    }
}
