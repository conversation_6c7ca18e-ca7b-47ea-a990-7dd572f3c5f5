body {
    opacity: 1 !important;
    overflow-y: scroll;
    height: 100%;
    --bs-body-font-size: 0.925rem;
}

a {
    text-decoration: none;
}

.btn {
    text-decoration: none;
}

.d-contents {
    display: contents !important;
}

.dataTables_length {
    padding: 0.25rem !important;
}

.form-control.is-invalid:focus {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.25rem rgb(220 53 69 / 25%) !important;
}

.form-control.is-invalid {
    border-color: #dc3545 !important;
    padding-right: calc(1.5em + 0.75rem) !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right calc(0.375em + 0.1875rem) center !important;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) !important;
}

legend {
    float: inherit;
}

.wrapper {
    align-items: stretch;
    display: flex;
    width: 100%;
}

.sidebar {
    background: #2d6bcf;
    direction: ltr;
    max-width: 240px;
    min-width: 240px;
    transition: margin-left .35s ease-in-out, left .35s ease-in-out, margin-right .35s ease-in-out, right .35s ease-in-out;
}

.navbar-bg {
    background: #fff;
}

.navbar {
    border-bottom: 0;
    box-shadow: 0 0 2rem 0 rgb(41 48 66 / 10%);
}

.navbar-expand {
    flex-wrap: nowrap;
    justify-content: flex-start;
}

.navbar {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    /*justify-content: space-between;*/
    padding: 0.875rem 1.25rem;
    /*position: sticky;
    top: 0;*/
}

.main {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    min-width: 0;
    transition: margin-left .35s ease-in-out, left .35s ease-in-out, margin-right .35s ease-in-out, right .35s ease-in-out;
    width: 100%;
    background-color: #F2F4F9;
}

.sidebar-toggle {
    margin-right: 1rem;
    cursor: pointer;
    display: flex;
    height: 26px;
    width: 26px;
    text-decoration: none;
}

.content {
    direction: ltr;
    flex: 1;
    overflow-y: scroll;
  /*  padding: 1.5rem 1.5rem 2.5rem; */
}

[data-simplebar] {
    align-content: flex-start;
    align-items: flex-start;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;
    position: relative;
}

.sidebar-content {
    height: 100vh;
    left: 0;
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    transition: margin-left .35s ease-in-out, left .35s ease-in-out, margin-right .35s ease-in-out, right .35s ease-in-out;
}



.sidebar.collapsed {
    margin-left: -240px;
}

.hamburger {
    position: relative;
}

.hamburger,
.hamburger:after,
.hamburger:before {
    background: #495057;
    border-radius: 1px;
    content: "";
    cursor: pointer;
    display: block;
    height: 3px;
    transition: background .1s ease-in-out, color .1s ease-in-out;
    width: 24px;
}

.hamburger:before {
    position: absolute;
    top: -8px;
    width: 20px;
}

.hamburger:after {
    bottom: -8px;
    position: absolute;
    width: 16px;
}

.sub-menu {
    margin-top: 30px;
    list-style: none;
    line-height: 35px;
    overflow: scroll;
    margin-left: -45px;
}

li.sub-menu-item {
    padding: 8px 12px;
    color: #8F97A3;
    font-size: 14px;
    font-weight: 700;
    white-space: nowrap;
}

.sub-menu-item a {
    display: block;
    text-decoration: none;
}

a.link-secondary.active {
    color: #495057;
}

a.link-secondary {
    color: #8F97A3;
}

.sidebar-brand {
    color: #fff;
    display: block;
    font-size: 1.5rem;
    font-weight: 500;
    padding: 1.15rem 1.5rem;
    text-align: center;
    text-decoration: none;
}

.sidebar-nav {
    list-style: none;
    padding-left: 0;
}

a.sidebar-link {
    color: #f8f9faa8;
    ;
    cursor: pointer;
    display: block;
    font-weight: 400;
    padding: 0.625rem 1.625rem;
    position: relative;
    text-decoration: none;
    transition: color 75ms ease-in-out;
}

a.sidebar-link.active {
    color: #fff;
}

a.sidebar-link i {
    margin-right: 1em;
}

.left-sub-menu {
    padding-top: 30px;
}

.content-body {
    margin-top: 30px;
    font-size: 13px;
}

footer.footer {
    background: #fff;
    border-top: 1px solid #dee6ed;
    direction: ltr;
    padding: 1rem 0.75rem;
}

.form-control:disabled,
.form-control[readonly] {
    background-color: #f8f9fa;
    opacity: 1;
    border: 0;
}

#sites_table tbody tr {
    height: 70px;
}

.navbar-expand .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
}

.navbar-collapse {
    align-items: center;
    flex-basis: 100%;
    flex-grow: 1;
}

.navbar-align {
    margin-left: auto;
}

.avatar {
    height: 40px;
    margin-bottom: -15px;
    margin-top: -15px;
    width: 40px;
}

.cursor-pointer {
    cursor: pointer;
}

@media (max-width: 575.98px) {
    .navbar {
        padding: 0.75rem;
    }

}

@media (max-width: 767.98px) {

    .sub-menu {
        display: flex;
        padding: 0 0 0 0;
        margin-left: 0px;
    }

    .navbar {
        width: 100vw;
    }

    .main {
        overflow-y: hidden;
    }

    .sidebar.collapsed {
        margin-left: 0px;
    }

    .content {
        max-width: 100vw;
        width: 100vw;
        padding-top: 1rem;
    }

    .left-sub-menu {
        padding-top: 0px;
    }
}


@media (min-width: 1px) and (max-width: 991.98px) {
    .sidebar {
        margin-left: -240px;
    }
}

@media (min-width: 768.98px) and (max-width: 991.97px) {
    .sidebar.collapsed {
        margin-left: 0px;
    }
}

@media (min-width: 768px) {
    .vh-md-100 {
        height: 100vh !important;
    }

    .popover {
        min-width: 420px;
    }

    .sub-menu-item a:before {
        border: solid;
        border-width: 0 0.1rem 0.1rem 0;
        content: " ";
        position: relative;
        padding: 2px;
        float: right;
        top: 1rem;
        transform: rotate(309deg);
        transition: all .2s ease-out;
    }
}

.timeline {
    list-style-type: none;
    position: relative
}

.timeline:before {
    background: #dee6ed;
    height: 100%;
    left: 9px;
    width: 2px
}

.timeline-item:before,
.timeline:before {
    content: " ";
    display: inline-block;
    position: absolute;
    z-index: 1
}

.timeline-item:before {
    background: #fff;
    border: 3px solid #3f80ea;
    border-radius: 50%;
    height: 20px;
    left: 0;
    width: 20px
}


.timeline-danger:before {
   
    border: 3px solid #b0394c !important;
    
}

.h3, h3 {
    font-size: 1.2375rem;
}
 

.card {
    box-shadow: 0 0 0.875rem 0 rgb(41 48 66 / 5%);
    margin-bottom: 24px;
}
.card-title {
    color: #495057;
    font-size: .95rem;
    font-weight: 500;
}
.stat-sm {
    height: 40px;
    padding: 0.625rem;
    width: 40px;
}

.stat {
    background: #e0eafc;
    border-radius: 50%;
    height: 48px;
    padding: 0.75rem;
    width: 48px;
}

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
    color: #495057;
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: 0.5rem;
    margin-top: 0;
}

.card {
    border: 0px solid rgba(0,0,0,.125);
}

.stat svg {
    color: #3f80ea!important;
    height: 24px;
    width: 24px;
}