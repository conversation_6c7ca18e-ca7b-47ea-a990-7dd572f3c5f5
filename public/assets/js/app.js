document.addEventListener("DOMContentLoaded", (function () {
    if (document.getElementsByClassName("js-simplebar")[0]) {
        var t = document.getElementsByClassName("sidebar")[0];
        document.getElementsByClassName("sidebar-toggle")[0].addEventListener("click", (function () {
            t.classList.toggle("collapsed"), t.addEventListener("transitionend", (function () {
                window.dispatchEvent(new Event("resize"))
            }))
        }))
    }
}));


var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
  return new bootstrap.Popover(popoverTriggerEl)
})

var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
  return new bootstrap.Tooltip(tooltipTriggerEl, {trigger : 'hover'})
})

var notyf = new Notyf({duration: 5000,
  dismissible: true,
  position: {
      x: 'right',
      y: 'top',
}});
