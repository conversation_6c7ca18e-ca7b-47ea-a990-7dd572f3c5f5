!function(){"use strict";var n=function(){return n=Object.assign||function(n){for(var t,e=1,r=arguments.length;e<r;e++)for(var i in t=arguments[e])Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=t[i]);return n},n.apply(this,arguments)},t="undefined"!=typeof window?window:Function("return this;")(),e=function(){var n,e=null!==(n=t&&t.jQuery)&&void 0!==n?n:null;if(null!=e)return e;throw new Error("Expected global jQuery")};const r=n=>t=>(n=>{const t=typeof n;return null===n?"null":"object"===t&&Array.isArray(n)?"array":"object"===t&&(e=i=n,r=(o=String).prototype,r.isPrototypeOf(e)||(null===(u=i.constructor)||void 0===u?void 0:u.name)===o.name)?"string":t;var e,r;var i,o,u})(t)===n,i=r("string"),o=r("object"),u=r("array"),l=(c="function",n=>typeof n===c);var c;const a=Object.keys,f=Object.hasOwnProperty,s="undefined"!=typeof window?window:Function("return this;")(),v=(n,t)=>((n,t)=>{let e=null!=t?t:s;for(let t=0;t<n.length&&null!=e;++t)e=e[n[t]];return e})(n.split("."),t),h=(n,t)=>{const e=((n,t)=>v(n,t))(n,t);if(null==e)throw new Error(n+" not available on this browser");return e},p=Object.getPrototypeOf,d=n=>{const t=v("ownerDocument.defaultView",n);return o(n)&&((n=>h("HTMLElement",n))(t).prototype.isPrototypeOf(n)||/^HTML\w*Element$/.test(p(n).constructor.name))};var y,g=function(){var n;return null!==(n=t.tinymce)&&void 0!==n?n:null},m=function(){return!!g()},O=function(){var n=g();if(null!=n)return n;throw new Error("Expected global tinymce")},w=function(n){var t=null;return n&&n.id&&m()&&(t=O().get(n.id)),t},D=function(n,t,e){var r=w(n);return r?t(r):e?e(n):void 0};!function(n){n[n.NOT_LOADING=0]="NOT_LOADING",n[n.LOADING_STARTED=1]="LOADING_STARTED",n[n.LOADING_FINISHED=2]="LOADING_FINISHED"}(y||(y={}));var _,I=y.NOT_LOADING,N=[],b=function(n,t){n.each((function(e,r){return D(r,(function(e){return t(e,r,n)}))}))},A=function(n){return function(n,t){n.each((function(e,r){for(var i=0,o=O().get();i<o.length;i++){var u=o[i];if($.contains(r,u.getContentAreaContainer())&&!1===t(u,r,n))return!1}}))}(n,(function(n){return n.remove()}))},E=function(n){!function(n){b(n,(function(n){return n.remove()}))}(n),A(n)},C=function(t){return function(){for(var e=this,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];var u=function(n){void 0!==n&&(A(e),e.each((function(e,r){return D(r,(function(t){var i=l(n)?n.call(r,e,t.getContent()):n;void 0!==i&&t.setContent(null===i?"":"".concat(i))}),(function(r){if(l(n)){var i=t.call($(r),"value"),o=n.call(r,e,i);t.call($(r),"value",o)}else t.call($(r),"value",n)}))})))},c=r[0];if(i(c)){if("value"!==c)return t.apply(this,r);var s=r[1];return void 0!==s?(u(s),this):this.length>=1?D(this[0],(function(n){return n.getContent()}),(function(n){return t.call(e,"value")})):void 0}var v,h,p=n({},c);return v=p,h="value",f.call(v,h)&&(u(p.value),delete p.value),a(p).length>0?t.call(this,p):this}},T=function(n,t){var e=document.createElement("div");return n.apply($(e),t),e.innerHTML},L=function(n,t){return function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var i,o="prepend"===t;if(1===e.length&&l(e[0])){var u=e[0];i=function(t,e){return T(n,[u.call(t,0,e)])}}else{var c=e;i=(n=>{let t,e=!1;return(...r)=>(e||(e=!0,t=n.apply(null,r)),t)})((function(t,e){return T(n,c)}))}return this.each((function(t,r){return D(r,(function(n){var t=n.getContent(),e=i(r,t);n.setContent(o?e+t:t+e)}),(function(t){n.apply($(t),e)}))})),this}},j=function(n){var t;n.fn.html=(t=n.fn.html,function(n){return 0===arguments.length?this.length>=1?D(this[0],(function(n){return n.getContent()}),(function(n){return t.call($(n))})):void 0:(A(this),this.each((function(e,r){D(r,(function(o){var u=l(n)?n.call(r,e,o.getContent()):n,c=i(u)?u:function(){d(u)&&E($(u));var n=document.createElement("div");return t.call($(n),null!=u?u:""),n.innerHTML}();o.setContent(c)}),(function(i){if(l(n)){var o=t.call($(r)),u=n.call(r,e,o);t.call($(r),u)}else t.call($(i),null!=n?n:"")}))})),this)}),n.fn.text=function(n){return function(t){if(0===arguments.length){var e="";return this.each((function(t,r){e+=D(r,(function(n){return n.getContent({format:"text"})}),(function(t){return n.call($(t))}))})),e}return A(this),this.each((function(e,r){D(r,(function(n){var i=l(t)?t.call(r,e,n.getContent({format:"text"})):t,o=document.createElement("div");o.innerText="".concat(i),n.setContent(o.innerHTML)}),(function(i){if(l(t)){var o=n.call($(r)),u=t.call(r,e,o);n.call($(r),u)}else n.call($(i),null!=t?t:"")}))})),this}}(n.fn.text),n.fn.val=function(n){return function(t){return 0===arguments.length?this.length>=1?D(this[0],(function(n){return n.getContent()}),(function(t){return n.call($(t))})):void 0:(this.each((function(e,r){D(r,(function(n){var i=l(t)?t.call(r,e,n.getContent()):null!=t?t:"",o=u(i)?i.join(""):"".concat(i);n.setContent(o)}),(function(i){if(l(t)){var o=n.call($(r)),u=t.call(r,e,null!=o?o:"");n.call($(r),u)}else n.call($(i),null!=t?t:"")}))})),this)}}(n.fn.val),n.fn.append=L(n.fn.append,"append"),n.fn.prepend=L(n.fn.prepend,"prepend"),n.fn.remove=function(n){return function(t){return E(void 0!==t?this.filter(t):this),n.call(this,t)}}(n.fn.remove),n.fn.empty=function(n){return function(){return A(this),b(this,(function(n){n.setContent("")})),n.call(this)}}(n.fn.empty),n.fn.attr=C(n.fn.attr)},G=!1,x=function(t){var r,i=this;return this.length?t?(this.css("visibility","hidden"),new Promise((function(r){!function(n,t){if(m()||I!==y.NOT_LOADING)I===y.LOADING_STARTED?N.push(t):t(O(),!1);else{I=y.LOADING_STARTED;var e=document.createElement("script");e.type="text/javascript",e.onload=function(n){if(I!==y.LOADING_FINISHED&&"load"===n.type){I=y.LOADING_FINISHED;var e=O();t(e,!0);for(var r=0;r<N.length;r++)N[r](e,!1)}},e.src=n,document.body.appendChild(e)}}(function(n){if("string"==typeof n.script_url)return n.script_url;var t="string"==typeof n.channel?n.channel:"6",e="string"==typeof n.api_key?n.api_key:"no-api-key";return"https://cdn.tiny.cloud/1/".concat(e,"/tinymce/").concat(t,"/tinymce.min.js")}(t),(function(o,u){u&&t.script_loaded&&t.script_loaded(),G||(G=!0,j(e()));var l=0,c=function(n,t){if("string"==typeof t){var e=n.resolve(t);if("function"==typeof e){var r=-1===t.indexOf(".")?n:n.resolve(t.replace(/\.\w+$/,""));return e.bind(r)}}else if("function"==typeof t)return t.bind(n);return null}(o,t.oninit),a=function(){var n=function(n,t){var e=[];return t.each((function(t,r){var i=n.get(r.id);null!=i&&e.push(i)})),e}(o,i);c&&c(n),r(n)};i.each((function(e,r){if(r.id||(r.id=o.DOM.uniqueId()),o.get(r.id))l++;else{o.init(n(n({},t),{selector:void 0,target:r,init_instance_callback:function(n){i.css("visibility",""),l++;var e=t.init_instance_callback;"function"==typeof e&&e.call(n,n),l===i.length&&a()}}))}})),l===i.length&&a()}))}))):null!==(r=w(this[0]))&&void 0!==r?r:void 0:t?Promise.resolve([]):void 0};(_=e()).expr.pseudos.tinymce=function(n){return!!w(n)},_.fn.tinymce=x}();
