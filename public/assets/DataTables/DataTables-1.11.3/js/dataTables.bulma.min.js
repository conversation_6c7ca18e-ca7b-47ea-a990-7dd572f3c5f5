/*!
 DataTables Bulma integration
 ©2020 SpryMedia Ltd - datatables.net/license
*/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.findInternal=function(a,b,c){a instanceof String&&(a=String(a));for(var e=a.length,d=0;d<e;d++){var f=a[d];if(b.call(c,f,d,a))return{i:d,v:f}}return{i:-1,v:void 0}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};$jscomp.getGlobal=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(a,b){var c=$jscomp.propertyToPolyfillSymbol[b];if(null==c)return a[b];c=a[c];return void 0!==c?c:a[b]};
$jscomp.polyfill=function(a,b,c,e){b&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(a,b,c,e):$jscomp.polyfillUnisolated(a,b,c,e))};$jscomp.polyfillUnisolated=function(a,b,c,e){c=$jscomp.global;a=a.split(".");for(e=0;e<a.length-1;e++){var d=a[e];if(!(d in c))return;c=c[d]}a=a[a.length-1];e=c[a];b=b(e);b!=e&&null!=b&&$jscomp.defineProperty(c,a,{configurable:!0,writable:!0,value:b})};
$jscomp.polyfillIsolated=function(a,b,c,e){var d=a.split(".");a=1===d.length;e=d[0];e=!a&&e in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var f=0;f<d.length-1;f++){var k=d[f];if(!(k in e))return;e=e[k]}d=d[d.length-1];c=$jscomp.IS_SYMBOL_NATIVE&&"es6"===c?e[d]:null;b=b(c);null!=b&&(a?$jscomp.defineProperty($jscomp.polyfills,d,{configurable:!0,writable:!0,value:b}):b!==c&&($jscomp.propertyToPolyfillSymbol[d]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol(d):$jscomp.POLYFILL_PREFIX+d,d=
$jscomp.propertyToPolyfillSymbol[d],$jscomp.defineProperty(e,d,{configurable:!0,writable:!0,value:b})))};$jscomp.polyfill("Array.prototype.find",function(a){return a?a:function(b,c){return $jscomp.findInternal(this,b,c).v}},"es6","es3");
(function(a){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(b){return a(b,window,document)}):"object"===typeof exports?module.exports=function(b,c){b||(b=window);c&&c.fn.dataTable||(c=require("datatables.net")(b,c).$);return a(c,b,b.document)}:a(jQuery,window,document)})(function(a,b,c,e){var d=a.fn.dataTable;a.extend(!0,d.defaults,{dom:"<'columns is-gapless is-multiline'<'column is-half'l><'column is-half'f><'column is-full'tr><'column is-half'i><'column is-half'p>>",
renderer:"bulma"});a.extend(d.ext.classes,{sWrapper:"dataTables_wrapper dt-bulma",sFilterInput:"input",sLengthSelect:"custom-select custom-select-sm form-control form-control-sm",sProcessing:"dataTables_processing card"});d.ext.renderer.pageButton.bulma=function(f,k,D,E,m,v){var w=new d.Api(f),p=f.oLanguage.oPaginate,F=f.oLanguage.oAria.paginate||{},h,l,x=0,A=function(t,y){var z,G=function(q){q.preventDefault();a(q.currentTarget).hasClass("disabled")||w.page()==q.data.action||w.page(q.data.action).draw("page")};
var u=0;for(z=y.length;u<z;u++){var g=y[u];if(Array.isArray(g))A(t,g);else{l=h="";var r="a";var n=!1;switch(g){case "ellipsis":h="&#x2026;";l="pagination-link";r="span";break;case "first":h=p.sFirst;l=g;n=0>=m;break;case "previous":h=p.sPrevious;l=g;n=0>=m;break;case "next":h=p.sNext;l=g;n=m>=v-1;break;case "last":h=p.sLast;l=g;n=m>=v-1;break;default:h=g+1,l=m===g?"is-current":""}h&&(r=a("<li>",{id:0===D&&"string"===typeof g?f.sTableId+"_"+g:null}).append(a("<"+r+">",{href:"#","aria-controls":f.sTableId,
"aria-label":F[g],"data-dt-idx":x,tabindex:f.iTabIndex,"class":"pagination-link "+l,disabled:n}).html(h)).appendTo(t),f.oApi._fnBindAction(r,{action:g},G),x++)}}};try{var B=a(k).find(c.activeElement).data("dt-idx")}catch(t){}var C=a('<nav class="pagination" role="navigation" aria-label="pagination"><ul class="pagination-list"></ul></nav>');a(k).empty().append(C);A(C.find("ul"),E);B!==e&&a(k).find("[data-dt-idx="+B+"]").trigger("focus")};a(c).on("init.dt",function(f,k){"dt"===f.namespace&&(f=new a.fn.dataTable.Api(k),
a("div.dataTables_length select",f.table().container()).wrap('<div class="select">'))});return d});
