/*!
 DataTables Foundation integration
 ©2011-2015 SpryMedia Ltd - datatables.net/license
*/
(function(b){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(c){return b(c,window,document)}):"object"===typeof exports?module.exports=function(c,l){c||(c=window);l&&l.fn.dataTable||(l=require("datatables.net")(c,l).$);return b(l,c,c.document)}:b(jQuery,window,document)})(function(b,c,l,F){var k=b.fn.dataTable;c=b('<meta class="foundation-mq"/>').appendTo("head");k.ext.foundationVersion=c.css("font-family").match(/small|medium|large/)?6:5;c.remove();b.extend(k.ext.classes,
{sWrapper:"dataTables_wrapper dt-foundation",sProcessing:"dataTables_processing panel callout"});b.extend(!0,k.defaults,{dom:"<'row grid-x'<'small-6 columns cell'l><'small-6 columns cell'f>r>t<'row grid-x'<'small-6 columns cell'i><'small-6 columns cell'p>>",renderer:"foundation"});k.ext.renderer.pageButton.foundation=function(g,x,y,z,d,m){var r=new k.Api(g),A=g.oClasses,n=g.oLanguage.oPaginate,B=g.oLanguage.oAria.paginate||{},e,h,f,C=5===k.ext.foundationVersion,w=function(t,u){var v,D=function(p){p.preventDefault();
b(p.currentTarget).hasClass("unavailable")||r.page()==p.data.action||r.page(p.data.action).draw("page")};var q=0;for(v=u.length;q<v;q++){var a=u[q];if(Array.isArray(a))w(t,a);else{h=e="";f=null;switch(a){case "ellipsis":e="&#x2026;";h="unavailable disabled";f=null;break;case "first":e=n.sFirst;h=a+(0<d?"":" unavailable disabled");f=0<d?"a":null;break;case "previous":e=n.sPrevious;h=a+(0<d?"":" unavailable disabled");f=0<d?"a":null;break;case "next":e=n.sNext;h=a+(d<m-1?"":" unavailable disabled");
f=d<m-1?"a":null;break;case "last":e=n.sLast;h=a+(d<m-1?"":" unavailable disabled");f=d<m-1?"a":null;break;default:e=a+1,h=d===a?"current":"",f=d===a?null:"a"}C&&(f="a");if(e){var E=b("<li>",{"class":A.sPageButton+" "+h,"aria-controls":g.sTableId,"aria-label":B[a],tabindex:g.iTabIndex,id:0===y&&"string"===typeof a?g.sTableId+"_"+a:null}).append(f?b("<"+f+"/>",{href:"#"}).html(e):e).appendTo(t);g.oApi._fnBindAction(E,{action:a},D)}}}};w(b(x).empty().html('<ul class="pagination"/>').children("ul"),
z)};return k});
